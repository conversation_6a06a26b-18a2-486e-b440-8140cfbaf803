{"ast": null, "code": "import _taggedTemplateLiteral from\"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4;import React,{useState,useEffect}from'react';import styled from'styled-components';import MapComponent from'./MapComponent';import NavigationPanel from'./NavigationPanel';import SearchPanel from'./SearchPanel';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AppContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: flex;\\n  height: 100vh;\\n  width: 100vw;\\n  background-color: #1a1a1a;\\n  color: white;\\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n  overflow: hidden;\\n\"])));const MapContainer=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  flex: 1;\\n  position: relative;\\n  height: 100%;\\n\"])));const SidePanel=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  width: \",\";\\n  transition: width 0.3s ease;\\n  background-color: #2d2d2d;\\n  border-left: 1px solid #444;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n  \\n  @media (max-width: 768px) {\\n    position: absolute;\\n    right: 0;\\n    top: 0;\\n    height: 100%;\\n    z-index: 1000;\\n    width: \",\";\\n  }\\n\"])),props=>props.isOpen?'400px':'0px',props=>props.isOpen?'100%':'0px');const ToggleButton=styled.button(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  position: absolute;\\n  top: 20px;\\n  right: 20px;\\n  z-index: 1001;\\n  background-color: #007bff;\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  width: 60px;\\n  height: 60px;\\n  font-size: 24px;\\n  cursor: pointer;\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\\n  transition: all 0.3s ease;\\n  \\n  &:hover {\\n    background-color: #0056b3;\\n    transform: scale(1.1);\\n  }\\n  \\n  &:active {\\n    transform: scale(0.95);\\n  }\\n\"])));const GPSNavigationApp=()=>{const[currentLocation,setCurrentLocation]=useState(null);const[destination,setDestination]=useState(null);const[route,setRoute]=useState(null);const[isNavigating,setIsNavigating]=useState(false);const[isPanelOpen,setIsPanelOpen]=useState(false);const[searchMode,setSearchMode]=useState('search');// Get user's current location\nuseEffect(()=>{if(navigator.geolocation){const watchId=navigator.geolocation.watchPosition(position=>{const newLocation={lat:position.coords.latitude,lng:position.coords.longitude,accuracy:position.coords.accuracy,timestamp:Date.now()};setCurrentLocation(newLocation);},error=>{console.error('Error getting location:',error);// Fallback to a default location (Tehran, Iran)\nsetCurrentLocation({lat:35.6892,lng:51.3890,accuracy:100,timestamp:Date.now()});},{enableHighAccuracy:true,timeout:10000,maximumAge:60000});return()=>navigator.geolocation.clearWatch(watchId);}},[]);const handleDestinationSelect=location=>{setDestination(location);setSearchMode('navigation');setIsPanelOpen(true);};const handleStartNavigation=routeData=>{setRoute(routeData);setIsNavigating(true);setIsPanelOpen(false);};const handleStopNavigation=()=>{setIsNavigating(false);setRoute(null);setDestination(null);setSearchMode('search');};const togglePanel=()=>{setIsPanelOpen(!isPanelOpen);};return/*#__PURE__*/_jsxs(AppContainer,{children:[/*#__PURE__*/_jsxs(MapContainer,{children:[/*#__PURE__*/_jsx(MapComponent,{currentLocation:currentLocation,destination:destination,route:route,isNavigating:isNavigating,onLocationSelect:handleDestinationSelect}),/*#__PURE__*/_jsx(ToggleButton,{onClick:togglePanel,children:isPanelOpen?'×':'☰'})]}),/*#__PURE__*/_jsx(SidePanel,{isOpen:isPanelOpen,children:searchMode==='search'?/*#__PURE__*/_jsx(SearchPanel,{currentLocation:currentLocation,onDestinationSelect:handleDestinationSelect}):/*#__PURE__*/_jsx(NavigationPanel,{currentLocation:currentLocation,destination:destination,route:route,isNavigating:isNavigating,onStartNavigation:handleStartNavigation,onStopNavigation:handleStopNavigation,onBackToSearch:()=>setSearchMode('search')})})]});};export default GPSNavigationApp;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "MapComponent", "NavigationPanel", "SearchPanel", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_templateObject", "_taggedTemplateLiteral", "MapContainer", "_templateObject2", "SidePanel", "_templateObject3", "props", "isOpen", "ToggleButton", "button", "_templateObject4", "GPSNavigationApp", "currentLocation", "setCurrentLocation", "destination", "setDestination", "route", "setRoute", "isNavigating", "setIsNavigating", "isPanelOpen", "setIsPanelOpen", "searchMode", "setSearchMode", "navigator", "geolocation", "watchId", "watchPosition", "position", "newLocation", "lat", "coords", "latitude", "lng", "longitude", "accuracy", "timestamp", "Date", "now", "error", "console", "enableHighAccuracy", "timeout", "maximumAge", "clearWatch", "handleDestinationSelect", "location", "handleStartNavigation", "routeData", "handleStopNavigation", "togglePanel", "children", "onLocationSelect", "onClick", "onDestinationSelect", "onStartNavigation", "onStopNavigation", "onBackToSearch"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/src/components/GPSNavigationApp.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport MapComponent from './MapComponent';\nimport NavigationPanel from './NavigationPanel';\nimport SearchPanel from './SearchPanel';\nimport { LocationData, RouteData } from '../types/gps.types';\n\nconst AppContainer = styled.div`\n  display: flex;\n  height: 100vh;\n  width: 100vw;\n  background-color: #1a1a1a;\n  color: white;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  overflow: hidden;\n`;\n\nconst MapContainer = styled.div`\n  flex: 1;\n  position: relative;\n  height: 100%;\n`;\n\nconst SidePanel = styled.div<{ isOpen: boolean }>`\n  width: ${props => props.isOpen ? '400px' : '0px'};\n  transition: width 0.3s ease;\n  background-color: #2d2d2d;\n  border-left: 1px solid #444;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  \n  @media (max-width: 768px) {\n    position: absolute;\n    right: 0;\n    top: 0;\n    height: 100%;\n    z-index: 1000;\n    width: ${props => props.isOpen ? '100%' : '0px'};\n  }\n`;\n\nconst ToggleButton = styled.button`\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  z-index: 1001;\n  background-color: #007bff;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 60px;\n  height: 60px;\n  font-size: 24px;\n  cursor: pointer;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background-color: #0056b3;\n    transform: scale(1.1);\n  }\n  \n  &:active {\n    transform: scale(0.95);\n  }\n`;\n\nconst GPSNavigationApp: React.FC = () => {\n  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);\n  const [destination, setDestination] = useState<LocationData | null>(null);\n  const [route, setRoute] = useState<RouteData | null>(null);\n  const [isNavigating, setIsNavigating] = useState(false);\n  const [isPanelOpen, setIsPanelOpen] = useState(false);\n  const [searchMode, setSearchMode] = useState<'search' | 'navigation'>('search');\n\n  // Get user's current location\n  useEffect(() => {\n    if (navigator.geolocation) {\n      const watchId = navigator.geolocation.watchPosition(\n        (position) => {\n          const newLocation: LocationData = {\n            lat: position.coords.latitude,\n            lng: position.coords.longitude,\n            accuracy: position.coords.accuracy,\n            timestamp: Date.now()\n          };\n          setCurrentLocation(newLocation);\n        },\n        (error) => {\n          console.error('Error getting location:', error);\n          // Fallback to a default location (Tehran, Iran)\n          setCurrentLocation({\n            lat: 35.6892,\n            lng: 51.3890,\n            accuracy: 100,\n            timestamp: Date.now()\n          });\n        },\n        {\n          enableHighAccuracy: true,\n          timeout: 10000,\n          maximumAge: 60000\n        }\n      );\n\n      return () => navigator.geolocation.clearWatch(watchId);\n    }\n  }, []);\n\n  const handleDestinationSelect = (location: LocationData) => {\n    setDestination(location);\n    setSearchMode('navigation');\n    setIsPanelOpen(true);\n  };\n\n  const handleStartNavigation = (routeData: RouteData) => {\n    setRoute(routeData);\n    setIsNavigating(true);\n    setIsPanelOpen(false);\n  };\n\n  const handleStopNavigation = () => {\n    setIsNavigating(false);\n    setRoute(null);\n    setDestination(null);\n    setSearchMode('search');\n  };\n\n  const togglePanel = () => {\n    setIsPanelOpen(!isPanelOpen);\n  };\n\n  return (\n    <AppContainer>\n      <MapContainer>\n        <MapComponent\n          currentLocation={currentLocation}\n          destination={destination}\n          route={route}\n          isNavigating={isNavigating}\n          onLocationSelect={handleDestinationSelect}\n        />\n        <ToggleButton onClick={togglePanel}>\n          {isPanelOpen ? '×' : '☰'}\n        </ToggleButton>\n      </MapContainer>\n      \n      <SidePanel isOpen={isPanelOpen}>\n        {searchMode === 'search' ? (\n          <SearchPanel\n            currentLocation={currentLocation}\n            onDestinationSelect={handleDestinationSelect}\n          />\n        ) : (\n          <NavigationPanel\n            currentLocation={currentLocation}\n            destination={destination}\n            route={route}\n            isNavigating={isNavigating}\n            onStartNavigation={handleStartNavigation}\n            onStopNavigation={handleStopNavigation}\n            onBackToSearch={() => setSearchMode('search')}\n          />\n        )}\n      </SidePanel>\n    </AppContainer>\n  );\n};\n\nexport default GPSNavigationApp;\n"], "mappings": "yOAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CACzC,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,MAAO,CAAAC,WAAW,KAAM,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGxC,KAAM,CAAAC,YAAY,CAAGR,MAAM,CAACS,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,oMAQ9B,CAED,KAAM,CAAAC,YAAY,CAAGZ,MAAM,CAACS,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,8DAI9B,CAED,KAAM,CAAAG,SAAS,CAAGd,MAAM,CAACS,GAAG,CAAAM,gBAAA,GAAAA,gBAAA,CAAAJ,sBAAA,8UACjBK,KAAK,EAAIA,KAAK,CAACC,MAAM,CAAG,OAAO,CAAG,KAAK,CAcrCD,KAAK,EAAIA,KAAK,CAACC,MAAM,CAAG,MAAM,CAAG,KAAK,CAElD,CAED,KAAM,CAAAC,YAAY,CAAGlB,MAAM,CAACmB,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAAT,sBAAA,kcAwBjC,CAED,KAAM,CAAAU,gBAA0B,CAAGA,CAAA,GAAM,CACvC,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGzB,QAAQ,CAAsB,IAAI,CAAC,CACjF,KAAM,CAAC0B,WAAW,CAAEC,cAAc,CAAC,CAAG3B,QAAQ,CAAsB,IAAI,CAAC,CACzE,KAAM,CAAC4B,KAAK,CAAEC,QAAQ,CAAC,CAAG7B,QAAQ,CAAmB,IAAI,CAAC,CAC1D,KAAM,CAAC8B,YAAY,CAAEC,eAAe,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACgC,WAAW,CAAEC,cAAc,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACkC,UAAU,CAAEC,aAAa,CAAC,CAAGnC,QAAQ,CAA0B,QAAQ,CAAC,CAE/E;AACAC,SAAS,CAAC,IAAM,CACd,GAAImC,SAAS,CAACC,WAAW,CAAE,CACzB,KAAM,CAAAC,OAAO,CAAGF,SAAS,CAACC,WAAW,CAACE,aAAa,CAChDC,QAAQ,EAAK,CACZ,KAAM,CAAAC,WAAyB,CAAG,CAChCC,GAAG,CAAEF,QAAQ,CAACG,MAAM,CAACC,QAAQ,CAC7BC,GAAG,CAAEL,QAAQ,CAACG,MAAM,CAACG,SAAS,CAC9BC,QAAQ,CAAEP,QAAQ,CAACG,MAAM,CAACI,QAAQ,CAClCC,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CACtB,CAAC,CACDzB,kBAAkB,CAACgB,WAAW,CAAC,CACjC,CAAC,CACAU,KAAK,EAAK,CACTC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C;AACA1B,kBAAkB,CAAC,CACjBiB,GAAG,CAAE,OAAO,CACZG,GAAG,CAAE,OAAO,CACZE,QAAQ,CAAE,GAAG,CACbC,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CACtB,CAAC,CAAC,CACJ,CAAC,CACD,CACEG,kBAAkB,CAAE,IAAI,CACxBC,OAAO,CAAE,KAAK,CACdC,UAAU,CAAE,KACd,CACF,CAAC,CAED,MAAO,IAAMnB,SAAS,CAACC,WAAW,CAACmB,UAAU,CAAClB,OAAO,CAAC,CACxD,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAmB,uBAAuB,CAAIC,QAAsB,EAAK,CAC1D/B,cAAc,CAAC+B,QAAQ,CAAC,CACxBvB,aAAa,CAAC,YAAY,CAAC,CAC3BF,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAED,KAAM,CAAA0B,qBAAqB,CAAIC,SAAoB,EAAK,CACtD/B,QAAQ,CAAC+B,SAAS,CAAC,CACnB7B,eAAe,CAAC,IAAI,CAAC,CACrBE,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAED,KAAM,CAAA4B,oBAAoB,CAAGA,CAAA,GAAM,CACjC9B,eAAe,CAAC,KAAK,CAAC,CACtBF,QAAQ,CAAC,IAAI,CAAC,CACdF,cAAc,CAAC,IAAI,CAAC,CACpBQ,aAAa,CAAC,QAAQ,CAAC,CACzB,CAAC,CAED,KAAM,CAAA2B,WAAW,CAAGA,CAAA,GAAM,CACxB7B,cAAc,CAAC,CAACD,WAAW,CAAC,CAC9B,CAAC,CAED,mBACEvB,KAAA,CAACC,YAAY,EAAAqD,QAAA,eACXtD,KAAA,CAACK,YAAY,EAAAiD,QAAA,eACXxD,IAAA,CAACJ,YAAY,EACXqB,eAAe,CAAEA,eAAgB,CACjCE,WAAW,CAAEA,WAAY,CACzBE,KAAK,CAAEA,KAAM,CACbE,YAAY,CAAEA,YAAa,CAC3BkC,gBAAgB,CAAEP,uBAAwB,CAC3C,CAAC,cACFlD,IAAA,CAACa,YAAY,EAAC6C,OAAO,CAAEH,WAAY,CAAAC,QAAA,CAChC/B,WAAW,CAAG,GAAG,CAAG,GAAG,CACZ,CAAC,EACH,CAAC,cAEfzB,IAAA,CAACS,SAAS,EAACG,MAAM,CAAEa,WAAY,CAAA+B,QAAA,CAC5B7B,UAAU,GAAK,QAAQ,cACtB3B,IAAA,CAACF,WAAW,EACVmB,eAAe,CAAEA,eAAgB,CACjC0C,mBAAmB,CAAET,uBAAwB,CAC9C,CAAC,cAEFlD,IAAA,CAACH,eAAe,EACdoB,eAAe,CAAEA,eAAgB,CACjCE,WAAW,CAAEA,WAAY,CACzBE,KAAK,CAAEA,KAAM,CACbE,YAAY,CAAEA,YAAa,CAC3BqC,iBAAiB,CAAER,qBAAsB,CACzCS,gBAAgB,CAAEP,oBAAqB,CACvCQ,cAAc,CAAEA,CAAA,GAAMlC,aAAa,CAAC,QAAQ,CAAE,CAC/C,CACF,CACQ,CAAC,EACA,CAAC,CAEnB,CAAC,CAED,cAAe,CAAAZ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}