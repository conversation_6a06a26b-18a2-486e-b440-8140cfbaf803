import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
// Using Unicode symbols instead of react-icons for React 19 compatibility
import { LocationData, RouteData, RouteInstruction, NavigationState } from '../types/gps.types';
import { getDistance } from 'geolib';

const PanelContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #2d2d2d;
  color: white;
`;

const Header = styled.div`
  padding: 20px;
  border-bottom: 1px solid #444;
  background-color: #1a1a1a;
`;

const BackButton = styled.button`
  background: none;
  border: none;
  color: #007bff;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding: 8px 0;
  
  &:hover {
    color: #0056b3;
  }
`;

const DestinationInfo = styled.div`
  margin-bottom: 20px;
`;

const DestinationName = styled.h2`
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
`;

const DestinationAddress = styled.div`
  font-size: 14px;
  color: #aaa;
  margin-bottom: 12px;
`;

const RouteOptions = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
`;

const RouteOption = styled.button<{ active?: boolean }>`
  flex: 1;
  padding: 12px;
  border: 1px solid #444;
  border-radius: 8px;
  background-color: ${props => props.active ? '#007bff' : '#3d3d3d'};
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #007bff;
    border-color: #007bff;
  }
`;

const NavigationControls = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
`;

const ControlButton = styled.button<{ primary?: boolean }>`
  flex: 1;
  padding: 16px;
  border: none;
  border-radius: 12px;
  background-color: ${props => props.primary ? '#28a745' : '#dc3545'};
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  &:hover {
    opacity: 0.9;
    transform: translateY(-2px);
  }
  
  &:active {
    transform: translateY(0);
  }
`;

const RouteInfo = styled.div`
  background-color: #3d3d3d;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
`;

const RouteStats = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
`;

const StatItem = styled.div`
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: 600;
  color: #007bff;
  margin-bottom: 4px;
`;

const StatLabel = styled.div`
  font-size: 12px;
  color: #aaa;
`;

const Content = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 0 20px 20px;
`;

const NavigationStatus = styled.div<{ isNavigating: boolean }>`
  background-color: ${props => props.isNavigating ? '#28a745' : '#6c757d'};
  color: white;
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 20px;
  text-align: center;
  font-weight: 600;
`;

const CurrentInstruction = styled.div`
  background-color: #007bff;
  color: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  text-align: center;
`;

const InstructionText = styled.div`
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
`;

const InstructionDistance = styled.div`
  font-size: 14px;
  opacity: 0.9;
`;

const InstructionsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const InstructionItem = styled.div<{ isCurrent?: boolean }>`
  padding: 12px;
  border: 1px solid #444;
  border-radius: 8px;
  background-color: ${props => props.isCurrent ? '#007bff' : '#3d3d3d'};
  font-size: 14px;
`;

const VoiceControls = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #3d3d3d;
  border-radius: 12px;
  margin-bottom: 20px;
`;

const VoiceButton = styled.button<{ active?: boolean }>`
  background: none;
  border: none;
  color: ${props => props.active ? '#007bff' : '#aaa'};
  font-size: 24px;
  cursor: pointer;
  transition: color 0.3s ease;
  
  &:hover {
    color: #007bff;
  }
`;

interface NavigationPanelProps {
  currentLocation: LocationData | null;
  destination: LocationData | null;
  route: RouteData | null;
  isNavigating: boolean;
  onStartNavigation: (route: RouteData) => void;
  onStopNavigation: () => void;
  onBackToSearch: () => void;
}

const NavigationPanel: React.FC<NavigationPanelProps> = ({
  currentLocation,
  destination,
  route,
  isNavigating,
  onStartNavigation,
  onStopNavigation,
  onBackToSearch
}) => {
  const [selectedRouteType, setSelectedRouteType] = useState<'fastest' | 'shortest' | 'eco'>('fastest');
  const [voiceEnabled, setVoiceEnabled] = useState(true);
  const [navigationState, setNavigationState] = useState<NavigationState>({
    isNavigating: false,
    remainingDistance: 0,
    remainingTime: 0
  });

  // Generate mock route data
  const generateRoute = (): RouteData => {
    if (!currentLocation || !destination) {
      throw new Error('Current location and destination are required');
    }

    const distance = getDistance(
      { latitude: currentLocation.lat, longitude: currentLocation.lng },
      { latitude: destination.lat, longitude: destination.lng }
    );

    // Generate simple route coordinates (in real app, this would come from routing API)
    const coordinates: [number, number][] = [
      [currentLocation.lng, currentLocation.lat],
      [destination.lng, destination.lat]
    ];

    const mockInstructions: RouteInstruction[] = [
      {
        text: 'از موقعیت فعلی شروع کنید',
        distance: 0,
        duration: 0,
        maneuver: 'start',
        location: [currentLocation.lng, currentLocation.lat]
      },
      {
        text: `به سمت ${destination.name || 'مقصد'} حرکت کنید`,
        distance: distance * 0.8,
        duration: (distance * 0.8) / 50 * 3.6, // Assuming 50 km/h average speed
        maneuver: 'straight',
        location: [destination.lng, destination.lat]
      },
      {
        text: 'به مقصد رسیده‌اید',
        distance: distance,
        duration: distance / 50 * 3.6,
        maneuver: 'arrive',
        location: [destination.lng, destination.lat]
      }
    ];

    return {
      coordinates,
      distance,
      duration: distance / 50 * 3.6, // Assuming 50 km/h average speed
      instructions: mockInstructions,
      bounds: {
        north: Math.max(currentLocation.lat, destination.lat),
        south: Math.min(currentLocation.lat, destination.lat),
        east: Math.max(currentLocation.lng, destination.lng),
        west: Math.min(currentLocation.lng, destination.lng)
      }
    };
  };

  const handleStartNavigation = () => {
    if (!currentLocation || !destination) return;
    
    const routeData = generateRoute();
    setNavigationState({
      isNavigating: true,
      remainingDistance: routeData.distance,
      remainingTime: routeData.duration,
      currentInstruction: routeData.instructions[0],
      nextInstruction: routeData.instructions[1]
    });
    
    onStartNavigation(routeData);
  };

  const handleStopNavigation = () => {
    setNavigationState({
      isNavigating: false,
      remainingDistance: 0,
      remainingTime: 0
    });
    onStopNavigation();
  };

  const formatDistance = (meters: number): string => {
    if (meters < 1000) {
      return `${Math.round(meters)} متر`;
    }
    return `${(meters / 1000).toFixed(1)} کیلومتر`;
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours} ساعت ${minutes} دقیقه`;
    }
    return `${minutes} دقیقه`;
  };

  const currentRoute = route || (currentLocation && destination ? generateRoute() : null);

  return (
    <PanelContainer>
      <Header>
        <BackButton onClick={onBackToSearch}>
          ← بازگشت به جستجو
        </BackButton>
        
        {destination && (
          <DestinationInfo>
            <DestinationName>{destination.name || 'مقصد انتخاب شده'}</DestinationName>
            {destination.address && (
              <DestinationAddress>{destination.address}</DestinationAddress>
            )}
          </DestinationInfo>
        )}

        <RouteOptions>
          <RouteOption 
            active={selectedRouteType === 'fastest'}
            onClick={() => setSelectedRouteType('fastest')}
          >
            سریع‌ترین
          </RouteOption>
          <RouteOption 
            active={selectedRouteType === 'shortest'}
            onClick={() => setSelectedRouteType('shortest')}
          >
            کوتاه‌ترین
          </RouteOption>
          <RouteOption 
            active={selectedRouteType === 'eco'}
            onClick={() => setSelectedRouteType('eco')}
          >
            اقتصادی
          </RouteOption>
        </RouteOptions>

        <NavigationControls>
          {!isNavigating ? (
            <ControlButton primary onClick={handleStartNavigation}>
              ▶ شروع مسیریابی
            </ControlButton>
          ) : (
            <ControlButton onClick={handleStopNavigation}>
              ⏹ توقف مسیریابی
            </ControlButton>
          )}
        </NavigationControls>
      </Header>

      <Content>
        <NavigationStatus isNavigating={isNavigating}>
          {isNavigating ? 'در حال مسیریابی...' : 'آماده برای شروع'}
        </NavigationStatus>

        {currentRoute && (
          <RouteInfo>
            <RouteStats>
              <StatItem>
                <StatValue>{formatDistance(currentRoute.distance)}</StatValue>
                <StatLabel>مسافت</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{formatDuration(currentRoute.duration)}</StatValue>
                <StatLabel>زمان تقریبی</StatLabel>
              </StatItem>
            </RouteStats>
          </RouteInfo>
        )}

        {isNavigating && navigationState.currentInstruction && (
          <CurrentInstruction>
            <InstructionText>{navigationState.currentInstruction.text}</InstructionText>
            <InstructionDistance>
              {formatDistance(navigationState.currentInstruction.distance)}
            </InstructionDistance>
          </CurrentInstruction>
        )}

        <VoiceControls>
          <span>راهنمایی صوتی</span>
          <VoiceButton
            active={voiceEnabled}
            onClick={() => setVoiceEnabled(!voiceEnabled)}
          >
            {voiceEnabled ? '🔊' : '🔇'}
          </VoiceButton>
        </VoiceControls>

        {currentRoute && currentRoute.instructions && (
          <>
            <h3 style={{ margin: '20px 0 12px 0', fontSize: '16px', color: '#ccc' }}>
              دستورالعمل‌های مسیر
            </h3>
            <InstructionsList>
              {currentRoute.instructions.map((instruction, index) => (
                <InstructionItem 
                  key={index}
                  isCurrent={isNavigating && index === 0}
                >
                  <div style={{ fontWeight: '500', marginBottom: '4px' }}>
                    {instruction.text}
                  </div>
                  <div style={{ fontSize: '12px', color: '#aaa' }}>
                    {formatDistance(instruction.distance)}
                  </div>
                </InstructionItem>
              ))}
            </InstructionsList>
          </>
        )}
      </Content>
    </PanelContainer>
  );
};

export default NavigationPanel;
