{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getDistanceFromLine = _interopRequireDefault(require(\"./getDistanceFromLine\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar isPointNearLine = function isPointNearLine(point, start, end, distance) {\n  return (0, _getDistanceFromLine.default)(point, start, end) < distance;\n};\nvar _default = isPointNearLine;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getDistanceFromLine", "_interopRequireDefault", "require", "obj", "__esModule", "isPointNearLine", "point", "start", "end", "distance", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/isPointNearLine.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getDistanceFromLine=_interopRequireDefault(require(\"./getDistanceFromLine\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var isPointNearLine=function isPointNearLine(point,start,end,distance){return(0,_getDistanceFromLine.default)(point,start,end)<distance};var _default=isPointNearLine;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,oBAAoB,GAACC,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACE,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACJ,OAAO,EAACI;EAAG,CAAC;AAAA;AAAC,IAAIE,eAAe,GAAC,SAASA,eAAeA,CAACC,KAAK,EAACC,KAAK,EAACC,GAAG,EAACC,QAAQ,EAAC;EAAC,OAAM,CAAC,CAAC,EAACT,oBAAoB,CAACD,OAAO,EAAEO,KAAK,EAACC,KAAK,EAACC,GAAG,CAAC,GAACC,QAAQ;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACL,eAAe;AAACR,OAAO,CAACE,OAAO,GAACW,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}