{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getLatitude = _interopRequireDefault(require(\"./getLatitude\"));\nvar _getLongitude = _interopRequireDefault(require(\"./getLongitude\"));\nvar _toRad = _interopRequireDefault(require(\"./toRad\"));\nvar _toDeg = _interopRequireDefault(require(\"./toDeg\"));\nvar _constants = require(\"./constants\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar computeDestinationPoint = function computeDestinationPoint(start, distance, bearing) {\n  var radius = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 6371000;\n  var lat = (0, _getLatitude.default)(start);\n  var lng = (0, _getLongitude.default)(start);\n  var delta = distance / radius;\n  var theta = (0, _toRad.default)(bearing);\n  var phi1 = (0, _toRad.default)(lat);\n  var lambda1 = (0, _toRad.default)(lng);\n  var phi2 = Math.asin(Math.sin(phi1) * Math.cos(delta) + Math.cos(phi1) * Math.sin(delta) * Math.cos(theta));\n  var lambda2 = lambda1 + Math.atan2(Math.sin(theta) * Math.sin(delta) * Math.cos(phi1), Math.cos(delta) - Math.sin(phi1) * Math.sin(phi2));\n  var longitude = (0, _toDeg.default)(lambda2);\n  if (longitude < _constants.MINLON || longitude > _constants.MAXLON) {\n    lambda2 = (lambda2 + 3 * Math.PI) % (2 * Math.PI) - Math.PI;\n    longitude = (0, _toDeg.default)(lambda2);\n  }\n  return {\n    latitude: (0, _toDeg.default)(phi2),\n    longitude: longitude\n  };\n};\nvar _default = computeDestinationPoint;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getLatitude", "_interopRequireDefault", "require", "_getLongitude", "_toRad", "_toDeg", "_constants", "obj", "__esModule", "computeDestinationPoint", "start", "distance", "bearing", "radius", "arguments", "length", "undefined", "lat", "lng", "delta", "theta", "phi1", "lambda1", "phi2", "Math", "asin", "sin", "cos", "lambda2", "atan2", "longitude", "MINLON", "MAXLON", "PI", "latitude", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/computeDestinationPoint.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getLatitude=_interopRequireDefault(require(\"./getLatitude\"));var _getLongitude=_interopRequireDefault(require(\"./getLongitude\"));var _toRad=_interopRequireDefault(require(\"./toRad\"));var _toDeg=_interopRequireDefault(require(\"./toDeg\"));var _constants=require(\"./constants\");function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var computeDestinationPoint=function computeDestinationPoint(start,distance,bearing){var radius=arguments.length>3&&arguments[3]!==undefined?arguments[3]:6371000;var lat=(0,_getLatitude.default)(start);var lng=(0,_getLongitude.default)(start);var delta=distance/radius;var theta=(0,_toRad.default)(bearing);var phi1=(0,_toRad.default)(lat);var lambda1=(0,_toRad.default)(lng);var phi2=Math.asin(Math.sin(phi1)*Math.cos(delta)+Math.cos(phi1)*Math.sin(delta)*Math.cos(theta));var lambda2=lambda1+Math.atan2(Math.sin(theta)*Math.sin(delta)*Math.cos(phi1),Math.cos(delta)-Math.sin(phi1)*Math.sin(phi2));var longitude=(0,_toDeg.default)(lambda2);if(longitude<_constants.MINLON||longitude>_constants.MAXLON){lambda2=(lambda2+3*Math.PI)%(2*Math.PI)-Math.PI;longitude=(0,_toDeg.default)(lambda2)}return{latitude:(0,_toDeg.default)(phi2),longitude:longitude}};var _default=computeDestinationPoint;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,IAAIC,aAAa,GAACF,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAAC,IAAIE,MAAM,GAACH,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAAC,IAAIG,MAAM,GAACJ,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAAC,IAAII,UAAU,GAACJ,OAAO,CAAC,aAAa,CAAC;AAAC,SAASD,sBAAsBA,CAACM,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACR,OAAO,EAACQ;EAAG,CAAC;AAAA;AAAC,IAAIE,uBAAuB,GAAC,SAASA,uBAAuBA,CAACC,KAAK,EAACC,QAAQ,EAACC,OAAO,EAAC;EAAC,IAAIC,MAAM,GAACC,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAGE,SAAS,GAACF,SAAS,CAAC,CAAC,CAAC,GAAC,OAAO;EAAC,IAAIG,GAAG,GAAC,CAAC,CAAC,EAACjB,YAAY,CAACD,OAAO,EAAEW,KAAK,CAAC;EAAC,IAAIQ,GAAG,GAAC,CAAC,CAAC,EAACf,aAAa,CAACJ,OAAO,EAAEW,KAAK,CAAC;EAAC,IAAIS,KAAK,GAACR,QAAQ,GAACE,MAAM;EAAC,IAAIO,KAAK,GAAC,CAAC,CAAC,EAAChB,MAAM,CAACL,OAAO,EAAEa,OAAO,CAAC;EAAC,IAAIS,IAAI,GAAC,CAAC,CAAC,EAACjB,MAAM,CAACL,OAAO,EAAEkB,GAAG,CAAC;EAAC,IAAIK,OAAO,GAAC,CAAC,CAAC,EAAClB,MAAM,CAACL,OAAO,EAAEmB,GAAG,CAAC;EAAC,IAAIK,IAAI,GAACC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACL,IAAI,CAAC,GAACG,IAAI,CAACG,GAAG,CAACR,KAAK,CAAC,GAACK,IAAI,CAACG,GAAG,CAACN,IAAI,CAAC,GAACG,IAAI,CAACE,GAAG,CAACP,KAAK,CAAC,GAACK,IAAI,CAACG,GAAG,CAACP,KAAK,CAAC,CAAC;EAAC,IAAIQ,OAAO,GAACN,OAAO,GAACE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAACI,IAAI,CAACE,GAAG,CAACP,KAAK,CAAC,GAACK,IAAI,CAACG,GAAG,CAACN,IAAI,CAAC,EAACG,IAAI,CAACG,GAAG,CAACR,KAAK,CAAC,GAACK,IAAI,CAACE,GAAG,CAACL,IAAI,CAAC,GAACG,IAAI,CAACE,GAAG,CAACH,IAAI,CAAC,CAAC;EAAC,IAAIO,SAAS,GAAC,CAAC,CAAC,EAACzB,MAAM,CAACN,OAAO,EAAE6B,OAAO,CAAC;EAAC,IAAGE,SAAS,GAACxB,UAAU,CAACyB,MAAM,IAAED,SAAS,GAACxB,UAAU,CAAC0B,MAAM,EAAC;IAACJ,OAAO,GAAC,CAACA,OAAO,GAAC,CAAC,GAACJ,IAAI,CAACS,EAAE,KAAG,CAAC,GAACT,IAAI,CAACS,EAAE,CAAC,GAACT,IAAI,CAACS,EAAE;IAACH,SAAS,GAAC,CAAC,CAAC,EAACzB,MAAM,CAACN,OAAO,EAAE6B,OAAO,CAAC;EAAA;EAAC,OAAM;IAACM,QAAQ,EAAC,CAAC,CAAC,EAAC7B,MAAM,CAACN,OAAO,EAAEwB,IAAI,CAAC;IAACO,SAAS,EAACA;EAAS,CAAC;AAAA,CAAC;AAAC,IAAIK,QAAQ,GAAC1B,uBAAuB;AAACZ,OAAO,CAACE,OAAO,GAACoC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}