export interface LocationData {
  lat: number;
  lng: number;
  accuracy?: number;
  timestamp?: number;
  address?: string;
  name?: string;
}

export interface RouteData {
  coordinates: [number, number][];
  distance: number; // in meters
  duration: number; // in seconds
  instructions: RouteInstruction[];
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
}

export interface RouteInstruction {
  text: string;
  distance: number;
  duration: number;
  maneuver: string;
  location: [number, number];
  direction?: string;
  streetName?: string;
}

export interface POI {
  id: string;
  name: string;
  category: string;
  location: LocationData;
  rating?: number;
  description?: string;
  phone?: string;
  website?: string;
  openingHours?: string;
}

export interface SearchResult {
  id: string;
  name: string;
  address: string;
  location: LocationData;
  type: 'address' | 'poi' | 'coordinate';
  category?: string;
  distance?: number;
}

export interface NavigationState {
  isNavigating: boolean;
  currentInstruction?: RouteInstruction;
  nextInstruction?: RouteInstruction;
  remainingDistance: number;
  remainingTime: number;
  currentSpeed?: number;
  speedLimit?: number;
}

export interface MapSettings {
  theme: 'light' | 'dark' | 'satellite';
  showTraffic: boolean;
  showPOI: boolean;
  autoZoom: boolean;
  voiceGuidance: boolean;
  units: 'metric' | 'imperial';
}

export interface UserPreferences {
  avoidTolls: boolean;
  avoidHighways: boolean;
  avoidFerries: boolean;
  routeType: 'fastest' | 'shortest' | 'eco';
  language: string;
  voiceEnabled: boolean;
  mapSettings: MapSettings;
}

export interface TripData {
  id: string;
  startTime: number;
  endTime?: number;
  startLocation: LocationData;
  endLocation?: LocationData;
  route?: RouteData;
  distance: number;
  duration: number;
  averageSpeed: number;
  maxSpeed: number;
  fuelUsed?: number;
  cost?: number;
}

export interface TrafficInfo {
  severity: 'low' | 'moderate' | 'heavy' | 'severe';
  description: string;
  location: LocationData;
  affectedRoute?: string;
  estimatedDelay: number;
}

export interface SpeedCamera {
  id: string;
  location: LocationData;
  speedLimit: number;
  type: 'fixed' | 'mobile' | 'average';
  direction?: number;
}

export interface SafetyAlert {
  id: string;
  type: 'speed_camera' | 'accident' | 'construction' | 'police' | 'hazard';
  location: LocationData;
  description: string;
  severity: 'low' | 'medium' | 'high';
  timestamp: number;
  distance?: number;
}
