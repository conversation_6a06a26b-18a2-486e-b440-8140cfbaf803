{"ast": null, "code": "import { useEffect, useRef } from 'react';\nexport function useEventHandlers(element, eventHandlers) {\n  const eventHandlersRef = useRef(undefined);\n  useEffect(function addEventHandlers() {\n    if (eventHandlers != null) {\n      element.instance.on(eventHandlers);\n    }\n    eventHandlersRef.current = eventHandlers;\n    return function removeEventHandlers() {\n      if (eventHandlersRef.current != null) {\n        element.instance.off(eventHandlersRef.current);\n      }\n      eventHandlersRef.current = null;\n    };\n  }, [element, eventHandlers]);\n}", "map": {"version": 3, "names": ["useEffect", "useRef", "useEventHandlers", "element", "eventHandlers", "eventHandlersRef", "undefined", "addEventHandlers", "instance", "on", "current", "removeEventHandlers", "off"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@react-leaflet/core/lib/events.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nexport function useEventHandlers(element, eventHandlers) {\n    const eventHandlersRef = useRef(undefined);\n    useEffect(function addEventHandlers() {\n        if (eventHandlers != null) {\n            element.instance.on(eventHandlers);\n        }\n        eventHandlersRef.current = eventHandlers;\n        return function removeEventHandlers() {\n            if (eventHandlersRef.current != null) {\n                element.instance.off(eventHandlersRef.current);\n            }\n            eventHandlersRef.current = null;\n        };\n    }, [\n        element,\n        eventHandlers\n    ]);\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAO,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,aAAa,EAAE;EACrD,MAAMC,gBAAgB,GAAGJ,MAAM,CAACK,SAAS,CAAC;EAC1CN,SAAS,CAAC,SAASO,gBAAgBA,CAAA,EAAG;IAClC,IAAIH,aAAa,IAAI,IAAI,EAAE;MACvBD,OAAO,CAACK,QAAQ,CAACC,EAAE,CAACL,aAAa,CAAC;IACtC;IACAC,gBAAgB,CAACK,OAAO,GAAGN,aAAa;IACxC,OAAO,SAASO,mBAAmBA,CAAA,EAAG;MAClC,IAAIN,gBAAgB,CAACK,OAAO,IAAI,IAAI,EAAE;QAClCP,OAAO,CAACK,QAAQ,CAACI,GAAG,CAACP,gBAAgB,CAACK,OAAO,CAAC;MAClD;MACAL,gBAAgB,CAACK,OAAO,GAAG,IAAI;IACnC,CAAC;EACL,CAAC,EAAE,CACCP,OAAO,EACPC,aAAa,CAChB,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}