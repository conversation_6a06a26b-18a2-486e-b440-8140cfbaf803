{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"attributes\", \"bounds\"],\n  _excluded2 = [\"children\"];\nimport { createElementHook, createElementObject, createLayerHook, updateMediaOverlay } from '@react-leaflet/core';\nimport { SVGOverlay as LeafletSVGOverlay } from 'leaflet';\nimport { forwardRef, useImperativeHandle } from 'react';\nimport { createPortal } from 'react-dom';\nexport const useSVGOverlayElement = createElementHook(function createSVGOverlay(props, context) {\n  const {\n      attributes,\n      bounds\n    } = props,\n    options = _objectWithoutProperties(props, _excluded);\n  const container = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n  container.setAttribute('xmlns', 'http://www.w3.org/2000/svg');\n  if (attributes != null) {\n    for (const name of Object.keys(attributes)) {\n      container.setAttribute(name, attributes[name]);\n    }\n  }\n  const overlay = new LeafletSVGOverlay(container, bounds, options);\n  return createElementObject(overlay, context, container);\n}, updateMediaOverlay);\nexport const useSVGOverlay = createLayerHook(useSVGOverlayElement);\nfunction SVGOverlayComponent(_ref, forwardedRef) {\n  let {\n      children\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded2);\n  const {\n    instance,\n    container\n  } = useSVGOverlay(options).current;\n  useImperativeHandle(forwardedRef, () => instance);\n  return container == null || children == null ? null : /*#__PURE__*/createPortal(children, container);\n}\nexport const SVGOverlay = /*#__PURE__*/forwardRef(SVGOverlayComponent);", "map": {"version": 3, "names": ["createElementHook", "createElementObject", "createLayerHook", "updateMediaOverlay", "SVGOverlay", "LeafletSVGOverlay", "forwardRef", "useImperativeHandle", "createPortal", "useSVGOverlayElement", "createSVGOverlay", "props", "context", "attributes", "bounds", "options", "_objectWithoutProperties", "_excluded", "container", "document", "createElementNS", "setAttribute", "name", "Object", "keys", "overlay", "useSVGOverlay", "SVGOverlayComponent", "_ref", "forwardedRef", "children", "_excluded2", "instance", "current"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/SVGOverlay.js"], "sourcesContent": ["import { createElementHook, createElementObject, createLayerHook, updateMediaOverlay } from '@react-leaflet/core';\nimport { SVGOverlay as LeafletSVGOverlay } from 'leaflet';\nimport { forwardRef, useImperativeHandle } from 'react';\nimport { createPortal } from 'react-dom';\nexport const useSVGOverlayElement = createElementHook(function createSVGOverlay(props, context) {\n    const { attributes, bounds, ...options } = props;\n    const container = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    container.setAttribute('xmlns', 'http://www.w3.org/2000/svg');\n    if (attributes != null) {\n        for (const name of Object.keys(attributes)){\n            container.setAttribute(name, attributes[name]);\n        }\n    }\n    const overlay = new LeafletSVGOverlay(container, bounds, options);\n    return createElementObject(overlay, context, container);\n}, updateMediaOverlay);\nexport const useSVGOverlay = createLayerHook(useSVGOverlayElement);\nfunction SVGOverlayComponent({ children, ...options }, forwardedRef) {\n    const { instance, container } = useSVGOverlay(options).current;\n    useImperativeHandle(forwardedRef, ()=>instance);\n    return container == null || children == null ? null : /*#__PURE__*/ createPortal(children, container);\n}\nexport const SVGOverlay = /*#__PURE__*/ forwardRef(SVGOverlayComponent);\n"], "mappings": ";;;AAAA,SAASA,iBAAiB,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,qBAAqB;AACjH,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,SAAS;AACzD,SAASC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACvD,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,oBAAoB,GAAGT,iBAAiB,CAAC,SAASU,gBAAgBA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC5F,MAAM;MAAEC,UAAU;MAAEC;IAAmB,CAAC,GAAGH,KAAK;IAAjBI,OAAO,GAAAC,wBAAA,CAAKL,KAAK,EAAAM,SAAA;EAChD,MAAMC,SAAS,GAAGC,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC;EAC/EF,SAAS,CAACG,YAAY,CAAC,OAAO,EAAE,4BAA4B,CAAC;EAC7D,IAAIR,UAAU,IAAI,IAAI,EAAE;IACpB,KAAK,MAAMS,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACX,UAAU,CAAC,EAAC;MACvCK,SAAS,CAACG,YAAY,CAACC,IAAI,EAAET,UAAU,CAACS,IAAI,CAAC,CAAC;IAClD;EACJ;EACA,MAAMG,OAAO,GAAG,IAAIpB,iBAAiB,CAACa,SAAS,EAAEJ,MAAM,EAAEC,OAAO,CAAC;EACjE,OAAOd,mBAAmB,CAACwB,OAAO,EAAEb,OAAO,EAAEM,SAAS,CAAC;AAC3D,CAAC,EAAEf,kBAAkB,CAAC;AACtB,OAAO,MAAMuB,aAAa,GAAGxB,eAAe,CAACO,oBAAoB,CAAC;AAClE,SAASkB,mBAAmBA,CAAAC,IAAA,EAA2BC,YAAY,EAAE;EAAA,IAAxC;MAAEC;IAAqB,CAAC,GAAAF,IAAA;IAATb,OAAO,GAAAC,wBAAA,CAAAY,IAAA,EAAAG,UAAA;EAC/C,MAAM;IAAEC,QAAQ;IAAEd;EAAU,CAAC,GAAGQ,aAAa,CAACX,OAAO,CAAC,CAACkB,OAAO;EAC9D1B,mBAAmB,CAACsB,YAAY,EAAE,MAAIG,QAAQ,CAAC;EAC/C,OAAOd,SAAS,IAAI,IAAI,IAAIY,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,aAActB,YAAY,CAACsB,QAAQ,EAAEZ,SAAS,CAAC;AACzG;AACA,OAAO,MAAMd,UAAU,GAAG,aAAcE,UAAU,CAACqB,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}