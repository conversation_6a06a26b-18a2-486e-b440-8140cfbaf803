{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar robustAcos = function robustAcos(value) {\n  if (value > 1) {\n    return 1;\n  }\n  if (value < -1) {\n    return -1;\n  }\n  return value;\n};\nvar _default = robustAcos;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "robustAcos", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/robustAcos.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var robustAcos=function robustAcos(value){if(value>1){return 1}if(value<-1){return-1}return value};var _default=robustAcos;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,UAAU,GAAC,SAASA,UAAUA,CAACF,KAAK,EAAC;EAAC,IAAGA,KAAK,GAAC,CAAC,EAAC;IAAC,OAAO,CAAC;EAAA;EAAC,IAAGA,KAAK,GAAC,CAAC,CAAC,EAAC;IAAC,OAAM,CAAC,CAAC;EAAA;EAAC,OAAOA,KAAK;AAAA,CAAC;AAAC,IAAIG,QAAQ,GAACD,UAAU;AAACH,OAAO,CAACE,OAAO,GAACE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}