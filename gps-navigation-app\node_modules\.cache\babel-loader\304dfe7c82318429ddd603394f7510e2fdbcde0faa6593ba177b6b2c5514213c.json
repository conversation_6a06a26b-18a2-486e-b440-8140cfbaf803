{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _isDecimal = _interopRequireDefault(require(\"./isDecimal\"));\nvar _isSexagesimal = _interopRequireDefault(require(\"./isSexagesimal\"));\nvar _sexagesimalToDecimal = _interopRequireDefault(require(\"./sexagesimalToDecimal\"));\nvar _isValidCoordinate = _interopRequireDefault(require(\"./isValidCoordinate\"));\nvar _getCoordinateKeys = _interopRequireDefault(require(\"./getCoordinateKeys\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nvar toDecimal = function toDecimal(value) {\n  if ((0, _isDecimal.default)(value)) {\n    return Number(value);\n  }\n  if ((0, _isSexagesimal.default)(value)) {\n    return (0, _sexagesimalToDecimal.default)(value);\n  }\n  if ((0, _isValidCoordinate.default)(value)) {\n    var keys = (0, _getCoordinateKeys.default)(value);\n    if (Array.isArray(value)) {\n      return value.map(function (v, index) {\n        return [0, 1].includes(index) ? toDecimal(v) : v;\n      });\n    }\n    return _objectSpread(_objectSpread(_objectSpread({}, value), keys.latitude && _defineProperty({}, keys.latitude, toDecimal(value[keys.latitude]))), keys.longitude && _defineProperty({}, keys.longitude, toDecimal(value[keys.longitude])));\n  }\n  if (Array.isArray(value)) {\n    return value.map(function (point) {\n      return (0, _isValidCoordinate.default)(point) ? toDecimal(point) : point;\n    });\n  }\n  return value;\n};\nvar _default = toDecimal;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_isDecimal", "_interopRequireDefault", "require", "_isSexagesimal", "_sexagesimalToDecimal", "_isValidCoordinate", "_getCoordinateKeys", "obj", "__esModule", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "configurable", "writable", "toDecimal", "Number", "Array", "isArray", "map", "v", "index", "includes", "latitude", "longitude", "point", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/toDecimal.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _isDecimal=_interopRequireDefault(require(\"./isDecimal\"));var _isSexagesimal=_interopRequireDefault(require(\"./isSexagesimal\"));var _sexagesimalToDecimal=_interopRequireDefault(require(\"./sexagesimalToDecimal\"));var _isValidCoordinate=_interopRequireDefault(require(\"./isValidCoordinate\"));var _getCoordinateKeys=_interopRequireDefault(require(\"./getCoordinateKeys\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function ownKeys(object,enumerableOnly){var keys=Object.keys(object);if(Object.getOwnPropertySymbols){var symbols=Object.getOwnPropertySymbols(object);if(enumerableOnly)symbols=symbols.filter(function(sym){return Object.getOwnPropertyDescriptor(object,sym).enumerable});keys.push.apply(keys,symbols)}return keys}function _objectSpread(target){for(var i=1;i<arguments.length;i++){var source=arguments[i]!=null?arguments[i]:{};if(i%2){ownKeys(Object(source),true).forEach(function(key){_defineProperty(target,key,source[key])})}else if(Object.getOwnPropertyDescriptors){Object.defineProperties(target,Object.getOwnPropertyDescriptors(source))}else{ownKeys(Object(source)).forEach(function(key){Object.defineProperty(target,key,Object.getOwnPropertyDescriptor(source,key))})}}return target}function _defineProperty(obj,key,value){if(key in obj){Object.defineProperty(obj,key,{value:value,enumerable:true,configurable:true,writable:true})}else{obj[key]=value}return obj}var toDecimal=function toDecimal(value){if((0,_isDecimal.default)(value)){return Number(value)}if((0,_isSexagesimal.default)(value)){return(0,_sexagesimalToDecimal.default)(value)}if((0,_isValidCoordinate.default)(value)){var keys=(0,_getCoordinateKeys.default)(value);if(Array.isArray(value)){return value.map(function(v,index){return[0,1].includes(index)?toDecimal(v):v})}return _objectSpread(_objectSpread(_objectSpread({},value),keys.latitude&&_defineProperty({},keys.latitude,toDecimal(value[keys.latitude]))),keys.longitude&&_defineProperty({},keys.longitude,toDecimal(value[keys.longitude])))}if(Array.isArray(value)){return value.map(function(point){return(0,_isValidCoordinate.default)(point)?toDecimal(point):point})}return value};var _default=toDecimal;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,UAAU,GAACC,sBAAsB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAAC,IAAIC,cAAc,GAACF,sBAAsB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAAC,IAAIE,qBAAqB,GAACH,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAAC,IAAIG,kBAAkB,GAACJ,sBAAsB,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAAC,IAAII,kBAAkB,GAACL,sBAAsB,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACM,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACR,OAAO,EAACQ;EAAG,CAAC;AAAA;AAAC,SAASE,OAAOA,CAACC,MAAM,EAACC,cAAc,EAAC;EAAC,IAAIC,IAAI,GAACjB,MAAM,CAACiB,IAAI,CAACF,MAAM,CAAC;EAAC,IAAGf,MAAM,CAACkB,qBAAqB,EAAC;IAAC,IAAIC,OAAO,GAACnB,MAAM,CAACkB,qBAAqB,CAACH,MAAM,CAAC;IAAC,IAAGC,cAAc,EAACG,OAAO,GAACA,OAAO,CAACC,MAAM,CAAC,UAASC,GAAG,EAAC;MAAC,OAAOrB,MAAM,CAACsB,wBAAwB,CAACP,MAAM,EAACM,GAAG,CAAC,CAACE,UAAU;IAAA,CAAC,CAAC;IAACN,IAAI,CAACO,IAAI,CAACC,KAAK,CAACR,IAAI,EAACE,OAAO,CAAC;EAAA;EAAC,OAAOF,IAAI;AAAA;AAAC,SAASS,aAAaA,CAACC,MAAM,EAAC;EAAC,KAAI,IAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACC,SAAS,CAACC,MAAM,EAACF,CAAC,EAAE,EAAC;IAAC,IAAIG,MAAM,GAACF,SAAS,CAACD,CAAC,CAAC,IAAE,IAAI,GAACC,SAAS,CAACD,CAAC,CAAC,GAAC,CAAC,CAAC;IAAC,IAAGA,CAAC,GAAC,CAAC,EAAC;MAACd,OAAO,CAACd,MAAM,CAAC+B,MAAM,CAAC,EAAC,IAAI,CAAC,CAACC,OAAO,CAAC,UAASC,GAAG,EAAC;QAACC,eAAe,CAACP,MAAM,EAACM,GAAG,EAACF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAA,CAAC,CAAC;IAAA,CAAC,MAAK,IAAGjC,MAAM,CAACmC,yBAAyB,EAAC;MAACnC,MAAM,CAACoC,gBAAgB,CAACT,MAAM,EAAC3B,MAAM,CAACmC,yBAAyB,CAACJ,MAAM,CAAC,CAAC;IAAA,CAAC,MAAI;MAACjB,OAAO,CAACd,MAAM,CAAC+B,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAASC,GAAG,EAAC;QAACjC,MAAM,CAACC,cAAc,CAAC0B,MAAM,EAACM,GAAG,EAACjC,MAAM,CAACsB,wBAAwB,CAACS,MAAM,EAACE,GAAG,CAAC,CAAC;MAAA,CAAC,CAAC;IAAA;EAAC;EAAC,OAAON,MAAM;AAAA;AAAC,SAASO,eAAeA,CAACtB,GAAG,EAACqB,GAAG,EAAC9B,KAAK,EAAC;EAAC,IAAG8B,GAAG,IAAIrB,GAAG,EAAC;IAACZ,MAAM,CAACC,cAAc,CAACW,GAAG,EAACqB,GAAG,EAAC;MAAC9B,KAAK,EAACA,KAAK;MAACoB,UAAU,EAAC,IAAI;MAACc,YAAY,EAAC,IAAI;MAACC,QAAQ,EAAC;IAAI,CAAC,CAAC;EAAA,CAAC,MAAI;IAAC1B,GAAG,CAACqB,GAAG,CAAC,GAAC9B,KAAK;EAAA;EAAC,OAAOS,GAAG;AAAA;AAAC,IAAI2B,SAAS,GAAC,SAASA,SAASA,CAACpC,KAAK,EAAC;EAAC,IAAG,CAAC,CAAC,EAACE,UAAU,CAACD,OAAO,EAAED,KAAK,CAAC,EAAC;IAAC,OAAOqC,MAAM,CAACrC,KAAK,CAAC;EAAA;EAAC,IAAG,CAAC,CAAC,EAACK,cAAc,CAACJ,OAAO,EAAED,KAAK,CAAC,EAAC;IAAC,OAAM,CAAC,CAAC,EAACM,qBAAqB,CAACL,OAAO,EAAED,KAAK,CAAC;EAAA;EAAC,IAAG,CAAC,CAAC,EAACO,kBAAkB,CAACN,OAAO,EAAED,KAAK,CAAC,EAAC;IAAC,IAAIc,IAAI,GAAC,CAAC,CAAC,EAACN,kBAAkB,CAACP,OAAO,EAAED,KAAK,CAAC;IAAC,IAAGsC,KAAK,CAACC,OAAO,CAACvC,KAAK,CAAC,EAAC;MAAC,OAAOA,KAAK,CAACwC,GAAG,CAAC,UAASC,CAAC,EAACC,KAAK,EAAC;QAAC,OAAM,CAAC,CAAC,EAAC,CAAC,CAAC,CAACC,QAAQ,CAACD,KAAK,CAAC,GAACN,SAAS,CAACK,CAAC,CAAC,GAACA,CAAC;MAAA,CAAC,CAAC;IAAA;IAAC,OAAOlB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAACvB,KAAK,CAAC,EAACc,IAAI,CAAC8B,QAAQ,IAAEb,eAAe,CAAC,CAAC,CAAC,EAACjB,IAAI,CAAC8B,QAAQ,EAACR,SAAS,CAACpC,KAAK,CAACc,IAAI,CAAC8B,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAC9B,IAAI,CAAC+B,SAAS,IAAEd,eAAe,CAAC,CAAC,CAAC,EAACjB,IAAI,CAAC+B,SAAS,EAACT,SAAS,CAACpC,KAAK,CAACc,IAAI,CAAC+B,SAAS,CAAC,CAAC,CAAC,CAAC;EAAA;EAAC,IAAGP,KAAK,CAACC,OAAO,CAACvC,KAAK,CAAC,EAAC;IAAC,OAAOA,KAAK,CAACwC,GAAG,CAAC,UAASM,KAAK,EAAC;MAAC,OAAM,CAAC,CAAC,EAACvC,kBAAkB,CAACN,OAAO,EAAE6C,KAAK,CAAC,GAACV,SAAS,CAACU,KAAK,CAAC,GAACA,KAAK;IAAA,CAAC,CAAC;EAAA;EAAC,OAAO9C,KAAK;AAAA,CAAC;AAAC,IAAI+C,QAAQ,GAACX,SAAS;AAACrC,OAAO,CAACE,OAAO,GAAC8C,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}