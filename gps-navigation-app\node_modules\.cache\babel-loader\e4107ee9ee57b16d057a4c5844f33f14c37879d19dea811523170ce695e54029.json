{"ast": null, "code": "import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Polygon as LeafletPolygon } from 'leaflet';\nexport const Polygon = createPathComponent(function createPolygon({\n  positions,\n  ...options\n}, ctx) {\n  const polygon = new LeafletPolygon(positions, options);\n  return createElementObject(polygon, extendContext(ctx, {\n    overlayContainer: polygon\n  }));\n}, function updatePolygon(layer, props, prevProps) {\n  if (props.positions !== prevProps.positions) {\n    layer.setLatLngs(props.positions);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "Polygon", "LeafletPolygon", "createPolygon", "positions", "options", "ctx", "polygon", "overlayContainer", "updatePolygon", "layer", "props", "prevProps", "setLatLngs"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/Polygon.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Polygon as LeafletPolygon } from 'leaflet';\nexport const Polygon = createPathComponent(function createPolygon({ positions, ...options }, ctx) {\n    const polygon = new LeafletPolygon(positions, options);\n    return createElementObject(polygon, extendContext(ctx, {\n        overlayContainer: polygon\n    }));\n}, function updatePolygon(layer, props, prevProps) {\n    if (props.positions !== prevProps.positions) {\n        layer.setLatLngs(props.positions);\n    }\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,qBAAqB;AAC7F,SAASC,OAAO,IAAIC,cAAc,QAAQ,SAAS;AACnD,OAAO,MAAMD,OAAO,GAAGF,mBAAmB,CAAC,SAASI,aAAaA,CAAC;EAAEC,SAAS;EAAE,GAAGC;AAAQ,CAAC,EAAEC,GAAG,EAAE;EAC9F,MAAMC,OAAO,GAAG,IAAIL,cAAc,CAACE,SAAS,EAAEC,OAAO,CAAC;EACtD,OAAOP,mBAAmB,CAACS,OAAO,EAAEP,aAAa,CAACM,GAAG,EAAE;IACnDE,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASE,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC/C,IAAID,KAAK,CAACP,SAAS,KAAKQ,SAAS,CAACR,SAAS,EAAE;IACzCM,KAAK,CAACG,UAAU,CAACF,KAAK,CAACP,SAAS,CAAC;EACrC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}