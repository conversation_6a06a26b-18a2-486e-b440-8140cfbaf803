{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { createContext, use } from 'react';\nexport const CONTEXT_VERSION = 1;\nexport function createLeafletContext(map) {\n  return Object.freeze({\n    __version: CONTEXT_VERSION,\n    map\n  });\n}\nexport function extendContext(source, extra) {\n  return Object.freeze(_objectSpread(_objectSpread({}, source), extra));\n}\nexport const LeafletContext = createContext(null);\nexport function useLeafletContext() {\n  const context = use(LeafletContext);\n  if (context == null) {\n    throw new Error('No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>');\n  }\n  return context;\n}", "map": {"version": 3, "names": ["createContext", "use", "CONTEXT_VERSION", "createLeafletContext", "map", "Object", "freeze", "__version", "extendContext", "source", "extra", "_objectSpread", "LeafletContext", "useLeafletContext", "context", "Error"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@react-leaflet/core/lib/context.js"], "sourcesContent": ["import { createContext, use } from 'react';\nexport const CONTEXT_VERSION = 1;\nexport function createLeafletContext(map) {\n    return Object.freeze({\n        __version: CONTEXT_VERSION,\n        map\n    });\n}\nexport function extendContext(source, extra) {\n    return Object.freeze({\n        ...source,\n        ...extra\n    });\n}\nexport const LeafletContext = createContext(null);\nexport function useLeafletContext() {\n    const context = use(LeafletContext);\n    if (context == null) {\n        throw new Error('No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>');\n    }\n    return context;\n}\n"], "mappings": ";AAAA,SAASA,aAAa,EAAEC,GAAG,QAAQ,OAAO;AAC1C,OAAO,MAAMC,eAAe,GAAG,CAAC;AAChC,OAAO,SAASC,oBAAoBA,CAACC,GAAG,EAAE;EACtC,OAAOC,MAAM,CAACC,MAAM,CAAC;IACjBC,SAAS,EAAEL,eAAe;IAC1BE;EACJ,CAAC,CAAC;AACN;AACA,OAAO,SAASI,aAAaA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACzC,OAAOL,MAAM,CAACC,MAAM,CAAAK,aAAA,CAAAA,aAAA,KACbF,MAAM,GACNC,KAAK,CACX,CAAC;AACN;AACA,OAAO,MAAME,cAAc,GAAGZ,aAAa,CAAC,IAAI,CAAC;AACjD,OAAO,SAASa,iBAAiBA,CAAA,EAAG;EAChC,MAAMC,OAAO,GAAGb,GAAG,CAACW,cAAc,CAAC;EACnC,IAAIE,OAAO,IAAI,IAAI,EAAE;IACjB,MAAM,IAAIC,KAAK,CAAC,6FAA6F,CAAC;EAClH;EACA,OAAOD,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}