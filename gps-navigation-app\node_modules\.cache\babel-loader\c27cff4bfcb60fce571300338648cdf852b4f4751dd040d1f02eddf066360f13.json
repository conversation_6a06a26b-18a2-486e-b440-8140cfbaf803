{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _toRad = _interopRequireDefault(require(\"./toRad\"));\nvar _getLatitude = _interopRequireDefault(require(\"./getLatitude\"));\nvar _getLongitude = _interopRequireDefault(require(\"./getLongitude\"));\nvar _constants = require(\"./constants\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar getAreaOfPolygon = function getAreaOfPolygon(points) {\n  var area = 0;\n  if (points.length > 2) {\n    var lowerIndex;\n    var middleIndex;\n    var upperIndex;\n    for (var i = 0; i < points.length; i++) {\n      if (i === points.length - 2) {\n        lowerIndex = points.length - 2;\n        middleIndex = points.length - 1;\n        upperIndex = 0;\n      } else if (i === points.length - 1) {\n        lowerIndex = points.length - 1;\n        middleIndex = 0;\n        upperIndex = 1;\n      } else {\n        lowerIndex = i;\n        middleIndex = i + 1;\n        upperIndex = i + 2;\n      }\n      var p1lon = (0, _getLongitude.default)(points[lowerIndex]);\n      var p2lat = (0, _getLatitude.default)(points[middleIndex]);\n      var p3lon = (0, _getLongitude.default)(points[upperIndex]);\n      area += ((0, _toRad.default)(p3lon) - (0, _toRad.default)(p1lon)) * Math.sin((0, _toRad.default)(p2lat));\n    }\n    area = area * _constants.earthRadius * _constants.earthRadius / 2;\n  }\n  return Math.abs(area);\n};\nvar _default = getAreaOfPolygon;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_toRad", "_interopRequireDefault", "require", "_getLatitude", "_getLongitude", "_constants", "obj", "__esModule", "getAreaOfPolygon", "points", "area", "length", "lowerIndex", "middleIndex", "upperIndex", "i", "p1lon", "p2lat", "p3lon", "Math", "sin", "earthRadius", "abs", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getAreaOfPolygon.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _toRad=_interopRequireDefault(require(\"./toRad\"));var _getLatitude=_interopRequireDefault(require(\"./getLatitude\"));var _getLongitude=_interopRequireDefault(require(\"./getLongitude\"));var _constants=require(\"./constants\");function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var getAreaOfPolygon=function getAreaOfPolygon(points){var area=0;if(points.length>2){var lowerIndex;var middleIndex;var upperIndex;for(var i=0;i<points.length;i++){if(i===points.length-2){lowerIndex=points.length-2;middleIndex=points.length-1;upperIndex=0}else if(i===points.length-1){lowerIndex=points.length-1;middleIndex=0;upperIndex=1}else{lowerIndex=i;middleIndex=i+1;upperIndex=i+2}var p1lon=(0,_getLongitude.default)(points[lowerIndex]);var p2lat=(0,_getLatitude.default)(points[middleIndex]);var p3lon=(0,_getLongitude.default)(points[upperIndex]);area+=((0,_toRad.default)(p3lon)-(0,_toRad.default)(p1lon))*Math.sin((0,_toRad.default)(p2lat))}area=area*_constants.earthRadius*_constants.earthRadius/2}return Math.abs(area)};var _default=getAreaOfPolygon;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,MAAM,GAACC,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAAC,IAAIC,YAAY,GAACF,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,IAAIE,aAAa,GAACH,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAAC,IAAIG,UAAU,GAACH,OAAO,CAAC,aAAa,CAAC;AAAC,SAASD,sBAAsBA,CAACK,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACP,OAAO,EAACO;EAAG,CAAC;AAAA;AAAC,IAAIE,gBAAgB,GAAC,SAASA,gBAAgBA,CAACC,MAAM,EAAC;EAAC,IAAIC,IAAI,GAAC,CAAC;EAAC,IAAGD,MAAM,CAACE,MAAM,GAAC,CAAC,EAAC;IAAC,IAAIC,UAAU;IAAC,IAAIC,WAAW;IAAC,IAAIC,UAAU;IAAC,KAAI,IAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACN,MAAM,CAACE,MAAM,EAACI,CAAC,EAAE,EAAC;MAAC,IAAGA,CAAC,KAAGN,MAAM,CAACE,MAAM,GAAC,CAAC,EAAC;QAACC,UAAU,GAACH,MAAM,CAACE,MAAM,GAAC,CAAC;QAACE,WAAW,GAACJ,MAAM,CAACE,MAAM,GAAC,CAAC;QAACG,UAAU,GAAC,CAAC;MAAA,CAAC,MAAK,IAAGC,CAAC,KAAGN,MAAM,CAACE,MAAM,GAAC,CAAC,EAAC;QAACC,UAAU,GAACH,MAAM,CAACE,MAAM,GAAC,CAAC;QAACE,WAAW,GAAC,CAAC;QAACC,UAAU,GAAC,CAAC;MAAA,CAAC,MAAI;QAACF,UAAU,GAACG,CAAC;QAACF,WAAW,GAACE,CAAC,GAAC,CAAC;QAACD,UAAU,GAACC,CAAC,GAAC,CAAC;MAAA;MAAC,IAAIC,KAAK,GAAC,CAAC,CAAC,EAACZ,aAAa,CAACL,OAAO,EAAEU,MAAM,CAACG,UAAU,CAAC,CAAC;MAAC,IAAIK,KAAK,GAAC,CAAC,CAAC,EAACd,YAAY,CAACJ,OAAO,EAAEU,MAAM,CAACI,WAAW,CAAC,CAAC;MAAC,IAAIK,KAAK,GAAC,CAAC,CAAC,EAACd,aAAa,CAACL,OAAO,EAAEU,MAAM,CAACK,UAAU,CAAC,CAAC;MAACJ,IAAI,IAAE,CAAC,CAAC,CAAC,EAACV,MAAM,CAACD,OAAO,EAAEmB,KAAK,CAAC,GAAC,CAAC,CAAC,EAAClB,MAAM,CAACD,OAAO,EAAEiB,KAAK,CAAC,IAAEG,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,EAACpB,MAAM,CAACD,OAAO,EAAEkB,KAAK,CAAC,CAAC;IAAA;IAACP,IAAI,GAACA,IAAI,GAACL,UAAU,CAACgB,WAAW,GAAChB,UAAU,CAACgB,WAAW,GAAC,CAAC;EAAA;EAAC,OAAOF,IAAI,CAACG,GAAG,CAACZ,IAAI,CAAC;AAAA,CAAC;AAAC,IAAIa,QAAQ,GAACf,gBAAgB;AAACX,OAAO,CAACE,OAAO,GAACwB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}