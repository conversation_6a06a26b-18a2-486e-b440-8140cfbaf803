{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getLatitude = _interopRequireDefault(require(\"./getLatitude\"));\nvar _getLongitude = _interopRequireDefault(require(\"./getLongitude\"));\nvar _toRad = _interopRequireDefault(require(\"./toRad\"));\nvar _toDeg = _interopRequireDefault(require(\"./toDeg\"));\nvar _constants = require(\"./constants\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar getBoundsOfDistance = function getBoundsOfDistance(point, distance) {\n  var latitude = (0, _getLatitude.default)(point);\n  var longitude = (0, _getLongitude.default)(point);\n  var radLat = (0, _toRad.default)(latitude);\n  var radLon = (0, _toRad.default)(longitude);\n  var radDist = distance / _constants.earthRadius;\n  var minLat = radLat - radDist;\n  var maxLat = radLat + radDist;\n  var MAX_LAT_RAD = (0, _toRad.default)(_constants.MAXLAT);\n  var MIN_LAT_RAD = (0, _toRad.default)(_constants.MINLAT);\n  var MAX_LON_RAD = (0, _toRad.default)(_constants.MAXLON);\n  var MIN_LON_RAD = (0, _toRad.default)(_constants.MINLON);\n  var minLon;\n  var maxLon;\n  if (minLat > MIN_LAT_RAD && maxLat < MAX_LAT_RAD) {\n    var deltaLon = Math.asin(Math.sin(radDist) / Math.cos(radLat));\n    minLon = radLon - deltaLon;\n    if (minLon < MIN_LON_RAD) {\n      minLon += Math.PI * 2;\n    }\n    maxLon = radLon + deltaLon;\n    if (maxLon > MAX_LON_RAD) {\n      maxLon -= Math.PI * 2;\n    }\n  } else {\n    minLat = Math.max(minLat, MIN_LAT_RAD);\n    maxLat = Math.min(maxLat, MAX_LAT_RAD);\n    minLon = MIN_LON_RAD;\n    maxLon = MAX_LON_RAD;\n  }\n  return [{\n    latitude: (0, _toDeg.default)(minLat),\n    longitude: (0, _toDeg.default)(minLon)\n  }, {\n    latitude: (0, _toDeg.default)(maxLat),\n    longitude: (0, _toDeg.default)(maxLon)\n  }];\n};\nvar _default = getBoundsOfDistance;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getLatitude", "_interopRequireDefault", "require", "_getLongitude", "_toRad", "_toDeg", "_constants", "obj", "__esModule", "getBoundsOfDistance", "point", "distance", "latitude", "longitude", "radLat", "rad<PERSON>on", "radDist", "earthRadius", "minLat", "maxLat", "MAX_LAT_RAD", "MAXLAT", "MIN_LAT_RAD", "MINLAT", "MAX_LON_RAD", "MAXLON", "MIN_LON_RAD", "MINLON", "minLon", "maxLon", "deltaLon", "Math", "asin", "sin", "cos", "PI", "max", "min", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getBoundsOfDistance.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getLatitude=_interopRequireDefault(require(\"./getLatitude\"));var _getLongitude=_interopRequireDefault(require(\"./getLongitude\"));var _toRad=_interopRequireDefault(require(\"./toRad\"));var _toDeg=_interopRequireDefault(require(\"./toDeg\"));var _constants=require(\"./constants\");function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var getBoundsOfDistance=function getBoundsOfDistance(point,distance){var latitude=(0,_getLatitude.default)(point);var longitude=(0,_getLongitude.default)(point);var radLat=(0,_toRad.default)(latitude);var radLon=(0,_toRad.default)(longitude);var radDist=distance/_constants.earthRadius;var minLat=radLat-radDist;var maxLat=radLat+radDist;var MAX_LAT_RAD=(0,_toRad.default)(_constants.MAXLAT);var MIN_LAT_RAD=(0,_toRad.default)(_constants.MINLAT);var MAX_LON_RAD=(0,_toRad.default)(_constants.MAXLON);var MIN_LON_RAD=(0,_toRad.default)(_constants.MINLON);var minLon;var maxLon;if(minLat>MIN_LAT_RAD&&maxLat<MAX_LAT_RAD){var deltaLon=Math.asin(Math.sin(radDist)/Math.cos(radLat));minLon=radLon-deltaLon;if(minLon<MIN_LON_RAD){minLon+=Math.PI*2}maxLon=radLon+deltaLon;if(maxLon>MAX_LON_RAD){maxLon-=Math.PI*2}}else{minLat=Math.max(minLat,MIN_LAT_RAD);maxLat=Math.min(maxLat,MAX_LAT_RAD);minLon=MIN_LON_RAD;maxLon=MAX_LON_RAD}return[{latitude:(0,_toDeg.default)(minLat),longitude:(0,_toDeg.default)(minLon)},{latitude:(0,_toDeg.default)(maxLat),longitude:(0,_toDeg.default)(maxLon)}]};var _default=getBoundsOfDistance;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,IAAIC,aAAa,GAACF,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAAC,IAAIE,MAAM,GAACH,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAAC,IAAIG,MAAM,GAACJ,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAAC,IAAII,UAAU,GAACJ,OAAO,CAAC,aAAa,CAAC;AAAC,SAASD,sBAAsBA,CAACM,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACR,OAAO,EAACQ;EAAG,CAAC;AAAA;AAAC,IAAIE,mBAAmB,GAAC,SAASA,mBAAmBA,CAACC,KAAK,EAACC,QAAQ,EAAC;EAAC,IAAIC,QAAQ,GAAC,CAAC,CAAC,EAACZ,YAAY,CAACD,OAAO,EAAEW,KAAK,CAAC;EAAC,IAAIG,SAAS,GAAC,CAAC,CAAC,EAACV,aAAa,CAACJ,OAAO,EAAEW,KAAK,CAAC;EAAC,IAAII,MAAM,GAAC,CAAC,CAAC,EAACV,MAAM,CAACL,OAAO,EAAEa,QAAQ,CAAC;EAAC,IAAIG,MAAM,GAAC,CAAC,CAAC,EAACX,MAAM,CAACL,OAAO,EAAEc,SAAS,CAAC;EAAC,IAAIG,OAAO,GAACL,QAAQ,GAACL,UAAU,CAACW,WAAW;EAAC,IAAIC,MAAM,GAACJ,MAAM,GAACE,OAAO;EAAC,IAAIG,MAAM,GAACL,MAAM,GAACE,OAAO;EAAC,IAAII,WAAW,GAAC,CAAC,CAAC,EAAChB,MAAM,CAACL,OAAO,EAAEO,UAAU,CAACe,MAAM,CAAC;EAAC,IAAIC,WAAW,GAAC,CAAC,CAAC,EAAClB,MAAM,CAACL,OAAO,EAAEO,UAAU,CAACiB,MAAM,CAAC;EAAC,IAAIC,WAAW,GAAC,CAAC,CAAC,EAACpB,MAAM,CAACL,OAAO,EAAEO,UAAU,CAACmB,MAAM,CAAC;EAAC,IAAIC,WAAW,GAAC,CAAC,CAAC,EAACtB,MAAM,CAACL,OAAO,EAAEO,UAAU,CAACqB,MAAM,CAAC;EAAC,IAAIC,MAAM;EAAC,IAAIC,MAAM;EAAC,IAAGX,MAAM,GAACI,WAAW,IAAEH,MAAM,GAACC,WAAW,EAAC;IAAC,IAAIU,QAAQ,GAACC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACjB,OAAO,CAAC,GAACe,IAAI,CAACG,GAAG,CAACpB,MAAM,CAAC,CAAC;IAACc,MAAM,GAACb,MAAM,GAACe,QAAQ;IAAC,IAAGF,MAAM,GAACF,WAAW,EAAC;MAACE,MAAM,IAAEG,IAAI,CAACI,EAAE,GAAC,CAAC;IAAA;IAACN,MAAM,GAACd,MAAM,GAACe,QAAQ;IAAC,IAAGD,MAAM,GAACL,WAAW,EAAC;MAACK,MAAM,IAAEE,IAAI,CAACI,EAAE,GAAC,CAAC;IAAA;EAAC,CAAC,MAAI;IAACjB,MAAM,GAACa,IAAI,CAACK,GAAG,CAAClB,MAAM,EAACI,WAAW,CAAC;IAACH,MAAM,GAACY,IAAI,CAACM,GAAG,CAAClB,MAAM,EAACC,WAAW,CAAC;IAACQ,MAAM,GAACF,WAAW;IAACG,MAAM,GAACL,WAAW;EAAA;EAAC,OAAM,CAAC;IAACZ,QAAQ,EAAC,CAAC,CAAC,EAACP,MAAM,CAACN,OAAO,EAAEmB,MAAM,CAAC;IAACL,SAAS,EAAC,CAAC,CAAC,EAACR,MAAM,CAACN,OAAO,EAAE6B,MAAM;EAAC,CAAC,EAAC;IAAChB,QAAQ,EAAC,CAAC,CAAC,EAACP,MAAM,CAACN,OAAO,EAAEoB,MAAM,CAAC;IAACN,SAAS,EAAC,CAAC,CAAC,EAACR,MAAM,CAACN,OAAO,EAAE8B,MAAM;EAAC,CAAC,CAAC;AAAA,CAAC;AAAC,IAAIS,QAAQ,GAAC7B,mBAAmB;AAACZ,OAAO,CAACE,OAAO,GAACuC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}