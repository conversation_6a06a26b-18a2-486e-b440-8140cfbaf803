{"ast": null, "code": "import { DomUtil } from 'leaflet';\nfunction splitClassName(className) {\n  return className.split(' ').filter(Boolean);\n}\nexport function addClassName(element, className) {\n  for (const cls of splitClassName(className)) {\n    DomUtil.addClass(element, cls);\n  }\n}\nexport function removeClassName(element, className) {\n  for (const cls of splitClassName(className)) {\n    DomUtil.removeClass(element, cls);\n  }\n}\nexport function updateClassName(element, prevClassName, nextClassName) {\n  if (element != null && nextClassName !== prevClassName) {\n    if (prevClassName != null && prevClassName.length > 0) {\n      removeClassName(element, prevClassName);\n    }\n    if (nextClassName != null && nextClassName.length > 0) {\n      addClassName(element, nextClassName);\n    }\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "splitClassName", "className", "split", "filter", "Boolean", "addClassName", "element", "cls", "addClass", "removeClassName", "removeClass", "updateClassName", "prevClassName", "nextClassName", "length"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@react-leaflet/core/lib/dom.js"], "sourcesContent": ["import { DomUtil } from 'leaflet';\nfunction splitClassName(className) {\n    return className.split(' ').filter(Boolean);\n}\nexport function addClassName(element, className) {\n    for (const cls of splitClassName(className)){\n        DomUtil.addClass(element, cls);\n    }\n}\nexport function removeClassName(element, className) {\n    for (const cls of splitClassName(className)){\n        DomUtil.removeClass(element, cls);\n    }\n}\nexport function updateClassName(element, prevClassName, nextClassName) {\n    if (element != null && nextClassName !== prevClassName) {\n        if (prevClassName != null && prevClassName.length > 0) {\n            removeClassName(element, prevClassName);\n        }\n        if (nextClassName != null && nextClassName.length > 0) {\n            addClassName(element, nextClassName);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,SAAS;AACjC,SAASC,cAAcA,CAACC,SAAS,EAAE;EAC/B,OAAOA,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;AAC/C;AACA,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEL,SAAS,EAAE;EAC7C,KAAK,MAAMM,GAAG,IAAIP,cAAc,CAACC,SAAS,CAAC,EAAC;IACxCF,OAAO,CAACS,QAAQ,CAACF,OAAO,EAAEC,GAAG,CAAC;EAClC;AACJ;AACA,OAAO,SAASE,eAAeA,CAACH,OAAO,EAAEL,SAAS,EAAE;EAChD,KAAK,MAAMM,GAAG,IAAIP,cAAc,CAACC,SAAS,CAAC,EAAC;IACxCF,OAAO,CAACW,WAAW,CAACJ,OAAO,EAAEC,GAAG,CAAC;EACrC;AACJ;AACA,OAAO,SAASI,eAAeA,CAACL,OAAO,EAAEM,aAAa,EAAEC,aAAa,EAAE;EACnE,IAAIP,OAAO,IAAI,IAAI,IAAIO,aAAa,KAAKD,aAAa,EAAE;IACpD,IAAIA,aAAa,IAAI,IAAI,IAAIA,aAAa,CAACE,MAAM,GAAG,CAAC,EAAE;MACnDL,eAAe,CAACH,OAAO,EAAEM,aAAa,CAAC;IAC3C;IACA,IAAIC,aAAa,IAAI,IAAI,IAAIA,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACnDT,YAAY,CAACC,OAAO,EAAEO,aAAa,CAAC;IACxC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}