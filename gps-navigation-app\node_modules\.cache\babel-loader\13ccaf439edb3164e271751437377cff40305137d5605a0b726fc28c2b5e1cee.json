{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getRhumbLineBearing = _interopRequireDefault(require(\"./getRhumbLineBearing\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar getCompassDirection = function getCompassDirection(origin, dest) {\n  var bearingFn = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : _getRhumbLineBearing.default;\n  var bearing = typeof bearingFn === \"function\" ? bearingFn(origin, dest) : (0, _getRhumbLineBearing.default)(origin, dest);\n  if (isNaN(bearing)) {\n    throw new Error(\"Could not calculate bearing for given points. Check your bearing function\");\n  }\n  switch (Math.round(bearing / 22.5)) {\n    case 1:\n      return \"NNE\";\n    case 2:\n      return \"NE\";\n    case 3:\n      return \"ENE\";\n    case 4:\n      return \"E\";\n    case 5:\n      return \"ESE\";\n    case 6:\n      return \"SE\";\n    case 7:\n      return \"SSE\";\n    case 8:\n      return \"S\";\n    case 9:\n      return \"SSW\";\n    case 10:\n      return \"SW\";\n    case 11:\n      return \"WSW\";\n    case 12:\n      return \"W\";\n    case 13:\n      return \"WNW\";\n    case 14:\n      return \"NW\";\n    case 15:\n      return \"NNW\";\n    default:\n      return \"N\";\n  }\n};\nvar _default = getCompassDirection;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getRhumbLineBearing", "_interopRequireDefault", "require", "obj", "__esModule", "getCompassDirection", "origin", "dest", "bearingFn", "arguments", "length", "undefined", "bearing", "isNaN", "Error", "Math", "round", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getCompassDirection.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getRhumbLineBearing=_interopRequireDefault(require(\"./getRhumbLineBearing\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var getCompassDirection=function getCompassDirection(origin,dest){var bearingFn=arguments.length>2&&arguments[2]!==undefined?arguments[2]:_getRhumbLineBearing.default;var bearing=typeof bearingFn===\"function\"?bearingFn(origin,dest):(0,_getRhumbLineBearing.default)(origin,dest);if(isNaN(bearing)){throw new Error(\"Could not calculate bearing for given points. Check your bearing function\")}switch(Math.round(bearing/22.5)){case 1:return\"NNE\";case 2:return\"NE\";case 3:return\"ENE\";case 4:return\"E\";case 5:return\"ESE\";case 6:return\"SE\";case 7:return\"SSE\";case 8:return\"S\";case 9:return\"SSW\";case 10:return\"SW\";case 11:return\"WSW\";case 12:return\"W\";case 13:return\"WNW\";case 14:return\"NW\";case 15:return\"NNW\";default:return\"N\";}};var _default=getCompassDirection;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,oBAAoB,GAACC,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACE,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACJ,OAAO,EAACI;EAAG,CAAC;AAAA;AAAC,IAAIE,mBAAmB,GAAC,SAASA,mBAAmBA,CAACC,MAAM,EAACC,IAAI,EAAC;EAAC,IAAIC,SAAS,GAACC,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAGE,SAAS,GAACF,SAAS,CAAC,CAAC,CAAC,GAACT,oBAAoB,CAACD,OAAO;EAAC,IAAIa,OAAO,GAAC,OAAOJ,SAAS,KAAG,UAAU,GAACA,SAAS,CAACF,MAAM,EAACC,IAAI,CAAC,GAAC,CAAC,CAAC,EAACP,oBAAoB,CAACD,OAAO,EAAEO,MAAM,EAACC,IAAI,CAAC;EAAC,IAAGM,KAAK,CAACD,OAAO,CAAC,EAAC;IAAC,MAAM,IAAIE,KAAK,CAAC,2EAA2E,CAAC;EAAA;EAAC,QAAOC,IAAI,CAACC,KAAK,CAACJ,OAAO,GAAC,IAAI,CAAC;IAAE,KAAK,CAAC;MAAC,OAAM,KAAK;IAAC,KAAK,CAAC;MAAC,OAAM,IAAI;IAAC,KAAK,CAAC;MAAC,OAAM,KAAK;IAAC,KAAK,CAAC;MAAC,OAAM,GAAG;IAAC,KAAK,CAAC;MAAC,OAAM,KAAK;IAAC,KAAK,CAAC;MAAC,OAAM,IAAI;IAAC,KAAK,CAAC;MAAC,OAAM,KAAK;IAAC,KAAK,CAAC;MAAC,OAAM,GAAG;IAAC,KAAK,CAAC;MAAC,OAAM,KAAK;IAAC,KAAK,EAAE;MAAC,OAAM,IAAI;IAAC,KAAK,EAAE;MAAC,OAAM,KAAK;IAAC,KAAK,EAAE;MAAC,OAAM,GAAG;IAAC,KAAK,EAAE;MAAC,OAAM,KAAK;IAAC,KAAK,EAAE;MAAC,OAAM,IAAI;IAAC,KAAK,EAAE;MAAC,OAAM,KAAK;IAAC;MAAQ,OAAM,GAAG;EAAC;AAAC,CAAC;AAAC,IAAIK,QAAQ,GAACZ,mBAAmB;AAACR,OAAO,CAACE,OAAO,GAACkB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}