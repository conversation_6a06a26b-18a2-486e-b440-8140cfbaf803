{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _taggedTemplateLiteral from\"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13,_templateObject14,_templateObject15,_templateObject16;import React,{useState,useEffect}from'react';import styled from'styled-components';import{FiSearch,FiMapPin,FiClock,FiStar,FiNavigation}from'react-icons/fi';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const PanelContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #2d2d2d;\\n  color: white;\\n\"])));const Header=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  padding: 20px;\\n  border-bottom: 1px solid #444;\\n  background-color: #1a1a1a;\\n\"])));const Title=styled.h2(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  margin: 0 0 16px 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #fff;\\n  text-align: center;\\n\"])));const SearchContainer=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  position: relative;\\n  margin-bottom: 16px;\\n\"])));const SearchInput=styled.input(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  padding: 16px 20px 16px 50px;\\n  border: 2px solid #444;\\n  border-radius: 12px;\\n  background-color: #3d3d3d;\\n  color: white;\\n  font-size: 16px;\\n  outline: none;\\n  transition: all 0.3s ease;\\n  \\n  &:focus {\\n    border-color: #007bff;\\n    background-color: #4d4d4d;\\n  }\\n  \\n  &::placeholder {\\n    color: #aaa;\\n  }\\n\"])));const SearchIcon=styled(FiSearch)(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  position: absolute;\\n  left: 16px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: #aaa;\\n  font-size: 20px;\\n\"])));const QuickActions=styled.div(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 12px;\\n  margin-bottom: 20px;\\n\"])));const QuickActionButton=styled.button(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  padding: 16px;\\n  border: 1px solid #444;\\n  border-radius: 12px;\\n  background-color: #3d3d3d;\\n  color: white;\\n  font-size: 14px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  \\n  &:hover {\\n    background-color: #007bff;\\n    border-color: #007bff;\\n  }\\n\"])));const Content=styled.div(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 0 20px 20px;\\n\"])));const SectionTitle=styled.h3(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  margin: 20px 0 12px 0;\\n  font-size: 18px;\\n  font-weight: 500;\\n  color: #ccc;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n\"])));const ResultsList=styled.div(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n\"])));const ResultItem=styled.div(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  padding: 16px;\\n  border: 1px solid #444;\\n  border-radius: 12px;\\n  background-color: #3d3d3d;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  \\n  &:hover {\\n    background-color: #4d4d4d;\\n    border-color: #007bff;\\n  }\\n\"])));const ResultName=styled.div(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n  color: white;\\n\"])));const ResultAddress=styled.div(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  font-size: 14px;\\n  color: #aaa;\\n  margin-bottom: 8px;\\n\"])));const ResultMeta=styled.div(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  font-size: 12px;\\n  color: #888;\\n\"])));const CategoryChips=styled.div(_templateObject14||(_templateObject14=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n\"])));const CategoryChip=styled.button(_templateObject15||(_templateObject15=_taggedTemplateLiteral([\"\\n  padding: 8px 16px;\\n  border: 1px solid #444;\\n  border-radius: 20px;\\n  background-color: \",\";\\n  color: white;\\n  font-size: 12px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  \\n  &:hover {\\n    background-color: #007bff;\\n    border-color: #007bff;\\n  }\\n\"])),props=>props.active?'#007bff':'#3d3d3d');const EmptyState=styled.div(_templateObject16||(_templateObject16=_taggedTemplateLiteral([\"\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #888;\\n\"])));const SearchPanel=_ref=>{let{currentLocation,onDestinationSelect}=_ref;const[searchQuery,setSearchQuery]=useState('');const[searchResults,setSearchResults]=useState([]);const[recentSearches,setRecentSearches]=useState([]);const[favorites,setFavorites]=useState([]);const[selectedCategory,setSelectedCategory]=useState('');const[isLoading,setIsLoading]=useState(false);const categories=['رستوران','پمپ بنزین','بیمارستان','بانک','مرکز خرید','پارکینگ','هتل','داروخانه','مدرسه','پارک'];// Mock search function - in real app, this would call a geocoding API\nconst performSearch=async query=>{if(!query.trim()){setSearchResults([]);return;}setIsLoading(true);// Simulate API call\nsetTimeout(()=>{const mockResults=[{id:'1',name:'میدان آزادی',address:'تهران، میدان آزادی',location:{lat:35.6958,lng:51.3370},type:'poi',category:'نقاط دیدنی',distance:currentLocation?5000:undefined},{id:'2',name:'برج میلاد',address:'تهران، برج میلاد',location:{lat:35.7447,lng:51.3753},type:'poi',category:'نقاط دیدنی',distance:currentLocation?8000:undefined},{id:'3',name:'بازار بزرگ تهران',address:'تهران، بازار بزرگ',location:{lat:35.6736,lng:51.4208},type:'poi',category:'مرکز خرید',distance:currentLocation?3000:undefined}].filter(result=>result.name.includes(query)||result.address.includes(query));setSearchResults(mockResults);setIsLoading(false);},500);};useEffect(()=>{const timeoutId=setTimeout(()=>{performSearch(searchQuery);},300);return()=>clearTimeout(timeoutId);},[searchQuery,currentLocation]);// Load recent searches and favorites from localStorage\nuseEffect(()=>{const savedRecentSearches=localStorage.getItem('recentSearches');const savedFavorites=localStorage.getItem('favorites');if(savedRecentSearches){setRecentSearches(JSON.parse(savedRecentSearches));}if(savedFavorites){setFavorites(JSON.parse(savedFavorites));}},[]);const handleResultSelect=result=>{const locationData=_objectSpread(_objectSpread({},result.location),{},{name:result.name,address:result.address,timestamp:Date.now()});// Add to recent searches\nconst updatedRecent=[result,...recentSearches.filter(r=>r.id!==result.id)].slice(0,10);setRecentSearches(updatedRecent);localStorage.setItem('recentSearches',JSON.stringify(updatedRecent));onDestinationSelect(locationData);};const handleQuickAction=action=>{switch(action){case'home':// In a real app, this would use saved home location\nonDestinationSelect({lat:35.6892,lng:51.3890,name:'خانه',timestamp:Date.now()});break;case'work':// In a real app, this would use saved work location\nonDestinationSelect({lat:35.7219,lng:51.3347,name:'محل کار',timestamp:Date.now()});break;}};return/*#__PURE__*/_jsxs(PanelContainer,{children:[/*#__PURE__*/_jsxs(Header,{children:[/*#__PURE__*/_jsx(Title,{children:\"\\u062C\\u0633\\u062A\\u062C\\u0648\\u06CC \\u0645\\u0642\\u0635\\u062F\"}),/*#__PURE__*/_jsxs(SearchContainer,{children:[/*#__PURE__*/_jsx(SearchIcon,{}),/*#__PURE__*/_jsx(SearchInput,{type:\"text\",placeholder:\"\\u062C\\u0633\\u062A\\u062C\\u0648\\u06CC \\u0622\\u062F\\u0631\\u0633\\u060C \\u0645\\u06A9\\u0627\\u0646 \\u06CC\\u0627 \\u0646\\u0642\\u0637\\u0647 \\u0639\\u0644\\u0627\\u0642\\u0647...\",value:searchQuery,onChange:e=>setSearchQuery(e.target.value)})]}),/*#__PURE__*/_jsxs(QuickActions,{children:[/*#__PURE__*/_jsxs(QuickActionButton,{onClick:()=>handleQuickAction('home'),children:[/*#__PURE__*/_jsx(FiMapPin,{}),\"\\u062E\\u0627\\u0646\\u0647\"]}),/*#__PURE__*/_jsxs(QuickActionButton,{onClick:()=>handleQuickAction('work'),children:[/*#__PURE__*/_jsx(FiNavigation,{}),\"\\u0645\\u062D\\u0644 \\u06A9\\u0627\\u0631\"]})]}),/*#__PURE__*/_jsx(CategoryChips,{children:categories.map(category=>/*#__PURE__*/_jsx(CategoryChip,{active:selectedCategory===category,onClick:()=>setSelectedCategory(selectedCategory===category?'':category),children:category},category))})]}),/*#__PURE__*/_jsxs(Content,{children:[searchQuery&&searchResults.length>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(SectionTitle,{children:[/*#__PURE__*/_jsx(FiSearch,{}),\"\\u0646\\u062A\\u0627\\u06CC\\u062C \\u062C\\u0633\\u062A\\u062C\\u0648\"]}),/*#__PURE__*/_jsx(ResultsList,{children:searchResults.map(result=>/*#__PURE__*/_jsxs(ResultItem,{onClick:()=>handleResultSelect(result),children:[/*#__PURE__*/_jsx(ResultName,{children:result.name}),/*#__PURE__*/_jsx(ResultAddress,{children:result.address}),/*#__PURE__*/_jsxs(ResultMeta,{children:[/*#__PURE__*/_jsx(\"span\",{children:result.category}),result.distance&&/*#__PURE__*/_jsxs(\"span\",{children:[(result.distance/1000).toFixed(1),\" \\u06A9\\u06CC\\u0644\\u0648\\u0645\\u062A\\u0631\"]})]})]},result.id))})]}),!searchQuery&&recentSearches.length>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(SectionTitle,{children:[/*#__PURE__*/_jsx(FiClock,{}),\"\\u062C\\u0633\\u062A\\u062C\\u0648\\u0647\\u0627\\u06CC \\u0627\\u062E\\u06CC\\u0631\"]}),/*#__PURE__*/_jsx(ResultsList,{children:recentSearches.slice(0,5).map(result=>/*#__PURE__*/_jsxs(ResultItem,{onClick:()=>handleResultSelect(result),children:[/*#__PURE__*/_jsx(ResultName,{children:result.name}),/*#__PURE__*/_jsx(ResultAddress,{children:result.address}),/*#__PURE__*/_jsx(ResultMeta,{children:/*#__PURE__*/_jsx(\"span\",{children:result.category})})]},result.id))})]}),!searchQuery&&favorites.length>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(SectionTitle,{children:[/*#__PURE__*/_jsx(FiStar,{}),\"\\u0639\\u0644\\u0627\\u0642\\u0647\\u200C\\u0645\\u0646\\u062F\\u06CC\\u200C\\u0647\\u0627\"]}),/*#__PURE__*/_jsx(ResultsList,{children:favorites.slice(0,5).map(poi=>/*#__PURE__*/_jsxs(ResultItem,{onClick:()=>handleResultSelect({id:poi.id,name:poi.name,address:poi.location.address||'',location:poi.location,type:'poi',category:poi.category}),children:[/*#__PURE__*/_jsx(ResultName,{children:poi.name}),/*#__PURE__*/_jsx(ResultAddress,{children:poi.location.address}),/*#__PURE__*/_jsxs(ResultMeta,{children:[/*#__PURE__*/_jsx(\"span\",{children:poi.category}),poi.rating&&/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u2B50 \",poi.rating.toFixed(1)]})]})]},poi.id))})]}),!searchQuery&&recentSearches.length===0&&favorites.length===0&&/*#__PURE__*/_jsxs(EmptyState,{children:[/*#__PURE__*/_jsx(FiMapPin,{size:48,style:{marginBottom:'16px',opacity:0.5}}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u0628\\u0631\\u0627\\u06CC \\u0634\\u0631\\u0648\\u0639\\u060C \\u0645\\u0642\\u0635\\u062F \\u062E\\u0648\\u062F \\u0631\\u0627 \\u062C\\u0633\\u062A\\u062C\\u0648 \\u06A9\\u0646\\u06CC\\u062F\"})]}),searchQuery&&searchResults.length===0&&!isLoading&&/*#__PURE__*/_jsxs(EmptyState,{children:[/*#__PURE__*/_jsx(FiSearch,{size:48,style:{marginBottom:'16px',opacity:0.5}}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u0646\\u062A\\u06CC\\u062C\\u0647\\u200C\\u0627\\u06CC \\u06CC\\u0627\\u0641\\u062A \\u0646\\u0634\\u062F\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',marginTop:'8px'},children:\"\\u0644\\u0637\\u0641\\u0627\\u064B \\u06A9\\u0644\\u0645\\u0627\\u062A \\u06A9\\u0644\\u06CC\\u062F\\u06CC \\u062F\\u06CC\\u06AF\\u0631\\u06CC \\u0627\\u0645\\u062A\\u062D\\u0627\\u0646 \\u06A9\\u0646\\u06CC\\u062F\"})]})]})]});};export default SearchPanel;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "FiSearch", "FiMapPin", "<PERSON><PERSON><PERSON>", "FiStar", "FiNavigation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "PanelContainer", "div", "_templateObject", "_taggedTemplateLiteral", "Header", "_templateObject2", "Title", "h2", "_templateObject3", "SearchContainer", "_templateObject4", "SearchInput", "input", "_templateObject5", "SearchIcon", "_templateObject6", "QuickActions", "_templateObject7", "QuickActionButton", "button", "_templateObject8", "Content", "_templateObject9", "SectionTitle", "h3", "_templateObject0", "ResultsList", "_templateObject1", "ResultItem", "_templateObject10", "ResultName", "_templateObject11", "ResultAddress", "_templateObject12", "ResultMeta", "_templateObject13", "CategoryChips", "_templateObject14", "CategoryChip", "_templateObject15", "props", "active", "EmptyState", "_templateObject16", "SearchPanel", "_ref", "currentLocation", "onDestinationSelect", "searchQuery", "setSearch<PERSON>uery", "searchResults", "setSearchResults", "recentSearches", "setRecentSearches", "favorites", "setFavorites", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "isLoading", "setIsLoading", "categories", "performSearch", "query", "trim", "setTimeout", "mockResults", "id", "name", "address", "location", "lat", "lng", "type", "category", "distance", "undefined", "filter", "result", "includes", "timeoutId", "clearTimeout", "savedRecentSearches", "localStorage", "getItem", "savedFavorites", "JSON", "parse", "handleResultSelect", "locationData", "_objectSpread", "timestamp", "Date", "now", "updatedRecent", "r", "slice", "setItem", "stringify", "handleQuickAction", "action", "children", "placeholder", "value", "onChange", "e", "target", "onClick", "map", "length", "toFixed", "poi", "rating", "size", "style", "marginBottom", "opacity", "fontSize", "marginTop"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/src/components/SearchPanel.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { FiSearch, FiMapPin, FiClock, FiStar, FiNavigation } from 'react-icons/fi';\nimport { LocationData, SearchResult, POI } from '../types/gps.types';\n\nconst PanelContainer = styled.div`\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background-color: #2d2d2d;\n  color: white;\n`;\n\nconst Header = styled.div`\n  padding: 20px;\n  border-bottom: 1px solid #444;\n  background-color: #1a1a1a;\n`;\n\nconst Title = styled.h2`\n  margin: 0 0 16px 0;\n  font-size: 24px;\n  font-weight: 600;\n  color: #fff;\n  text-align: center;\n`;\n\nconst SearchContainer = styled.div`\n  position: relative;\n  margin-bottom: 16px;\n`;\n\nconst SearchInput = styled.input`\n  width: 100%;\n  padding: 16px 20px 16px 50px;\n  border: 2px solid #444;\n  border-radius: 12px;\n  background-color: #3d3d3d;\n  color: white;\n  font-size: 16px;\n  outline: none;\n  transition: all 0.3s ease;\n  \n  &:focus {\n    border-color: #007bff;\n    background-color: #4d4d4d;\n  }\n  \n  &::placeholder {\n    color: #aaa;\n  }\n`;\n\nconst SearchIcon = styled(FiSearch)`\n  position: absolute;\n  left: 16px;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #aaa;\n  font-size: 20px;\n`;\n\nconst QuickActions = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 12px;\n  margin-bottom: 20px;\n`;\n\nconst QuickActionButton = styled.button`\n  padding: 16px;\n  border: 1px solid #444;\n  border-radius: 12px;\n  background-color: #3d3d3d;\n  color: white;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  \n  &:hover {\n    background-color: #007bff;\n    border-color: #007bff;\n  }\n`;\n\nconst Content = styled.div`\n  flex: 1;\n  overflow-y: auto;\n  padding: 0 20px 20px;\n`;\n\nconst SectionTitle = styled.h3`\n  margin: 20px 0 12px 0;\n  font-size: 18px;\n  font-weight: 500;\n  color: #ccc;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\nconst ResultsList = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n\nconst ResultItem = styled.div`\n  padding: 16px;\n  border: 1px solid #444;\n  border-radius: 12px;\n  background-color: #3d3d3d;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background-color: #4d4d4d;\n    border-color: #007bff;\n  }\n`;\n\nconst ResultName = styled.div`\n  font-size: 16px;\n  font-weight: 500;\n  margin-bottom: 4px;\n  color: white;\n`;\n\nconst ResultAddress = styled.div`\n  font-size: 14px;\n  color: #aaa;\n  margin-bottom: 8px;\n`;\n\nconst ResultMeta = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  font-size: 12px;\n  color: #888;\n`;\n\nconst CategoryChips = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  margin-bottom: 16px;\n`;\n\nconst CategoryChip = styled.button<{ active?: boolean }>`\n  padding: 8px 16px;\n  border: 1px solid #444;\n  border-radius: 20px;\n  background-color: ${props => props.active ? '#007bff' : '#3d3d3d'};\n  color: white;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background-color: #007bff;\n    border-color: #007bff;\n  }\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: 40px 20px;\n  color: #888;\n`;\n\ninterface SearchPanelProps {\n  currentLocation: LocationData | null;\n  onDestinationSelect: (location: LocationData) => void;\n}\n\nconst SearchPanel: React.FC<SearchPanelProps> = ({\n  currentLocation,\n  onDestinationSelect\n}) => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);\n  const [recentSearches, setRecentSearches] = useState<SearchResult[]>([]);\n  const [favorites, setFavorites] = useState<POI[]>([]);\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [isLoading, setIsLoading] = useState(false);\n\n  const categories = [\n    'رستوران', 'پمپ بنزین', 'بیمارستان', 'بانک', 'مرکز خرید', \n    'پارکینگ', 'هتل', 'داروخانه', 'مدرسه', 'پارک'\n  ];\n\n  // Mock search function - in real app, this would call a geocoding API\n  const performSearch = async (query: string) => {\n    if (!query.trim()) {\n      setSearchResults([]);\n      return;\n    }\n\n    setIsLoading(true);\n    \n    // Simulate API call\n    setTimeout(() => {\n      const mockResults: SearchResult[] = [\n        {\n          id: '1',\n          name: 'میدان آزادی',\n          address: 'تهران، میدان آزادی',\n          location: { lat: 35.6958, lng: 51.3370 },\n          type: 'poi',\n          category: 'نقاط دیدنی',\n          distance: currentLocation ? 5000 : undefined\n        },\n        {\n          id: '2',\n          name: 'برج میلاد',\n          address: 'تهران، برج میلاد',\n          location: { lat: 35.7447, lng: 51.3753 },\n          type: 'poi',\n          category: 'نقاط دیدنی',\n          distance: currentLocation ? 8000 : undefined\n        },\n        {\n          id: '3',\n          name: 'بازار بزرگ تهران',\n          address: 'تهران، بازار بزرگ',\n          location: { lat: 35.6736, lng: 51.4208 },\n          type: 'poi',\n          category: 'مرکز خرید',\n          distance: currentLocation ? 3000 : undefined\n        }\n      ].filter(result => \n        result.name.includes(query) || result.address.includes(query)\n      );\n      \n      setSearchResults(mockResults);\n      setIsLoading(false);\n    }, 500);\n  };\n\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      performSearch(searchQuery);\n    }, 300);\n\n    return () => clearTimeout(timeoutId);\n  }, [searchQuery, currentLocation]);\n\n  // Load recent searches and favorites from localStorage\n  useEffect(() => {\n    const savedRecentSearches = localStorage.getItem('recentSearches');\n    const savedFavorites = localStorage.getItem('favorites');\n    \n    if (savedRecentSearches) {\n      setRecentSearches(JSON.parse(savedRecentSearches));\n    }\n    \n    if (savedFavorites) {\n      setFavorites(JSON.parse(savedFavorites));\n    }\n  }, []);\n\n  const handleResultSelect = (result: SearchResult) => {\n    const locationData: LocationData = {\n      ...result.location,\n      name: result.name,\n      address: result.address,\n      timestamp: Date.now()\n    };\n    \n    // Add to recent searches\n    const updatedRecent = [result, ...recentSearches.filter(r => r.id !== result.id)].slice(0, 10);\n    setRecentSearches(updatedRecent);\n    localStorage.setItem('recentSearches', JSON.stringify(updatedRecent));\n    \n    onDestinationSelect(locationData);\n  };\n\n  const handleQuickAction = (action: string) => {\n    switch (action) {\n      case 'home':\n        // In a real app, this would use saved home location\n        onDestinationSelect({\n          lat: 35.6892,\n          lng: 51.3890,\n          name: 'خانه',\n          timestamp: Date.now()\n        });\n        break;\n      case 'work':\n        // In a real app, this would use saved work location\n        onDestinationSelect({\n          lat: 35.7219,\n          lng: 51.3347,\n          name: 'محل کار',\n          timestamp: Date.now()\n        });\n        break;\n    }\n  };\n\n  return (\n    <PanelContainer>\n      <Header>\n        <Title>جستجوی مقصد</Title>\n        <SearchContainer>\n          <SearchIcon />\n          <SearchInput\n            type=\"text\"\n            placeholder=\"جستجوی آدرس، مکان یا نقطه علاقه...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n          />\n        </SearchContainer>\n        \n        <QuickActions>\n          <QuickActionButton onClick={() => handleQuickAction('home')}>\n            <FiMapPin />\n            خانه\n          </QuickActionButton>\n          <QuickActionButton onClick={() => handleQuickAction('work')}>\n            <FiNavigation />\n            محل کار\n          </QuickActionButton>\n        </QuickActions>\n        \n        <CategoryChips>\n          {categories.map(category => (\n            <CategoryChip\n              key={category}\n              active={selectedCategory === category}\n              onClick={() => setSelectedCategory(selectedCategory === category ? '' : category)}\n            >\n              {category}\n            </CategoryChip>\n          ))}\n        </CategoryChips>\n      </Header>\n\n      <Content>\n        {searchQuery && searchResults.length > 0 && (\n          <>\n            <SectionTitle>\n              <FiSearch />\n              نتایج جستجو\n            </SectionTitle>\n            <ResultsList>\n              {searchResults.map(result => (\n                <ResultItem key={result.id} onClick={() => handleResultSelect(result)}>\n                  <ResultName>{result.name}</ResultName>\n                  <ResultAddress>{result.address}</ResultAddress>\n                  <ResultMeta>\n                    <span>{result.category}</span>\n                    {result.distance && (\n                      <span>{(result.distance / 1000).toFixed(1)} کیلومتر</span>\n                    )}\n                  </ResultMeta>\n                </ResultItem>\n              ))}\n            </ResultsList>\n          </>\n        )}\n\n        {!searchQuery && recentSearches.length > 0 && (\n          <>\n            <SectionTitle>\n              <FiClock />\n              جستجوهای اخیر\n            </SectionTitle>\n            <ResultsList>\n              {recentSearches.slice(0, 5).map(result => (\n                <ResultItem key={result.id} onClick={() => handleResultSelect(result)}>\n                  <ResultName>{result.name}</ResultName>\n                  <ResultAddress>{result.address}</ResultAddress>\n                  <ResultMeta>\n                    <span>{result.category}</span>\n                  </ResultMeta>\n                </ResultItem>\n              ))}\n            </ResultsList>\n          </>\n        )}\n\n        {!searchQuery && favorites.length > 0 && (\n          <>\n            <SectionTitle>\n              <FiStar />\n              علاقه‌مندی‌ها\n            </SectionTitle>\n            <ResultsList>\n              {favorites.slice(0, 5).map(poi => (\n                <ResultItem \n                  key={poi.id} \n                  onClick={() => handleResultSelect({\n                    id: poi.id,\n                    name: poi.name,\n                    address: poi.location.address || '',\n                    location: poi.location,\n                    type: 'poi',\n                    category: poi.category\n                  })}\n                >\n                  <ResultName>{poi.name}</ResultName>\n                  <ResultAddress>{poi.location.address}</ResultAddress>\n                  <ResultMeta>\n                    <span>{poi.category}</span>\n                    {poi.rating && (\n                      <span>⭐ {poi.rating.toFixed(1)}</span>\n                    )}\n                  </ResultMeta>\n                </ResultItem>\n              ))}\n            </ResultsList>\n          </>\n        )}\n\n        {!searchQuery && recentSearches.length === 0 && favorites.length === 0 && (\n          <EmptyState>\n            <FiMapPin size={48} style={{ marginBottom: '16px', opacity: 0.5 }} />\n            <div>برای شروع، مقصد خود را جستجو کنید</div>\n          </EmptyState>\n        )}\n\n        {searchQuery && searchResults.length === 0 && !isLoading && (\n          <EmptyState>\n            <FiSearch size={48} style={{ marginBottom: '16px', opacity: 0.5 }} />\n            <div>نتیجه‌ای یافت نشد</div>\n            <div style={{ fontSize: '14px', marginTop: '8px' }}>\n              لطفاً کلمات کلیدی دیگری امتحان کنید\n            </div>\n          </EmptyState>\n        )}\n      </Content>\n    </PanelContainer>\n  );\n};\n\nexport default SearchPanel;\n"], "mappings": "+mBAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,QAAQ,CAAEC,QAAQ,CAAEC,OAAO,CAAEC,MAAM,CAAEC,YAAY,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAGnF,KAAM,CAAAC,cAAc,CAAGZ,MAAM,CAACa,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,uHAMhC,CAED,KAAM,CAAAC,MAAM,CAAGhB,MAAM,CAACa,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,4FAIxB,CAED,KAAM,CAAAG,KAAK,CAAGlB,MAAM,CAACmB,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAL,sBAAA,iHAMtB,CAED,KAAM,CAAAM,eAAe,CAAGrB,MAAM,CAACa,GAAG,CAAAS,gBAAA,GAAAA,gBAAA,CAAAP,sBAAA,yDAGjC,CAED,KAAM,CAAAQ,WAAW,CAAGvB,MAAM,CAACwB,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAAV,sBAAA,kWAmB/B,CAED,KAAM,CAAAW,UAAU,CAAG1B,MAAM,CAACC,QAAQ,CAAC,CAAA0B,gBAAA,GAAAA,gBAAA,CAAAZ,sBAAA,iIAOlC,CAED,KAAM,CAAAa,YAAY,CAAG5B,MAAM,CAACa,GAAG,CAAAgB,gBAAA,GAAAA,gBAAA,CAAAd,sBAAA,qGAK9B,CAED,KAAM,CAAAe,iBAAiB,CAAG9B,MAAM,CAAC+B,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAAjB,sBAAA,yWAkBtC,CAED,KAAM,CAAAkB,OAAO,CAAGjC,MAAM,CAACa,GAAG,CAAAqB,gBAAA,GAAAA,gBAAA,CAAAnB,sBAAA,oEAIzB,CAED,KAAM,CAAAoB,YAAY,CAAGnC,MAAM,CAACoC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAtB,sBAAA,oJAQ7B,CAED,KAAM,CAAAuB,WAAW,CAAGtC,MAAM,CAACa,GAAG,CAAA0B,gBAAA,GAAAA,gBAAA,CAAAxB,sBAAA,oEAI7B,CAED,KAAM,CAAAyB,UAAU,CAAGxC,MAAM,CAACa,GAAG,CAAA4B,iBAAA,GAAAA,iBAAA,CAAA1B,sBAAA,iPAY5B,CAED,KAAM,CAAA2B,UAAU,CAAG1C,MAAM,CAACa,GAAG,CAAA8B,iBAAA,GAAAA,iBAAA,CAAA5B,sBAAA,2FAK5B,CAED,KAAM,CAAA6B,aAAa,CAAG5C,MAAM,CAACa,GAAG,CAAAgC,iBAAA,GAAAA,iBAAA,CAAA9B,sBAAA,qEAI/B,CAED,KAAM,CAAA+B,UAAU,CAAG9C,MAAM,CAACa,GAAG,CAAAkC,iBAAA,GAAAA,iBAAA,CAAAhC,sBAAA,sGAM5B,CAED,KAAM,CAAAiC,aAAa,CAAGhD,MAAM,CAACa,GAAG,CAAAoC,iBAAA,GAAAA,iBAAA,CAAAlC,sBAAA,qFAK/B,CAED,KAAM,CAAAmC,YAAY,CAAGlD,MAAM,CAAC+B,MAAM,CAAAoB,iBAAA,GAAAA,iBAAA,CAAApC,sBAAA,sRAIZqC,KAAK,EAAIA,KAAK,CAACC,MAAM,CAAG,SAAS,CAAG,SAAS,CAUlE,CAED,KAAM,CAAAC,UAAU,CAAGtD,MAAM,CAACa,GAAG,CAAA0C,iBAAA,GAAAA,iBAAA,CAAAxC,sBAAA,wEAI5B,CAOD,KAAM,CAAAyC,WAAuC,CAAGC,IAAA,EAG1C,IAH2C,CAC/CC,eAAe,CACfC,mBACF,CAAC,CAAAF,IAAA,CACC,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAG/D,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACgE,aAAa,CAAEC,gBAAgB,CAAC,CAAGjE,QAAQ,CAAiB,EAAE,CAAC,CACtE,KAAM,CAACkE,cAAc,CAAEC,iBAAiB,CAAC,CAAGnE,QAAQ,CAAiB,EAAE,CAAC,CACxE,KAAM,CAACoE,SAAS,CAAEC,YAAY,CAAC,CAAGrE,QAAQ,CAAQ,EAAE,CAAC,CACrD,KAAM,CAACsE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvE,QAAQ,CAAS,EAAE,CAAC,CACpE,KAAM,CAACwE,SAAS,CAAEC,YAAY,CAAC,CAAGzE,QAAQ,CAAC,KAAK,CAAC,CAEjD,KAAM,CAAA0E,UAAU,CAAG,CACjB,SAAS,CAAE,WAAW,CAAE,WAAW,CAAE,MAAM,CAAE,WAAW,CACxD,SAAS,CAAE,KAAK,CAAE,UAAU,CAAE,OAAO,CAAE,MAAM,CAC9C,CAED;AACA,KAAM,CAAAC,aAAa,CAAG,KAAO,CAAAC,KAAa,EAAK,CAC7C,GAAI,CAACA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAE,CACjBZ,gBAAgB,CAAC,EAAE,CAAC,CACpB,OACF,CAEAQ,YAAY,CAAC,IAAI,CAAC,CAElB;AACAK,UAAU,CAAC,IAAM,CACf,KAAM,CAAAC,WAA2B,CAAG,CAClC,CACEC,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,aAAa,CACnBC,OAAO,CAAE,oBAAoB,CAC7BC,QAAQ,CAAE,CAAEC,GAAG,CAAE,OAAO,CAAEC,GAAG,CAAE,OAAQ,CAAC,CACxCC,IAAI,CAAE,KAAK,CACXC,QAAQ,CAAE,YAAY,CACtBC,QAAQ,CAAE5B,eAAe,CAAG,IAAI,CAAG6B,SACrC,CAAC,CACD,CACET,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,WAAW,CACjBC,OAAO,CAAE,kBAAkB,CAC3BC,QAAQ,CAAE,CAAEC,GAAG,CAAE,OAAO,CAAEC,GAAG,CAAE,OAAQ,CAAC,CACxCC,IAAI,CAAE,KAAK,CACXC,QAAQ,CAAE,YAAY,CACtBC,QAAQ,CAAE5B,eAAe,CAAG,IAAI,CAAG6B,SACrC,CAAC,CACD,CACET,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,kBAAkB,CACxBC,OAAO,CAAE,mBAAmB,CAC5BC,QAAQ,CAAE,CAAEC,GAAG,CAAE,OAAO,CAAEC,GAAG,CAAE,OAAQ,CAAC,CACxCC,IAAI,CAAE,KAAK,CACXC,QAAQ,CAAE,WAAW,CACrBC,QAAQ,CAAE5B,eAAe,CAAG,IAAI,CAAG6B,SACrC,CAAC,CACF,CAACC,MAAM,CAACC,MAAM,EACbA,MAAM,CAACV,IAAI,CAACW,QAAQ,CAAChB,KAAK,CAAC,EAAIe,MAAM,CAACT,OAAO,CAACU,QAAQ,CAAChB,KAAK,CAC9D,CAAC,CAEDX,gBAAgB,CAACc,WAAW,CAAC,CAC7BN,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,CAEDxE,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4F,SAAS,CAAGf,UAAU,CAAC,IAAM,CACjCH,aAAa,CAACb,WAAW,CAAC,CAC5B,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAMgC,YAAY,CAACD,SAAS,CAAC,CACtC,CAAC,CAAE,CAAC/B,WAAW,CAAEF,eAAe,CAAC,CAAC,CAElC;AACA3D,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8F,mBAAmB,CAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAClE,KAAM,CAAAC,cAAc,CAAGF,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAExD,GAAIF,mBAAmB,CAAE,CACvB5B,iBAAiB,CAACgC,IAAI,CAACC,KAAK,CAACL,mBAAmB,CAAC,CAAC,CACpD,CAEA,GAAIG,cAAc,CAAE,CAClB7B,YAAY,CAAC8B,IAAI,CAACC,KAAK,CAACF,cAAc,CAAC,CAAC,CAC1C,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAG,kBAAkB,CAAIV,MAAoB,EAAK,CACnD,KAAM,CAAAW,YAA0B,CAAAC,aAAA,CAAAA,aAAA,IAC3BZ,MAAM,CAACR,QAAQ,MAClBF,IAAI,CAAEU,MAAM,CAACV,IAAI,CACjBC,OAAO,CAAES,MAAM,CAACT,OAAO,CACvBsB,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EACtB,CAED;AACA,KAAM,CAAAC,aAAa,CAAG,CAAChB,MAAM,CAAE,GAAGzB,cAAc,CAACwB,MAAM,CAACkB,CAAC,EAAIA,CAAC,CAAC5B,EAAE,GAAKW,MAAM,CAACX,EAAE,CAAC,CAAC,CAAC6B,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAC9F1C,iBAAiB,CAACwC,aAAa,CAAC,CAChCX,YAAY,CAACc,OAAO,CAAC,gBAAgB,CAAEX,IAAI,CAACY,SAAS,CAACJ,aAAa,CAAC,CAAC,CAErE9C,mBAAmB,CAACyC,YAAY,CAAC,CACnC,CAAC,CAED,KAAM,CAAAU,iBAAiB,CAAIC,MAAc,EAAK,CAC5C,OAAQA,MAAM,EACZ,IAAK,MAAM,CACT;AACApD,mBAAmB,CAAC,CAClBuB,GAAG,CAAE,OAAO,CACZC,GAAG,CAAE,OAAO,CACZJ,IAAI,CAAE,MAAM,CACZuB,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CACtB,CAAC,CAAC,CACF,MACF,IAAK,MAAM,CACT;AACA7C,mBAAmB,CAAC,CAClBuB,GAAG,CAAE,OAAO,CACZC,GAAG,CAAE,OAAO,CACZJ,IAAI,CAAE,SAAS,CACfuB,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CACtB,CAAC,CAAC,CACF,MACJ,CACF,CAAC,CAED,mBACE/F,KAAA,CAACG,cAAc,EAAAoG,QAAA,eACbvG,KAAA,CAACO,MAAM,EAAAgG,QAAA,eACLzG,IAAA,CAACW,KAAK,EAAA8F,QAAA,CAAC,+DAAW,CAAO,CAAC,cAC1BvG,KAAA,CAACY,eAAe,EAAA2F,QAAA,eACdzG,IAAA,CAACmB,UAAU,GAAE,CAAC,cACdnB,IAAA,CAACgB,WAAW,EACV6D,IAAI,CAAC,MAAM,CACX6B,WAAW,CAAC,sKAAoC,CAChDC,KAAK,CAAEtD,WAAY,CACnBuD,QAAQ,CAAGC,CAAC,EAAKvD,cAAc,CAACuD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjD,CAAC,EACa,CAAC,cAElBzG,KAAA,CAACmB,YAAY,EAAAoF,QAAA,eACXvG,KAAA,CAACqB,iBAAiB,EAACwF,OAAO,CAAEA,CAAA,GAAMR,iBAAiB,CAAC,MAAM,CAAE,CAAAE,QAAA,eAC1DzG,IAAA,CAACL,QAAQ,GAAE,CAAC,2BAEd,EAAmB,CAAC,cACpBO,KAAA,CAACqB,iBAAiB,EAACwF,OAAO,CAAEA,CAAA,GAAMR,iBAAiB,CAAC,MAAM,CAAE,CAAAE,QAAA,eAC1DzG,IAAA,CAACF,YAAY,GAAE,CAAC,wCAElB,EAAmB,CAAC,EACR,CAAC,cAEfE,IAAA,CAACyC,aAAa,EAAAgE,QAAA,CACXxC,UAAU,CAAC+C,GAAG,CAAClC,QAAQ,eACtB9E,IAAA,CAAC2C,YAAY,EAEXG,MAAM,CAAEe,gBAAgB,GAAKiB,QAAS,CACtCiC,OAAO,CAAEA,CAAA,GAAMjD,mBAAmB,CAACD,gBAAgB,GAAKiB,QAAQ,CAAG,EAAE,CAAGA,QAAQ,CAAE,CAAA2B,QAAA,CAEjF3B,QAAQ,EAJJA,QAKO,CACf,CAAC,CACW,CAAC,EACV,CAAC,cAET5E,KAAA,CAACwB,OAAO,EAAA+E,QAAA,EACLpD,WAAW,EAAIE,aAAa,CAAC0D,MAAM,CAAG,CAAC,eACtC/G,KAAA,CAAAE,SAAA,EAAAqG,QAAA,eACEvG,KAAA,CAAC0B,YAAY,EAAA6E,QAAA,eACXzG,IAAA,CAACN,QAAQ,GAAE,CAAC,gEAEd,EAAc,CAAC,cACfM,IAAA,CAAC+B,WAAW,EAAA0E,QAAA,CACTlD,aAAa,CAACyD,GAAG,CAAC9B,MAAM,eACvBhF,KAAA,CAAC+B,UAAU,EAAiB8E,OAAO,CAAEA,CAAA,GAAMnB,kBAAkB,CAACV,MAAM,CAAE,CAAAuB,QAAA,eACpEzG,IAAA,CAACmC,UAAU,EAAAsE,QAAA,CAAEvB,MAAM,CAACV,IAAI,CAAa,CAAC,cACtCxE,IAAA,CAACqC,aAAa,EAAAoE,QAAA,CAAEvB,MAAM,CAACT,OAAO,CAAgB,CAAC,cAC/CvE,KAAA,CAACqC,UAAU,EAAAkE,QAAA,eACTzG,IAAA,SAAAyG,QAAA,CAAOvB,MAAM,CAACJ,QAAQ,CAAO,CAAC,CAC7BI,MAAM,CAACH,QAAQ,eACd7E,KAAA,SAAAuG,QAAA,EAAO,CAACvB,MAAM,CAACH,QAAQ,CAAG,IAAI,EAAEmC,OAAO,CAAC,CAAC,CAAC,CAAC,6CAAQ,EAAM,CAC1D,EACS,CAAC,GAREhC,MAAM,CAACX,EASZ,CACb,CAAC,CACS,CAAC,EACd,CACH,CAEA,CAAClB,WAAW,EAAII,cAAc,CAACwD,MAAM,CAAG,CAAC,eACxC/G,KAAA,CAAAE,SAAA,EAAAqG,QAAA,eACEvG,KAAA,CAAC0B,YAAY,EAAA6E,QAAA,eACXzG,IAAA,CAACJ,OAAO,GAAE,CAAC,4EAEb,EAAc,CAAC,cACfI,IAAA,CAAC+B,WAAW,EAAA0E,QAAA,CACThD,cAAc,CAAC2C,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACY,GAAG,CAAC9B,MAAM,eACpChF,KAAA,CAAC+B,UAAU,EAAiB8E,OAAO,CAAEA,CAAA,GAAMnB,kBAAkB,CAACV,MAAM,CAAE,CAAAuB,QAAA,eACpEzG,IAAA,CAACmC,UAAU,EAAAsE,QAAA,CAAEvB,MAAM,CAACV,IAAI,CAAa,CAAC,cACtCxE,IAAA,CAACqC,aAAa,EAAAoE,QAAA,CAAEvB,MAAM,CAACT,OAAO,CAAgB,CAAC,cAC/CzE,IAAA,CAACuC,UAAU,EAAAkE,QAAA,cACTzG,IAAA,SAAAyG,QAAA,CAAOvB,MAAM,CAACJ,QAAQ,CAAO,CAAC,CACpB,CAAC,GALEI,MAAM,CAACX,EAMZ,CACb,CAAC,CACS,CAAC,EACd,CACH,CAEA,CAAClB,WAAW,EAAIM,SAAS,CAACsD,MAAM,CAAG,CAAC,eACnC/G,KAAA,CAAAE,SAAA,EAAAqG,QAAA,eACEvG,KAAA,CAAC0B,YAAY,EAAA6E,QAAA,eACXzG,IAAA,CAACH,MAAM,GAAE,CAAC,iFAEZ,EAAc,CAAC,cACfG,IAAA,CAAC+B,WAAW,EAAA0E,QAAA,CACT9C,SAAS,CAACyC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACY,GAAG,CAACG,GAAG,eAC5BjH,KAAA,CAAC+B,UAAU,EAET8E,OAAO,CAAEA,CAAA,GAAMnB,kBAAkB,CAAC,CAChCrB,EAAE,CAAE4C,GAAG,CAAC5C,EAAE,CACVC,IAAI,CAAE2C,GAAG,CAAC3C,IAAI,CACdC,OAAO,CAAE0C,GAAG,CAACzC,QAAQ,CAACD,OAAO,EAAI,EAAE,CACnCC,QAAQ,CAAEyC,GAAG,CAACzC,QAAQ,CACtBG,IAAI,CAAE,KAAK,CACXC,QAAQ,CAAEqC,GAAG,CAACrC,QAChB,CAAC,CAAE,CAAA2B,QAAA,eAEHzG,IAAA,CAACmC,UAAU,EAAAsE,QAAA,CAAEU,GAAG,CAAC3C,IAAI,CAAa,CAAC,cACnCxE,IAAA,CAACqC,aAAa,EAAAoE,QAAA,CAAEU,GAAG,CAACzC,QAAQ,CAACD,OAAO,CAAgB,CAAC,cACrDvE,KAAA,CAACqC,UAAU,EAAAkE,QAAA,eACTzG,IAAA,SAAAyG,QAAA,CAAOU,GAAG,CAACrC,QAAQ,CAAO,CAAC,CAC1BqC,GAAG,CAACC,MAAM,eACTlH,KAAA,SAAAuG,QAAA,EAAM,SAAE,CAACU,GAAG,CAACC,MAAM,CAACF,OAAO,CAAC,CAAC,CAAC,EAAO,CACtC,EACS,CAAC,GAjBRC,GAAG,CAAC5C,EAkBC,CACb,CAAC,CACS,CAAC,EACd,CACH,CAEA,CAAClB,WAAW,EAAII,cAAc,CAACwD,MAAM,GAAK,CAAC,EAAItD,SAAS,CAACsD,MAAM,GAAK,CAAC,eACpE/G,KAAA,CAAC6C,UAAU,EAAA0D,QAAA,eACTzG,IAAA,CAACL,QAAQ,EAAC0H,IAAI,CAAE,EAAG,CAACC,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEC,OAAO,CAAE,GAAI,CAAE,CAAE,CAAC,cACrExH,IAAA,QAAAyG,QAAA,CAAK,0KAAiC,CAAK,CAAC,EAClC,CACb,CAEApD,WAAW,EAAIE,aAAa,CAAC0D,MAAM,GAAK,CAAC,EAAI,CAAClD,SAAS,eACtD7D,KAAA,CAAC6C,UAAU,EAAA0D,QAAA,eACTzG,IAAA,CAACN,QAAQ,EAAC2H,IAAI,CAAE,EAAG,CAACC,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEC,OAAO,CAAE,GAAI,CAAE,CAAE,CAAC,cACrExH,IAAA,QAAAyG,QAAA,CAAK,8FAAiB,CAAK,CAAC,cAC5BzG,IAAA,QAAKsH,KAAK,CAAE,CAAEG,QAAQ,CAAE,MAAM,CAAEC,SAAS,CAAE,KAAM,CAAE,CAAAjB,QAAA,CAAC,2LAEpD,CAAK,CAAC,EACI,CACb,EACM,CAAC,EACI,CAAC,CAErB,CAAC,CAED,cAAe,CAAAxD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}