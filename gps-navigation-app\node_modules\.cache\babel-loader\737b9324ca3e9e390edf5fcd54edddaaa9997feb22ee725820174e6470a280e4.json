{"ast": null, "code": "import { LeafletContext, createLeafletContext } from '@react-leaflet/core';\nimport { Map as LeafletMap } from 'leaflet';\nimport React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';\nfunction MapContainerComponent({\n  bounds,\n  boundsOptions,\n  center,\n  children,\n  className,\n  id,\n  placeholder,\n  style,\n  whenReady,\n  zoom,\n  ...options\n}, forwardedRef) {\n  const [props] = useState({\n    className,\n    id,\n    style\n  });\n  const [context, setContext] = useState(null);\n  const mapInstanceRef = useRef(undefined);\n  useImperativeHandle(forwardedRef, () => context?.map ?? null, [context]);\n  // biome-ignore lint/correctness/useExhaustiveDependencies: ref callback\n  const mapRef = useCallback(node => {\n    if (node !== null && !mapInstanceRef.current) {\n      const map = new LeafletMap(node, options);\n      mapInstanceRef.current = map;\n      if (center != null && zoom != null) {\n        map.setView(center, zoom);\n      } else if (bounds != null) {\n        map.fitBounds(bounds, boundsOptions);\n      }\n      if (whenReady != null) {\n        map.whenReady(whenReady);\n      }\n      setContext(createLeafletContext(map));\n    }\n  }, []);\n  useEffect(() => {\n    return () => {\n      context?.map.remove();\n    };\n  }, [context]);\n  const contents = context ? /*#__PURE__*/React.createElement(LeafletContext, {\n    value: context\n  }, children) : placeholder ?? null;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ...props,\n    ref: mapRef\n  }, contents);\n}\nexport const MapContainer = /*#__PURE__*/forwardRef(MapContainerComponent);", "map": {"version": 3, "names": ["LeafletContext", "createLeafletContext", "Map", "LeafletMap", "React", "forwardRef", "useCallback", "useEffect", "useImperativeHandle", "useRef", "useState", "MapContainerComponent", "bounds", "boundsOptions", "center", "children", "className", "id", "placeholder", "style", "when<PERSON><PERSON><PERSON>", "zoom", "options", "forwardedRef", "props", "context", "setContext", "mapInstanceRef", "undefined", "map", "mapRef", "node", "current", "<PERSON><PERSON><PERSON><PERSON>", "fitBounds", "remove", "contents", "createElement", "value", "ref", "MapContainer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/MapContainer.js"], "sourcesContent": ["import { LeafletContext, createLeafletContext } from '@react-leaflet/core';\nimport { Map as LeafletMap } from 'leaflet';\nimport React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';\nfunction MapContainerComponent({ bounds, boundsOptions, center, children, className, id, placeholder, style, whenReady, zoom, ...options }, forwardedRef) {\n    const [props] = useState({\n        className,\n        id,\n        style\n    });\n    const [context, setContext] = useState(null);\n    const mapInstanceRef = useRef(undefined);\n    useImperativeHandle(forwardedRef, ()=>context?.map ?? null, [\n        context\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: ref callback\n    const mapRef = useCallback((node)=>{\n        if (node !== null && !mapInstanceRef.current) {\n            const map = new LeafletMap(node, options);\n            mapInstanceRef.current = map;\n            if (center != null && zoom != null) {\n                map.setView(center, zoom);\n            } else if (bounds != null) {\n                map.fitBounds(bounds, boundsOptions);\n            }\n            if (whenReady != null) {\n                map.whenReady(whenReady);\n            }\n            setContext(createLeafletContext(map));\n        }\n    }, []);\n    useEffect(()=>{\n        return ()=>{\n            context?.map.remove();\n        };\n    }, [\n        context\n    ]);\n    const contents = context ? /*#__PURE__*/ React.createElement(LeafletContext, {\n        value: context\n    }, children) : placeholder ?? null;\n    return /*#__PURE__*/ React.createElement(\"div\", {\n        ...props,\n        ref: mapRef\n    }, contents);\n}\nexport const MapContainer = /*#__PURE__*/ forwardRef(MapContainerComponent);\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,oBAAoB,QAAQ,qBAAqB;AAC1E,SAASC,GAAG,IAAIC,UAAU,QAAQ,SAAS;AAC3C,OAAOC,KAAK,IAAIC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxG,SAASC,qBAAqBA,CAAC;EAAEC,MAAM;EAAEC,aAAa;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,SAAS;EAAEC,EAAE;EAAEC,WAAW;EAAEC,KAAK;EAAEC,SAAS;EAAEC,IAAI;EAAE,GAAGC;AAAQ,CAAC,EAAEC,YAAY,EAAE;EACtJ,MAAM,CAACC,KAAK,CAAC,GAAGd,QAAQ,CAAC;IACrBM,SAAS;IACTC,EAAE;IACFE;EACJ,CAAC,CAAC;EACF,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMiB,cAAc,GAAGlB,MAAM,CAACmB,SAAS,CAAC;EACxCpB,mBAAmB,CAACe,YAAY,EAAE,MAAIE,OAAO,EAAEI,GAAG,IAAI,IAAI,EAAE,CACxDJ,OAAO,CACV,CAAC;EACF;EACA,MAAMK,MAAM,GAAGxB,WAAW,CAAEyB,IAAI,IAAG;IAC/B,IAAIA,IAAI,KAAK,IAAI,IAAI,CAACJ,cAAc,CAACK,OAAO,EAAE;MAC1C,MAAMH,GAAG,GAAG,IAAI1B,UAAU,CAAC4B,IAAI,EAAET,OAAO,CAAC;MACzCK,cAAc,CAACK,OAAO,GAAGH,GAAG;MAC5B,IAAIf,MAAM,IAAI,IAAI,IAAIO,IAAI,IAAI,IAAI,EAAE;QAChCQ,GAAG,CAACI,OAAO,CAACnB,MAAM,EAAEO,IAAI,CAAC;MAC7B,CAAC,MAAM,IAAIT,MAAM,IAAI,IAAI,EAAE;QACvBiB,GAAG,CAACK,SAAS,CAACtB,MAAM,EAAEC,aAAa,CAAC;MACxC;MACA,IAAIO,SAAS,IAAI,IAAI,EAAE;QACnBS,GAAG,CAACT,SAAS,CAACA,SAAS,CAAC;MAC5B;MACAM,UAAU,CAACzB,oBAAoB,CAAC4B,GAAG,CAAC,CAAC;IACzC;EACJ,CAAC,EAAE,EAAE,CAAC;EACNtB,SAAS,CAAC,MAAI;IACV,OAAO,MAAI;MACPkB,OAAO,EAAEI,GAAG,CAACM,MAAM,CAAC,CAAC;IACzB,CAAC;EACL,CAAC,EAAE,CACCV,OAAO,CACV,CAAC;EACF,MAAMW,QAAQ,GAAGX,OAAO,GAAG,aAAcrB,KAAK,CAACiC,aAAa,CAACrC,cAAc,EAAE;IACzEsC,KAAK,EAAEb;EACX,CAAC,EAAEV,QAAQ,CAAC,GAAGG,WAAW,IAAI,IAAI;EAClC,OAAO,aAAcd,KAAK,CAACiC,aAAa,CAAC,KAAK,EAAE;IAC5C,GAAGb,KAAK;IACRe,GAAG,EAAET;EACT,CAAC,EAAEM,QAAQ,CAAC;AAChB;AACA,OAAO,MAAMI,YAAY,GAAG,aAAcnC,UAAU,CAACM,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}