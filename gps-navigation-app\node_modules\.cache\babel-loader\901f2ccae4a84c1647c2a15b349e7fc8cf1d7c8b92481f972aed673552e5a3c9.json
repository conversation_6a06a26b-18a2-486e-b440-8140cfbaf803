{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"position\"];\nimport { createElementObject, createLayerComponent, extendContext } from '@react-leaflet/core';\nimport { Marker as LeafletMarker } from 'leaflet';\nexport const Marker = createLayerComponent(function createMarker(_ref, ctx) {\n  let {\n      position\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const marker = new LeafletMarker(position, options);\n  return createElementObject(marker, extendContext(ctx, {\n    overlayContainer: marker\n  }));\n}, function updateMarker(marker, props, prevProps) {\n  if (props.position !== prevProps.position) {\n    marker.setLatLng(props.position);\n  }\n  if (props.icon != null && props.icon !== prevProps.icon) {\n    marker.setIcon(props.icon);\n  }\n  if (props.zIndexOffset != null && props.zIndexOffset !== prevProps.zIndexOffset) {\n    marker.setZIndexOffset(props.zIndexOffset);\n  }\n  if (props.opacity != null && props.opacity !== prevProps.opacity) {\n    marker.setOpacity(props.opacity);\n  }\n  if (marker.dragging != null && props.draggable !== prevProps.draggable) {\n    if (props.draggable === true) {\n      marker.dragging.enable();\n    } else {\n      marker.dragging.disable();\n    }\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createLayerComponent", "extendContext", "<PERSON><PERSON>", "LeafletMarker", "createMarker", "_ref", "ctx", "position", "options", "_objectWithoutProperties", "_excluded", "marker", "overlayContainer", "updateMarker", "props", "prevProps", "setLatLng", "icon", "setIcon", "zIndexOffset", "setZIndexOffset", "opacity", "setOpacity", "dragging", "draggable", "enable", "disable"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/Marker.js"], "sourcesContent": ["import { createElementObject, createLayerComponent, extendContext } from '@react-leaflet/core';\nimport { Marker as LeafletMarker } from 'leaflet';\nexport const Marker = createLayerComponent(function createMarker({ position, ...options }, ctx) {\n    const marker = new LeafletMarker(position, options);\n    return createElementObject(marker, extendContext(ctx, {\n        overlayContainer: marker\n    }));\n}, function updateMarker(marker, props, prevProps) {\n    if (props.position !== prevProps.position) {\n        marker.setLatLng(props.position);\n    }\n    if (props.icon != null && props.icon !== prevProps.icon) {\n        marker.setIcon(props.icon);\n    }\n    if (props.zIndexOffset != null && props.zIndexOffset !== prevProps.zIndexOffset) {\n        marker.setZIndexOffset(props.zIndexOffset);\n    }\n    if (props.opacity != null && props.opacity !== prevProps.opacity) {\n        marker.setOpacity(props.opacity);\n    }\n    if (marker.dragging != null && props.draggable !== prevProps.draggable) {\n        if (props.draggable === true) {\n            marker.dragging.enable();\n        } else {\n            marker.dragging.disable();\n        }\n    }\n});\n"], "mappings": ";;AAAA,SAASA,mBAAmB,EAAEC,oBAAoB,EAAEC,aAAa,QAAQ,qBAAqB;AAC9F,SAASC,MAAM,IAAIC,aAAa,QAAQ,SAAS;AACjD,OAAO,MAAMD,MAAM,GAAGF,oBAAoB,CAAC,SAASI,YAAYA,CAAAC,IAAA,EAA2BC,GAAG,EAAE;EAAA,IAA/B;MAAEC;IAAqB,CAAC,GAAAF,IAAA;IAATG,OAAO,GAAAC,wBAAA,CAAAJ,IAAA,EAAAK,SAAA;EACnF,MAAMC,MAAM,GAAG,IAAIR,aAAa,CAACI,QAAQ,EAAEC,OAAO,CAAC;EACnD,OAAOT,mBAAmB,CAACY,MAAM,EAAEV,aAAa,CAACK,GAAG,EAAE;IAClDM,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASE,YAAYA,CAACF,MAAM,EAAEG,KAAK,EAAEC,SAAS,EAAE;EAC/C,IAAID,KAAK,CAACP,QAAQ,KAAKQ,SAAS,CAACR,QAAQ,EAAE;IACvCI,MAAM,CAACK,SAAS,CAACF,KAAK,CAACP,QAAQ,CAAC;EACpC;EACA,IAAIO,KAAK,CAACG,IAAI,IAAI,IAAI,IAAIH,KAAK,CAACG,IAAI,KAAKF,SAAS,CAACE,IAAI,EAAE;IACrDN,MAAM,CAACO,OAAO,CAACJ,KAAK,CAACG,IAAI,CAAC;EAC9B;EACA,IAAIH,KAAK,CAACK,YAAY,IAAI,IAAI,IAAIL,KAAK,CAACK,YAAY,KAAKJ,SAAS,CAACI,YAAY,EAAE;IAC7ER,MAAM,CAACS,eAAe,CAACN,KAAK,CAACK,YAAY,CAAC;EAC9C;EACA,IAAIL,KAAK,CAACO,OAAO,IAAI,IAAI,IAAIP,KAAK,CAACO,OAAO,KAAKN,SAAS,CAACM,OAAO,EAAE;IAC9DV,MAAM,CAACW,UAAU,CAACR,KAAK,CAACO,OAAO,CAAC;EACpC;EACA,IAAIV,MAAM,CAACY,QAAQ,IAAI,IAAI,IAAIT,KAAK,CAACU,SAAS,KAAKT,SAAS,CAACS,SAAS,EAAE;IACpE,IAAIV,KAAK,CAACU,SAAS,KAAK,IAAI,EAAE;MAC1Bb,MAAM,CAACY,QAAQ,CAACE,MAAM,CAAC,CAAC;IAC5B,CAAC,MAAM;MACHd,MAAM,CAACY,QAAQ,CAACG,OAAO,CAAC,CAAC;IAC7B;EACJ;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}