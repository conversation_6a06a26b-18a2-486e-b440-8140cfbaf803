{"ast": null, "code": "import { LeafletContext, addClassName, useLeafletContext } from '@react-leaflet/core';\nimport React, { forwardRef, useState, useEffect, useImperativeHandle, useMemo } from 'react';\nimport { createPortal } from 'react-dom';\nconst DEFAULT_PANES = ['mapPane', 'markerPane', 'overlayPane', 'popupPane', 'shadowPane', 'tilePane', 'tooltipPane'];\nfunction omitPane(obj, pane) {\n  const {\n    [pane]: _p,\n    ...others\n  } = obj;\n  return others;\n}\nfunction createPane(name, props, context) {\n  if (DEFAULT_PANES.indexOf(name) !== -1) {\n    throw new Error(`You must use a unique name for a pane that is not a default Leaflet pane: ${name}`);\n  }\n  if (context.map.getPane(name) != null) {\n    throw new Error(`A pane with this name already exists: ${name}`);\n  }\n  const parentPaneName = props.pane ?? context.pane;\n  const parentPane = parentPaneName ? context.map.getPane(parentPaneName) : undefined;\n  const element = context.map.createPane(name, parentPane);\n  if (props.className != null) {\n    addClassName(element, props.className);\n  }\n  if (props.style != null) {\n    for (const key of Object.keys(props.style)) {\n      // @ts-ignore\n      element.style[key] = props.style[key];\n    }\n  }\n  return element;\n}\nfunction PaneComponent(props, forwardedRef) {\n  const [paneName] = useState(props.name);\n  const [paneElement, setPaneElement] = useState(null);\n  useImperativeHandle(forwardedRef, () => paneElement, [paneElement]);\n  const context = useLeafletContext();\n  // biome-ignore lint/correctness/useExhaustiveDependencies: paneName is immutable\n  const newContext = useMemo(() => ({\n    ...context,\n    pane: paneName\n  }), [context]);\n  // biome-ignore lint/correctness/useExhaustiveDependencies: lifecycle-only effect\n  useEffect(() => {\n    setPaneElement(createPane(paneName, props, context));\n    return function removeCreatedPane() {\n      const pane = context.map.getPane(paneName);\n      pane?.remove?.();\n      // @ts-ignore map internals\n      if (context.map._panes != null) {\n        // @ts-ignore map internals\n        context.map._panes = omitPane(context.map._panes, paneName);\n        // @ts-ignore map internals\n        context.map._paneRenderers = omitPane(\n        // @ts-ignore map internals\n        context.map._paneRenderers, paneName);\n      }\n    };\n  }, []);\n  return props.children != null && paneElement != null ? /*#__PURE__*/createPortal(/*#__PURE__*/React.createElement(LeafletContext, {\n    value: newContext\n  }, props.children), paneElement) : null;\n}\nexport const Pane = /*#__PURE__*/forwardRef(PaneComponent);", "map": {"version": 3, "names": ["LeafletContext", "addClassName", "useLeafletContext", "React", "forwardRef", "useState", "useEffect", "useImperativeHandle", "useMemo", "createPortal", "DEFAULT_PANES", "omitPane", "obj", "pane", "_p", "others", "createPane", "name", "props", "context", "indexOf", "Error", "map", "getPane", "parentPaneName", "parentPane", "undefined", "element", "className", "style", "key", "Object", "keys", "PaneComponent", "forwardedRef", "paneName", "paneElement", "setPaneElement", "newContext", "removeCreatedPane", "remove", "_panes", "_paneRenderers", "children", "createElement", "value", "Pane"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/Pane.js"], "sourcesContent": ["import { LeafletContext, addClassName, useLeafletContext } from '@react-leaflet/core';\nimport React, { forwardRef, useState, useEffect, useImperativeHandle, useMemo } from 'react';\nimport { createPortal } from 'react-dom';\nconst DEFAULT_PANES = [\n    'mapPane',\n    'markerPane',\n    'overlayPane',\n    'popupPane',\n    'shadowPane',\n    'tilePane',\n    'tooltipPane'\n];\nfunction omitPane(obj, pane) {\n    const { [pane]: _p, ...others } = obj;\n    return others;\n}\nfunction createPane(name, props, context) {\n    if (DEFAULT_PANES.indexOf(name) !== -1) {\n        throw new Error(`You must use a unique name for a pane that is not a default Leaflet pane: ${name}`);\n    }\n    if (context.map.getPane(name) != null) {\n        throw new Error(`A pane with this name already exists: ${name}`);\n    }\n    const parentPaneName = props.pane ?? context.pane;\n    const parentPane = parentPaneName ? context.map.getPane(parentPaneName) : undefined;\n    const element = context.map.createPane(name, parentPane);\n    if (props.className != null) {\n        addClassName(element, props.className);\n    }\n    if (props.style != null) {\n        for (const key of Object.keys(props.style)){\n            // @ts-ignore\n            element.style[key] = props.style[key];\n        }\n    }\n    return element;\n}\nfunction PaneComponent(props, forwardedRef) {\n    const [paneName] = useState(props.name);\n    const [paneElement, setPaneElement] = useState(null);\n    useImperativeHandle(forwardedRef, ()=>paneElement, [\n        paneElement\n    ]);\n    const context = useLeafletContext();\n    // biome-ignore lint/correctness/useExhaustiveDependencies: paneName is immutable\n    const newContext = useMemo(()=>({\n            ...context,\n            pane: paneName\n        }), [\n        context\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: lifecycle-only effect\n    useEffect(()=>{\n        setPaneElement(createPane(paneName, props, context));\n        return function removeCreatedPane() {\n            const pane = context.map.getPane(paneName);\n            pane?.remove?.();\n            // @ts-ignore map internals\n            if (context.map._panes != null) {\n                // @ts-ignore map internals\n                context.map._panes = omitPane(context.map._panes, paneName);\n                // @ts-ignore map internals\n                context.map._paneRenderers = omitPane(// @ts-ignore map internals\n                context.map._paneRenderers, paneName);\n            }\n        };\n    }, []);\n    return props.children != null && paneElement != null ? /*#__PURE__*/ createPortal(/*#__PURE__*/ React.createElement(LeafletContext, {\n        value: newContext\n    }, props.children), paneElement) : null;\n}\nexport const Pane = /*#__PURE__*/ forwardRef(PaneComponent);\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,YAAY,EAAEC,iBAAiB,QAAQ,qBAAqB;AACrF,OAAOC,KAAK,IAAIC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,QAAQ,OAAO;AAC5F,SAASC,YAAY,QAAQ,WAAW;AACxC,MAAMC,aAAa,GAAG,CAClB,SAAS,EACT,YAAY,EACZ,aAAa,EACb,WAAW,EACX,YAAY,EACZ,UAAU,EACV,aAAa,CAChB;AACD,SAASC,QAAQA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACzB,MAAM;IAAE,CAACA,IAAI,GAAGC,EAAE;IAAE,GAAGC;EAAO,CAAC,GAAGH,GAAG;EACrC,OAAOG,MAAM;AACjB;AACA,SAASC,UAAUA,CAACC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAE;EACtC,IAAIT,aAAa,CAACU,OAAO,CAACH,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;IACpC,MAAM,IAAII,KAAK,CAAC,6EAA6EJ,IAAI,EAAE,CAAC;EACxG;EACA,IAAIE,OAAO,CAACG,GAAG,CAACC,OAAO,CAACN,IAAI,CAAC,IAAI,IAAI,EAAE;IACnC,MAAM,IAAII,KAAK,CAAC,yCAAyCJ,IAAI,EAAE,CAAC;EACpE;EACA,MAAMO,cAAc,GAAGN,KAAK,CAACL,IAAI,IAAIM,OAAO,CAACN,IAAI;EACjD,MAAMY,UAAU,GAAGD,cAAc,GAAGL,OAAO,CAACG,GAAG,CAACC,OAAO,CAACC,cAAc,CAAC,GAAGE,SAAS;EACnF,MAAMC,OAAO,GAAGR,OAAO,CAACG,GAAG,CAACN,UAAU,CAACC,IAAI,EAAEQ,UAAU,CAAC;EACxD,IAAIP,KAAK,CAACU,SAAS,IAAI,IAAI,EAAE;IACzB3B,YAAY,CAAC0B,OAAO,EAAET,KAAK,CAACU,SAAS,CAAC;EAC1C;EACA,IAAIV,KAAK,CAACW,KAAK,IAAI,IAAI,EAAE;IACrB,KAAK,MAAMC,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACd,KAAK,CAACW,KAAK,CAAC,EAAC;MACvC;MACAF,OAAO,CAACE,KAAK,CAACC,GAAG,CAAC,GAAGZ,KAAK,CAACW,KAAK,CAACC,GAAG,CAAC;IACzC;EACJ;EACA,OAAOH,OAAO;AAClB;AACA,SAASM,aAAaA,CAACf,KAAK,EAAEgB,YAAY,EAAE;EACxC,MAAM,CAACC,QAAQ,CAAC,GAAG9B,QAAQ,CAACa,KAAK,CAACD,IAAI,CAAC;EACvC,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACpDE,mBAAmB,CAAC2B,YAAY,EAAE,MAAIE,WAAW,EAAE,CAC/CA,WAAW,CACd,CAAC;EACF,MAAMjB,OAAO,GAAGjB,iBAAiB,CAAC,CAAC;EACnC;EACA,MAAMoC,UAAU,GAAG9B,OAAO,CAAC,OAAK;IACxB,GAAGW,OAAO;IACVN,IAAI,EAAEsB;EACV,CAAC,CAAC,EAAE,CACJhB,OAAO,CACV,CAAC;EACF;EACAb,SAAS,CAAC,MAAI;IACV+B,cAAc,CAACrB,UAAU,CAACmB,QAAQ,EAAEjB,KAAK,EAAEC,OAAO,CAAC,CAAC;IACpD,OAAO,SAASoB,iBAAiBA,CAAA,EAAG;MAChC,MAAM1B,IAAI,GAAGM,OAAO,CAACG,GAAG,CAACC,OAAO,CAACY,QAAQ,CAAC;MAC1CtB,IAAI,EAAE2B,MAAM,GAAG,CAAC;MAChB;MACA,IAAIrB,OAAO,CAACG,GAAG,CAACmB,MAAM,IAAI,IAAI,EAAE;QAC5B;QACAtB,OAAO,CAACG,GAAG,CAACmB,MAAM,GAAG9B,QAAQ,CAACQ,OAAO,CAACG,GAAG,CAACmB,MAAM,EAAEN,QAAQ,CAAC;QAC3D;QACAhB,OAAO,CAACG,GAAG,CAACoB,cAAc,GAAG/B,QAAQ;QAAC;QACtCQ,OAAO,CAACG,GAAG,CAACoB,cAAc,EAAEP,QAAQ,CAAC;MACzC;IACJ,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EACN,OAAOjB,KAAK,CAACyB,QAAQ,IAAI,IAAI,IAAIP,WAAW,IAAI,IAAI,GAAG,aAAc3B,YAAY,CAAC,aAAcN,KAAK,CAACyC,aAAa,CAAC5C,cAAc,EAAE;IAChI6C,KAAK,EAAEP;EACX,CAAC,EAAEpB,KAAK,CAACyB,QAAQ,CAAC,EAAEP,WAAW,CAAC,GAAG,IAAI;AAC3C;AACA,OAAO,MAAMU,IAAI,GAAG,aAAc1C,UAAU,CAAC6B,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}