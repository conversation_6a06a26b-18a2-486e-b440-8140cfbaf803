{"ast": null, "code": "import { createElementObject, createOverlayComponent } from '@react-leaflet/core';\nimport { Popup as LeafletPopup } from 'leaflet';\nimport { useEffect } from 'react';\nexport const Popup = createOverlayComponent(function createPopup(props, context) {\n  const popup = new LeafletPopup(props, context.overlayContainer);\n  return createElementObject(popup, context);\n}, function usePopupLifecycle(element, context, {\n  position\n}, setOpen) {\n  useEffect(function addPopup() {\n    const {\n      instance\n    } = element;\n    function onPopupOpen(event) {\n      if (event.popup === instance) {\n        instance.update();\n        setOpen(true);\n      }\n    }\n    function onPopupClose(event) {\n      if (event.popup === instance) {\n        setOpen(false);\n      }\n    }\n    context.map.on({\n      popupopen: onPopupOpen,\n      popupclose: onPopupClose\n    });\n    if (context.overlayContainer == null) {\n      // Attach to a Map\n      if (position != null) {\n        instance.setLatLng(position);\n      }\n      instance.openOn(context.map);\n    } else {\n      // Attach to container component\n      context.overlayContainer.bindPopup(instance);\n    }\n    return function removePopup() {\n      context.map.off({\n        popupopen: onPopupOpen,\n        popupclose: onPopupClose\n      });\n      context.overlayContainer?.unbindPopup();\n      context.map.removeLayer(instance);\n    };\n  }, [element, context, setOpen, position]);\n});", "map": {"version": 3, "names": ["createElementObject", "createOverlayComponent", "Popup", "LeafletPopup", "useEffect", "createPopup", "props", "context", "popup", "overlayContainer", "usePopupLifecycle", "element", "position", "<PERSON><PERSON><PERSON>", "addPopup", "instance", "onPopupOpen", "event", "update", "onPopupClose", "map", "on", "popupopen", "popupclose", "setLatLng", "openOn", "bindPopup", "removePopup", "off", "unbindPopup", "<PERSON><PERSON><PERSON>er"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/Popup.js"], "sourcesContent": ["import { createElementObject, createOverlayComponent } from '@react-leaflet/core';\nimport { Popup as LeafletPopup } from 'leaflet';\nimport { useEffect } from 'react';\nexport const Popup = createOverlayComponent(function createPopup(props, context) {\n    const popup = new LeafletPopup(props, context.overlayContainer);\n    return createElementObject(popup, context);\n}, function usePopupLifecycle(element, context, { position }, setOpen) {\n    useEffect(function addPopup() {\n        const { instance } = element;\n        function onPopupOpen(event) {\n            if (event.popup === instance) {\n                instance.update();\n                setOpen(true);\n            }\n        }\n        function onPopupClose(event) {\n            if (event.popup === instance) {\n                setOpen(false);\n            }\n        }\n        context.map.on({\n            popupopen: onPopupOpen,\n            popupclose: onPopupClose\n        });\n        if (context.overlayContainer == null) {\n            // Attach to a Map\n            if (position != null) {\n                instance.setLatLng(position);\n            }\n            instance.openOn(context.map);\n        } else {\n            // Attach to container component\n            context.overlayContainer.bindPopup(instance);\n        }\n        return function removePopup() {\n            context.map.off({\n                popupopen: onPopupOpen,\n                popupclose: onPopupClose\n            });\n            context.overlayContainer?.unbindPopup();\n            context.map.removeLayer(instance);\n        };\n    }, [\n        element,\n        context,\n        setOpen,\n        position\n    ]);\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,sBAAsB,QAAQ,qBAAqB;AACjF,SAASC,KAAK,IAAIC,YAAY,QAAQ,SAAS;AAC/C,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAO,MAAMF,KAAK,GAAGD,sBAAsB,CAAC,SAASI,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC7E,MAAMC,KAAK,GAAG,IAAIL,YAAY,CAACG,KAAK,EAAEC,OAAO,CAACE,gBAAgB,CAAC;EAC/D,OAAOT,mBAAmB,CAACQ,KAAK,EAAED,OAAO,CAAC;AAC9C,CAAC,EAAE,SAASG,iBAAiBA,CAACC,OAAO,EAAEJ,OAAO,EAAE;EAAEK;AAAS,CAAC,EAAEC,OAAO,EAAE;EACnET,SAAS,CAAC,SAASU,QAAQA,CAAA,EAAG;IAC1B,MAAM;MAAEC;IAAS,CAAC,GAAGJ,OAAO;IAC5B,SAASK,WAAWA,CAACC,KAAK,EAAE;MACxB,IAAIA,KAAK,CAACT,KAAK,KAAKO,QAAQ,EAAE;QAC1BA,QAAQ,CAACG,MAAM,CAAC,CAAC;QACjBL,OAAO,CAAC,IAAI,CAAC;MACjB;IACJ;IACA,SAASM,YAAYA,CAACF,KAAK,EAAE;MACzB,IAAIA,KAAK,CAACT,KAAK,KAAKO,QAAQ,EAAE;QAC1BF,OAAO,CAAC,KAAK,CAAC;MAClB;IACJ;IACAN,OAAO,CAACa,GAAG,CAACC,EAAE,CAAC;MACXC,SAAS,EAAEN,WAAW;MACtBO,UAAU,EAAEJ;IAChB,CAAC,CAAC;IACF,IAAIZ,OAAO,CAACE,gBAAgB,IAAI,IAAI,EAAE;MAClC;MACA,IAAIG,QAAQ,IAAI,IAAI,EAAE;QAClBG,QAAQ,CAACS,SAAS,CAACZ,QAAQ,CAAC;MAChC;MACAG,QAAQ,CAACU,MAAM,CAAClB,OAAO,CAACa,GAAG,CAAC;IAChC,CAAC,MAAM;MACH;MACAb,OAAO,CAACE,gBAAgB,CAACiB,SAAS,CAACX,QAAQ,CAAC;IAChD;IACA,OAAO,SAASY,WAAWA,CAAA,EAAG;MAC1BpB,OAAO,CAACa,GAAG,CAACQ,GAAG,CAAC;QACZN,SAAS,EAAEN,WAAW;QACtBO,UAAU,EAAEJ;MAChB,CAAC,CAAC;MACFZ,OAAO,CAACE,gBAAgB,EAAEoB,WAAW,CAAC,CAAC;MACvCtB,OAAO,CAACa,GAAG,CAACU,WAAW,CAACf,QAAQ,CAAC;IACrC,CAAC;EACL,CAAC,EAAE,CACCJ,OAAO,EACPJ,OAAO,EACPM,OAAO,EACPD,QAAQ,CACX,CAAC;AACN,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}