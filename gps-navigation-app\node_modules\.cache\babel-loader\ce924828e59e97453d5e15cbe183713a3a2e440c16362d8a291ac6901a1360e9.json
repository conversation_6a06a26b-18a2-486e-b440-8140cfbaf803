{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nvar imprecise = function imprecise(number) {\n  var decimals = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 4;\n  var factor = Math.pow(10, decimals);\n  return Math.round(number * factor) / factor;\n};\nvar decimal2sexagesimalNext = function decimal2sexagesimalNext(decimal) {\n  var _decimal$toString$spl = decimal.toString().split(\".\"),\n    _decimal$toString$spl2 = _slicedToArray(_decimal$toString$spl, 2),\n    pre = _decimal$toString$spl2[0],\n    post = _decimal$toString$spl2[1];\n  var deg = Math.abs(Number(pre));\n  var min0 = Number(\"0.\" + (post || 0)) * 60;\n  var sec0 = min0.toString().split(\".\");\n  var min = Math.floor(min0);\n  var sec = imprecise(Number(\"0.\" + (sec0[1] || 0)) * 60).toString();\n  var _sec$split = sec.split(\".\"),\n    _sec$split2 = _slicedToArray(_sec$split, 2),\n    secPreDec = _sec$split2[0],\n    _sec$split2$ = _sec$split2[1],\n    secDec = _sec$split2$ === void 0 ? \"0\" : _sec$split2$;\n  return deg + \"\\xB0 \" + min.toString().padStart(2, \"0\") + \"' \" + secPreDec.padStart(2, \"0\") + \".\" + secDec.padEnd(1, \"0\") + \"\\\"\";\n};\nvar _default = decimal2sexagesimalNext;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_slicedToArray", "arr", "i", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "prototype", "toString", "call", "slice", "constructor", "name", "Array", "from", "test", "len", "length", "arr2", "Symbol", "iterator", "_arr", "_n", "_d", "_e", "undefined", "_i", "_s", "next", "done", "push", "err", "isArray", "imprecise", "number", "decimals", "arguments", "factor", "Math", "pow", "round", "decimal2sexagesimalNext", "decimal", "_decimal$toString$spl", "split", "_decimal$toString$spl2", "pre", "post", "deg", "abs", "Number", "min0", "sec0", "min", "floor", "sec", "_sec$split", "_sec$split2", "secPreDec", "_sec$split2$", "secDec", "padStart", "padEnd", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/decimalToSexagesimal.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;function _slicedToArray(arr,i){return _arrayWithHoles(arr)||_iterableToArrayLimit(arr,i)||_unsupportedIterableToArray(arr,i)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}function _unsupportedIterableToArray(o,minLen){if(!o)return;if(typeof o===\"string\")return _arrayLikeToArray(o,minLen);var n=Object.prototype.toString.call(o).slice(8,-1);if(n===\"Object\"&&o.constructor)n=o.constructor.name;if(n===\"Map\"||n===\"Set\")return Array.from(o);if(n===\"Arguments\"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(o,minLen)}function _arrayLikeToArray(arr,len){if(len==null||len>arr.length)len=arr.length;for(var i=0,arr2=new Array(len);i<len;i++){arr2[i]=arr[i]}return arr2}function _iterableToArrayLimit(arr,i){if(typeof Symbol===\"undefined\"||!(Symbol.iterator in Object(arr)))return;var _arr=[];var _n=true;var _d=false;var _e=undefined;try{for(var _i=arr[Symbol.iterator](),_s;!(_n=(_s=_i.next()).done);_n=true){_arr.push(_s.value);if(i&&_arr.length===i)break}}catch(err){_d=true;_e=err}finally{try{if(!_n&&_i[\"return\"]!=null)_i[\"return\"]()}finally{if(_d)throw _e}}return _arr}function _arrayWithHoles(arr){if(Array.isArray(arr))return arr}var imprecise=function imprecise(number){var decimals=arguments.length>1&&arguments[1]!==undefined?arguments[1]:4;var factor=Math.pow(10,decimals);return Math.round(number*factor)/factor};var decimal2sexagesimalNext=function decimal2sexagesimalNext(decimal){var _decimal$toString$spl=decimal.toString().split(\".\"),_decimal$toString$spl2=_slicedToArray(_decimal$toString$spl,2),pre=_decimal$toString$spl2[0],post=_decimal$toString$spl2[1];var deg=Math.abs(Number(pre));var min0=Number(\"0.\"+(post||0))*60;var sec0=min0.toString().split(\".\");var min=Math.floor(min0);var sec=imprecise(Number(\"0.\"+(sec0[1]||0))*60).toString();var _sec$split=sec.split(\".\"),_sec$split2=_slicedToArray(_sec$split,2),secPreDec=_sec$split2[0],_sec$split2$=_sec$split2[1],secDec=_sec$split2$===void 0?\"0\":_sec$split2$;return deg+\"\\xB0 \"+min.toString().padStart(2,\"0\")+\"' \"+secPreDec.padStart(2,\"0\")+\".\"+secDec.padEnd(1,\"0\")+\"\\\"\"};var _default=decimal2sexagesimalNext;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,SAASC,cAAcA,CAACC,GAAG,EAACC,CAAC,EAAC;EAAC,OAAOC,eAAe,CAACF,GAAG,CAAC,IAAEG,qBAAqB,CAACH,GAAG,EAACC,CAAC,CAAC,IAAEG,2BAA2B,CAACJ,GAAG,EAACC,CAAC,CAAC,IAAEI,gBAAgB,CAAC,CAAC;AAAA;AAAC,SAASA,gBAAgBA,CAAA,EAAE;EAAC,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAA;AAAC,SAASF,2BAA2BA,CAACG,CAAC,EAACC,MAAM,EAAC;EAAC,IAAG,CAACD,CAAC,EAAC;EAAO,IAAG,OAAOA,CAAC,KAAG,QAAQ,EAAC,OAAOE,iBAAiB,CAACF,CAAC,EAACC,MAAM,CAAC;EAAC,IAAIE,CAAC,GAAChB,MAAM,CAACiB,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACN,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;EAAC,IAAGJ,CAAC,KAAG,QAAQ,IAAEH,CAAC,CAACQ,WAAW,EAACL,CAAC,GAACH,CAAC,CAACQ,WAAW,CAACC,IAAI;EAAC,IAAGN,CAAC,KAAG,KAAK,IAAEA,CAAC,KAAG,KAAK,EAAC,OAAOO,KAAK,CAACC,IAAI,CAACX,CAAC,CAAC;EAAC,IAAGG,CAAC,KAAG,WAAW,IAAE,0CAA0C,CAACS,IAAI,CAACT,CAAC,CAAC,EAAC,OAAOD,iBAAiB,CAACF,CAAC,EAACC,MAAM,CAAC;AAAA;AAAC,SAASC,iBAAiBA,CAACT,GAAG,EAACoB,GAAG,EAAC;EAAC,IAAGA,GAAG,IAAE,IAAI,IAAEA,GAAG,GAACpB,GAAG,CAACqB,MAAM,EAACD,GAAG,GAACpB,GAAG,CAACqB,MAAM;EAAC,KAAI,IAAIpB,CAAC,GAAC,CAAC,EAACqB,IAAI,GAAC,IAAIL,KAAK,CAACG,GAAG,CAAC,EAACnB,CAAC,GAACmB,GAAG,EAACnB,CAAC,EAAE,EAAC;IAACqB,IAAI,CAACrB,CAAC,CAAC,GAACD,GAAG,CAACC,CAAC,CAAC;EAAA;EAAC,OAAOqB,IAAI;AAAA;AAAC,SAASnB,qBAAqBA,CAACH,GAAG,EAACC,CAAC,EAAC;EAAC,IAAG,OAAOsB,MAAM,KAAG,WAAW,IAAE,EAAEA,MAAM,CAACC,QAAQ,IAAI9B,MAAM,CAACM,GAAG,CAAC,CAAC,EAAC;EAAO,IAAIyB,IAAI,GAAC,EAAE;EAAC,IAAIC,EAAE,GAAC,IAAI;EAAC,IAAIC,EAAE,GAAC,KAAK;EAAC,IAAIC,EAAE,GAACC,SAAS;EAAC,IAAG;IAAC,KAAI,IAAIC,EAAE,GAAC9B,GAAG,CAACuB,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC,EAACO,EAAE,EAAC,EAAEL,EAAE,GAAC,CAACK,EAAE,GAACD,EAAE,CAACE,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAACP,EAAE,GAAC,IAAI,EAAC;MAACD,IAAI,CAACS,IAAI,CAACH,EAAE,CAAClC,KAAK,CAAC;MAAC,IAAGI,CAAC,IAAEwB,IAAI,CAACJ,MAAM,KAAGpB,CAAC,EAAC;IAAK;EAAC,CAAC,QAAMkC,GAAG,EAAC;IAACR,EAAE,GAAC,IAAI;IAACC,EAAE,GAACO,GAAG;EAAA,CAAC,SAAO;IAAC,IAAG;MAAC,IAAG,CAACT,EAAE,IAAEI,EAAE,CAAC,QAAQ,CAAC,IAAE,IAAI,EAACA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAA,CAAC,SAAO;MAAC,IAAGH,EAAE,EAAC,MAAMC,EAAE;IAAA;EAAC;EAAC,OAAOH,IAAI;AAAA;AAAC,SAASvB,eAAeA,CAACF,GAAG,EAAC;EAAC,IAAGiB,KAAK,CAACmB,OAAO,CAACpC,GAAG,CAAC,EAAC,OAAOA,GAAG;AAAA;AAAC,IAAIqC,SAAS,GAAC,SAASA,SAASA,CAACC,MAAM,EAAC;EAAC,IAAIC,QAAQ,GAACC,SAAS,CAACnB,MAAM,GAAC,CAAC,IAAEmB,SAAS,CAAC,CAAC,CAAC,KAAGX,SAAS,GAACW,SAAS,CAAC,CAAC,CAAC,GAAC,CAAC;EAAC,IAAIC,MAAM,GAACC,IAAI,CAACC,GAAG,CAAC,EAAE,EAACJ,QAAQ,CAAC;EAAC,OAAOG,IAAI,CAACE,KAAK,CAACN,MAAM,GAACG,MAAM,CAAC,GAACA,MAAM;AAAA,CAAC;AAAC,IAAII,uBAAuB,GAAC,SAASA,uBAAuBA,CAACC,OAAO,EAAC;EAAC,IAAIC,qBAAqB,GAACD,OAAO,CAAClC,QAAQ,CAAC,CAAC,CAACoC,KAAK,CAAC,GAAG,CAAC;IAACC,sBAAsB,GAAClD,cAAc,CAACgD,qBAAqB,EAAC,CAAC,CAAC;IAACG,GAAG,GAACD,sBAAsB,CAAC,CAAC,CAAC;IAACE,IAAI,GAACF,sBAAsB,CAAC,CAAC,CAAC;EAAC,IAAIG,GAAG,GAACV,IAAI,CAACW,GAAG,CAACC,MAAM,CAACJ,GAAG,CAAC,CAAC;EAAC,IAAIK,IAAI,GAACD,MAAM,CAAC,IAAI,IAAEH,IAAI,IAAE,CAAC,CAAC,CAAC,GAAC,EAAE;EAAC,IAAIK,IAAI,GAACD,IAAI,CAAC3C,QAAQ,CAAC,CAAC,CAACoC,KAAK,CAAC,GAAG,CAAC;EAAC,IAAIS,GAAG,GAACf,IAAI,CAACgB,KAAK,CAACH,IAAI,CAAC;EAAC,IAAII,GAAG,GAACtB,SAAS,CAACiB,MAAM,CAAC,IAAI,IAAEE,IAAI,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC,CAAC,GAAC,EAAE,CAAC,CAAC5C,QAAQ,CAAC,CAAC;EAAC,IAAIgD,UAAU,GAACD,GAAG,CAACX,KAAK,CAAC,GAAG,CAAC;IAACa,WAAW,GAAC9D,cAAc,CAAC6D,UAAU,EAAC,CAAC,CAAC;IAACE,SAAS,GAACD,WAAW,CAAC,CAAC,CAAC;IAACE,YAAY,GAACF,WAAW,CAAC,CAAC,CAAC;IAACG,MAAM,GAACD,YAAY,KAAG,KAAK,CAAC,GAAC,GAAG,GAACA,YAAY;EAAC,OAAOX,GAAG,GAAC,OAAO,GAACK,GAAG,CAAC7C,QAAQ,CAAC,CAAC,CAACqD,QAAQ,CAAC,CAAC,EAAC,GAAG,CAAC,GAAC,IAAI,GAACH,SAAS,CAACG,QAAQ,CAAC,CAAC,EAAC,GAAG,CAAC,GAAC,GAAG,GAACD,MAAM,CAACE,MAAM,CAAC,CAAC,EAAC,GAAG,CAAC,GAAC,IAAI;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACtB,uBAAuB;AAACjD,OAAO,CAACE,OAAO,GAACqE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}