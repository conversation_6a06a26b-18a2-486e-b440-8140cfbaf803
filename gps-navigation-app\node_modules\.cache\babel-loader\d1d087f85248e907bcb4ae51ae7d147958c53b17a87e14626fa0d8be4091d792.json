{"ast": null, "code": "import _taggedTemplateLiteral from\"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13,_templateObject14,_templateObject15,_templateObject16,_templateObject17,_templateObject18,_templateObject19,_templateObject20,_templateObject21,_templateObject22;import React,{useState}from'react';import styled from'styled-components';// Using Unicode symbols instead of react-icons for React 19 compatibility\nimport{getDistance}from'geolib';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const PanelContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #2d2d2d;\\n  color: white;\\n\"])));const Header=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  padding: 20px;\\n  border-bottom: 1px solid #444;\\n  background-color: #1a1a1a;\\n\"])));const BackButton=styled.button(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  background: none;\\n  border: none;\\n  color: #007bff;\\n  font-size: 16px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n  padding: 8px 0;\\n  \\n  &:hover {\\n    color: #0056b3;\\n  }\\n\"])));const DestinationInfo=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  margin-bottom: 20px;\\n\"])));const DestinationName=styled.h2(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  margin: 0 0 8px 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n\"])));const DestinationAddress=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  font-size: 14px;\\n  color: #aaa;\\n  margin-bottom: 12px;\\n\"])));const RouteOptions=styled.div(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 20px;\\n\"])));const RouteOption=styled.button(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  flex: 1;\\n  padding: 12px;\\n  border: 1px solid #444;\\n  border-radius: 8px;\\n  background-color: \",\";\\n  color: white;\\n  font-size: 12px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  \\n  &:hover {\\n    background-color: #007bff;\\n    border-color: #007bff;\\n  }\\n\"])),props=>props.active?'#007bff':'#3d3d3d');const NavigationControls=styled.div(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 20px;\\n\"])));const ControlButton=styled.button(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  flex: 1;\\n  padding: 16px;\\n  border: none;\\n  border-radius: 12px;\\n  background-color: \",\";\\n  color: white;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  \\n  &:hover {\\n    opacity: 0.9;\\n    transform: translateY(-2px);\\n  }\\n  \\n  &:active {\\n    transform: translateY(0);\\n  }\\n\"])),props=>props.primary?'#28a745':'#dc3545');const RouteInfo=styled.div(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  background-color: #3d3d3d;\\n  border-radius: 12px;\\n  padding: 16px;\\n  margin-bottom: 20px;\\n\"])));const RouteStats=styled.div(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n\"])));const StatItem=styled.div(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  text-align: center;\\n\"])));const StatValue=styled.div(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #007bff;\\n  margin-bottom: 4px;\\n\"])));const StatLabel=styled.div(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  font-size: 12px;\\n  color: #aaa;\\n\"])));const Content=styled.div(_templateObject14||(_templateObject14=_taggedTemplateLiteral([\"\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 0 20px 20px;\\n\"])));const NavigationStatus=styled.div(_templateObject15||(_templateObject15=_taggedTemplateLiteral([\"\\n  background-color: \",\";\\n  color: white;\\n  padding: 16px;\\n  border-radius: 12px;\\n  margin-bottom: 20px;\\n  text-align: center;\\n  font-weight: 600;\\n\"])),props=>props.isNavigating?'#28a745':'#6c757d');const CurrentInstruction=styled.div(_templateObject16||(_templateObject16=_taggedTemplateLiteral([\"\\n  background-color: #007bff;\\n  color: white;\\n  padding: 20px;\\n  border-radius: 12px;\\n  margin-bottom: 20px;\\n  text-align: center;\\n\"])));const InstructionText=styled.div(_templateObject17||(_templateObject17=_taggedTemplateLiteral([\"\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin-bottom: 8px;\\n\"])));const InstructionDistance=styled.div(_templateObject18||(_templateObject18=_taggedTemplateLiteral([\"\\n  font-size: 14px;\\n  opacity: 0.9;\\n\"])));const InstructionsList=styled.div(_templateObject19||(_templateObject19=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n\"])));const InstructionItem=styled.div(_templateObject20||(_templateObject20=_taggedTemplateLiteral([\"\\n  padding: 12px;\\n  border: 1px solid #444;\\n  border-radius: 8px;\\n  background-color: \",\";\\n  font-size: 14px;\\n\"])),props=>props.isCurrent?'#007bff':'#3d3d3d');const VoiceControls=styled.div(_templateObject21||(_templateObject21=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n  background-color: #3d3d3d;\\n  border-radius: 12px;\\n  margin-bottom: 20px;\\n\"])));const VoiceButton=styled.button(_templateObject22||(_templateObject22=_taggedTemplateLiteral([\"\\n  background: none;\\n  border: none;\\n  color: \",\";\\n  font-size: 24px;\\n  cursor: pointer;\\n  transition: color 0.3s ease;\\n  \\n  &:hover {\\n    color: #007bff;\\n  }\\n\"])),props=>props.active?'#007bff':'#aaa');const NavigationPanel=_ref=>{let{currentLocation,destination,route,isNavigating,onStartNavigation,onStopNavigation,onBackToSearch}=_ref;const[selectedRouteType,setSelectedRouteType]=useState('fastest');const[voiceEnabled,setVoiceEnabled]=useState(true);const[navigationState,setNavigationState]=useState({isNavigating:false,remainingDistance:0,remainingTime:0});// Generate mock route data\nconst generateRoute=()=>{if(!currentLocation||!destination){throw new Error('Current location and destination are required');}const distance=getDistance({latitude:currentLocation.lat,longitude:currentLocation.lng},{latitude:destination.lat,longitude:destination.lng});// Generate simple route coordinates (in real app, this would come from routing API)\nconst coordinates=[[currentLocation.lng,currentLocation.lat],[destination.lng,destination.lat]];const mockInstructions=[{text:'از موقعیت فعلی شروع کنید',distance:0,duration:0,maneuver:'start',location:[currentLocation.lng,currentLocation.lat]},{text:\"\\u0628\\u0647 \\u0633\\u0645\\u062A \".concat(destination.name||'مقصد',\" \\u062D\\u0631\\u06A9\\u062A \\u06A9\\u0646\\u06CC\\u062F\"),distance:distance*0.8,duration:distance*0.8/50*3.6,// Assuming 50 km/h average speed\nmaneuver:'straight',location:[destination.lng,destination.lat]},{text:'به مقصد رسیده‌اید',distance:distance,duration:distance/50*3.6,maneuver:'arrive',location:[destination.lng,destination.lat]}];return{coordinates,distance,duration:distance/50*3.6,// Assuming 50 km/h average speed\ninstructions:mockInstructions,bounds:{north:Math.max(currentLocation.lat,destination.lat),south:Math.min(currentLocation.lat,destination.lat),east:Math.max(currentLocation.lng,destination.lng),west:Math.min(currentLocation.lng,destination.lng)}};};const handleStartNavigation=()=>{if(!currentLocation||!destination)return;const routeData=generateRoute();setNavigationState({isNavigating:true,remainingDistance:routeData.distance,remainingTime:routeData.duration,currentInstruction:routeData.instructions[0],nextInstruction:routeData.instructions[1]});onStartNavigation(routeData);};const handleStopNavigation=()=>{setNavigationState({isNavigating:false,remainingDistance:0,remainingTime:0});onStopNavigation();};const formatDistance=meters=>{if(meters<1000){return\"\".concat(Math.round(meters),\" \\u0645\\u062A\\u0631\");}return\"\".concat((meters/1000).toFixed(1),\" \\u06A9\\u06CC\\u0644\\u0648\\u0645\\u062A\\u0631\");};const formatDuration=seconds=>{const hours=Math.floor(seconds/3600);const minutes=Math.floor(seconds%3600/60);if(hours>0){return\"\".concat(hours,\" \\u0633\\u0627\\u0639\\u062A \").concat(minutes,\" \\u062F\\u0642\\u06CC\\u0642\\u0647\");}return\"\".concat(minutes,\" \\u062F\\u0642\\u06CC\\u0642\\u0647\");};const currentRoute=route||(currentLocation&&destination?generateRoute():null);return/*#__PURE__*/_jsxs(PanelContainer,{children:[/*#__PURE__*/_jsxs(Header,{children:[/*#__PURE__*/_jsx(BackButton,{onClick:onBackToSearch,children:\"\\u2190 \\u0628\\u0627\\u0632\\u06AF\\u0634\\u062A \\u0628\\u0647 \\u062C\\u0633\\u062A\\u062C\\u0648\"}),destination&&/*#__PURE__*/_jsxs(DestinationInfo,{children:[/*#__PURE__*/_jsx(DestinationName,{children:destination.name||'مقصد انتخاب شده'}),destination.address&&/*#__PURE__*/_jsx(DestinationAddress,{children:destination.address})]}),/*#__PURE__*/_jsxs(RouteOptions,{children:[/*#__PURE__*/_jsx(RouteOption,{active:selectedRouteType==='fastest',onClick:()=>setSelectedRouteType('fastest'),children:\"\\u0633\\u0631\\u06CC\\u0639\\u200C\\u062A\\u0631\\u06CC\\u0646\"}),/*#__PURE__*/_jsx(RouteOption,{active:selectedRouteType==='shortest',onClick:()=>setSelectedRouteType('shortest'),children:\"\\u06A9\\u0648\\u062A\\u0627\\u0647\\u200C\\u062A\\u0631\\u06CC\\u0646\"}),/*#__PURE__*/_jsx(RouteOption,{active:selectedRouteType==='eco',onClick:()=>setSelectedRouteType('eco'),children:\"\\u0627\\u0642\\u062A\\u0635\\u0627\\u062F\\u06CC\"})]}),/*#__PURE__*/_jsx(NavigationControls,{children:!isNavigating?/*#__PURE__*/_jsx(ControlButton,{primary:true,onClick:handleStartNavigation,children:\"\\u25B6 \\u0634\\u0631\\u0648\\u0639 \\u0645\\u0633\\u06CC\\u0631\\u06CC\\u0627\\u0628\\u06CC\"}):/*#__PURE__*/_jsx(ControlButton,{onClick:handleStopNavigation,children:\"\\u23F9 \\u062A\\u0648\\u0642\\u0641 \\u0645\\u0633\\u06CC\\u0631\\u06CC\\u0627\\u0628\\u06CC\"})})]}),/*#__PURE__*/_jsxs(Content,{children:[/*#__PURE__*/_jsx(NavigationStatus,{isNavigating:isNavigating,children:isNavigating?'در حال مسیریابی...':'آماده برای شروع'}),currentRoute&&/*#__PURE__*/_jsx(RouteInfo,{children:/*#__PURE__*/_jsxs(RouteStats,{children:[/*#__PURE__*/_jsxs(StatItem,{children:[/*#__PURE__*/_jsx(StatValue,{children:formatDistance(currentRoute.distance)}),/*#__PURE__*/_jsx(StatLabel,{children:\"\\u0645\\u0633\\u0627\\u0641\\u062A\"})]}),/*#__PURE__*/_jsxs(StatItem,{children:[/*#__PURE__*/_jsx(StatValue,{children:formatDuration(currentRoute.duration)}),/*#__PURE__*/_jsx(StatLabel,{children:\"\\u0632\\u0645\\u0627\\u0646 \\u062A\\u0642\\u0631\\u06CC\\u0628\\u06CC\"})]})]})}),isNavigating&&navigationState.currentInstruction&&/*#__PURE__*/_jsxs(CurrentInstruction,{children:[/*#__PURE__*/_jsx(InstructionText,{children:navigationState.currentInstruction.text}),/*#__PURE__*/_jsx(InstructionDistance,{children:formatDistance(navigationState.currentInstruction.distance)})]}),/*#__PURE__*/_jsxs(VoiceControls,{children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u0631\\u0627\\u0647\\u0646\\u0645\\u0627\\u06CC\\u06CC \\u0635\\u0648\\u062A\\u06CC\"}),/*#__PURE__*/_jsx(VoiceButton,{active:voiceEnabled,onClick:()=>setVoiceEnabled(!voiceEnabled),children:voiceEnabled?'🔊':'🔇'})]}),currentRoute&&currentRoute.instructions&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'20px 0 12px 0',fontSize:'16px',color:'#ccc'},children:\"\\u062F\\u0633\\u062A\\u0648\\u0631\\u0627\\u0644\\u0639\\u0645\\u0644\\u200C\\u0647\\u0627\\u06CC \\u0645\\u0633\\u06CC\\u0631\"}),/*#__PURE__*/_jsx(InstructionsList,{children:currentRoute.instructions.map((instruction,index)=>/*#__PURE__*/_jsxs(InstructionItem,{isCurrent:isNavigating&&index===0,children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',marginBottom:'4px'},children:instruction.text}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px',color:'#aaa'},children:formatDistance(instruction.distance)})]},index))})]})]})]});};export default NavigationPanel;", "map": {"version": 3, "names": ["React", "useState", "styled", "getDistance", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "PanelContainer", "div", "_templateObject", "_taggedTemplateLiteral", "Header", "_templateObject2", "BackButton", "button", "_templateObject3", "DestinationInfo", "_templateObject4", "DestinationName", "h2", "_templateObject5", "DestinationAddress", "_templateObject6", "RouteOptions", "_templateObject7", "RouteOption", "_templateObject8", "props", "active", "NavigationControls", "_templateObject9", "ControlButton", "_templateObject0", "primary", "RouteInfo", "_templateObject1", "RouteStats", "_templateObject10", "StatItem", "_templateObject11", "StatValue", "_templateObject12", "StatLabel", "_templateObject13", "Content", "_templateObject14", "NavigationStatus", "_templateObject15", "isNavigating", "CurrentInstruction", "_templateObject16", "InstructionText", "_templateObject17", "InstructionDistance", "_templateObject18", "InstructionsList", "_templateObject19", "InstructionItem", "_templateObject20", "isCurrent", "VoiceControls", "_templateObject21", "VoiceButton", "_templateObject22", "NavigationPanel", "_ref", "currentLocation", "destination", "route", "onStartNavigation", "onStopNavigation", "onBackToSearch", "selectedRouteType", "setSelectedRouteType", "voiceEnabled", "setVoiceEnabled", "navigationState", "setNavigationState", "remainingDistance", "remainingTime", "generateRoute", "Error", "distance", "latitude", "lat", "longitude", "lng", "coordinates", "mockInstructions", "text", "duration", "maneuver", "location", "concat", "name", "instructions", "bounds", "north", "Math", "max", "south", "min", "east", "west", "handleStartNavigation", "routeData", "currentInstruction", "nextInstruction", "handleStopNavigation", "formatDistance", "meters", "round", "toFixed", "formatDuration", "seconds", "hours", "floor", "minutes", "currentRoute", "children", "onClick", "address", "style", "margin", "fontSize", "color", "map", "instruction", "index", "fontWeight", "marginBottom"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/src/components/NavigationPanel.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\n// Using Unicode symbols instead of react-icons for React 19 compatibility\nimport { LocationData, RouteData, RouteInstruction, NavigationState } from '../types/gps.types';\nimport { getDistance } from 'geolib';\n\nconst PanelContainer = styled.div`\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background-color: #2d2d2d;\n  color: white;\n`;\n\nconst Header = styled.div`\n  padding: 20px;\n  border-bottom: 1px solid #444;\n  background-color: #1a1a1a;\n`;\n\nconst BackButton = styled.button`\n  background: none;\n  border: none;\n  color: #007bff;\n  font-size: 16px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 16px;\n  padding: 8px 0;\n  \n  &:hover {\n    color: #0056b3;\n  }\n`;\n\nconst DestinationInfo = styled.div`\n  margin-bottom: 20px;\n`;\n\nconst DestinationName = styled.h2`\n  margin: 0 0 8px 0;\n  font-size: 20px;\n  font-weight: 600;\n  color: white;\n`;\n\nconst DestinationAddress = styled.div`\n  font-size: 14px;\n  color: #aaa;\n  margin-bottom: 12px;\n`;\n\nconst RouteOptions = styled.div`\n  display: flex;\n  gap: 12px;\n  margin-bottom: 20px;\n`;\n\nconst RouteOption = styled.button<{ active?: boolean }>`\n  flex: 1;\n  padding: 12px;\n  border: 1px solid #444;\n  border-radius: 8px;\n  background-color: ${props => props.active ? '#007bff' : '#3d3d3d'};\n  color: white;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background-color: #007bff;\n    border-color: #007bff;\n  }\n`;\n\nconst NavigationControls = styled.div`\n  display: flex;\n  gap: 12px;\n  margin-bottom: 20px;\n`;\n\nconst ControlButton = styled.button<{ primary?: boolean }>`\n  flex: 1;\n  padding: 16px;\n  border: none;\n  border-radius: 12px;\n  background-color: ${props => props.primary ? '#28a745' : '#dc3545'};\n  color: white;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  \n  &:hover {\n    opacity: 0.9;\n    transform: translateY(-2px);\n  }\n  \n  &:active {\n    transform: translateY(0);\n  }\n`;\n\nconst RouteInfo = styled.div`\n  background-color: #3d3d3d;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 20px;\n`;\n\nconst RouteStats = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 16px;\n  margin-bottom: 16px;\n`;\n\nconst StatItem = styled.div`\n  text-align: center;\n`;\n\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 600;\n  color: #007bff;\n  margin-bottom: 4px;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 12px;\n  color: #aaa;\n`;\n\nconst Content = styled.div`\n  flex: 1;\n  overflow-y: auto;\n  padding: 0 20px 20px;\n`;\n\nconst NavigationStatus = styled.div<{ isNavigating: boolean }>`\n  background-color: ${props => props.isNavigating ? '#28a745' : '#6c757d'};\n  color: white;\n  padding: 16px;\n  border-radius: 12px;\n  margin-bottom: 20px;\n  text-align: center;\n  font-weight: 600;\n`;\n\nconst CurrentInstruction = styled.div`\n  background-color: #007bff;\n  color: white;\n  padding: 20px;\n  border-radius: 12px;\n  margin-bottom: 20px;\n  text-align: center;\n`;\n\nconst InstructionText = styled.div`\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 8px;\n`;\n\nconst InstructionDistance = styled.div`\n  font-size: 14px;\n  opacity: 0.9;\n`;\n\nconst InstructionsList = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n\nconst InstructionItem = styled.div<{ isCurrent?: boolean }>`\n  padding: 12px;\n  border: 1px solid #444;\n  border-radius: 8px;\n  background-color: ${props => props.isCurrent ? '#007bff' : '#3d3d3d'};\n  font-size: 14px;\n`;\n\nconst VoiceControls = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px;\n  background-color: #3d3d3d;\n  border-radius: 12px;\n  margin-bottom: 20px;\n`;\n\nconst VoiceButton = styled.button<{ active?: boolean }>`\n  background: none;\n  border: none;\n  color: ${props => props.active ? '#007bff' : '#aaa'};\n  font-size: 24px;\n  cursor: pointer;\n  transition: color 0.3s ease;\n  \n  &:hover {\n    color: #007bff;\n  }\n`;\n\ninterface NavigationPanelProps {\n  currentLocation: LocationData | null;\n  destination: LocationData | null;\n  route: RouteData | null;\n  isNavigating: boolean;\n  onStartNavigation: (route: RouteData) => void;\n  onStopNavigation: () => void;\n  onBackToSearch: () => void;\n}\n\nconst NavigationPanel: React.FC<NavigationPanelProps> = ({\n  currentLocation,\n  destination,\n  route,\n  isNavigating,\n  onStartNavigation,\n  onStopNavigation,\n  onBackToSearch\n}) => {\n  const [selectedRouteType, setSelectedRouteType] = useState<'fastest' | 'shortest' | 'eco'>('fastest');\n  const [voiceEnabled, setVoiceEnabled] = useState(true);\n  const [navigationState, setNavigationState] = useState<NavigationState>({\n    isNavigating: false,\n    remainingDistance: 0,\n    remainingTime: 0\n  });\n\n  // Generate mock route data\n  const generateRoute = (): RouteData => {\n    if (!currentLocation || !destination) {\n      throw new Error('Current location and destination are required');\n    }\n\n    const distance = getDistance(\n      { latitude: currentLocation.lat, longitude: currentLocation.lng },\n      { latitude: destination.lat, longitude: destination.lng }\n    );\n\n    // Generate simple route coordinates (in real app, this would come from routing API)\n    const coordinates: [number, number][] = [\n      [currentLocation.lng, currentLocation.lat],\n      [destination.lng, destination.lat]\n    ];\n\n    const mockInstructions: RouteInstruction[] = [\n      {\n        text: 'از موقعیت فعلی شروع کنید',\n        distance: 0,\n        duration: 0,\n        maneuver: 'start',\n        location: [currentLocation.lng, currentLocation.lat]\n      },\n      {\n        text: `به سمت ${destination.name || 'مقصد'} حرکت کنید`,\n        distance: distance * 0.8,\n        duration: (distance * 0.8) / 50 * 3.6, // Assuming 50 km/h average speed\n        maneuver: 'straight',\n        location: [destination.lng, destination.lat]\n      },\n      {\n        text: 'به مقصد رسیده‌اید',\n        distance: distance,\n        duration: distance / 50 * 3.6,\n        maneuver: 'arrive',\n        location: [destination.lng, destination.lat]\n      }\n    ];\n\n    return {\n      coordinates,\n      distance,\n      duration: distance / 50 * 3.6, // Assuming 50 km/h average speed\n      instructions: mockInstructions,\n      bounds: {\n        north: Math.max(currentLocation.lat, destination.lat),\n        south: Math.min(currentLocation.lat, destination.lat),\n        east: Math.max(currentLocation.lng, destination.lng),\n        west: Math.min(currentLocation.lng, destination.lng)\n      }\n    };\n  };\n\n  const handleStartNavigation = () => {\n    if (!currentLocation || !destination) return;\n    \n    const routeData = generateRoute();\n    setNavigationState({\n      isNavigating: true,\n      remainingDistance: routeData.distance,\n      remainingTime: routeData.duration,\n      currentInstruction: routeData.instructions[0],\n      nextInstruction: routeData.instructions[1]\n    });\n    \n    onStartNavigation(routeData);\n  };\n\n  const handleStopNavigation = () => {\n    setNavigationState({\n      isNavigating: false,\n      remainingDistance: 0,\n      remainingTime: 0\n    });\n    onStopNavigation();\n  };\n\n  const formatDistance = (meters: number): string => {\n    if (meters < 1000) {\n      return `${Math.round(meters)} متر`;\n    }\n    return `${(meters / 1000).toFixed(1)} کیلومتر`;\n  };\n\n  const formatDuration = (seconds: number): string => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    \n    if (hours > 0) {\n      return `${hours} ساعت ${minutes} دقیقه`;\n    }\n    return `${minutes} دقیقه`;\n  };\n\n  const currentRoute = route || (currentLocation && destination ? generateRoute() : null);\n\n  return (\n    <PanelContainer>\n      <Header>\n        <BackButton onClick={onBackToSearch}>\n          ← بازگشت به جستجو\n        </BackButton>\n        \n        {destination && (\n          <DestinationInfo>\n            <DestinationName>{destination.name || 'مقصد انتخاب شده'}</DestinationName>\n            {destination.address && (\n              <DestinationAddress>{destination.address}</DestinationAddress>\n            )}\n          </DestinationInfo>\n        )}\n\n        <RouteOptions>\n          <RouteOption \n            active={selectedRouteType === 'fastest'}\n            onClick={() => setSelectedRouteType('fastest')}\n          >\n            سریع‌ترین\n          </RouteOption>\n          <RouteOption \n            active={selectedRouteType === 'shortest'}\n            onClick={() => setSelectedRouteType('shortest')}\n          >\n            کوتاه‌ترین\n          </RouteOption>\n          <RouteOption \n            active={selectedRouteType === 'eco'}\n            onClick={() => setSelectedRouteType('eco')}\n          >\n            اقتصادی\n          </RouteOption>\n        </RouteOptions>\n\n        <NavigationControls>\n          {!isNavigating ? (\n            <ControlButton primary onClick={handleStartNavigation}>\n              ▶ شروع مسیریابی\n            </ControlButton>\n          ) : (\n            <ControlButton onClick={handleStopNavigation}>\n              ⏹ توقف مسیریابی\n            </ControlButton>\n          )}\n        </NavigationControls>\n      </Header>\n\n      <Content>\n        <NavigationStatus isNavigating={isNavigating}>\n          {isNavigating ? 'در حال مسیریابی...' : 'آماده برای شروع'}\n        </NavigationStatus>\n\n        {currentRoute && (\n          <RouteInfo>\n            <RouteStats>\n              <StatItem>\n                <StatValue>{formatDistance(currentRoute.distance)}</StatValue>\n                <StatLabel>مسافت</StatLabel>\n              </StatItem>\n              <StatItem>\n                <StatValue>{formatDuration(currentRoute.duration)}</StatValue>\n                <StatLabel>زمان تقریبی</StatLabel>\n              </StatItem>\n            </RouteStats>\n          </RouteInfo>\n        )}\n\n        {isNavigating && navigationState.currentInstruction && (\n          <CurrentInstruction>\n            <InstructionText>{navigationState.currentInstruction.text}</InstructionText>\n            <InstructionDistance>\n              {formatDistance(navigationState.currentInstruction.distance)}\n            </InstructionDistance>\n          </CurrentInstruction>\n        )}\n\n        <VoiceControls>\n          <span>راهنمایی صوتی</span>\n          <VoiceButton\n            active={voiceEnabled}\n            onClick={() => setVoiceEnabled(!voiceEnabled)}\n          >\n            {voiceEnabled ? '🔊' : '🔇'}\n          </VoiceButton>\n        </VoiceControls>\n\n        {currentRoute && currentRoute.instructions && (\n          <>\n            <h3 style={{ margin: '20px 0 12px 0', fontSize: '16px', color: '#ccc' }}>\n              دستورالعمل‌های مسیر\n            </h3>\n            <InstructionsList>\n              {currentRoute.instructions.map((instruction, index) => (\n                <InstructionItem \n                  key={index}\n                  isCurrent={isNavigating && index === 0}\n                >\n                  <div style={{ fontWeight: '500', marginBottom: '4px' }}>\n                    {instruction.text}\n                  </div>\n                  <div style={{ fontSize: '12px', color: '#aaa' }}>\n                    {formatDistance(instruction.distance)}\n                  </div>\n                </InstructionItem>\n              ))}\n            </InstructionsList>\n          </>\n        )}\n      </Content>\n    </PanelContainer>\n  );\n};\n\nexport default NavigationPanel;\n"], "mappings": "0kBAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAmB,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC;AAEA,OAASC,WAAW,KAAQ,QAAQ,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErC,KAAM,CAAAC,cAAc,CAAGR,MAAM,CAACS,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,uHAMhC,CAED,KAAM,CAAAC,MAAM,CAAGZ,MAAM,CAACS,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,4FAIxB,CAED,KAAM,CAAAG,UAAU,CAAGd,MAAM,CAACe,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAAL,sBAAA,wPAe/B,CAED,KAAM,CAAAM,eAAe,CAAGjB,MAAM,CAACS,GAAG,CAAAS,gBAAA,GAAAA,gBAAA,CAAAP,sBAAA,kCAEjC,CAED,KAAM,CAAAQ,eAAe,CAAGnB,MAAM,CAACoB,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAV,sBAAA,0FAKhC,CAED,KAAM,CAAAW,kBAAkB,CAAGtB,MAAM,CAACS,GAAG,CAAAc,gBAAA,GAAAA,gBAAA,CAAAZ,sBAAA,sEAIpC,CAED,KAAM,CAAAa,YAAY,CAAGxB,MAAM,CAACS,GAAG,CAAAgB,gBAAA,GAAAA,gBAAA,CAAAd,sBAAA,kEAI9B,CAED,KAAM,CAAAe,WAAW,CAAG1B,MAAM,CAACe,MAAM,CAAAY,gBAAA,GAAAA,gBAAA,CAAAhB,sBAAA,6RAKXiB,KAAK,EAAIA,KAAK,CAACC,MAAM,CAAG,SAAS,CAAG,SAAS,CAUlE,CAED,KAAM,CAAAC,kBAAkB,CAAG9B,MAAM,CAACS,GAAG,CAAAsB,gBAAA,GAAAA,gBAAA,CAAApB,sBAAA,kEAIpC,CAED,KAAM,CAAAqB,aAAa,CAAGhC,MAAM,CAACe,MAAM,CAAAkB,gBAAA,GAAAA,gBAAA,CAAAtB,sBAAA,2aAKbiB,KAAK,EAAIA,KAAK,CAACM,OAAO,CAAG,SAAS,CAAG,SAAS,CAmBnE,CAED,KAAM,CAAAC,SAAS,CAAGnC,MAAM,CAACS,GAAG,CAAA2B,gBAAA,GAAAA,gBAAA,CAAAzB,sBAAA,0GAK3B,CAED,KAAM,CAAA0B,UAAU,CAAGrC,MAAM,CAACS,GAAG,CAAA6B,iBAAA,GAAAA,iBAAA,CAAA3B,sBAAA,qGAK5B,CAED,KAAM,CAAA4B,QAAQ,CAAGvC,MAAM,CAACS,GAAG,CAAA+B,iBAAA,GAAAA,iBAAA,CAAA7B,sBAAA,iCAE1B,CAED,KAAM,CAAA8B,SAAS,CAAGzC,MAAM,CAACS,GAAG,CAAAiC,iBAAA,GAAAA,iBAAA,CAAA/B,sBAAA,6FAK3B,CAED,KAAM,CAAAgC,SAAS,CAAG3C,MAAM,CAACS,GAAG,CAAAmC,iBAAA,GAAAA,iBAAA,CAAAjC,sBAAA,8CAG3B,CAED,KAAM,CAAAkC,OAAO,CAAG7C,MAAM,CAACS,GAAG,CAAAqC,iBAAA,GAAAA,iBAAA,CAAAnC,sBAAA,oEAIzB,CAED,KAAM,CAAAoC,gBAAgB,CAAG/C,MAAM,CAACS,GAAG,CAAAuC,iBAAA,GAAAA,iBAAA,CAAArC,sBAAA,mKACbiB,KAAK,EAAIA,KAAK,CAACqB,YAAY,CAAG,SAAS,CAAG,SAAS,CAOxE,CAED,KAAM,CAAAC,kBAAkB,CAAGlD,MAAM,CAACS,GAAG,CAAA0C,iBAAA,GAAAA,iBAAA,CAAAxC,sBAAA,kJAOpC,CAED,KAAM,CAAAyC,eAAe,CAAGpD,MAAM,CAACS,GAAG,CAAA4C,iBAAA,GAAAA,iBAAA,CAAA1C,sBAAA,0EAIjC,CAED,KAAM,CAAA2C,mBAAmB,CAAGtD,MAAM,CAACS,GAAG,CAAA8C,iBAAA,GAAAA,iBAAA,CAAA5C,sBAAA,+CAGrC,CAED,KAAM,CAAA6C,gBAAgB,CAAGxD,MAAM,CAACS,GAAG,CAAAgD,iBAAA,GAAAA,iBAAA,CAAA9C,sBAAA,oEAIlC,CAED,KAAM,CAAA+C,eAAe,CAAG1D,MAAM,CAACS,GAAG,CAAAkD,iBAAA,GAAAA,iBAAA,CAAAhD,sBAAA,4HAIZiB,KAAK,EAAIA,KAAK,CAACgC,SAAS,CAAG,SAAS,CAAG,SAAS,CAErE,CAED,KAAM,CAAAC,aAAa,CAAG7D,MAAM,CAACS,GAAG,CAAAqD,iBAAA,GAAAA,iBAAA,CAAAnD,sBAAA,uLAQ/B,CAED,KAAM,CAAAoD,WAAW,CAAG/D,MAAM,CAACe,MAAM,CAAAiD,iBAAA,GAAAA,iBAAA,CAAArD,sBAAA,kLAGtBiB,KAAK,EAAIA,KAAK,CAACC,MAAM,CAAG,SAAS,CAAG,MAAM,CAQpD,CAYD,KAAM,CAAAoC,eAA+C,CAAGC,IAAA,EAQlD,IARmD,CACvDC,eAAe,CACfC,WAAW,CACXC,KAAK,CACLpB,YAAY,CACZqB,iBAAiB,CACjBC,gBAAgB,CAChBC,cACF,CAAC,CAAAN,IAAA,CACC,KAAM,CAACO,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3E,QAAQ,CAAiC,SAAS,CAAC,CACrG,KAAM,CAAC4E,YAAY,CAAEC,eAAe,CAAC,CAAG7E,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAC8E,eAAe,CAAEC,kBAAkB,CAAC,CAAG/E,QAAQ,CAAkB,CACtEkD,YAAY,CAAE,KAAK,CACnB8B,iBAAiB,CAAE,CAAC,CACpBC,aAAa,CAAE,CACjB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAiB,CACrC,GAAI,CAACd,eAAe,EAAI,CAACC,WAAW,CAAE,CACpC,KAAM,IAAI,CAAAc,KAAK,CAAC,+CAA+C,CAAC,CAClE,CAEA,KAAM,CAAAC,QAAQ,CAAGlF,WAAW,CAC1B,CAAEmF,QAAQ,CAAEjB,eAAe,CAACkB,GAAG,CAAEC,SAAS,CAAEnB,eAAe,CAACoB,GAAI,CAAC,CACjE,CAAEH,QAAQ,CAAEhB,WAAW,CAACiB,GAAG,CAAEC,SAAS,CAAElB,WAAW,CAACmB,GAAI,CAC1D,CAAC,CAED;AACA,KAAM,CAAAC,WAA+B,CAAG,CACtC,CAACrB,eAAe,CAACoB,GAAG,CAAEpB,eAAe,CAACkB,GAAG,CAAC,CAC1C,CAACjB,WAAW,CAACmB,GAAG,CAAEnB,WAAW,CAACiB,GAAG,CAAC,CACnC,CAED,KAAM,CAAAI,gBAAoC,CAAG,CAC3C,CACEC,IAAI,CAAE,0BAA0B,CAChCP,QAAQ,CAAE,CAAC,CACXQ,QAAQ,CAAE,CAAC,CACXC,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,CAAC1B,eAAe,CAACoB,GAAG,CAAEpB,eAAe,CAACkB,GAAG,CACrD,CAAC,CACD,CACEK,IAAI,oCAAAI,MAAA,CAAY1B,WAAW,CAAC2B,IAAI,EAAI,MAAM,sDAAY,CACtDZ,QAAQ,CAAEA,QAAQ,CAAG,GAAG,CACxBQ,QAAQ,CAAGR,QAAQ,CAAG,GAAG,CAAI,EAAE,CAAG,GAAG,CAAE;AACvCS,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,CAACzB,WAAW,CAACmB,GAAG,CAAEnB,WAAW,CAACiB,GAAG,CAC7C,CAAC,CACD,CACEK,IAAI,CAAE,mBAAmB,CACzBP,QAAQ,CAAEA,QAAQ,CAClBQ,QAAQ,CAAER,QAAQ,CAAG,EAAE,CAAG,GAAG,CAC7BS,QAAQ,CAAE,QAAQ,CAClBC,QAAQ,CAAE,CAACzB,WAAW,CAACmB,GAAG,CAAEnB,WAAW,CAACiB,GAAG,CAC7C,CAAC,CACF,CAED,MAAO,CACLG,WAAW,CACXL,QAAQ,CACRQ,QAAQ,CAAER,QAAQ,CAAG,EAAE,CAAG,GAAG,CAAE;AAC/Ba,YAAY,CAAEP,gBAAgB,CAC9BQ,MAAM,CAAE,CACNC,KAAK,CAAEC,IAAI,CAACC,GAAG,CAACjC,eAAe,CAACkB,GAAG,CAAEjB,WAAW,CAACiB,GAAG,CAAC,CACrDgB,KAAK,CAAEF,IAAI,CAACG,GAAG,CAACnC,eAAe,CAACkB,GAAG,CAAEjB,WAAW,CAACiB,GAAG,CAAC,CACrDkB,IAAI,CAAEJ,IAAI,CAACC,GAAG,CAACjC,eAAe,CAACoB,GAAG,CAAEnB,WAAW,CAACmB,GAAG,CAAC,CACpDiB,IAAI,CAAEL,IAAI,CAACG,GAAG,CAACnC,eAAe,CAACoB,GAAG,CAAEnB,WAAW,CAACmB,GAAG,CACrD,CACF,CAAC,CACH,CAAC,CAED,KAAM,CAAAkB,qBAAqB,CAAGA,CAAA,GAAM,CAClC,GAAI,CAACtC,eAAe,EAAI,CAACC,WAAW,CAAE,OAEtC,KAAM,CAAAsC,SAAS,CAAGzB,aAAa,CAAC,CAAC,CACjCH,kBAAkB,CAAC,CACjB7B,YAAY,CAAE,IAAI,CAClB8B,iBAAiB,CAAE2B,SAAS,CAACvB,QAAQ,CACrCH,aAAa,CAAE0B,SAAS,CAACf,QAAQ,CACjCgB,kBAAkB,CAAED,SAAS,CAACV,YAAY,CAAC,CAAC,CAAC,CAC7CY,eAAe,CAAEF,SAAS,CAACV,YAAY,CAAC,CAAC,CAC3C,CAAC,CAAC,CAEF1B,iBAAiB,CAACoC,SAAS,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAG,oBAAoB,CAAGA,CAAA,GAAM,CACjC/B,kBAAkB,CAAC,CACjB7B,YAAY,CAAE,KAAK,CACnB8B,iBAAiB,CAAE,CAAC,CACpBC,aAAa,CAAE,CACjB,CAAC,CAAC,CACFT,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAED,KAAM,CAAAuC,cAAc,CAAIC,MAAc,EAAa,CACjD,GAAIA,MAAM,CAAG,IAAI,CAAE,CACjB,SAAAjB,MAAA,CAAUK,IAAI,CAACa,KAAK,CAACD,MAAM,CAAC,wBAC9B,CACA,SAAAjB,MAAA,CAAU,CAACiB,MAAM,CAAG,IAAI,EAAEE,OAAO,CAAC,CAAC,CAAC,gDACtC,CAAC,CAED,KAAM,CAAAC,cAAc,CAAIC,OAAe,EAAa,CAClD,KAAM,CAAAC,KAAK,CAAGjB,IAAI,CAACkB,KAAK,CAACF,OAAO,CAAG,IAAI,CAAC,CACxC,KAAM,CAAAG,OAAO,CAAGnB,IAAI,CAACkB,KAAK,CAAEF,OAAO,CAAG,IAAI,CAAI,EAAE,CAAC,CAEjD,GAAIC,KAAK,CAAG,CAAC,CAAE,CACb,SAAAtB,MAAA,CAAUsB,KAAK,+BAAAtB,MAAA,CAASwB,OAAO,oCACjC,CACA,SAAAxB,MAAA,CAAUwB,OAAO,oCACnB,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGlD,KAAK,GAAKF,eAAe,EAAIC,WAAW,CAAGa,aAAa,CAAC,CAAC,CAAG,IAAI,CAAC,CAEvF,mBACE5E,KAAA,CAACG,cAAc,EAAAgH,QAAA,eACbnH,KAAA,CAACO,MAAM,EAAA4G,QAAA,eACLrH,IAAA,CAACW,UAAU,EAAC2G,OAAO,CAAEjD,cAAe,CAAAgD,QAAA,CAAC,yFAErC,CAAY,CAAC,CAEZpD,WAAW,eACV/D,KAAA,CAACY,eAAe,EAAAuG,QAAA,eACdrH,IAAA,CAACgB,eAAe,EAAAqG,QAAA,CAAEpD,WAAW,CAAC2B,IAAI,EAAI,iBAAiB,CAAkB,CAAC,CACzE3B,WAAW,CAACsD,OAAO,eAClBvH,IAAA,CAACmB,kBAAkB,EAAAkG,QAAA,CAAEpD,WAAW,CAACsD,OAAO,CAAqB,CAC9D,EACc,CAClB,cAEDrH,KAAA,CAACmB,YAAY,EAAAgG,QAAA,eACXrH,IAAA,CAACuB,WAAW,EACVG,MAAM,CAAE4C,iBAAiB,GAAK,SAAU,CACxCgD,OAAO,CAAEA,CAAA,GAAM/C,oBAAoB,CAAC,SAAS,CAAE,CAAA8C,QAAA,CAChD,wDAED,CAAa,CAAC,cACdrH,IAAA,CAACuB,WAAW,EACVG,MAAM,CAAE4C,iBAAiB,GAAK,UAAW,CACzCgD,OAAO,CAAEA,CAAA,GAAM/C,oBAAoB,CAAC,UAAU,CAAE,CAAA8C,QAAA,CACjD,8DAED,CAAa,CAAC,cACdrH,IAAA,CAACuB,WAAW,EACVG,MAAM,CAAE4C,iBAAiB,GAAK,KAAM,CACpCgD,OAAO,CAAEA,CAAA,GAAM/C,oBAAoB,CAAC,KAAK,CAAE,CAAA8C,QAAA,CAC5C,4CAED,CAAa,CAAC,EACF,CAAC,cAEfrH,IAAA,CAAC2B,kBAAkB,EAAA0F,QAAA,CAChB,CAACvE,YAAY,cACZ9C,IAAA,CAAC6B,aAAa,EAACE,OAAO,MAACuF,OAAO,CAAEhB,qBAAsB,CAAAe,QAAA,CAAC,kFAEvD,CAAe,CAAC,cAEhBrH,IAAA,CAAC6B,aAAa,EAACyF,OAAO,CAAEZ,oBAAqB,CAAAW,QAAA,CAAC,kFAE9C,CAAe,CAChB,CACiB,CAAC,EACf,CAAC,cAETnH,KAAA,CAACwC,OAAO,EAAA2E,QAAA,eACNrH,IAAA,CAAC4C,gBAAgB,EAACE,YAAY,CAAEA,YAAa,CAAAuE,QAAA,CAC1CvE,YAAY,CAAG,oBAAoB,CAAG,iBAAiB,CACxC,CAAC,CAElBsE,YAAY,eACXpH,IAAA,CAACgC,SAAS,EAAAqF,QAAA,cACRnH,KAAA,CAACgC,UAAU,EAAAmF,QAAA,eACTnH,KAAA,CAACkC,QAAQ,EAAAiF,QAAA,eACPrH,IAAA,CAACsC,SAAS,EAAA+E,QAAA,CAAEV,cAAc,CAACS,YAAY,CAACpC,QAAQ,CAAC,CAAY,CAAC,cAC9DhF,IAAA,CAACwC,SAAS,EAAA6E,QAAA,CAAC,gCAAK,CAAW,CAAC,EACpB,CAAC,cACXnH,KAAA,CAACkC,QAAQ,EAAAiF,QAAA,eACPrH,IAAA,CAACsC,SAAS,EAAA+E,QAAA,CAAEN,cAAc,CAACK,YAAY,CAAC5B,QAAQ,CAAC,CAAY,CAAC,cAC9DxF,IAAA,CAACwC,SAAS,EAAA6E,QAAA,CAAC,+DAAW,CAAW,CAAC,EAC1B,CAAC,EACD,CAAC,CACJ,CACZ,CAEAvE,YAAY,EAAI4B,eAAe,CAAC8B,kBAAkB,eACjDtG,KAAA,CAAC6C,kBAAkB,EAAAsE,QAAA,eACjBrH,IAAA,CAACiD,eAAe,EAAAoE,QAAA,CAAE3C,eAAe,CAAC8B,kBAAkB,CAACjB,IAAI,CAAkB,CAAC,cAC5EvF,IAAA,CAACmD,mBAAmB,EAAAkE,QAAA,CACjBV,cAAc,CAACjC,eAAe,CAAC8B,kBAAkB,CAACxB,QAAQ,CAAC,CACzC,CAAC,EACJ,CACrB,cAED9E,KAAA,CAACwD,aAAa,EAAA2D,QAAA,eACZrH,IAAA,SAAAqH,QAAA,CAAM,2EAAa,CAAM,CAAC,cAC1BrH,IAAA,CAAC4D,WAAW,EACVlC,MAAM,CAAE8C,YAAa,CACrB8C,OAAO,CAAEA,CAAA,GAAM7C,eAAe,CAAC,CAACD,YAAY,CAAE,CAAA6C,QAAA,CAE7C7C,YAAY,CAAG,IAAI,CAAG,IAAI,CAChB,CAAC,EACD,CAAC,CAEf4C,YAAY,EAAIA,YAAY,CAACvB,YAAY,eACxC3F,KAAA,CAAAE,SAAA,EAAAiH,QAAA,eACErH,IAAA,OAAIwH,KAAK,CAAE,CAAEC,MAAM,CAAE,eAAe,CAAEC,QAAQ,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAN,QAAA,CAAC,+GAEzE,CAAI,CAAC,cACLrH,IAAA,CAACqD,gBAAgB,EAAAgE,QAAA,CACdD,YAAY,CAACvB,YAAY,CAAC+B,GAAG,CAAC,CAACC,WAAW,CAAEC,KAAK,gBAChD5H,KAAA,CAACqD,eAAe,EAEdE,SAAS,CAAEX,YAAY,EAAIgF,KAAK,GAAK,CAAE,CAAAT,QAAA,eAEvCrH,IAAA,QAAKwH,KAAK,CAAE,CAAEO,UAAU,CAAE,KAAK,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAX,QAAA,CACpDQ,WAAW,CAACtC,IAAI,CACd,CAAC,cACNvF,IAAA,QAAKwH,KAAK,CAAE,CAAEE,QAAQ,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAN,QAAA,CAC7CV,cAAc,CAACkB,WAAW,CAAC7C,QAAQ,CAAC,CAClC,CAAC,GARD8C,KASU,CAClB,CAAC,CACc,CAAC,EACnB,CACH,EACM,CAAC,EACI,CAAC,CAErB,CAAC,CAED,cAAe,CAAAhE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}