{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar toRad = function toRad(value) {\n  return value * Math.PI / 180;\n};\nvar _default = toRad;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "toRad", "Math", "PI", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/toRad.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var toRad=function toRad(value){return value*Math.PI/180};var _default=toRad;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,KAAK,GAAC,SAASA,KAAKA,CAACF,KAAK,EAAC;EAAC,OAAOA,KAAK,GAACG,IAAI,CAACC,EAAE,GAAC,GAAG;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACH,KAAK;AAACH,OAAO,CAACE,OAAO,GAACI,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}