{"ast": null, "code": "import { createControlComponent } from '@react-leaflet/core';\nimport { Control } from 'leaflet';\nexport const AttributionControl = createControlComponent(function createAttributionControl(props) {\n  return new Control.Attribution(props);\n});", "map": {"version": 3, "names": ["createControlComponent", "Control", "AttributionControl", "createAttributionControl", "props", "Attribution"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/AttributionControl.js"], "sourcesContent": ["import { createControlComponent } from '@react-leaflet/core';\nimport { Control } from 'leaflet';\nexport const AttributionControl = createControlComponent(function createAttributionControl(props) {\n    return new Control.Attribution(props);\n});\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAO,MAAMC,kBAAkB,GAAGF,sBAAsB,CAAC,SAASG,wBAAwBA,CAACC,KAAK,EAAE;EAC9F,OAAO,IAAIH,OAAO,CAACI,WAAW,CAACD,KAAK,CAAC;AACzC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}