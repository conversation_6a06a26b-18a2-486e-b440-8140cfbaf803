{"ast": null, "code": "import { createElementObject, createOverlayComponent } from '@react-leaflet/core';\nimport { Tooltip as LeafletTooltip } from 'leaflet';\nimport { useEffect } from 'react';\nexport const Tooltip = createOverlayComponent(function createTooltip(props, context) {\n  const tooltip = new LeafletTooltip(props, context.overlayContainer);\n  return createElementObject(tooltip, context);\n}, function useTooltipLifecycle(element, context, _ref, setOpen) {\n  let {\n    position\n  } = _ref;\n  useEffect(function addTooltip() {\n    const container = context.overlayContainer;\n    if (container == null) {\n      return;\n    }\n    const {\n      instance\n    } = element;\n    const onTooltipOpen = event => {\n      if (event.tooltip === instance) {\n        if (position != null) {\n          instance.setLatLng(position);\n        }\n        instance.update();\n        setOpen(true);\n      }\n    };\n    const onTooltipClose = event => {\n      if (event.tooltip === instance) {\n        setOpen(false);\n      }\n    };\n    container.on({\n      tooltipopen: onTooltipOpen,\n      tooltipclose: onTooltipClose\n    });\n    container.bindTooltip(instance);\n    return function removeTooltip() {\n      container.off({\n        tooltipopen: onTooltipOpen,\n        tooltipclose: onTooltipClose\n      });\n      // @ts-ignore protected property\n      if (container._map != null) {\n        container.unbindTooltip();\n      }\n    };\n  }, [element, context, setOpen, position]);\n});", "map": {"version": 3, "names": ["createElementObject", "createOverlayComponent", "<PERSON><PERSON><PERSON>", "LeafletTooltip", "useEffect", "createTooltip", "props", "context", "tooltip", "overlayContainer", "useTooltipLifecycle", "element", "_ref", "<PERSON><PERSON><PERSON>", "position", "addTooltip", "container", "instance", "onTooltipOpen", "event", "setLatLng", "update", "onTooltipClose", "on", "tooltipopen", "tooltipclose", "bindTooltip", "removeTooltip", "off", "_map", "unbindTooltip"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/Tooltip.js"], "sourcesContent": ["import { createElementObject, createOverlayComponent } from '@react-leaflet/core';\nimport { Tooltip as LeafletTooltip } from 'leaflet';\nimport { useEffect } from 'react';\nexport const Tooltip = createOverlayComponent(function createTooltip(props, context) {\n    const tooltip = new LeafletTooltip(props, context.overlayContainer);\n    return createElementObject(tooltip, context);\n}, function useTooltipLifecycle(element, context, { position }, setOpen) {\n    useEffect(function addTooltip() {\n        const container = context.overlayContainer;\n        if (container == null) {\n            return;\n        }\n        const { instance } = element;\n        const onTooltipOpen = (event)=>{\n            if (event.tooltip === instance) {\n                if (position != null) {\n                    instance.setLatLng(position);\n                }\n                instance.update();\n                setOpen(true);\n            }\n        };\n        const onTooltipClose = (event)=>{\n            if (event.tooltip === instance) {\n                setOpen(false);\n            }\n        };\n        container.on({\n            tooltipopen: onTooltipOpen,\n            tooltipclose: onTooltipClose\n        });\n        container.bindTooltip(instance);\n        return function removeTooltip() {\n            container.off({\n                tooltipopen: onTooltipOpen,\n                tooltipclose: onTooltipClose\n            });\n            // @ts-ignore protected property\n            if (container._map != null) {\n                container.unbindTooltip();\n            }\n        };\n    }, [\n        element,\n        context,\n        setOpen,\n        position\n    ]);\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,sBAAsB,QAAQ,qBAAqB;AACjF,SAASC,OAAO,IAAIC,cAAc,QAAQ,SAAS;AACnD,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAO,MAAMF,OAAO,GAAGD,sBAAsB,CAAC,SAASI,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACjF,MAAMC,OAAO,GAAG,IAAIL,cAAc,CAACG,KAAK,EAAEC,OAAO,CAACE,gBAAgB,CAAC;EACnE,OAAOT,mBAAmB,CAACQ,OAAO,EAAED,OAAO,CAAC;AAChD,CAAC,EAAE,SAASG,mBAAmBA,CAACC,OAAO,EAAEJ,OAAO,EAAAK,IAAA,EAAgBC,OAAO,EAAE;EAAA,IAAvB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EAC1DR,SAAS,CAAC,SAASW,UAAUA,CAAA,EAAG;IAC5B,MAAMC,SAAS,GAAGT,OAAO,CAACE,gBAAgB;IAC1C,IAAIO,SAAS,IAAI,IAAI,EAAE;MACnB;IACJ;IACA,MAAM;MAAEC;IAAS,CAAC,GAAGN,OAAO;IAC5B,MAAMO,aAAa,GAAIC,KAAK,IAAG;MAC3B,IAAIA,KAAK,CAACX,OAAO,KAAKS,QAAQ,EAAE;QAC5B,IAAIH,QAAQ,IAAI,IAAI,EAAE;UAClBG,QAAQ,CAACG,SAAS,CAACN,QAAQ,CAAC;QAChC;QACAG,QAAQ,CAACI,MAAM,CAAC,CAAC;QACjBR,OAAO,CAAC,IAAI,CAAC;MACjB;IACJ,CAAC;IACD,MAAMS,cAAc,GAAIH,KAAK,IAAG;MAC5B,IAAIA,KAAK,CAACX,OAAO,KAAKS,QAAQ,EAAE;QAC5BJ,OAAO,CAAC,KAAK,CAAC;MAClB;IACJ,CAAC;IACDG,SAAS,CAACO,EAAE,CAAC;MACTC,WAAW,EAAEN,aAAa;MAC1BO,YAAY,EAAEH;IAClB,CAAC,CAAC;IACFN,SAAS,CAACU,WAAW,CAACT,QAAQ,CAAC;IAC/B,OAAO,SAASU,aAAaA,CAAA,EAAG;MAC5BX,SAAS,CAACY,GAAG,CAAC;QACVJ,WAAW,EAAEN,aAAa;QAC1BO,YAAY,EAAEH;MAClB,CAAC,CAAC;MACF;MACA,IAAIN,SAAS,CAACa,IAAI,IAAI,IAAI,EAAE;QACxBb,SAAS,CAACc,aAAa,CAAC,CAAC;MAC7B;IACJ,CAAC;EACL,CAAC,EAAE,CACCnB,OAAO,EACPJ,OAAO,EACPM,OAAO,EACPC,QAAQ,CACX,CAAC;AACN,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}