{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getDistance = _interopRequireDefault(require(\"./getDistance\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nvar getPathLength = function getPathLength(points) {\n  var distanceFn = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _getDistance.default;\n  return points.reduce(function (acc, point) {\n    if (_typeof(acc) === \"object\" && acc.last !== null) {\n      acc.distance += distanceFn(point, acc.last);\n    }\n    acc.last = point;\n    return acc;\n  }, {\n    last: null,\n    distance: 0\n  }).distance;\n};\nvar _default = getPathLength;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getDistance", "_interopRequireDefault", "require", "obj", "__esModule", "_typeof", "Symbol", "iterator", "constructor", "prototype", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "points", "distanceFn", "arguments", "length", "undefined", "reduce", "acc", "point", "last", "distance", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getPathLength.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getDistance=_interopRequireDefault(require(\"./getDistance\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _typeof(obj){\"@babel/helpers - typeof\";if(typeof Symbol===\"function\"&&typeof Symbol.iterator===\"symbol\"){_typeof=function _typeof(obj){return typeof obj}}else{_typeof=function _typeof(obj){return obj&&typeof Symbol===\"function\"&&obj.constructor===Symbol&&obj!==Symbol.prototype?\"symbol\":typeof obj}}return _typeof(obj)}var getPathLength=function getPathLength(points){var distanceFn=arguments.length>1&&arguments[1]!==undefined?arguments[1]:_getDistance.default;return points.reduce(function(acc,point){if(_typeof(acc)===\"object\"&&acc.last!==null){acc.distance+=distanceFn(point,acc.last)}acc.last=point;return acc},{last:null,distance:0}).distance};var _default=getPathLength;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACE,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACJ,OAAO,EAACI;EAAG,CAAC;AAAA;AAAC,SAASE,OAAOA,CAACF,GAAG,EAAC;EAAC,yBAAyB;;EAAC,IAAG,OAAOG,MAAM,KAAG,UAAU,IAAE,OAAOA,MAAM,CAACC,QAAQ,KAAG,QAAQ,EAAC;IAACF,OAAO,GAAC,SAASA,OAAOA,CAACF,GAAG,EAAC;MAAC,OAAO,OAAOA,GAAG;IAAA,CAAC;EAAA,CAAC,MAAI;IAACE,OAAO,GAAC,SAASA,OAAOA,CAACF,GAAG,EAAC;MAAC,OAAOA,GAAG,IAAE,OAAOG,MAAM,KAAG,UAAU,IAAEH,GAAG,CAACK,WAAW,KAAGF,MAAM,IAAEH,GAAG,KAAGG,MAAM,CAACG,SAAS,GAAC,QAAQ,GAAC,OAAON,GAAG;IAAA,CAAC;EAAA;EAAC,OAAOE,OAAO,CAACF,GAAG,CAAC;AAAA;AAAC,IAAIO,aAAa,GAAC,SAASA,aAAaA,CAACC,MAAM,EAAC;EAAC,IAAIC,UAAU,GAACC,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAGE,SAAS,GAACF,SAAS,CAAC,CAAC,CAAC,GAACb,YAAY,CAACD,OAAO;EAAC,OAAOY,MAAM,CAACK,MAAM,CAAC,UAASC,GAAG,EAACC,KAAK,EAAC;IAAC,IAAGb,OAAO,CAACY,GAAG,CAAC,KAAG,QAAQ,IAAEA,GAAG,CAACE,IAAI,KAAG,IAAI,EAAC;MAACF,GAAG,CAACG,QAAQ,IAAER,UAAU,CAACM,KAAK,EAACD,GAAG,CAACE,IAAI,CAAC;IAAA;IAACF,GAAG,CAACE,IAAI,GAACD,KAAK;IAAC,OAAOD,GAAG;EAAA,CAAC,EAAC;IAACE,IAAI,EAAC,IAAI;IAACC,QAAQ,EAAC;EAAC,CAAC,CAAC,CAACA,QAAQ;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACX,aAAa;AAACb,OAAO,CAACE,OAAO,GAACsB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}