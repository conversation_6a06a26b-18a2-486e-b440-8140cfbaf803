{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getDistance = _interopRequireDefault(require(\"./getDistance\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar isPointWithinRadius = function isPointWithinRadius(point, center, radius) {\n  var accuracy = 0.01;\n  return (0, _getDistance.default)(point, center, accuracy) < radius;\n};\nvar _default = isPointWithinRadius;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getDistance", "_interopRequireDefault", "require", "obj", "__esModule", "isPointWithinRadius", "point", "center", "radius", "accuracy", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/isPointWithinRadius.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getDistance=_interopRequireDefault(require(\"./getDistance\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var isPointWithinRadius=function isPointWithinRadius(point,center,radius){var accuracy=0.01;return(0,_getDistance.default)(point,center,accuracy)<radius};var _default=isPointWithinRadius;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACE,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACJ,OAAO,EAACI;EAAG,CAAC;AAAA;AAAC,IAAIE,mBAAmB,GAAC,SAASA,mBAAmBA,CAACC,KAAK,EAACC,MAAM,EAACC,MAAM,EAAC;EAAC,IAAIC,QAAQ,GAAC,IAAI;EAAC,OAAM,CAAC,CAAC,EAACT,YAAY,CAACD,OAAO,EAAEO,KAAK,EAACC,MAAM,EAACE,QAAQ,CAAC,GAACD,MAAM;AAAA,CAAC;AAAC,IAAIE,QAAQ,GAACL,mBAAmB;AAACR,OAAO,CAACE,OAAO,GAACW,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}