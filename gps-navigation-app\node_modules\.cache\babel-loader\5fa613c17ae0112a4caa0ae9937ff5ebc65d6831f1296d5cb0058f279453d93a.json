{"ast": null, "code": "import { createElementObject, createPathComponent, extendContext, updateCircle } from '@react-leaflet/core';\nimport { CircleMarker as LeafletCircleMarker } from 'leaflet';\nexport const CircleMarker = createPathComponent(function createCircleMarker({\n  center,\n  children: _c,\n  ...options\n}, ctx) {\n  const marker = new LeafletCircleMarker(center, options);\n  return createElementObject(marker, extendContext(ctx, {\n    overlayContainer: marker\n  }));\n}, updateCircle);", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "updateCircle", "CircleMarker", "LeafletCircleMarker", "createCircleMarker", "center", "children", "_c", "options", "ctx", "marker", "overlayContainer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/CircleMarker.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext, updateCircle } from '@react-leaflet/core';\nimport { CircleMarker as LeafletCircleMarker } from 'leaflet';\nexport const CircleMarker = createPathComponent(function createCircleMarker({ center, children: _c, ...options }, ctx) {\n    const marker = new LeafletCircleMarker(center, options);\n    return createElementObject(marker, extendContext(ctx, {\n        overlayContainer: marker\n    }));\n}, updateCircle);\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,qBAAqB;AAC3G,SAASC,YAAY,IAAIC,mBAAmB,QAAQ,SAAS;AAC7D,OAAO,MAAMD,YAAY,GAAGH,mBAAmB,CAAC,SAASK,kBAAkBA,CAAC;EAAEC,MAAM;EAAEC,QAAQ,EAAEC,EAAE;EAAE,GAAGC;AAAQ,CAAC,EAAEC,GAAG,EAAE;EACnH,MAAMC,MAAM,GAAG,IAAIP,mBAAmB,CAACE,MAAM,EAAEG,OAAO,CAAC;EACvD,OAAOV,mBAAmB,CAACY,MAAM,EAAEV,aAAa,CAACS,GAAG,EAAE;IAClDE,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAET,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}