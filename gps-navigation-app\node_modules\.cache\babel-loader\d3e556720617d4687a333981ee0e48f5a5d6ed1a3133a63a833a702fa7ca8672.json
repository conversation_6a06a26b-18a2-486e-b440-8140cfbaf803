{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar isDecimal = function isDecimal(value) {\n  var checkedValue = value.toString().trim();\n  if (isNaN(parseFloat(checkedValue))) {\n    return false;\n  }\n  return parseFloat(checkedValue) === Number(checkedValue);\n};\nvar _default = isDecimal;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "isDecimal", "checkedValue", "toString", "trim", "isNaN", "parseFloat", "Number", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/isDecimal.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var isDecimal=function isDecimal(value){var checkedValue=value.toString().trim();if(isNaN(parseFloat(checkedValue))){return false}return parseFloat(checkedValue)===Number(checkedValue)};var _default=isDecimal;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,SAAS,GAAC,SAASA,SAASA,CAACF,KAAK,EAAC;EAAC,IAAIG,YAAY,GAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;EAAC,IAAGC,KAAK,CAACC,UAAU,CAACJ,YAAY,CAAC,CAAC,EAAC;IAAC,OAAO,KAAK;EAAA;EAAC,OAAOI,UAAU,CAACJ,YAAY,CAAC,KAAGK,MAAM,CAACL,YAAY,CAAC;AAAA,CAAC;AAAC,IAAIM,QAAQ,GAACP,SAAS;AAACH,OAAO,CAACE,OAAO,GAACQ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}