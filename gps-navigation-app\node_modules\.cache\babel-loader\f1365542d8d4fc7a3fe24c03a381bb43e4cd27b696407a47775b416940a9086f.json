{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"bounds\", \"boundsOptions\", \"center\", \"children\", \"className\", \"id\", \"placeholder\", \"style\", \"whenReady\", \"zoom\"];\nimport { LeafletContext, createLeafletContext } from '@react-leaflet/core';\nimport { Map as LeafletMap } from 'leaflet';\nimport React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';\nfunction MapContainerComponent(_ref, forwardedRef) {\n  let {\n      bounds,\n      boundsOptions,\n      center,\n      children,\n      className,\n      id,\n      placeholder,\n      style,\n      whenReady,\n      zoom\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const [props] = useState({\n    className,\n    id,\n    style\n  });\n  const [context, setContext] = useState(null);\n  const mapInstanceRef = useRef(undefined);\n  useImperativeHandle(forwardedRef, () => {\n    var _context$map;\n    return (_context$map = context === null || context === void 0 ? void 0 : context.map) !== null && _context$map !== void 0 ? _context$map : null;\n  }, [context]);\n  // biome-ignore lint/correctness/useExhaustiveDependencies: ref callback\n  const mapRef = useCallback(node => {\n    if (node !== null && !mapInstanceRef.current) {\n      const map = new LeafletMap(node, options);\n      mapInstanceRef.current = map;\n      if (center != null && zoom != null) {\n        map.setView(center, zoom);\n      } else if (bounds != null) {\n        map.fitBounds(bounds, boundsOptions);\n      }\n      if (whenReady != null) {\n        map.whenReady(whenReady);\n      }\n      setContext(createLeafletContext(map));\n    }\n  }, []);\n  useEffect(() => {\n    return () => {\n      context === null || context === void 0 || context.map.remove();\n    };\n  }, [context]);\n  const contents = context ? /*#__PURE__*/React.createElement(LeafletContext, {\n    value: context\n  }, children) : placeholder !== null && placeholder !== void 0 ? placeholder : null;\n  return /*#__PURE__*/React.createElement(\"div\", _objectSpread(_objectSpread({}, props), {}, {\n    ref: mapRef\n  }), contents);\n}\nexport const MapContainer = /*#__PURE__*/forwardRef(MapContainerComponent);", "map": {"version": 3, "names": ["LeafletContext", "createLeafletContext", "Map", "LeafletMap", "React", "forwardRef", "useCallback", "useEffect", "useImperativeHandle", "useRef", "useState", "MapContainerComponent", "_ref", "forwardedRef", "bounds", "boundsOptions", "center", "children", "className", "id", "placeholder", "style", "when<PERSON><PERSON><PERSON>", "zoom", "options", "_objectWithoutProperties", "_excluded", "props", "context", "setContext", "mapInstanceRef", "undefined", "_context$map", "map", "mapRef", "node", "current", "<PERSON><PERSON><PERSON><PERSON>", "fitBounds", "remove", "contents", "createElement", "value", "_objectSpread", "ref", "MapContainer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/MapContainer.js"], "sourcesContent": ["import { LeafletContext, createLeafletContext } from '@react-leaflet/core';\nimport { Map as LeafletMap } from 'leaflet';\nimport React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';\nfunction MapContainerComponent({ bounds, boundsOptions, center, children, className, id, placeholder, style, whenReady, zoom, ...options }, forwardedRef) {\n    const [props] = useState({\n        className,\n        id,\n        style\n    });\n    const [context, setContext] = useState(null);\n    const mapInstanceRef = useRef(undefined);\n    useImperativeHandle(forwardedRef, ()=>context?.map ?? null, [\n        context\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: ref callback\n    const mapRef = useCallback((node)=>{\n        if (node !== null && !mapInstanceRef.current) {\n            const map = new LeafletMap(node, options);\n            mapInstanceRef.current = map;\n            if (center != null && zoom != null) {\n                map.setView(center, zoom);\n            } else if (bounds != null) {\n                map.fitBounds(bounds, boundsOptions);\n            }\n            if (whenReady != null) {\n                map.whenReady(whenReady);\n            }\n            setContext(createLeafletContext(map));\n        }\n    }, []);\n    useEffect(()=>{\n        return ()=>{\n            context?.map.remove();\n        };\n    }, [\n        context\n    ]);\n    const contents = context ? /*#__PURE__*/ React.createElement(LeafletContext, {\n        value: context\n    }, children) : placeholder ?? null;\n    return /*#__PURE__*/ React.createElement(\"div\", {\n        ...props,\n        ref: mapRef\n    }, contents);\n}\nexport const MapContainer = /*#__PURE__*/ forwardRef(MapContainerComponent);\n"], "mappings": ";;;AAAA,SAASA,cAAc,EAAEC,oBAAoB,QAAQ,qBAAqB;AAC1E,SAASC,GAAG,IAAIC,UAAU,QAAQ,SAAS;AAC3C,OAAOC,KAAK,IAAIC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxG,SAASC,qBAAqBA,CAAAC,IAAA,EAA8GC,YAAY,EAAE;EAAA,IAA3H;MAAEC,MAAM;MAAEC,aAAa;MAAEC,MAAM;MAAEC,QAAQ;MAAEC,SAAS;MAAEC,EAAE;MAAEC,WAAW;MAAEC,KAAK;MAAEC,SAAS;MAAEC;IAAiB,CAAC,GAAAX,IAAA;IAATY,OAAO,GAAAC,wBAAA,CAAAb,IAAA,EAAAc,SAAA;EACpI,MAAM,CAACC,KAAK,CAAC,GAAGjB,QAAQ,CAAC;IACrBQ,SAAS;IACTC,EAAE;IACFE;EACJ,CAAC,CAAC;EACF,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMoB,cAAc,GAAGrB,MAAM,CAACsB,SAAS,CAAC;EACxCvB,mBAAmB,CAACK,YAAY,EAAE;IAAA,IAAAmB,YAAA;IAAA,QAAAA,YAAA,GAAIJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,GAAG,cAAAD,YAAA,cAAAA,YAAA,GAAI,IAAI;EAAA,GAAE,CACxDJ,OAAO,CACV,CAAC;EACF;EACA,MAAMM,MAAM,GAAG5B,WAAW,CAAE6B,IAAI,IAAG;IAC/B,IAAIA,IAAI,KAAK,IAAI,IAAI,CAACL,cAAc,CAACM,OAAO,EAAE;MAC1C,MAAMH,GAAG,GAAG,IAAI9B,UAAU,CAACgC,IAAI,EAAEX,OAAO,CAAC;MACzCM,cAAc,CAACM,OAAO,GAAGH,GAAG;MAC5B,IAAIjB,MAAM,IAAI,IAAI,IAAIO,IAAI,IAAI,IAAI,EAAE;QAChCU,GAAG,CAACI,OAAO,CAACrB,MAAM,EAAEO,IAAI,CAAC;MAC7B,CAAC,MAAM,IAAIT,MAAM,IAAI,IAAI,EAAE;QACvBmB,GAAG,CAACK,SAAS,CAACxB,MAAM,EAAEC,aAAa,CAAC;MACxC;MACA,IAAIO,SAAS,IAAI,IAAI,EAAE;QACnBW,GAAG,CAACX,SAAS,CAACA,SAAS,CAAC;MAC5B;MACAO,UAAU,CAAC5B,oBAAoB,CAACgC,GAAG,CAAC,CAAC;IACzC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN1B,SAAS,CAAC,MAAI;IACV,OAAO,MAAI;MACPqB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,GAAG,CAACM,MAAM,CAAC,CAAC;IACzB,CAAC;EACL,CAAC,EAAE,CACCX,OAAO,CACV,CAAC;EACF,MAAMY,QAAQ,GAAGZ,OAAO,GAAG,aAAcxB,KAAK,CAACqC,aAAa,CAACzC,cAAc,EAAE;IACzE0C,KAAK,EAAEd;EACX,CAAC,EAAEX,QAAQ,CAAC,GAAGG,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAI,IAAI;EAClC,OAAO,aAAchB,KAAK,CAACqC,aAAa,CAAC,KAAK,EAAAE,aAAA,CAAAA,aAAA,KACvChB,KAAK;IACRiB,GAAG,EAAEV;EAAM,IACZM,QAAQ,CAAC;AAChB;AACA,OAAO,MAAMK,YAAY,GAAG,aAAcxC,UAAU,CAACM,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}