{"ast": null, "code": "import { createContainerComponent, createDivOverlayComponent, createLeafComponent } from './component.js';\nimport { createControlHook } from './control.js';\nimport { createDivOverlayHook } from './div-overlay.js';\nimport { createElementHook, createElementObject } from './element.js';\nimport { createLayerHook } from './layer.js';\nimport { createPathHook } from './path.js';\nexport function createControlComponent(createInstance) {\n  function createElement(props, context) {\n    return createElementObject(createInstance(props), context);\n  }\n  const useElement = createElementHook(createElement);\n  const useControl = createControlHook(useElement);\n  return createLeafComponent(useControl);\n}\nexport function createLayerComponent(createElement, updateElement) {\n  const useElement = createElementHook(createElement, updateElement);\n  const useLayer = createLayerHook(useElement);\n  return createContainerComponent(useLayer);\n}\nexport function createOverlayComponent(createElement, useLifecycle) {\n  const useElement = createElementHook(createElement);\n  const useOverlay = createDivOverlayHook(useElement, useLifecycle);\n  return createDivOverlayComponent(useOverlay);\n}\nexport function createPathComponent(createElement, updateElement) {\n  const useElement = createElementHook(createElement, updateElement);\n  const usePath = createPathHook(useElement);\n  return createContainerComponent(usePath);\n}\nexport function createTileLayerComponent(createElement, updateElement) {\n  const useElement = createElementHook(createElement, updateElement);\n  const useLayer = createLayerHook(useElement);\n  return createLeafComponent(useLayer);\n}", "map": {"version": 3, "names": ["createContainerComponent", "createDivOverlayComponent", "createLeafComponent", "createControlHook", "createDivOverlayHook", "createElementHook", "createElementObject", "createLayerHook", "createPathHook", "createControlComponent", "createInstance", "createElement", "props", "context", "useElement", "useControl", "createLayerComponent", "updateElement", "useLayer", "createOverlayComponent", "useLifecycle", "useOverlay", "createPathComponent", "usePath", "createTileLayerComponent"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@react-leaflet/core/lib/generic.js"], "sourcesContent": ["import { createContainerComponent, createDivOverlayComponent, createLeafComponent } from './component.js';\nimport { createControlHook } from './control.js';\nimport { createDivOverlayHook } from './div-overlay.js';\nimport { createElementHook, createElementObject } from './element.js';\nimport { createLayerHook } from './layer.js';\nimport { createPathHook } from './path.js';\nexport function createControlComponent(createInstance) {\n    function createElement(props, context) {\n        return createElementObject(createInstance(props), context);\n    }\n    const useElement = createElementHook(createElement);\n    const useControl = createControlHook(useElement);\n    return createLeafComponent(useControl);\n}\nexport function createLayerComponent(createElement, updateElement) {\n    const useElement = createElementHook(createElement, updateElement);\n    const useLayer = createLayerHook(useElement);\n    return createContainerComponent(useLayer);\n}\nexport function createOverlayComponent(createElement, useLifecycle) {\n    const useElement = createElementHook(createElement);\n    const useOverlay = createDivOverlayHook(useElement, useLifecycle);\n    return createDivOverlayComponent(useOverlay);\n}\nexport function createPathComponent(createElement, updateElement) {\n    const useElement = createElementHook(createElement, updateElement);\n    const usePath = createPathHook(useElement);\n    return createContainerComponent(usePath);\n}\nexport function createTileLayerComponent(createElement, updateElement) {\n    const useElement = createElementHook(createElement, updateElement);\n    const useLayer = createLayerHook(useElement);\n    return createLeafComponent(useLayer);\n}\n"], "mappings": "AAAA,SAASA,wBAAwB,EAAEC,yBAAyB,EAAEC,mBAAmB,QAAQ,gBAAgB;AACzG,SAASC,iBAAiB,QAAQ,cAAc;AAChD,SAASC,oBAAoB,QAAQ,kBAAkB;AACvD,SAASC,iBAAiB,EAAEC,mBAAmB,QAAQ,cAAc;AACrE,SAASC,eAAe,QAAQ,YAAY;AAC5C,SAASC,cAAc,QAAQ,WAAW;AAC1C,OAAO,SAASC,sBAAsBA,CAACC,cAAc,EAAE;EACnD,SAASC,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACnC,OAAOP,mBAAmB,CAACI,cAAc,CAACE,KAAK,CAAC,EAAEC,OAAO,CAAC;EAC9D;EACA,MAAMC,UAAU,GAAGT,iBAAiB,CAACM,aAAa,CAAC;EACnD,MAAMI,UAAU,GAAGZ,iBAAiB,CAACW,UAAU,CAAC;EAChD,OAAOZ,mBAAmB,CAACa,UAAU,CAAC;AAC1C;AACA,OAAO,SAASC,oBAAoBA,CAACL,aAAa,EAAEM,aAAa,EAAE;EAC/D,MAAMH,UAAU,GAAGT,iBAAiB,CAACM,aAAa,EAAEM,aAAa,CAAC;EAClE,MAAMC,QAAQ,GAAGX,eAAe,CAACO,UAAU,CAAC;EAC5C,OAAOd,wBAAwB,CAACkB,QAAQ,CAAC;AAC7C;AACA,OAAO,SAASC,sBAAsBA,CAACR,aAAa,EAAES,YAAY,EAAE;EAChE,MAAMN,UAAU,GAAGT,iBAAiB,CAACM,aAAa,CAAC;EACnD,MAAMU,UAAU,GAAGjB,oBAAoB,CAACU,UAAU,EAAEM,YAAY,CAAC;EACjE,OAAOnB,yBAAyB,CAACoB,UAAU,CAAC;AAChD;AACA,OAAO,SAASC,mBAAmBA,CAACX,aAAa,EAAEM,aAAa,EAAE;EAC9D,MAAMH,UAAU,GAAGT,iBAAiB,CAACM,aAAa,EAAEM,aAAa,CAAC;EAClE,MAAMM,OAAO,GAAGf,cAAc,CAACM,UAAU,CAAC;EAC1C,OAAOd,wBAAwB,CAACuB,OAAO,CAAC;AAC5C;AACA,OAAO,SAASC,wBAAwBA,CAACb,aAAa,EAAEM,aAAa,EAAE;EACnE,MAAMH,UAAU,GAAGT,iBAAiB,CAACM,aAAa,EAAEM,aAAa,CAAC;EAClE,MAAMC,QAAQ,GAAGX,eAAe,CAACO,UAAU,CAAC;EAC5C,OAAOZ,mBAAmB,CAACgB,QAAQ,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}