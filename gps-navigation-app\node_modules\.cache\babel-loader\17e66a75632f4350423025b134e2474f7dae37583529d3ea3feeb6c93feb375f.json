{"ast": null, "code": "import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { FeatureGroup as LeafletFeatureGroup } from 'leaflet';\nexport const FeatureGroup = createPathComponent(function createFeatureGroup({\n  children: _c,\n  ...options\n}, ctx) {\n  const group = new LeafletFeatureGroup([], options);\n  return createElementObject(group, extendContext(ctx, {\n    layerContainer: group,\n    overlayContainer: group\n  }));\n});", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "FeatureGroup", "LeafletFeatureGroup", "createFeatureGroup", "children", "_c", "options", "ctx", "group", "layerContainer", "overlayContainer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/FeatureGroup.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { FeatureGroup as LeafletFeatureGroup } from 'leaflet';\nexport const FeatureGroup = createPathComponent(function createFeatureGroup({ children: _c, ...options }, ctx) {\n    const group = new LeafletFeatureGroup([], options);\n    return createElementObject(group, extendContext(ctx, {\n        layerContainer: group,\n        overlayContainer: group\n    }));\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,qBAAqB;AAC7F,SAASC,YAAY,IAAIC,mBAAmB,QAAQ,SAAS;AAC7D,OAAO,MAAMD,YAAY,GAAGF,mBAAmB,CAAC,SAASI,kBAAkBA,CAAC;EAAEC,QAAQ,EAAEC,EAAE;EAAE,GAAGC;AAAQ,CAAC,EAAEC,GAAG,EAAE;EAC3G,MAAMC,KAAK,GAAG,IAAIN,mBAAmB,CAAC,EAAE,EAAEI,OAAO,CAAC;EAClD,OAAOR,mBAAmB,CAACU,KAAK,EAAER,aAAa,CAACO,GAAG,EAAE;IACjDE,cAAc,EAAED,KAAK;IACrBE,gBAAgB,EAAEF;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}