{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nimport _toPropertyKey from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js\";\nimport { LeafletContext, addClassName, useLeafletContext } from '@react-leaflet/core';\nimport React, { forwardRef, useState, useEffect, useImperativeHandle, useMemo } from 'react';\nimport { createPortal } from 'react-dom';\nconst DEFAULT_PANES = ['mapPane', 'markerPane', 'overlayPane', 'popupPane', 'shadowPane', 'tilePane', 'tooltipPane'];\nfunction omitPane(obj, pane) {\n  const {\n      [pane]: _p\n    } = obj,\n    others = _objectWithoutProperties(obj, [pane].map(_toPropertyKey));\n  return others;\n}\nfunction createPane(name, props, context) {\n  var _props$pane;\n  if (DEFAULT_PANES.indexOf(name) !== -1) {\n    throw new Error(\"You must use a unique name for a pane that is not a default Leaflet pane: \".concat(name));\n  }\n  if (context.map.getPane(name) != null) {\n    throw new Error(\"A pane with this name already exists: \".concat(name));\n  }\n  const parentPaneName = (_props$pane = props.pane) !== null && _props$pane !== void 0 ? _props$pane : context.pane;\n  const parentPane = parentPaneName ? context.map.getPane(parentPaneName) : undefined;\n  const element = context.map.createPane(name, parentPane);\n  if (props.className != null) {\n    addClassName(element, props.className);\n  }\n  if (props.style != null) {\n    for (const key of Object.keys(props.style)) {\n      // @ts-ignore\n      element.style[key] = props.style[key];\n    }\n  }\n  return element;\n}\nfunction PaneComponent(props, forwardedRef) {\n  const [paneName] = useState(props.name);\n  const [paneElement, setPaneElement] = useState(null);\n  useImperativeHandle(forwardedRef, () => paneElement, [paneElement]);\n  const context = useLeafletContext();\n  // biome-ignore lint/correctness/useExhaustiveDependencies: paneName is immutable\n  const newContext = useMemo(() => _objectSpread(_objectSpread({}, context), {}, {\n    pane: paneName\n  }), [context]);\n  // biome-ignore lint/correctness/useExhaustiveDependencies: lifecycle-only effect\n  useEffect(() => {\n    setPaneElement(createPane(paneName, props, context));\n    return function removeCreatedPane() {\n      var _pane$remove;\n      const pane = context.map.getPane(paneName);\n      pane === null || pane === void 0 || (_pane$remove = pane.remove) === null || _pane$remove === void 0 || _pane$remove.call(pane);\n      // @ts-ignore map internals\n      if (context.map._panes != null) {\n        // @ts-ignore map internals\n        context.map._panes = omitPane(context.map._panes, paneName);\n        // @ts-ignore map internals\n        context.map._paneRenderers = omitPane(\n        // @ts-ignore map internals\n        context.map._paneRenderers, paneName);\n      }\n    };\n  }, []);\n  return props.children != null && paneElement != null ? /*#__PURE__*/createPortal(/*#__PURE__*/React.createElement(LeafletContext, {\n    value: newContext\n  }, props.children), paneElement) : null;\n}\nexport const Pane = /*#__PURE__*/forwardRef(PaneComponent);", "map": {"version": 3, "names": ["LeafletContext", "addClassName", "useLeafletContext", "React", "forwardRef", "useState", "useEffect", "useImperativeHandle", "useMemo", "createPortal", "DEFAULT_PANES", "omitPane", "obj", "pane", "_p", "others", "_objectWithoutProperties", "map", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "createPane", "name", "props", "context", "_props$pane", "indexOf", "Error", "concat", "getPane", "parentPaneName", "parentPane", "undefined", "element", "className", "style", "key", "Object", "keys", "PaneComponent", "forwardedRef", "paneName", "paneElement", "setPaneElement", "newContext", "_objectSpread", "removeCreatedPane", "_pane$remove", "remove", "call", "_panes", "_paneRenderers", "children", "createElement", "value", "Pane"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/Pane.js"], "sourcesContent": ["import { LeafletContext, addClassName, useLeafletContext } from '@react-leaflet/core';\nimport React, { forwardRef, useState, useEffect, useImperativeHandle, useMemo } from 'react';\nimport { createPortal } from 'react-dom';\nconst DEFAULT_PANES = [\n    'mapPane',\n    'markerPane',\n    'overlayPane',\n    'popupPane',\n    'shadowPane',\n    'tilePane',\n    'tooltipPane'\n];\nfunction omitPane(obj, pane) {\n    const { [pane]: _p, ...others } = obj;\n    return others;\n}\nfunction createPane(name, props, context) {\n    if (DEFAULT_PANES.indexOf(name) !== -1) {\n        throw new Error(`You must use a unique name for a pane that is not a default Leaflet pane: ${name}`);\n    }\n    if (context.map.getPane(name) != null) {\n        throw new Error(`A pane with this name already exists: ${name}`);\n    }\n    const parentPaneName = props.pane ?? context.pane;\n    const parentPane = parentPaneName ? context.map.getPane(parentPaneName) : undefined;\n    const element = context.map.createPane(name, parentPane);\n    if (props.className != null) {\n        addClassName(element, props.className);\n    }\n    if (props.style != null) {\n        for (const key of Object.keys(props.style)){\n            // @ts-ignore\n            element.style[key] = props.style[key];\n        }\n    }\n    return element;\n}\nfunction PaneComponent(props, forwardedRef) {\n    const [paneName] = useState(props.name);\n    const [paneElement, setPaneElement] = useState(null);\n    useImperativeHandle(forwardedRef, ()=>paneElement, [\n        paneElement\n    ]);\n    const context = useLeafletContext();\n    // biome-ignore lint/correctness/useExhaustiveDependencies: paneName is immutable\n    const newContext = useMemo(()=>({\n            ...context,\n            pane: paneName\n        }), [\n        context\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: lifecycle-only effect\n    useEffect(()=>{\n        setPaneElement(createPane(paneName, props, context));\n        return function removeCreatedPane() {\n            const pane = context.map.getPane(paneName);\n            pane?.remove?.();\n            // @ts-ignore map internals\n            if (context.map._panes != null) {\n                // @ts-ignore map internals\n                context.map._panes = omitPane(context.map._panes, paneName);\n                // @ts-ignore map internals\n                context.map._paneRenderers = omitPane(// @ts-ignore map internals\n                context.map._paneRenderers, paneName);\n            }\n        };\n    }, []);\n    return props.children != null && paneElement != null ? /*#__PURE__*/ createPortal(/*#__PURE__*/ React.createElement(LeafletContext, {\n        value: newContext\n    }, props.children), paneElement) : null;\n}\nexport const Pane = /*#__PURE__*/ forwardRef(PaneComponent);\n"], "mappings": ";;;AAAA,SAASA,cAAc,EAAEC,YAAY,EAAEC,iBAAiB,QAAQ,qBAAqB;AACrF,OAAOC,KAAK,IAAIC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,QAAQ,OAAO;AAC5F,SAASC,YAAY,QAAQ,WAAW;AACxC,MAAMC,aAAa,GAAG,CAClB,SAAS,EACT,YAAY,EACZ,aAAa,EACb,WAAW,EACX,YAAY,EACZ,UAAU,EACV,aAAa,CAChB;AACD,SAASC,QAAQA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACzB,MAAM;MAAE,CAACA,IAAI,GAAGC;IAAc,CAAC,GAAGF,GAAG;IAAdG,MAAM,GAAAC,wBAAA,CAAKJ,GAAG,GAA5BC,IAAI,EAAAI,GAAA,CAAAC,cAAA;EACb,OAAOH,MAAM;AACjB;AACA,SAASI,UAAUA,CAACC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAAA,IAAAC,WAAA;EACtC,IAAIb,aAAa,CAACc,OAAO,CAACJ,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;IACpC,MAAM,IAAIK,KAAK,8EAAAC,MAAA,CAA8EN,IAAI,CAAE,CAAC;EACxG;EACA,IAAIE,OAAO,CAACL,GAAG,CAACU,OAAO,CAACP,IAAI,CAAC,IAAI,IAAI,EAAE;IACnC,MAAM,IAAIK,KAAK,0CAAAC,MAAA,CAA0CN,IAAI,CAAE,CAAC;EACpE;EACA,MAAMQ,cAAc,IAAAL,WAAA,GAAGF,KAAK,CAACR,IAAI,cAAAU,WAAA,cAAAA,WAAA,GAAID,OAAO,CAACT,IAAI;EACjD,MAAMgB,UAAU,GAAGD,cAAc,GAAGN,OAAO,CAACL,GAAG,CAACU,OAAO,CAACC,cAAc,CAAC,GAAGE,SAAS;EACnF,MAAMC,OAAO,GAAGT,OAAO,CAACL,GAAG,CAACE,UAAU,CAACC,IAAI,EAAES,UAAU,CAAC;EACxD,IAAIR,KAAK,CAACW,SAAS,IAAI,IAAI,EAAE;IACzB/B,YAAY,CAAC8B,OAAO,EAAEV,KAAK,CAACW,SAAS,CAAC;EAC1C;EACA,IAAIX,KAAK,CAACY,KAAK,IAAI,IAAI,EAAE;IACrB,KAAK,MAAMC,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACf,KAAK,CAACY,KAAK,CAAC,EAAC;MACvC;MACAF,OAAO,CAACE,KAAK,CAACC,GAAG,CAAC,GAAGb,KAAK,CAACY,KAAK,CAACC,GAAG,CAAC;IACzC;EACJ;EACA,OAAOH,OAAO;AAClB;AACA,SAASM,aAAaA,CAAChB,KAAK,EAAEiB,YAAY,EAAE;EACxC,MAAM,CAACC,QAAQ,CAAC,GAAGlC,QAAQ,CAACgB,KAAK,CAACD,IAAI,CAAC;EACvC,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACpDE,mBAAmB,CAAC+B,YAAY,EAAE,MAAIE,WAAW,EAAE,CAC/CA,WAAW,CACd,CAAC;EACF,MAAMlB,OAAO,GAAGpB,iBAAiB,CAAC,CAAC;EACnC;EACA,MAAMwC,UAAU,GAAGlC,OAAO,CAAC,MAAAmC,aAAA,CAAAA,aAAA,KAChBrB,OAAO;IACVT,IAAI,EAAE0B;EAAQ,EAChB,EAAE,CACJjB,OAAO,CACV,CAAC;EACF;EACAhB,SAAS,CAAC,MAAI;IACVmC,cAAc,CAACtB,UAAU,CAACoB,QAAQ,EAAElB,KAAK,EAAEC,OAAO,CAAC,CAAC;IACpD,OAAO,SAASsB,iBAAiBA,CAAA,EAAG;MAAA,IAAAC,YAAA;MAChC,MAAMhC,IAAI,GAAGS,OAAO,CAACL,GAAG,CAACU,OAAO,CAACY,QAAQ,CAAC;MAC1C1B,IAAI,aAAJA,IAAI,gBAAAgC,YAAA,GAAJhC,IAAI,CAAEiC,MAAM,cAAAD,YAAA,eAAZA,YAAA,CAAAE,IAAA,CAAAlC,IAAe,CAAC;MAChB;MACA,IAAIS,OAAO,CAACL,GAAG,CAAC+B,MAAM,IAAI,IAAI,EAAE;QAC5B;QACA1B,OAAO,CAACL,GAAG,CAAC+B,MAAM,GAAGrC,QAAQ,CAACW,OAAO,CAACL,GAAG,CAAC+B,MAAM,EAAET,QAAQ,CAAC;QAC3D;QACAjB,OAAO,CAACL,GAAG,CAACgC,cAAc,GAAGtC,QAAQ;QAAC;QACtCW,OAAO,CAACL,GAAG,CAACgC,cAAc,EAAEV,QAAQ,CAAC;MACzC;IACJ,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EACN,OAAOlB,KAAK,CAAC6B,QAAQ,IAAI,IAAI,IAAIV,WAAW,IAAI,IAAI,GAAG,aAAc/B,YAAY,CAAC,aAAcN,KAAK,CAACgD,aAAa,CAACnD,cAAc,EAAE;IAChIoD,KAAK,EAAEV;EACX,CAAC,EAAErB,KAAK,CAAC6B,QAAQ,CAAC,EAAEV,WAAW,CAAC,GAAG,IAAI;AAC3C;AACA,OAAO,MAAMa,IAAI,GAAG,aAAcjD,UAAU,CAACiC,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}