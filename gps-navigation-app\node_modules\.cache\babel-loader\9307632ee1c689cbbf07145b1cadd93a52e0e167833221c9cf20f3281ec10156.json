{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"center\", \"children\"];\nimport { createElementObject, createPathComponent, extendContext, updateCircle } from '@react-leaflet/core';\nimport { CircleMarker as LeafletCircleMarker } from 'leaflet';\nexport const CircleMarker = createPathComponent(function createCircleMarker(_ref, ctx) {\n  let {\n      center,\n      children: _c\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const marker = new LeafletCircleMarker(center, options);\n  return createElementObject(marker, extendContext(ctx, {\n    overlayContainer: marker\n  }));\n}, updateCircle);", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "updateCircle", "CircleMarker", "LeafletCircleMarker", "createCircleMarker", "_ref", "ctx", "center", "children", "_c", "options", "_objectWithoutProperties", "_excluded", "marker", "overlayContainer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/CircleMarker.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext, updateCircle } from '@react-leaflet/core';\nimport { CircleMarker as LeafletCircleMarker } from 'leaflet';\nexport const CircleMarker = createPathComponent(function createCircleMarker({ center, children: _c, ...options }, ctx) {\n    const marker = new LeafletCircleMarker(center, options);\n    return createElementObject(marker, extendContext(ctx, {\n        overlayContainer: marker\n    }));\n}, updateCircle);\n"], "mappings": ";;AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,qBAAqB;AAC3G,SAASC,YAAY,IAAIC,mBAAmB,QAAQ,SAAS;AAC7D,OAAO,MAAMD,YAAY,GAAGH,mBAAmB,CAAC,SAASK,kBAAkBA,CAAAC,IAAA,EAAuCC,GAAG,EAAE;EAAA,IAA3C;MAAEC,MAAM;MAAEC,QAAQ,EAAEC;IAAe,CAAC,GAAAJ,IAAA;IAATK,OAAO,GAAAC,wBAAA,CAAAN,IAAA,EAAAO,SAAA;EAC1G,MAAMC,MAAM,GAAG,IAAIV,mBAAmB,CAACI,MAAM,EAAEG,OAAO,CAAC;EACvD,OAAOZ,mBAAmB,CAACe,MAAM,EAAEb,aAAa,CAACM,GAAG,EAAE;IAClDQ,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAEZ,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}