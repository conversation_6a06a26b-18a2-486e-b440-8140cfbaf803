{"ast": null, "code": "import { useEffect, useRef } from 'react';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { useLayerLifecycle } from './layer.js';\nimport { withPane } from './pane.js';\nexport function usePathOptions(element, props) {\n  const optionsRef = useRef(undefined);\n  useEffect(function updatePathOptions() {\n    if (props.pathOptions !== optionsRef.current) {\n      var _props$pathOptions;\n      const options = (_props$pathOptions = props.pathOptions) !== null && _props$pathOptions !== void 0 ? _props$pathOptions : {};\n      element.instance.setStyle(options);\n      optionsRef.current = options;\n    }\n  }, [element, props]);\n}\nexport function createPathHook(useElement) {\n  return function usePath(props) {\n    const context = useLeafletContext();\n    const elementRef = useElement(withPane(props, context), context);\n    useEventHandlers(elementRef.current, props.eventHandlers);\n    useLayerLifecycle(elementRef.current, context);\n    usePathOptions(elementRef.current, props);\n    return elementRef;\n  };\n}", "map": {"version": 3, "names": ["useEffect", "useRef", "useLeafletContext", "useEventHandlers", "useLayerLifecycle", "with<PERSON>ane", "usePathOptions", "element", "props", "optionsRef", "undefined", "updatePathOptions", "pathOptions", "current", "_props$pathOptions", "options", "instance", "setStyle", "createPathHook", "useElement", "usePath", "context", "elementRef", "eventHandlers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@react-leaflet/core/lib/path.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { useLayerLifecycle } from './layer.js';\nimport { withPane } from './pane.js';\nexport function usePathOptions(element, props) {\n    const optionsRef = useRef(undefined);\n    useEffect(function updatePathOptions() {\n        if (props.pathOptions !== optionsRef.current) {\n            const options = props.pathOptions ?? {};\n            element.instance.setStyle(options);\n            optionsRef.current = options;\n        }\n    }, [\n        element,\n        props\n    ]);\n}\nexport function createPathHook(useElement) {\n    return function usePath(props) {\n        const context = useLeafletContext();\n        const elementRef = useElement(withPane(props, context), context);\n        useEventHandlers(elementRef.current, props.eventHandlers);\n        useLayerLifecycle(elementRef.current, context);\n        usePathOptions(elementRef.current, props);\n        return elementRef;\n    };\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,SAASC,iBAAiB,QAAQ,cAAc;AAChD,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,iBAAiB,QAAQ,YAAY;AAC9C,SAASC,QAAQ,QAAQ,WAAW;AACpC,OAAO,SAASC,cAAcA,CAACC,OAAO,EAAEC,KAAK,EAAE;EAC3C,MAAMC,UAAU,GAAGR,MAAM,CAACS,SAAS,CAAC;EACpCV,SAAS,CAAC,SAASW,iBAAiBA,CAAA,EAAG;IACnC,IAAIH,KAAK,CAACI,WAAW,KAAKH,UAAU,CAACI,OAAO,EAAE;MAAA,IAAAC,kBAAA;MAC1C,MAAMC,OAAO,IAAAD,kBAAA,GAAGN,KAAK,CAACI,WAAW,cAAAE,kBAAA,cAAAA,kBAAA,GAAI,CAAC,CAAC;MACvCP,OAAO,CAACS,QAAQ,CAACC,QAAQ,CAACF,OAAO,CAAC;MAClCN,UAAU,CAACI,OAAO,GAAGE,OAAO;IAChC;EACJ,CAAC,EAAE,CACCR,OAAO,EACPC,KAAK,CACR,CAAC;AACN;AACA,OAAO,SAASU,cAAcA,CAACC,UAAU,EAAE;EACvC,OAAO,SAASC,OAAOA,CAACZ,KAAK,EAAE;IAC3B,MAAMa,OAAO,GAAGnB,iBAAiB,CAAC,CAAC;IACnC,MAAMoB,UAAU,GAAGH,UAAU,CAACd,QAAQ,CAACG,KAAK,EAAEa,OAAO,CAAC,EAAEA,OAAO,CAAC;IAChElB,gBAAgB,CAACmB,UAAU,CAACT,OAAO,EAAEL,KAAK,CAACe,aAAa,CAAC;IACzDnB,iBAAiB,CAACkB,UAAU,CAACT,OAAO,EAAEQ,OAAO,CAAC;IAC9Cf,cAAc,CAACgB,UAAU,CAACT,OAAO,EAAEL,KAAK,CAAC;IACzC,OAAOc,UAAU;EACrB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}