{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getDistance = _interopRequireDefault(require(\"./getDistance\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar getSpeed = function getSpeed(start, end) {\n  var distanceFn = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : _getDistance.default;\n  var distance = distanceFn(start, end);\n  var time = Number(end.time) - Number(start.time);\n  var metersPerSecond = distance / time * 1000;\n  return metersPerSecond;\n};\nvar _default = getSpeed;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getDistance", "_interopRequireDefault", "require", "obj", "__esModule", "getSpeed", "start", "end", "distanceFn", "arguments", "length", "undefined", "distance", "time", "Number", "metersPerSecond", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getSpeed.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getDistance=_interopRequireDefault(require(\"./getDistance\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var getSpeed=function getSpeed(start,end){var distanceFn=arguments.length>2&&arguments[2]!==undefined?arguments[2]:_getDistance.default;var distance=distanceFn(start,end);var time=Number(end.time)-Number(start.time);var metersPerSecond=distance/time*1000;return metersPerSecond};var _default=getSpeed;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACE,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACJ,OAAO,EAACI;EAAG,CAAC;AAAA;AAAC,IAAIE,QAAQ,GAAC,SAASA,QAAQA,CAACC,KAAK,EAACC,GAAG,EAAC;EAAC,IAAIC,UAAU,GAACC,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAGE,SAAS,GAACF,SAAS,CAAC,CAAC,CAAC,GAACT,YAAY,CAACD,OAAO;EAAC,IAAIa,QAAQ,GAACJ,UAAU,CAACF,KAAK,EAACC,GAAG,CAAC;EAAC,IAAIM,IAAI,GAACC,MAAM,CAACP,GAAG,CAACM,IAAI,CAAC,GAACC,MAAM,CAACR,KAAK,CAACO,IAAI,CAAC;EAAC,IAAIE,eAAe,GAACH,QAAQ,GAACC,IAAI,GAAC,IAAI;EAAC,OAAOE,eAAe;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACX,QAAQ;AAACR,OAAO,CAACE,OAAO,GAACiB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}