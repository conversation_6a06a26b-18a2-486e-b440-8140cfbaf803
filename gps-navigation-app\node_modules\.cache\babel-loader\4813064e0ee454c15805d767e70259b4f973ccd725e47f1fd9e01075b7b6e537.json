{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getDistance = _interopRequireDefault(require(\"./getDistance\"));\nvar _robustAcos = _interopRequireDefault(require(\"./robustAcos\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar getDistanceFromLine = function getDistanceFromLine(point, lineStart, lineEnd) {\n  var accuracy = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n  var d1 = (0, _getDistance.default)(lineStart, point, accuracy);\n  var d2 = (0, _getDistance.default)(point, lineEnd, accuracy);\n  var d3 = (0, _getDistance.default)(lineStart, lineEnd, accuracy);\n  var alpha = Math.acos((0, _robustAcos.default)((d1 * d1 + d3 * d3 - d2 * d2) / (2 * d1 * d3)));\n  var beta = Math.acos((0, _robustAcos.default)((d2 * d2 + d3 * d3 - d1 * d1) / (2 * d2 * d3)));\n  if (alpha > Math.PI / 2) {\n    return d1;\n  }\n  if (beta > Math.PI / 2) {\n    return d2;\n  }\n  return Math.sin(alpha) * d1;\n};\nvar _default = getDistanceFromLine;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getDistance", "_interopRequireDefault", "require", "_robustAcos", "obj", "__esModule", "getDistanceFromLine", "point", "lineStart", "lineEnd", "accuracy", "arguments", "length", "undefined", "d1", "d2", "d3", "alpha", "Math", "acos", "beta", "PI", "sin", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getDistanceFromLine.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getDistance=_interopRequireDefault(require(\"./getDistance\"));var _robustAcos=_interopRequireDefault(require(\"./robustAcos\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var getDistanceFromLine=function getDistanceFromLine(point,lineStart,lineEnd){var accuracy=arguments.length>3&&arguments[3]!==undefined?arguments[3]:1;var d1=(0,_getDistance.default)(lineStart,point,accuracy);var d2=(0,_getDistance.default)(point,lineEnd,accuracy);var d3=(0,_getDistance.default)(lineStart,lineEnd,accuracy);var alpha=Math.acos((0,_robustAcos.default)((d1*d1+d3*d3-d2*d2)/(2*d1*d3)));var beta=Math.acos((0,_robustAcos.default)((d2*d2+d3*d3-d1*d1)/(2*d2*d3)));if(alpha>Math.PI/2){return d1}if(beta>Math.PI/2){return d2}return Math.sin(alpha)*d1};var _default=getDistanceFromLine;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,IAAIC,WAAW,GAACF,sBAAsB,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACG,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACL,OAAO,EAACK;EAAG,CAAC;AAAA;AAAC,IAAIE,mBAAmB,GAAC,SAASA,mBAAmBA,CAACC,KAAK,EAACC,SAAS,EAACC,OAAO,EAAC;EAAC,IAAIC,QAAQ,GAACC,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAGE,SAAS,GAACF,SAAS,CAAC,CAAC,CAAC,GAAC,CAAC;EAAC,IAAIG,EAAE,GAAC,CAAC,CAAC,EAACd,YAAY,CAACD,OAAO,EAAES,SAAS,EAACD,KAAK,EAACG,QAAQ,CAAC;EAAC,IAAIK,EAAE,GAAC,CAAC,CAAC,EAACf,YAAY,CAACD,OAAO,EAAEQ,KAAK,EAACE,OAAO,EAACC,QAAQ,CAAC;EAAC,IAAIM,EAAE,GAAC,CAAC,CAAC,EAAChB,YAAY,CAACD,OAAO,EAAES,SAAS,EAACC,OAAO,EAACC,QAAQ,CAAC;EAAC,IAAIO,KAAK,GAACC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAChB,WAAW,CAACJ,OAAO,EAAE,CAACe,EAAE,GAACA,EAAE,GAACE,EAAE,GAACA,EAAE,GAACD,EAAE,GAACA,EAAE,KAAG,CAAC,GAACD,EAAE,GAACE,EAAE,CAAC,CAAC,CAAC;EAAC,IAAII,IAAI,GAACF,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAChB,WAAW,CAACJ,OAAO,EAAE,CAACgB,EAAE,GAACA,EAAE,GAACC,EAAE,GAACA,EAAE,GAACF,EAAE,GAACA,EAAE,KAAG,CAAC,GAACC,EAAE,GAACC,EAAE,CAAC,CAAC,CAAC;EAAC,IAAGC,KAAK,GAACC,IAAI,CAACG,EAAE,GAAC,CAAC,EAAC;IAAC,OAAOP,EAAE;EAAA;EAAC,IAAGM,IAAI,GAACF,IAAI,CAACG,EAAE,GAAC,CAAC,EAAC;IAAC,OAAON,EAAE;EAAA;EAAC,OAAOG,IAAI,CAACI,GAAG,CAACL,KAAK,CAAC,GAACH,EAAE;AAAA,CAAC;AAAC,IAAIS,QAAQ,GAACjB,mBAAmB;AAACT,OAAO,CAACE,OAAO,GAACwB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}