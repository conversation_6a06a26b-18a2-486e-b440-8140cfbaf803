{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _orderByDistance = _interopRequireDefault(require(\"./orderByDistance\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar findNearest = function findNearest(point, coords) {\n  return (0, _orderByDistance.default)(point, coords)[0];\n};\nvar _default = findNearest;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_orderByDistance", "_interopRequireDefault", "require", "obj", "__esModule", "findNearest", "point", "coords", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/findNearest.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _orderByDistance=_interopRequireDefault(require(\"./orderByDistance\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var findNearest=function findNearest(point,coords){return(0,_orderByDistance.default)(point,coords)[0]};var _default=findNearest;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,gBAAgB,GAACC,sBAAsB,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACE,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACJ,OAAO,EAACI;EAAG,CAAC;AAAA;AAAC,IAAIE,WAAW,GAAC,SAASA,WAAWA,CAACC,KAAK,EAACC,MAAM,EAAC;EAAC,OAAM,CAAC,CAAC,EAACP,gBAAgB,CAACD,OAAO,EAAEO,KAAK,EAACC,MAAM,CAAC,CAAC,CAAC,CAAC;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACH,WAAW;AAACR,OAAO,CAACE,OAAO,GAACS,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}