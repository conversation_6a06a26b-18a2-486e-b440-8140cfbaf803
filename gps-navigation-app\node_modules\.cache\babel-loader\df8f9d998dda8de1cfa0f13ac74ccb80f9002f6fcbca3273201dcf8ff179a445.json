{"ast": null, "code": "import { createElementHook, createElementObject, createLayerHook, updateMediaOverlay } from '@react-leaflet/core';\nimport { SVGOverlay as LeafletSVGOverlay } from 'leaflet';\nimport { forwardRef, useImperativeHandle } from 'react';\nimport { createPortal } from 'react-dom';\nexport const useSVGOverlayElement = createElementHook(function createSVGOverlay(props, context) {\n  const {\n    attributes,\n    bounds,\n    ...options\n  } = props;\n  const container = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n  container.setAttribute('xmlns', 'http://www.w3.org/2000/svg');\n  if (attributes != null) {\n    for (const name of Object.keys(attributes)) {\n      container.setAttribute(name, attributes[name]);\n    }\n  }\n  const overlay = new LeafletSVGOverlay(container, bounds, options);\n  return createElementObject(overlay, context, container);\n}, updateMediaOverlay);\nexport const useSVGOverlay = createLayerHook(useSVGOverlayElement);\nfunction SVGOverlayComponent({\n  children,\n  ...options\n}, forwardedRef) {\n  const {\n    instance,\n    container\n  } = useSVGOverlay(options).current;\n  useImperativeHandle(forwardedRef, () => instance);\n  return container == null || children == null ? null : /*#__PURE__*/createPortal(children, container);\n}\nexport const SVGOverlay = /*#__PURE__*/forwardRef(SVGOverlayComponent);", "map": {"version": 3, "names": ["createElementHook", "createElementObject", "createLayerHook", "updateMediaOverlay", "SVGOverlay", "LeafletSVGOverlay", "forwardRef", "useImperativeHandle", "createPortal", "useSVGOverlayElement", "createSVGOverlay", "props", "context", "attributes", "bounds", "options", "container", "document", "createElementNS", "setAttribute", "name", "Object", "keys", "overlay", "useSVGOverlay", "SVGOverlayComponent", "children", "forwardedRef", "instance", "current"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/SVGOverlay.js"], "sourcesContent": ["import { createElementHook, createElementObject, createLayerHook, updateMediaOverlay } from '@react-leaflet/core';\nimport { SVGOverlay as LeafletSVGOverlay } from 'leaflet';\nimport { forwardRef, useImperativeHandle } from 'react';\nimport { createPortal } from 'react-dom';\nexport const useSVGOverlayElement = createElementHook(function createSVGOverlay(props, context) {\n    const { attributes, bounds, ...options } = props;\n    const container = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    container.setAttribute('xmlns', 'http://www.w3.org/2000/svg');\n    if (attributes != null) {\n        for (const name of Object.keys(attributes)){\n            container.setAttribute(name, attributes[name]);\n        }\n    }\n    const overlay = new LeafletSVGOverlay(container, bounds, options);\n    return createElementObject(overlay, context, container);\n}, updateMediaOverlay);\nexport const useSVGOverlay = createLayerHook(useSVGOverlayElement);\nfunction SVGOverlayComponent({ children, ...options }, forwardedRef) {\n    const { instance, container } = useSVGOverlay(options).current;\n    useImperativeHandle(forwardedRef, ()=>instance);\n    return container == null || children == null ? null : /*#__PURE__*/ createPortal(children, container);\n}\nexport const SVGOverlay = /*#__PURE__*/ forwardRef(SVGOverlayComponent);\n"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,qBAAqB;AACjH,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,SAAS;AACzD,SAASC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACvD,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,oBAAoB,GAAGT,iBAAiB,CAAC,SAASU,gBAAgBA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC5F,MAAM;IAAEC,UAAU;IAAEC,MAAM;IAAE,GAAGC;EAAQ,CAAC,GAAGJ,KAAK;EAChD,MAAMK,SAAS,GAAGC,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC;EAC/EF,SAAS,CAACG,YAAY,CAAC,OAAO,EAAE,4BAA4B,CAAC;EAC7D,IAAIN,UAAU,IAAI,IAAI,EAAE;IACpB,KAAK,MAAMO,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACT,UAAU,CAAC,EAAC;MACvCG,SAAS,CAACG,YAAY,CAACC,IAAI,EAAEP,UAAU,CAACO,IAAI,CAAC,CAAC;IAClD;EACJ;EACA,MAAMG,OAAO,GAAG,IAAIlB,iBAAiB,CAACW,SAAS,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjE,OAAOd,mBAAmB,CAACsB,OAAO,EAAEX,OAAO,EAAEI,SAAS,CAAC;AAC3D,CAAC,EAAEb,kBAAkB,CAAC;AACtB,OAAO,MAAMqB,aAAa,GAAGtB,eAAe,CAACO,oBAAoB,CAAC;AAClE,SAASgB,mBAAmBA,CAAC;EAAEC,QAAQ;EAAE,GAAGX;AAAQ,CAAC,EAAEY,YAAY,EAAE;EACjE,MAAM;IAAEC,QAAQ;IAAEZ;EAAU,CAAC,GAAGQ,aAAa,CAACT,OAAO,CAAC,CAACc,OAAO;EAC9DtB,mBAAmB,CAACoB,YAAY,EAAE,MAAIC,QAAQ,CAAC;EAC/C,OAAOZ,SAAS,IAAI,IAAI,IAAIU,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,aAAclB,YAAY,CAACkB,QAAQ,EAAEV,SAAS,CAAC;AACzG;AACA,OAAO,MAAMZ,UAAU,GAAG,aAAcE,UAAU,CAACmB,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}