{"ast": null, "code": "import { createControlComponent } from '@react-leaflet/core';\nimport { Control } from 'leaflet';\nexport const ScaleControl = createControlComponent(function createScaleControl(props) {\n  return new Control.Scale(props);\n});", "map": {"version": 3, "names": ["createControlComponent", "Control", "ScaleControl", "createScaleControl", "props", "Scale"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/ScaleControl.js"], "sourcesContent": ["import { createControlComponent } from '@react-leaflet/core';\nimport { Control } from 'leaflet';\nexport const ScaleControl = createControlComponent(function createScaleControl(props) {\n    return new Control.Scale(props);\n});\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAO,MAAMC,YAAY,GAAGF,sBAAsB,CAAC,SAASG,kBAAkBA,CAACC,KAAK,EAAE;EAClF,OAAO,IAAIH,OAAO,CAACI,KAAK,CAACD,KAAK,CAAC;AACnC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}