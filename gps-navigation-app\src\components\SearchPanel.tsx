import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
// Using Unicode symbols instead of react-icons for React 19 compatibility
import { LocationData, SearchResult, POI } from '../types/gps.types';

const PanelContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #2d2d2d;
  color: white;
`;

const Header = styled.div`
  padding: 20px;
  border-bottom: 1px solid #444;
  background-color: #1a1a1a;
`;

const Title = styled.h2`
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  text-align: center;
`;

const SearchContainer = styled.div`
  position: relative;
  margin-bottom: 16px;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 16px 20px 16px 50px;
  border: 2px solid #444;
  border-radius: 12px;
  background-color: #3d3d3d;
  color: white;
  font-size: 16px;
  outline: none;
  transition: all 0.3s ease;
  
  &:focus {
    border-color: #007bff;
    background-color: #4d4d4d;
  }
  
  &::placeholder {
    color: #aaa;
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #aaa;
  font-size: 20px;
  &::before {
    content: '🔍';
  }
`;

const QuickActions = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;
`;

const QuickActionButton = styled.button`
  padding: 16px;
  border: 1px solid #444;
  border-radius: 12px;
  background-color: #3d3d3d;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  &:hover {
    background-color: #007bff;
    border-color: #007bff;
  }
`;

const Content = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 0 20px 20px;
`;

const SectionTitle = styled.h3`
  margin: 20px 0 12px 0;
  font-size: 18px;
  font-weight: 500;
  color: #ccc;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ResultsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const ResultItem = styled.div`
  padding: 16px;
  border: 1px solid #444;
  border-radius: 12px;
  background-color: #3d3d3d;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #4d4d4d;
    border-color: #007bff;
  }
`;

const ResultName = styled.div`
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
  color: white;
`;

const ResultAddress = styled.div`
  font-size: 14px;
  color: #aaa;
  margin-bottom: 8px;
`;

const ResultMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #888;
`;

const CategoryChips = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
`;

const CategoryChip = styled.button<{ active?: boolean }>`
  padding: 8px 16px;
  border: 1px solid #444;
  border-radius: 20px;
  background-color: ${props => props.active ? '#007bff' : '#3d3d3d'};
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #007bff;
    border-color: #007bff;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: #888;
`;

interface SearchPanelProps {
  currentLocation: LocationData | null;
  onDestinationSelect: (location: LocationData) => void;
}

const SearchPanel: React.FC<SearchPanelProps> = ({
  currentLocation,
  onDestinationSelect
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = useState<SearchResult[]>([]);
  const [favorites, setFavorites] = useState<POI[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const categories = [
    'رستوران', 'پمپ بنزین', 'بیمارستان', 'بانک', 'مرکز خرید', 
    'پارکینگ', 'هتل', 'داروخانه', 'مدرسه', 'پارک'
  ];

  // Mock search function - in real app, this would call a geocoding API
  const performSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const mockResults: SearchResult[] = [
        {
          id: '1',
          name: 'میدان آزادی',
          address: 'تهران، میدان آزادی',
          location: { lat: 35.6958, lng: 51.3370 },
          type: 'poi' as const,
          category: 'نقاط دیدنی',
          distance: currentLocation ? 5000 : undefined
        },
        {
          id: '2',
          name: 'برج میلاد',
          address: 'تهران، برج میلاد',
          location: { lat: 35.7447, lng: 51.3753 },
          type: 'poi' as const,
          category: 'نقاط دیدنی',
          distance: currentLocation ? 8000 : undefined
        },
        {
          id: '3',
          name: 'بازار بزرگ تهران',
          address: 'تهران، بازار بزرگ',
          location: { lat: 35.6736, lng: 51.4208 },
          type: 'poi' as const,
          category: 'مرکز خرید',
          distance: currentLocation ? 3000 : undefined
        }
      ].filter(result =>
        result.name.includes(query) || result.address.includes(query)
      );
      
      setSearchResults(mockResults);
      setIsLoading(false);
    }, 500);
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, currentLocation]);

  // Load recent searches and favorites from localStorage
  useEffect(() => {
    const savedRecentSearches = localStorage.getItem('recentSearches');
    const savedFavorites = localStorage.getItem('favorites');
    
    if (savedRecentSearches) {
      setRecentSearches(JSON.parse(savedRecentSearches));
    }
    
    if (savedFavorites) {
      setFavorites(JSON.parse(savedFavorites));
    }
  }, []);

  const handleResultSelect = (result: SearchResult) => {
    const locationData: LocationData = {
      ...result.location,
      name: result.name,
      address: result.address,
      timestamp: Date.now()
    };
    
    // Add to recent searches
    const updatedRecent = [result, ...recentSearches.filter(r => r.id !== result.id)].slice(0, 10);
    setRecentSearches(updatedRecent);
    localStorage.setItem('recentSearches', JSON.stringify(updatedRecent));
    
    onDestinationSelect(locationData);
  };

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'home':
        // In a real app, this would use saved home location
        onDestinationSelect({
          lat: 35.6892,
          lng: 51.3890,
          name: 'خانه',
          timestamp: Date.now()
        });
        break;
      case 'work':
        // In a real app, this would use saved work location
        onDestinationSelect({
          lat: 35.7219,
          lng: 51.3347,
          name: 'محل کار',
          timestamp: Date.now()
        });
        break;
    }
  };

  return (
    <PanelContainer>
      <Header>
        <Title>جستجوی مقصد</Title>
        <SearchContainer>
          <SearchIcon />
          <SearchInput
            type="text"
            placeholder="جستجوی آدرس، مکان یا نقطه علاقه..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </SearchContainer>
        
        <QuickActions>
          <QuickActionButton onClick={() => handleQuickAction('home')}>
            🏠 خانه
          </QuickActionButton>
          <QuickActionButton onClick={() => handleQuickAction('work')}>
            🏢 محل کار
          </QuickActionButton>
        </QuickActions>
        
        <CategoryChips>
          {categories.map(category => (
            <CategoryChip
              key={category}
              active={selectedCategory === category}
              onClick={() => setSelectedCategory(selectedCategory === category ? '' : category)}
            >
              {category}
            </CategoryChip>
          ))}
        </CategoryChips>
      </Header>

      <Content>
        {searchQuery && searchResults.length > 0 && (
          <>
            <SectionTitle>
              🔍 نتایج جستجو
            </SectionTitle>
            <ResultsList>
              {searchResults.map(result => (
                <ResultItem key={result.id} onClick={() => handleResultSelect(result)}>
                  <ResultName>{result.name}</ResultName>
                  <ResultAddress>{result.address}</ResultAddress>
                  <ResultMeta>
                    <span>{result.category}</span>
                    {result.distance && (
                      <span>{(result.distance / 1000).toFixed(1)} کیلومتر</span>
                    )}
                  </ResultMeta>
                </ResultItem>
              ))}
            </ResultsList>
          </>
        )}

        {!searchQuery && recentSearches.length > 0 && (
          <>
            <SectionTitle>
              🕒 جستجوهای اخیر
            </SectionTitle>
            <ResultsList>
              {recentSearches.slice(0, 5).map(result => (
                <ResultItem key={result.id} onClick={() => handleResultSelect(result)}>
                  <ResultName>{result.name}</ResultName>
                  <ResultAddress>{result.address}</ResultAddress>
                  <ResultMeta>
                    <span>{result.category}</span>
                  </ResultMeta>
                </ResultItem>
              ))}
            </ResultsList>
          </>
        )}

        {!searchQuery && favorites.length > 0 && (
          <>
            <SectionTitle>
              ⭐ علاقه‌مندی‌ها
            </SectionTitle>
            <ResultsList>
              {favorites.slice(0, 5).map(poi => (
                <ResultItem 
                  key={poi.id} 
                  onClick={() => handleResultSelect({
                    id: poi.id,
                    name: poi.name,
                    address: poi.location.address || '',
                    location: poi.location,
                    type: 'poi',
                    category: poi.category
                  })}
                >
                  <ResultName>{poi.name}</ResultName>
                  <ResultAddress>{poi.location.address}</ResultAddress>
                  <ResultMeta>
                    <span>{poi.category}</span>
                    {poi.rating && (
                      <span>⭐ {poi.rating.toFixed(1)}</span>
                    )}
                  </ResultMeta>
                </ResultItem>
              ))}
            </ResultsList>
          </>
        )}

        {!searchQuery && recentSearches.length === 0 && favorites.length === 0 && (
          <EmptyState>
            <div style={{ fontSize: '48px', marginBottom: '16px', opacity: 0.5 }}>📍</div>
            <div>برای شروع، مقصد خود را جستجو کنید</div>
          </EmptyState>
        )}

        {searchQuery && searchResults.length === 0 && !isLoading && (
          <EmptyState>
            <div style={{ fontSize: '48px', marginBottom: '16px', opacity: 0.5 }}>🔍</div>
            <div>نتیجه‌ای یافت نشد</div>
            <div style={{ fontSize: '14px', marginTop: '8px' }}>
              لطفاً کلمات کلیدی دیگری امتحان کنید
            </div>
          </EmptyState>
        )}
      </Content>
    </PanelContainer>
  );
};

export default SearchPanel;
