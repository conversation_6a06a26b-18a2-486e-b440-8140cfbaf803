{"ast": null, "code": "import { useEffect, useRef } from 'react';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { useLayerLifecycle } from './layer.js';\nimport { withPane } from './pane.js';\nexport function usePathOptions(element, props) {\n  const optionsRef = useRef(undefined);\n  useEffect(function updatePathOptions() {\n    if (props.pathOptions !== optionsRef.current) {\n      const options = props.pathOptions ?? {};\n      element.instance.setStyle(options);\n      optionsRef.current = options;\n    }\n  }, [element, props]);\n}\nexport function createPathHook(useElement) {\n  return function usePath(props) {\n    const context = useLeafletContext();\n    const elementRef = useElement(withPane(props, context), context);\n    useEventHandlers(elementRef.current, props.eventHandlers);\n    useLayerLifecycle(elementRef.current, context);\n    usePathOptions(elementRef.current, props);\n    return elementRef;\n  };\n}", "map": {"version": 3, "names": ["useEffect", "useRef", "useLeafletContext", "useEventHandlers", "useLayerLifecycle", "with<PERSON>ane", "usePathOptions", "element", "props", "optionsRef", "undefined", "updatePathOptions", "pathOptions", "current", "options", "instance", "setStyle", "createPathHook", "useElement", "usePath", "context", "elementRef", "eventHandlers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@react-leaflet/core/lib/path.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { useLayerLifecycle } from './layer.js';\nimport { withPane } from './pane.js';\nexport function usePathOptions(element, props) {\n    const optionsRef = useRef(undefined);\n    useEffect(function updatePathOptions() {\n        if (props.pathOptions !== optionsRef.current) {\n            const options = props.pathOptions ?? {};\n            element.instance.setStyle(options);\n            optionsRef.current = options;\n        }\n    }, [\n        element,\n        props\n    ]);\n}\nexport function createPathHook(useElement) {\n    return function usePath(props) {\n        const context = useLeafletContext();\n        const elementRef = useElement(withPane(props, context), context);\n        useEventHandlers(elementRef.current, props.eventHandlers);\n        useLayerLifecycle(elementRef.current, context);\n        usePathOptions(elementRef.current, props);\n        return elementRef;\n    };\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,SAASC,iBAAiB,QAAQ,cAAc;AAChD,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,iBAAiB,QAAQ,YAAY;AAC9C,SAASC,QAAQ,QAAQ,WAAW;AACpC,OAAO,SAASC,cAAcA,CAACC,OAAO,EAAEC,KAAK,EAAE;EAC3C,MAAMC,UAAU,GAAGR,MAAM,CAACS,SAAS,CAAC;EACpCV,SAAS,CAAC,SAASW,iBAAiBA,CAAA,EAAG;IACnC,IAAIH,KAAK,CAACI,WAAW,KAAKH,UAAU,CAACI,OAAO,EAAE;MAC1C,MAAMC,OAAO,GAAGN,KAAK,CAACI,WAAW,IAAI,CAAC,CAAC;MACvCL,OAAO,CAACQ,QAAQ,CAACC,QAAQ,CAACF,OAAO,CAAC;MAClCL,UAAU,CAACI,OAAO,GAAGC,OAAO;IAChC;EACJ,CAAC,EAAE,CACCP,OAAO,EACPC,KAAK,CACR,CAAC;AACN;AACA,OAAO,SAASS,cAAcA,CAACC,UAAU,EAAE;EACvC,OAAO,SAASC,OAAOA,CAACX,KAAK,EAAE;IAC3B,MAAMY,OAAO,GAAGlB,iBAAiB,CAAC,CAAC;IACnC,MAAMmB,UAAU,GAAGH,UAAU,CAACb,QAAQ,CAACG,KAAK,EAAEY,OAAO,CAAC,EAAEA,OAAO,CAAC;IAChEjB,gBAAgB,CAACkB,UAAU,CAACR,OAAO,EAAEL,KAAK,CAACc,aAAa,CAAC;IACzDlB,iBAAiB,CAACiB,UAAU,CAACR,OAAO,EAAEO,OAAO,CAAC;IAC9Cd,cAAc,CAACe,UAAU,CAACR,OAAO,EAAEL,KAAK,CAAC;IACzC,OAAOa,UAAU;EACrB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}