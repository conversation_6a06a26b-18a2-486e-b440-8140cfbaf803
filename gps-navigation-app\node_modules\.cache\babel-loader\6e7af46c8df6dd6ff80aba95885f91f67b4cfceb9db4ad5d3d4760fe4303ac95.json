{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\GPS\\\\gps-navigation-app\\\\src\\\\components\\\\MapComponent.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMap, useMapEvents } from 'react-leaflet';\nimport L from 'leaflet';\nimport styled from 'styled-components';\nimport 'leaflet/dist/leaflet.css';\n\n// Fix for default markers in react-leaflet\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),\n  iconUrl: require('leaflet/dist/images/marker-icon.png'),\n  shadowUrl: require('leaflet/dist/images/marker-shadow.png')\n});\nconst MapWrapper = styled.div`\n  height: 100%;\n  width: 100%;\n  position: relative;\n  \n  .leaflet-container {\n    height: 100%;\n    width: 100%;\n    background-color: #1a1a1a;\n  }\n  \n  .leaflet-control-zoom {\n    border: none;\n    border-radius: 8px;\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  }\n  \n  .leaflet-control-zoom a {\n    background-color: #2d2d2d;\n    color: white;\n    border: 1px solid #444;\n    font-size: 18px;\n    line-height: 26px;\n    \n    &:hover {\n      background-color: #007bff;\n      color: white;\n    }\n  }\n`;\n_c = MapWrapper;\nconst MapControls = styled.div`\n  position: absolute;\n  top: 20px;\n  left: 20px;\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n`;\n_c2 = MapControls;\nconst ControlButton = styled.button`\n  background-color: #2d2d2d;\n  color: white;\n  border: 1px solid #444;\n  border-radius: 8px;\n  padding: 12px;\n  cursor: pointer;\n  font-size: 16px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background-color: #007bff;\n  }\n  \n  &:active {\n    transform: scale(0.95);\n  }\n`;\n_c3 = ControlButton;\nconst LocationInfo = styled.div`\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n  background-color: rgba(45, 45, 45, 0.9);\n  color: white;\n  padding: 12px 16px;\n  border-radius: 8px;\n  font-size: 14px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  max-width: 300px;\n`;\n\n// Custom icons\n_c4 = LocationInfo;\nconst currentLocationIcon = new L.Icon({\n  iconUrl: 'data:image/svg+xml;base64,' + btoa(`\n    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n      <circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"#007bff\" stroke=\"white\" stroke-width=\"2\"/>\n      <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"white\"/>\n    </svg>\n  `),\n  iconSize: [24, 24],\n  iconAnchor: [12, 12],\n  popupAnchor: [0, -12]\n});\nconst destinationIcon = new L.Icon({\n  iconUrl: 'data:image/svg+xml;base64,' + btoa(`\n    <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z\" fill=\"#dc3545\" stroke=\"white\" stroke-width=\"1\"/>\n      <circle cx=\"12\" cy=\"9\" r=\"2.5\" fill=\"white\"/>\n    </svg>\n  `),\n  iconSize: [32, 32],\n  iconAnchor: [16, 32],\n  popupAnchor: [0, -32]\n});\n// Component to handle map events\nconst MapEventHandler = ({\n  onLocationSelect\n}) => {\n  _s();\n  useMapEvents({\n    click: e => {\n      const {\n        lat,\n        lng\n      } = e.latlng;\n      onLocationSelect({\n        lat,\n        lng,\n        timestamp: Date.now()\n      });\n    }\n  });\n  return null;\n};\n\n// Component to handle map centering\n_s(MapEventHandler, \"Ld/tk8Iz8AdZhC1l7acENaOEoCo=\", false, function () {\n  return [useMapEvents];\n});\n_c5 = MapEventHandler;\nconst MapCenter = ({\n  center,\n  zoom = 15\n}) => {\n  _s2();\n  const map = useMap();\n  useEffect(() => {\n    map.setView(center, zoom);\n  }, [map, center, zoom]);\n  return null;\n};\n_s2(MapCenter, \"IoceErwr5KVGS9kN4RQ1bOkYMAg=\", false, function () {\n  return [useMap];\n});\n_c6 = MapCenter;\nconst MapComponent = ({\n  currentLocation,\n  destination,\n  route,\n  isNavigating,\n  onLocationSelect\n}) => {\n  _s3();\n  const [mapTheme, setMapTheme] = useState('dark');\n  const [showTraffic, setShowTraffic] = useState(false);\n  const mapRef = useRef(null);\n  const defaultCenter = [35.6892, 51.3890]; // Tehran, Iran\n  const mapCenter = currentLocation ? [currentLocation.lat, currentLocation.lng] : defaultCenter;\n  const getTileLayerUrl = () => {\n    switch (mapTheme) {\n      case 'light':\n        return 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';\n      case 'satellite':\n        return 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}';\n      case 'dark':\n      default:\n        return 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png';\n    }\n  };\n  const handleCenterOnLocation = () => {\n    if (currentLocation && mapRef.current) {\n      mapRef.current.setView([currentLocation.lat, currentLocation.lng], 16);\n    }\n  };\n  const toggleMapTheme = () => {\n    const themes = ['light', 'dark', 'satellite'];\n    const currentIndex = themes.indexOf(mapTheme);\n    const nextIndex = (currentIndex + 1) % themes.length;\n    setMapTheme(themes[nextIndex]);\n  };\n  return /*#__PURE__*/_jsxDEV(MapWrapper, {\n    children: [/*#__PURE__*/_jsxDEV(MapContainer, {\n      center: mapCenter,\n      zoom: 15,\n      style: {\n        height: '100%',\n        width: '100%'\n      },\n      ref: mapRef,\n      zoomControl: true,\n      attributionControl: false,\n      children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n        url: getTileLayerUrl(),\n        attribution: \"\\xA9 OpenStreetMap contributors\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MapEventHandler, {\n        onLocationSelect: onLocationSelect\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), currentLocation && /*#__PURE__*/_jsxDEV(MapCenter, {\n        center: [currentLocation.lat, currentLocation.lng]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this), currentLocation && /*#__PURE__*/_jsxDEV(Marker, {\n        position: [currentLocation.lat, currentLocation.lng],\n        icon: currentLocationIcon,\n        children: /*#__PURE__*/_jsxDEV(Popup, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u0645\\u0648\\u0642\\u0639\\u06CC\\u062A \\u0641\\u0639\\u0644\\u06CC \\u0634\\u0645\\u0627\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 49\n            }, this), \"\\u0639\\u0631\\u0636 \\u062C\\u063A\\u0631\\u0627\\u0641\\u06CC\\u0627\\u06CC\\u06CC: \", currentLocation.lat.toFixed(6), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 64\n            }, this), \"\\u0637\\u0648\\u0644 \\u062C\\u063A\\u0631\\u0627\\u0641\\u06CC\\u0627\\u06CC\\u06CC: \", currentLocation.lng.toFixed(6), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 64\n            }, this), currentLocation.accuracy && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [\"\\u062F\\u0642\\u062A: \", currentLocation.accuracy.toFixed(0), \" \\u0645\\u062A\\u0631\"]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this), destination && /*#__PURE__*/_jsxDEV(Marker, {\n        position: [destination.lat, destination.lng],\n        icon: destinationIcon,\n        children: /*#__PURE__*/_jsxDEV(Popup, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u0645\\u0642\\u0635\\u062F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 38\n            }, this), destination.name || 'موقعیت انتخاب شده', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 58\n            }, this), destination.address && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [destination.address, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 64\n              }, this)]\n            }, void 0, true), \"\\u0639\\u0631\\u0636 \\u062C\\u063A\\u0631\\u0627\\u0641\\u06CC\\u0627\\u06CC\\u06CC: \", destination.lat.toFixed(6), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 60\n            }, this), \"\\u0637\\u0648\\u0644 \\u062C\\u063A\\u0631\\u0627\\u0641\\u06CC\\u0627\\u06CC\\u06CC: \", destination.lng.toFixed(6)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this), route && route.coordinates && /*#__PURE__*/_jsxDEV(Polyline, {\n        positions: route.coordinates.map(coord => [coord[1], coord[0]]),\n        color: \"#007bff\",\n        weight: 6,\n        opacity: 0.8\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MapControls, {\n      children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n        onClick: handleCenterOnLocation,\n        title: \"\\u0645\\u0631\\u06A9\\u0632 \\u06A9\\u0631\\u062F\\u0646 \\u0631\\u0648\\u06CC \\u0645\\u0648\\u0642\\u0639\\u06CC\\u062A \\u0641\\u0639\\u0644\\u06CC\",\n        children: \"\\uD83D\\uDCCD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n        onClick: toggleMapTheme,\n        title: \"\\u062A\\u063A\\u06CC\\u06CC\\u0631 \\u062A\\u0645 \\u0646\\u0642\\u0634\\u0647\",\n        children: \"\\uD83C\\uDF13\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n        onClick: () => setShowTraffic(!showTraffic),\n        title: \"\\u0646\\u0645\\u0627\\u06CC\\u0634 \\u062A\\u0631\\u0627\\u0641\\u06CC\\u06A9\",\n        children: \"\\uD83D\\uDEA6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this), currentLocation && /*#__PURE__*/_jsxDEV(LocationInfo, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"\\u0645\\u0648\\u0642\\u0639\\u06CC\\u062A \\u0641\\u0639\\u0644\\u06CC:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 16\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\u0639\\u0631\\u0636: \", currentLocation.lat.toFixed(6)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\u0637\\u0648\\u0644: \", currentLocation.lng.toFixed(6)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this), currentLocation.accuracy && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\u062F\\u0642\\u062A: \", currentLocation.accuracy.toFixed(0), \"\\u0645\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 191,\n    columnNumber: 5\n  }, this);\n};\n_s3(MapComponent, \"B6WW/6egT/6dDfrQh0iDm+Y4oz4=\");\n_c7 = MapComponent;\nexport default MapComponent;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"MapWrapper\");\n$RefreshReg$(_c2, \"MapControls\");\n$RefreshReg$(_c3, \"ControlButton\");\n$RefreshReg$(_c4, \"LocationInfo\");\n$RefreshReg$(_c5, \"MapEventHandler\");\n$RefreshReg$(_c6, \"MapCenter\");\n$RefreshReg$(_c7, \"MapComponent\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "Polyline", "useMap", "useMapEvents", "L", "styled", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "require", "iconUrl", "shadowUrl", "MapWrapper", "div", "_c", "MapControls", "_c2", "ControlButton", "button", "_c3", "LocationInfo", "_c4", "currentLocationIcon", "btoa", "iconSize", "iconAnchor", "popupAnchor", "destinationIcon", "MapEventHandler", "onLocationSelect", "_s", "click", "e", "lat", "lng", "latlng", "timestamp", "Date", "now", "_c5", "MapCenter", "center", "zoom", "_s2", "map", "<PERSON><PERSON><PERSON><PERSON>", "_c6", "MapComponent", "currentLocation", "destination", "route", "isNavigating", "_s3", "mapTheme", "setMapTheme", "showTraffic", "setShow<PERSON>raffic", "mapRef", "defaultCenter", "mapCenter", "getTileLayerUrl", "handleCenterOnLocation", "current", "toggleMapTheme", "themes", "currentIndex", "indexOf", "nextIndex", "length", "children", "style", "height", "width", "ref", "zoomControl", "attributionControl", "url", "attribution", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "icon", "toFixed", "accuracy", "name", "address", "coordinates", "positions", "coord", "color", "weight", "opacity", "onClick", "title", "_c7", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/src/components/MapComponent.tsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMap, useMapEvents } from 'react-leaflet';\nimport L from 'leaflet';\nimport styled from 'styled-components';\nimport { LocationData, RouteData } from '../types/gps.types';\nimport 'leaflet/dist/leaflet.css';\n\n// Fix for default markers in react-leaflet\ndelete (L.Icon.Default.prototype as any)._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),\n  iconUrl: require('leaflet/dist/images/marker-icon.png'),\n  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),\n});\n\nconst MapWrapper = styled.div`\n  height: 100%;\n  width: 100%;\n  position: relative;\n  \n  .leaflet-container {\n    height: 100%;\n    width: 100%;\n    background-color: #1a1a1a;\n  }\n  \n  .leaflet-control-zoom {\n    border: none;\n    border-radius: 8px;\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  }\n  \n  .leaflet-control-zoom a {\n    background-color: #2d2d2d;\n    color: white;\n    border: 1px solid #444;\n    font-size: 18px;\n    line-height: 26px;\n    \n    &:hover {\n      background-color: #007bff;\n      color: white;\n    }\n  }\n`;\n\nconst MapControls = styled.div`\n  position: absolute;\n  top: 20px;\n  left: 20px;\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n`;\n\nconst ControlButton = styled.button`\n  background-color: #2d2d2d;\n  color: white;\n  border: 1px solid #444;\n  border-radius: 8px;\n  padding: 12px;\n  cursor: pointer;\n  font-size: 16px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background-color: #007bff;\n  }\n  \n  &:active {\n    transform: scale(0.95);\n  }\n`;\n\nconst LocationInfo = styled.div`\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n  background-color: rgba(45, 45, 45, 0.9);\n  color: white;\n  padding: 12px 16px;\n  border-radius: 8px;\n  font-size: 14px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  max-width: 300px;\n`;\n\n// Custom icons\nconst currentLocationIcon = new L.Icon({\n  iconUrl: 'data:image/svg+xml;base64,' + btoa(`\n    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n      <circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"#007bff\" stroke=\"white\" stroke-width=\"2\"/>\n      <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"white\"/>\n    </svg>\n  `),\n  iconSize: [24, 24],\n  iconAnchor: [12, 12],\n  popupAnchor: [0, -12],\n});\n\nconst destinationIcon = new L.Icon({\n  iconUrl: 'data:image/svg+xml;base64,' + btoa(`\n    <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z\" fill=\"#dc3545\" stroke=\"white\" stroke-width=\"1\"/>\n      <circle cx=\"12\" cy=\"9\" r=\"2.5\" fill=\"white\"/>\n    </svg>\n  `),\n  iconSize: [32, 32],\n  iconAnchor: [16, 32],\n  popupAnchor: [0, -32],\n});\n\ninterface MapComponentProps {\n  currentLocation: LocationData | null;\n  destination: LocationData | null;\n  route: RouteData | null;\n  isNavigating: boolean;\n  onLocationSelect: (location: LocationData) => void;\n}\n\n// Component to handle map events\nconst MapEventHandler: React.FC<{ onLocationSelect: (location: LocationData) => void }> = ({ onLocationSelect }) => {\n  useMapEvents({\n    click: (e) => {\n      const { lat, lng } = e.latlng;\n      onLocationSelect({\n        lat,\n        lng,\n        timestamp: Date.now()\n      });\n    }\n  });\n  return null;\n};\n\n// Component to handle map centering\nconst MapCenter: React.FC<{ center: [number, number]; zoom?: number }> = ({ center, zoom = 15 }) => {\n  const map = useMap();\n  \n  useEffect(() => {\n    map.setView(center, zoom);\n  }, [map, center, zoom]);\n  \n  return null;\n};\n\nconst MapComponent: React.FC<MapComponentProps> = ({\n  currentLocation,\n  destination,\n  route,\n  isNavigating,\n  onLocationSelect\n}) => {\n  const [mapTheme, setMapTheme] = useState<'light' | 'dark' | 'satellite'>('dark');\n  const [showTraffic, setShowTraffic] = useState(false);\n  const mapRef = useRef<L.Map | null>(null);\n\n  const defaultCenter: [number, number] = [35.6892, 51.3890]; // Tehran, Iran\n  const mapCenter: [number, number] = currentLocation \n    ? [currentLocation.lat, currentLocation.lng] \n    : defaultCenter;\n\n  const getTileLayerUrl = () => {\n    switch (mapTheme) {\n      case 'light':\n        return 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';\n      case 'satellite':\n        return 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}';\n      case 'dark':\n      default:\n        return 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png';\n    }\n  };\n\n  const handleCenterOnLocation = () => {\n    if (currentLocation && mapRef.current) {\n      mapRef.current.setView([currentLocation.lat, currentLocation.lng], 16);\n    }\n  };\n\n  const toggleMapTheme = () => {\n    const themes: ('light' | 'dark' | 'satellite')[] = ['light', 'dark', 'satellite'];\n    const currentIndex = themes.indexOf(mapTheme);\n    const nextIndex = (currentIndex + 1) % themes.length;\n    setMapTheme(themes[nextIndex]);\n  };\n\n  return (\n    <MapWrapper>\n      <MapContainer\n        center={mapCenter}\n        zoom={15}\n        style={{ height: '100%', width: '100%' }}\n        ref={mapRef}\n        zoomControl={true}\n        attributionControl={false}\n      >\n        <TileLayer\n          url={getTileLayerUrl()}\n          attribution='&copy; OpenStreetMap contributors'\n        />\n        \n        <MapEventHandler onLocationSelect={onLocationSelect} />\n        \n        {currentLocation && (\n          <MapCenter center={[currentLocation.lat, currentLocation.lng]} />\n        )}\n        \n        {currentLocation && (\n          <Marker \n            position={[currentLocation.lat, currentLocation.lng]} \n            icon={currentLocationIcon}\n          >\n            <Popup>\n              <div>\n                <strong>موقعیت فعلی شما</strong><br />\n                عرض جغرافیایی: {currentLocation.lat.toFixed(6)}<br />\n                طول جغرافیایی: {currentLocation.lng.toFixed(6)}<br />\n                {currentLocation.accuracy && (\n                  <>دقت: {currentLocation.accuracy.toFixed(0)} متر</>\n                )}\n              </div>\n            </Popup>\n          </Marker>\n        )}\n        \n        {destination && (\n          <Marker \n            position={[destination.lat, destination.lng]} \n            icon={destinationIcon}\n          >\n            <Popup>\n              <div>\n                <strong>مقصد</strong><br />\n                {destination.name || 'موقعیت انتخاب شده'}<br />\n                {destination.address && <>{destination.address}<br /></>}\n                عرض جغرافیایی: {destination.lat.toFixed(6)}<br />\n                طول جغرافیایی: {destination.lng.toFixed(6)}\n              </div>\n            </Popup>\n          </Marker>\n        )}\n        \n        {route && route.coordinates && (\n          <Polyline\n            positions={route.coordinates.map(coord => [coord[1], coord[0]])}\n            color=\"#007bff\"\n            weight={6}\n            opacity={0.8}\n          />\n        )}\n      </MapContainer>\n      \n      <MapControls>\n        <ControlButton onClick={handleCenterOnLocation} title=\"مرکز کردن روی موقعیت فعلی\">\n          📍\n        </ControlButton>\n        <ControlButton onClick={toggleMapTheme} title=\"تغییر تم نقشه\">\n          🌓\n        </ControlButton>\n        <ControlButton onClick={() => setShowTraffic(!showTraffic)} title=\"نمایش ترافیک\">\n          🚦\n        </ControlButton>\n      </MapControls>\n      \n      {currentLocation && (\n        <LocationInfo>\n          <div><strong>موقعیت فعلی:</strong></div>\n          <div>عرض: {currentLocation.lat.toFixed(6)}</div>\n          <div>طول: {currentLocation.lng.toFixed(6)}</div>\n          {currentLocation.accuracy && (\n            <div>دقت: {currentLocation.accuracy.toFixed(0)}م</div>\n          )}\n        </LocationInfo>\n      )}\n    </MapWrapper>\n  );\n};\n\nexport default MapComponent;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,YAAY,QAAQ,eAAe;AACtG,OAAOC,CAAC,MAAM,SAAS;AACvB,OAAOC,MAAM,MAAM,mBAAmB;AAEtC,OAAO,0BAA0B;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,OAAQL,CAAC,CAACM,IAAI,CAACC,OAAO,CAACC,SAAS,CAASC,WAAW;AACpDT,CAAC,CAACM,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAEC,OAAO,CAAC,wCAAwC,CAAC;EAChEC,OAAO,EAAED,OAAO,CAAC,qCAAqC,CAAC;EACvDE,SAAS,EAAEF,OAAO,CAAC,uCAAuC;AAC5D,CAAC,CAAC;AAEF,MAAMG,UAAU,GAAGd,MAAM,CAACe,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GA7BIF,UAAU;AA+BhB,MAAMG,WAAW,GAAGjB,MAAM,CAACe,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GARID,WAAW;AAUjB,MAAME,aAAa,GAAGnB,MAAM,CAACoB,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAlBIF,aAAa;AAoBnB,MAAMG,YAAY,GAAGtB,MAAM,CAACe,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAQ,GAAA,GAbMD,YAAY;AAclB,MAAME,mBAAmB,GAAG,IAAIzB,CAAC,CAACM,IAAI,CAAC;EACrCO,OAAO,EAAE,4BAA4B,GAAGa,IAAI,CAAC;AAC/C;AACA;AACA;AACA;AACA,GAAG,CAAC;EACFC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EAClBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EACpBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;AACtB,CAAC,CAAC;AAEF,MAAMC,eAAe,GAAG,IAAI9B,CAAC,CAACM,IAAI,CAAC;EACjCO,OAAO,EAAE,4BAA4B,GAAGa,IAAI,CAAC;AAC/C;AACA;AACA;AACA;AACA,GAAG,CAAC;EACFC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EAClBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EACpBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;AACtB,CAAC,CAAC;AAUF;AACA,MAAME,eAAiF,GAAGA,CAAC;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAClHlC,YAAY,CAAC;IACXmC,KAAK,EAAGC,CAAC,IAAK;MACZ,MAAM;QAAEC,GAAG;QAAEC;MAAI,CAAC,GAAGF,CAAC,CAACG,MAAM;MAC7BN,gBAAgB,CAAC;QACfI,GAAG;QACHC,GAAG;QACHE,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;MACtB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAO,IAAI;AACb,CAAC;;AAED;AAAAR,EAAA,CAdMF,eAAiF;EAAA,QACrFhC,YAAY;AAAA;AAAA2C,GAAA,GADRX,eAAiF;AAevF,MAAMY,SAAgE,GAAGA,CAAC;EAAEC,MAAM;EAAEC,IAAI,GAAG;AAAG,CAAC,KAAK;EAAAC,GAAA;EAClG,MAAMC,GAAG,GAAGjD,MAAM,CAAC,CAAC;EAEpBR,SAAS,CAAC,MAAM;IACdyD,GAAG,CAACC,OAAO,CAACJ,MAAM,EAAEC,IAAI,CAAC;EAC3B,CAAC,EAAE,CAACE,GAAG,EAAEH,MAAM,EAAEC,IAAI,CAAC,CAAC;EAEvB,OAAO,IAAI;AACb,CAAC;AAACC,GAAA,CARIH,SAAgE;EAAA,QACxD7C,MAAM;AAAA;AAAAmD,GAAA,GADdN,SAAgE;AAUtE,MAAMO,YAAyC,GAAGA,CAAC;EACjDC,eAAe;EACfC,WAAW;EACXC,KAAK;EACLC,YAAY;EACZtB;AACF,CAAC,KAAK;EAAAuB,GAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,QAAQ,CAAiC,MAAM,CAAC;EAChF,MAAM,CAACkE,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMoE,MAAM,GAAGrE,MAAM,CAAe,IAAI,CAAC;EAEzC,MAAMsE,aAA+B,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;EAC5D,MAAMC,SAA2B,GAAGX,eAAe,GAC/C,CAACA,eAAe,CAACf,GAAG,EAAEe,eAAe,CAACd,GAAG,CAAC,GAC1CwB,aAAa;EAEjB,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,QAAQP,QAAQ;MACd,KAAK,OAAO;QACV,OAAO,oDAAoD;MAC7D,KAAK,WAAW;QACd,OAAO,+FAA+F;MACxG,KAAK,MAAM;MACX;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;EAED,MAAMQ,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAIb,eAAe,IAAIS,MAAM,CAACK,OAAO,EAAE;MACrCL,MAAM,CAACK,OAAO,CAACjB,OAAO,CAAC,CAACG,eAAe,CAACf,GAAG,EAAEe,eAAe,CAACd,GAAG,CAAC,EAAE,EAAE,CAAC;IACxE;EACF,CAAC;EAED,MAAM6B,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,MAA0C,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,WAAW,CAAC;IACjF,MAAMC,YAAY,GAAGD,MAAM,CAACE,OAAO,CAACb,QAAQ,CAAC;IAC7C,MAAMc,SAAS,GAAG,CAACF,YAAY,GAAG,CAAC,IAAID,MAAM,CAACI,MAAM;IACpDd,WAAW,CAACU,MAAM,CAACG,SAAS,CAAC,CAAC;EAChC,CAAC;EAED,oBACEnE,OAAA,CAACY,UAAU;IAAAyD,QAAA,gBACTrE,OAAA,CAACV,YAAY;MACXmD,MAAM,EAAEkB,SAAU;MAClBjB,IAAI,EAAE,EAAG;MACT4B,KAAK,EAAE;QAAEC,MAAM,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAO,CAAE;MACzCC,GAAG,EAAEhB,MAAO;MACZiB,WAAW,EAAE,IAAK;MAClBC,kBAAkB,EAAE,KAAM;MAAAN,QAAA,gBAE1BrE,OAAA,CAACT,SAAS;QACRqF,GAAG,EAAEhB,eAAe,CAAC,CAAE;QACvBiB,WAAW,EAAC;MAAmC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eAEFjF,OAAA,CAAC4B,eAAe;QAACC,gBAAgB,EAAEA;MAAiB;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEtDjC,eAAe,iBACdhD,OAAA,CAACwC,SAAS;QAACC,MAAM,EAAE,CAACO,eAAe,CAACf,GAAG,EAAEe,eAAe,CAACd,GAAG;MAAE;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACjE,EAEAjC,eAAe,iBACdhD,OAAA,CAACR,MAAM;QACL0F,QAAQ,EAAE,CAAClC,eAAe,CAACf,GAAG,EAAEe,eAAe,CAACd,GAAG,CAAE;QACrDiD,IAAI,EAAE7D,mBAAoB;QAAA+C,QAAA,eAE1BrE,OAAA,CAACP,KAAK;UAAA4E,QAAA,eACJrE,OAAA;YAAAqE,QAAA,gBACErE,OAAA;cAAAqE,QAAA,EAAQ;YAAe;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAjF,OAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,+EACvB,EAACjC,eAAe,CAACf,GAAG,CAACmD,OAAO,CAAC,CAAC,CAAC,eAACpF,OAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,+EACtC,EAACjC,eAAe,CAACd,GAAG,CAACkD,OAAO,CAAC,CAAC,CAAC,eAACpF,OAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACpDjC,eAAe,CAACqC,QAAQ,iBACvBrF,OAAA,CAAAE,SAAA;cAAAmE,QAAA,GAAE,sBAAK,EAACrB,eAAe,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,qBAAI;YAAA,eAAE,CACnD;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACT,EAEAhC,WAAW,iBACVjD,OAAA,CAACR,MAAM;QACL0F,QAAQ,EAAE,CAACjC,WAAW,CAAChB,GAAG,EAAEgB,WAAW,CAACf,GAAG,CAAE;QAC7CiD,IAAI,EAAExD,eAAgB;QAAA0C,QAAA,eAEtBrE,OAAA,CAACP,KAAK;UAAA4E,QAAA,eACJrE,OAAA;YAAAqE,QAAA,gBACErE,OAAA;cAAAqE,QAAA,EAAQ;YAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAjF,OAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAC1BhC,WAAW,CAACqC,IAAI,IAAI,mBAAmB,eAACtF,OAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAC9ChC,WAAW,CAACsC,OAAO,iBAAIvF,OAAA,CAAAE,SAAA;cAAAmE,QAAA,GAAGpB,WAAW,CAACsC,OAAO,eAACvF,OAAA;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eAAE,CAAC,EAAC,6EAC1C,EAAChC,WAAW,CAAChB,GAAG,CAACmD,OAAO,CAAC,CAAC,CAAC,eAACpF,OAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,+EAClC,EAAChC,WAAW,CAACf,GAAG,CAACkD,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACT,EAEA/B,KAAK,IAAIA,KAAK,CAACsC,WAAW,iBACzBxF,OAAA,CAACN,QAAQ;QACP+F,SAAS,EAAEvC,KAAK,CAACsC,WAAW,CAAC5C,GAAG,CAAC8C,KAAK,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAE;QAChEC,KAAK,EAAC,SAAS;QACfC,MAAM,EAAE,CAAE;QACVC,OAAO,EAAE;MAAI;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC,eAEfjF,OAAA,CAACe,WAAW;MAAAsD,QAAA,gBACVrE,OAAA,CAACiB,aAAa;QAAC6E,OAAO,EAAEjC,sBAAuB;QAACkC,KAAK,EAAC,oIAA2B;QAAA1B,QAAA,EAAC;MAElF;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAChBjF,OAAA,CAACiB,aAAa;QAAC6E,OAAO,EAAE/B,cAAe;QAACgC,KAAK,EAAC,sEAAe;QAAA1B,QAAA,EAAC;MAE9D;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAChBjF,OAAA,CAACiB,aAAa;QAAC6E,OAAO,EAAEA,CAAA,KAAMtC,cAAc,CAAC,CAACD,WAAW,CAAE;QAACwC,KAAK,EAAC,qEAAc;QAAA1B,QAAA,EAAC;MAEjF;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEbjC,eAAe,iBACdhD,OAAA,CAACoB,YAAY;MAAAiD,QAAA,gBACXrE,OAAA;QAAAqE,QAAA,eAAKrE,OAAA;UAAAqE,QAAA,EAAQ;QAAY;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxCjF,OAAA;QAAAqE,QAAA,GAAK,sBAAK,EAACrB,eAAe,CAACf,GAAG,CAACmD,OAAO,CAAC,CAAC,CAAC;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChDjF,OAAA;QAAAqE,QAAA,GAAK,sBAAK,EAACrB,eAAe,CAACd,GAAG,CAACkD,OAAO,CAAC,CAAC,CAAC;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAC/CjC,eAAe,CAACqC,QAAQ,iBACvBrF,OAAA;QAAAqE,QAAA,GAAK,sBAAK,EAACrB,eAAe,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,QAAC;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACtD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CACf;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAEjB,CAAC;AAAC7B,GAAA,CAnIIL,YAAyC;AAAAiD,GAAA,GAAzCjD,YAAyC;AAqI/C,eAAeA,YAAY;AAAC,IAAAjC,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAkB,GAAA,EAAAO,GAAA,EAAAkD,GAAA;AAAAC,YAAA,CAAAnF,EAAA;AAAAmF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAnD,GAAA;AAAAmD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}