{"ast": null, "code": "import { createContext, use } from 'react';\nexport const CONTEXT_VERSION = 1;\nexport function createLeafletContext(map) {\n  return Object.freeze({\n    __version: CONTEXT_VERSION,\n    map\n  });\n}\nexport function extendContext(source, extra) {\n  return Object.freeze({\n    ...source,\n    ...extra\n  });\n}\nexport const LeafletContext = createContext(null);\nexport function useLeafletContext() {\n  const context = use(LeafletContext);\n  if (context == null) {\n    throw new Error('No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>');\n  }\n  return context;\n}", "map": {"version": 3, "names": ["createContext", "use", "CONTEXT_VERSION", "createLeafletContext", "map", "Object", "freeze", "__version", "extendContext", "source", "extra", "LeafletContext", "useLeafletContext", "context", "Error"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@react-leaflet/core/lib/context.js"], "sourcesContent": ["import { createContext, use } from 'react';\nexport const CONTEXT_VERSION = 1;\nexport function createLeafletContext(map) {\n    return Object.freeze({\n        __version: CONTEXT_VERSION,\n        map\n    });\n}\nexport function extendContext(source, extra) {\n    return Object.freeze({\n        ...source,\n        ...extra\n    });\n}\nexport const LeafletContext = createContext(null);\nexport function useLeafletContext() {\n    const context = use(LeafletContext);\n    if (context == null) {\n        throw new Error('No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>');\n    }\n    return context;\n}\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,GAAG,QAAQ,OAAO;AAC1C,OAAO,MAAMC,eAAe,GAAG,CAAC;AAChC,OAAO,SAASC,oBAAoBA,CAACC,GAAG,EAAE;EACtC,OAAOC,MAAM,CAACC,MAAM,CAAC;IACjBC,SAAS,EAAEL,eAAe;IAC1BE;EACJ,CAAC,CAAC;AACN;AACA,OAAO,SAASI,aAAaA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACzC,OAAOL,MAAM,CAACC,MAAM,CAAC;IACjB,GAAGG,MAAM;IACT,GAAGC;EACP,CAAC,CAAC;AACN;AACA,OAAO,MAAMC,cAAc,GAAGX,aAAa,CAAC,IAAI,CAAC;AACjD,OAAO,SAASY,iBAAiBA,CAAA,EAAG;EAChC,MAAMC,OAAO,GAAGZ,GAAG,CAACU,cAAc,CAAC;EACnC,IAAIE,OAAO,IAAI,IAAI,EAAE;IACjB,MAAM,IAAIC,KAAK,CAAC,6FAA6F,CAAC;EAClH;EACA,OAAOD,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}