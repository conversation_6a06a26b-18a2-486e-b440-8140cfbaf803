{"ast": null, "code": "import { createElementObject, createOverlayComponent } from '@react-leaflet/core';\nimport { Popup as LeafletPopup } from 'leaflet';\nimport { useEffect } from 'react';\nexport const Popup = createOverlayComponent(function createPopup(props, context) {\n  const popup = new LeafletPopup(props, context.overlayContainer);\n  return createElementObject(popup, context);\n}, function usePopupLifecycle(element, context, _ref, setOpen) {\n  let {\n    position\n  } = _ref;\n  useEffect(function addPopup() {\n    const {\n      instance\n    } = element;\n    function onPopupOpen(event) {\n      if (event.popup === instance) {\n        instance.update();\n        setOpen(true);\n      }\n    }\n    function onPopupClose(event) {\n      if (event.popup === instance) {\n        setOpen(false);\n      }\n    }\n    context.map.on({\n      popupopen: onPopupOpen,\n      popupclose: onPopupClose\n    });\n    if (context.overlayContainer == null) {\n      // Attach to a Map\n      if (position != null) {\n        instance.setLatLng(position);\n      }\n      instance.openOn(context.map);\n    } else {\n      // Attach to container component\n      context.overlayContainer.bindPopup(instance);\n    }\n    return function removePopup() {\n      var _context$overlayConta;\n      context.map.off({\n        popupopen: onPopupOpen,\n        popupclose: onPopupClose\n      });\n      (_context$overlayConta = context.overlayContainer) === null || _context$overlayConta === void 0 || _context$overlayConta.unbindPopup();\n      context.map.removeLayer(instance);\n    };\n  }, [element, context, setOpen, position]);\n});", "map": {"version": 3, "names": ["createElementObject", "createOverlayComponent", "Popup", "LeafletPopup", "useEffect", "createPopup", "props", "context", "popup", "overlayContainer", "usePopupLifecycle", "element", "_ref", "<PERSON><PERSON><PERSON>", "position", "addPopup", "instance", "onPopupOpen", "event", "update", "onPopupClose", "map", "on", "popupopen", "popupclose", "setLatLng", "openOn", "bindPopup", "removePopup", "_context$overlayConta", "off", "unbindPopup", "<PERSON><PERSON><PERSON>er"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/Popup.js"], "sourcesContent": ["import { createElementObject, createOverlayComponent } from '@react-leaflet/core';\nimport { Popup as LeafletPopup } from 'leaflet';\nimport { useEffect } from 'react';\nexport const Popup = createOverlayComponent(function createPopup(props, context) {\n    const popup = new LeafletPopup(props, context.overlayContainer);\n    return createElementObject(popup, context);\n}, function usePopupLifecycle(element, context, { position }, setOpen) {\n    useEffect(function addPopup() {\n        const { instance } = element;\n        function onPopupOpen(event) {\n            if (event.popup === instance) {\n                instance.update();\n                setOpen(true);\n            }\n        }\n        function onPopupClose(event) {\n            if (event.popup === instance) {\n                setOpen(false);\n            }\n        }\n        context.map.on({\n            popupopen: onPopupOpen,\n            popupclose: onPopupClose\n        });\n        if (context.overlayContainer == null) {\n            // Attach to a Map\n            if (position != null) {\n                instance.setLatLng(position);\n            }\n            instance.openOn(context.map);\n        } else {\n            // Attach to container component\n            context.overlayContainer.bindPopup(instance);\n        }\n        return function removePopup() {\n            context.map.off({\n                popupopen: onPopupOpen,\n                popupclose: onPopupClose\n            });\n            context.overlayContainer?.unbindPopup();\n            context.map.removeLayer(instance);\n        };\n    }, [\n        element,\n        context,\n        setOpen,\n        position\n    ]);\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,sBAAsB,QAAQ,qBAAqB;AACjF,SAASC,KAAK,IAAIC,YAAY,QAAQ,SAAS;AAC/C,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAO,MAAMF,KAAK,GAAGD,sBAAsB,CAAC,SAASI,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC7E,MAAMC,KAAK,GAAG,IAAIL,YAAY,CAACG,KAAK,EAAEC,OAAO,CAACE,gBAAgB,CAAC;EAC/D,OAAOT,mBAAmB,CAACQ,KAAK,EAAED,OAAO,CAAC;AAC9C,CAAC,EAAE,SAASG,iBAAiBA,CAACC,OAAO,EAAEJ,OAAO,EAAAK,IAAA,EAAgBC,OAAO,EAAE;EAAA,IAAvB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EACxDR,SAAS,CAAC,SAASW,QAAQA,CAAA,EAAG;IAC1B,MAAM;MAAEC;IAAS,CAAC,GAAGL,OAAO;IAC5B,SAASM,WAAWA,CAACC,KAAK,EAAE;MACxB,IAAIA,KAAK,CAACV,KAAK,KAAKQ,QAAQ,EAAE;QAC1BA,QAAQ,CAACG,MAAM,CAAC,CAAC;QACjBN,OAAO,CAAC,IAAI,CAAC;MACjB;IACJ;IACA,SAASO,YAAYA,CAACF,KAAK,EAAE;MACzB,IAAIA,KAAK,CAACV,KAAK,KAAKQ,QAAQ,EAAE;QAC1BH,OAAO,CAAC,KAAK,CAAC;MAClB;IACJ;IACAN,OAAO,CAACc,GAAG,CAACC,EAAE,CAAC;MACXC,SAAS,EAAEN,WAAW;MACtBO,UAAU,EAAEJ;IAChB,CAAC,CAAC;IACF,IAAIb,OAAO,CAACE,gBAAgB,IAAI,IAAI,EAAE;MAClC;MACA,IAAIK,QAAQ,IAAI,IAAI,EAAE;QAClBE,QAAQ,CAACS,SAAS,CAACX,QAAQ,CAAC;MAChC;MACAE,QAAQ,CAACU,MAAM,CAACnB,OAAO,CAACc,GAAG,CAAC;IAChC,CAAC,MAAM;MACH;MACAd,OAAO,CAACE,gBAAgB,CAACkB,SAAS,CAACX,QAAQ,CAAC;IAChD;IACA,OAAO,SAASY,WAAWA,CAAA,EAAG;MAAA,IAAAC,qBAAA;MAC1BtB,OAAO,CAACc,GAAG,CAACS,GAAG,CAAC;QACZP,SAAS,EAAEN,WAAW;QACtBO,UAAU,EAAEJ;MAChB,CAAC,CAAC;MACF,CAAAS,qBAAA,GAAAtB,OAAO,CAACE,gBAAgB,cAAAoB,qBAAA,eAAxBA,qBAAA,CAA0BE,WAAW,CAAC,CAAC;MACvCxB,OAAO,CAACc,GAAG,CAACW,WAAW,CAAChB,QAAQ,CAAC;IACrC,CAAC;EACL,CAAC,EAAE,CACCL,OAAO,EACPJ,OAAO,EACPM,OAAO,EACPC,QAAQ,CACX,CAAC;AACN,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}