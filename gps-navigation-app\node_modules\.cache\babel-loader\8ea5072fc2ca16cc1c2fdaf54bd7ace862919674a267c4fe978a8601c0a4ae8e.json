{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _constants = require(\"./constants\");\nvar convertArea = function convertArea(squareMeters) {\n  var targetUnit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"m\";\n  var factor = _constants.areaConversion[targetUnit];\n  if (factor) {\n    return squareMeters * factor;\n  }\n  throw new Error(\"Invalid unit used for area conversion.\");\n};\nvar _default = convertArea;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_constants", "require", "convertArea", "squareMeters", "targetUnit", "arguments", "length", "undefined", "factor", "areaConversion", "Error", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/convertArea.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _constants=require(\"./constants\");var convertArea=function convertArea(squareMeters){var targetUnit=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"m\";var factor=_constants.areaConversion[targetUnit];if(factor){return squareMeters*factor}throw new Error(\"Invalid unit used for area conversion.\")};var _default=convertArea;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,UAAU,GAACC,OAAO,CAAC,aAAa,CAAC;AAAC,IAAIC,WAAW,GAAC,SAASA,WAAWA,CAACC,YAAY,EAAC;EAAC,IAAIC,UAAU,GAACC,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAGE,SAAS,GAACF,SAAS,CAAC,CAAC,CAAC,GAAC,GAAG;EAAC,IAAIG,MAAM,GAACR,UAAU,CAACS,cAAc,CAACL,UAAU,CAAC;EAAC,IAAGI,MAAM,EAAC;IAAC,OAAOL,YAAY,GAACK,MAAM;EAAA;EAAC,MAAM,IAAIE,KAAK,CAAC,wCAAwC,CAAC;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACT,WAAW;AAACL,OAAO,CAACE,OAAO,GAACY,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}