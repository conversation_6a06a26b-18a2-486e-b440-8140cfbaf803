{"ast": null, "code": "/**\n * react-router v7.8.2\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { ENABLE_DEV_WARNINGS, ErrorResponseImpl, FrameworkContext, NO_BODY_STATUS_CODES, Outlet, RSCRouterContext, RemixErrorBoundary, RouterProvider, SINGLE_FETCH_REDIRECT_STATUS, SingleFetchRedirectSymbol, StaticRouterProvider, StreamTransfer, convertRoutesToDataRoutes, createBrowserHistory, createMemoryRouter, createRequestInit, createRouter, createServerRoutes, createStaticHandler, createStaticRouter, decodeViaTurboStream, encode, getManifestPath, getSingleFetchDataStrategyImpl, getStaticContextFromError, invariant, isDataWithResponseInit, isMutationMethod, isRedirectResponse, isRedirectStatusCode, isResponse, isRouteErrorResponse, matchRoutes, noActionDefinedError, redirect, redirectDocument, replace, setIsHydrated, shouldHydrateRouteLoader, singleFetchUrl, stripBasename, stripIndexParam, unstable_RouterContextProvider, unstable_createContext, useRouteError, warnOnce, withComponentProps, withErrorBoundaryProps, withHydrateFallbackProps } from \"./chunk-PVWAREVJ.mjs\";\n\n// lib/dom/ssr/server.tsx\nimport * as React from \"react\";\nfunction ServerRouter({\n  context,\n  url,\n  nonce\n}) {\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let {\n    manifest,\n    routeModules,\n    criticalCss,\n    serverHandoffString\n  } = context;\n  let routes = createServerRoutes(manifest.routes, routeModules, context.future, context.isSpaMode);\n  context.staticHandlerContext.loaderData = {\n    ...context.staticHandlerContext.loaderData\n  };\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    if (route && manifestRoute && shouldHydrateRouteLoader(routeId, route.clientLoader, manifestRoute.hasLoader, context.isSpaMode) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      delete context.staticHandlerContext.loaderData[routeId];\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext);\n  return /* @__PURE__ */React.createElement(React.Fragment, null, /* @__PURE__ */React.createElement(FrameworkContext.Provider, {\n    value: {\n      manifest,\n      routeModules,\n      criticalCss,\n      serverHandoffString,\n      future: context.future,\n      ssr: context.ssr,\n      isSpaMode: context.isSpaMode,\n      routeDiscovery: context.routeDiscovery,\n      serializeError: context.serializeError,\n      renderMeta: context.renderMeta\n    }\n  }, /* @__PURE__ */React.createElement(RemixErrorBoundary, {\n    location: router.state.location\n  }, /* @__PURE__ */React.createElement(StaticRouterProvider, {\n    router,\n    context: context.staticHandlerContext,\n    hydrate: false\n  }))), context.serverHandoffStream ? /* @__PURE__ */React.createElement(React.Suspense, null, /* @__PURE__ */React.createElement(StreamTransfer, {\n    context,\n    identifier: 0,\n    reader: context.serverHandoffStream.getReader(),\n    textDecoder: new TextDecoder(),\n    nonce\n  })) : null);\n}\n\n// lib/dom/ssr/routes-test-stub.tsx\nimport * as React2 from \"react\";\nfunction createRoutesStub(routes, _context) {\n  return function RoutesTestStub({\n    initialEntries,\n    initialIndex,\n    hydrationData,\n    future\n  }) {\n    let routerRef = React2.useRef();\n    let frameworkContextRef = React2.useRef();\n    if (routerRef.current == null) {\n      frameworkContextRef.current = {\n        future: {\n          unstable_subResourceIntegrity: future?.unstable_subResourceIntegrity === true,\n          unstable_middleware: future?.unstable_middleware === true\n        },\n        manifest: {\n          routes: {},\n          entry: {\n            imports: [],\n            module: \"\"\n          },\n          url: \"\",\n          version: \"\"\n        },\n        routeModules: {},\n        ssr: false,\n        isSpaMode: false,\n        routeDiscovery: {\n          mode: \"lazy\",\n          manifestPath: \"/__manifest\"\n        }\n      };\n      let patched = processRoutes(\n      // @ts-expect-error `StubRouteObject` is stricter about `loader`/`action`\n      // types compared to `AgnosticRouteObject`\n      convertRoutesToDataRoutes(routes, r => r), _context !== void 0 ? _context : future?.unstable_middleware ? new unstable_RouterContextProvider() : {}, frameworkContextRef.current.manifest, frameworkContextRef.current.routeModules);\n      routerRef.current = createMemoryRouter(patched, {\n        initialEntries,\n        initialIndex,\n        hydrationData\n      });\n    }\n    return /* @__PURE__ */React2.createElement(FrameworkContext.Provider, {\n      value: frameworkContextRef.current\n    }, /* @__PURE__ */React2.createElement(RouterProvider, {\n      router: routerRef.current\n    }));\n  };\n}\nfunction processRoutes(routes, context, manifest, routeModules, parentId) {\n  return routes.map(route => {\n    if (!route.id) {\n      throw new Error(\"Expected a route.id in react-router processRoutes() function\");\n    }\n    let newRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      Component: route.Component ? withComponentProps(route.Component) : void 0,\n      HydrateFallback: route.HydrateFallback ? withHydrateFallbackProps(route.HydrateFallback) : void 0,\n      ErrorBoundary: route.ErrorBoundary ? withErrorBoundaryProps(route.ErrorBoundary) : void 0,\n      action: route.action ? args => route.action({\n        ...args,\n        context\n      }) : void 0,\n      loader: route.loader ? args => route.loader({\n        ...args,\n        context\n      }) : void 0,\n      handle: route.handle,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    let entryRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      parentId,\n      hasAction: route.action != null,\n      hasLoader: route.loader != null,\n      // When testing routes, you should be stubbing loader/action/middleware,\n      // not trying to re-implement the full loader/clientLoader/SSR/hydration\n      // flow. That is better tested via E2E tests.\n      hasClientAction: false,\n      hasClientLoader: false,\n      hasClientMiddleware: false,\n      hasErrorBoundary: route.ErrorBoundary != null,\n      // any need for these?\n      module: \"build/stub-path-to-module.js\",\n      clientActionModule: void 0,\n      clientLoaderModule: void 0,\n      clientMiddlewareModule: void 0,\n      hydrateFallbackModule: void 0\n    };\n    manifest.routes[newRoute.id] = entryRoute;\n    routeModules[route.id] = {\n      default: newRoute.Component || Outlet,\n      ErrorBoundary: newRoute.ErrorBoundary || void 0,\n      handle: route.handle,\n      links: route.links,\n      meta: route.meta,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    if (route.children) {\n      newRoute.children = processRoutes(route.children, context, manifest, routeModules, newRoute.id);\n    }\n    return newRoute;\n  });\n}\n\n// lib/server-runtime/cookies.ts\nimport { parse, serialize } from \"cookie\";\n\n// lib/server-runtime/crypto.ts\nvar encoder = /* @__PURE__ */new TextEncoder();\nvar sign = async (value, secret) => {\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"sign\"]);\n  let signature = await crypto.subtle.sign(\"HMAC\", key, data2);\n  let hash = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(/=+$/, \"\");\n  return value + \".\" + hash;\n};\nvar unsign = async (cookie, secret) => {\n  let index = cookie.lastIndexOf(\".\");\n  let value = cookie.slice(0, index);\n  let hash = cookie.slice(index + 1);\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"verify\"]);\n  try {\n    let signature = byteStringToUint8Array(atob(hash));\n    let valid = await crypto.subtle.verify(\"HMAC\", key, signature, data2);\n    return valid ? value : false;\n  } catch (error) {\n    return false;\n  }\n};\nvar createKey = async (secret, usages) => crypto.subtle.importKey(\"raw\", encoder.encode(secret), {\n  name: \"HMAC\",\n  hash: \"SHA-256\"\n}, false, usages);\nfunction byteStringToUint8Array(byteString) {\n  let array = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    array[i] = byteString.charCodeAt(i);\n  }\n  return array;\n}\n\n// lib/server-runtime/cookies.ts\nvar createCookie = (name, cookieOptions = {}) => {\n  let {\n    secrets = [],\n    ...options\n  } = {\n    path: \"/\",\n    sameSite: \"lax\",\n    ...cookieOptions\n  };\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1e3) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, {\n        ...options,\n        ...parseOptions\n      });\n      if (name in cookies) {\n        let value = cookies[name];\n        if (typeof value === \"string\" && value !== \"\") {\n          let decoded = await decodeCookieValue(value, secrets);\n          return decoded;\n        } else {\n          return \"\";\n        }\n      } else {\n        return null;\n      }\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(name, value === \"\" ? \"\" : await encodeCookieValue(value, secrets), {\n        ...options,\n        ...serializeOptions\n      });\n    }\n  };\n};\nvar isCookie = object => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(!expires, `The \"${name}\" cookie has an \"expires\" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use \\`commitSession(session, { expires })\\` if using a session storage object, or \\`cookie.serialize(\"value\", { expires })\\` if you're using the cookie directly.`);\n}\n\n// lib/server-runtime/entry.ts\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo, routeId) => {\n    let route = manifest[routeId];\n    if (route) {\n      memo[routeId] = route.module;\n    }\n    return memo;\n  }, {});\n}\n\n// lib/server-runtime/mode.ts\nvar ServerMode = /* @__PURE__ */(ServerMode2 => {\n  ServerMode2[\"Development\"] = \"development\";\n  ServerMode2[\"Production\"] = \"production\";\n  ServerMode2[\"Test\"] = \"test\";\n  return ServerMode2;\n})(ServerMode || {});\nfunction isServerMode(value) {\n  return value === \"development\" /* Development */ || value === \"production\" /* Production */ || value === \"test\" /* Test */;\n}\n\n// lib/server-runtime/errors.ts\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== \"development\" /* Development */) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = void 0;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, [routeId, error]) => {\n    return Object.assign(acc, {\n      [routeId]: sanitizeError(error, serverMode)\n    });\n  }, {});\n}\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = {\n        ...val,\n        __type: \"RouteErrorResponse\"\n      };\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = {\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.  This will only apply\n        // in dev mode since all production errors are sanitized to normal\n        // Error instances\n        ...(sanitized.name !== \"Error\" ? {\n          __subType: sanitized.name\n        } : {})\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/server-runtime/routeMatching.ts\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(routes, pathname, basename);\n  if (!matches) return null;\n  return matches.map(match => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\n// lib/server-runtime/data.ts\nasync function callRouteHandler(handler, args) {\n  let result = await handler({\n    request: stripRoutesParam(stripIndexParam2(args.request)),\n    params: args.params,\n    context: args.context\n  });\n  if (isDataWithResponseInit(result) && result.init && result.init.status && isRedirectStatusCode(result.init.status)) {\n    throw new Response(null, result.init);\n  }\n  return result;\n}\nfunction stripIndexParam2(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\n// lib/server-runtime/invariant.ts\nfunction invariant2(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\"The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose\");\n    throw new Error(message);\n  }\n}\n\n// lib/server-runtime/dev.ts\nvar globalDevServerHooksKey = \"__reactRouterDevServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  return globalThis[globalDevServerHooksKey];\n}\nfunction getBuildTimeHeader(request, headerName) {\n  if (typeof process !== \"undefined\") {\n    try {\n      if (process.env?.IS_RR_BUILD_REQUEST === \"yes\") {\n        return request.headers.get(headerName);\n      }\n    } catch (e) {}\n  }\n  return null;\n}\n\n// lib/server-runtime/routes.ts\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach(route => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction createRoutes(manifest, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map(route => ({\n    ...route,\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\nfunction createStaticHandlerDataRoutes(manifest, future, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map(route => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      unstable_middleware: route.module.unstable_middleware,\n      // Need to use RR's version in the param typed here to permit the optional\n      // context even though we know it'll always be provided in remix\n      loader: route.module.loader ? async args => {\n        let preRenderedData = getBuildTimeHeader(args.request, \"X-React-Router-Prerender-Data\");\n        if (preRenderedData != null) {\n          let encoded = preRenderedData ? decodeURI(preRenderedData) : preRenderedData;\n          invariant2(encoded, \"Missing prerendered data for route\");\n          let uint8array = new TextEncoder().encode(encoded);\n          let stream = new ReadableStream({\n            start(controller) {\n              controller.enqueue(uint8array);\n              controller.close();\n            }\n          });\n          let decoded = await decodeViaTurboStream(stream, global);\n          let data2 = decoded.value;\n          if (data2 && SingleFetchRedirectSymbol in data2) {\n            let result = data2[SingleFetchRedirectSymbol];\n            let init = {\n              status: result.status\n            };\n            if (result.reload) {\n              throw redirectDocument(result.redirect, init);\n            } else if (result.replace) {\n              throw replace(result.redirect, init);\n            } else {\n              throw redirect(result.redirect, init);\n            }\n          } else {\n            invariant2(data2 && route.id in data2, \"Unable to decode prerendered data\");\n            let result = data2[route.id];\n            invariant2(\"data\" in result, \"Unable to process prerendered data\");\n            return result.data;\n          }\n        }\n        let val = await callRouteHandler(route.module.loader, args);\n        return val;\n      } : void 0,\n      action: route.module.action ? args => callRouteHandler(route.module.action, args) : void 0,\n      handle: route.module.handle\n    };\n    return route.index ? {\n      index: true,\n      ...commonRoute\n    } : {\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(manifest, future, route.id, routesByParentId),\n      ...commonRoute\n    };\n  });\n}\n\n// lib/server-runtime/markup.ts\nvar ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nvar ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction escapeHtml(html) {\n  return html.replace(ESCAPE_REGEX, match => ESCAPE_LOOKUP[match]);\n}\n\n// lib/server-runtime/serverHandoff.ts\nfunction createServerHandoffString(serverHandoff) {\n  return escapeHtml(JSON.stringify(serverHandoff));\n}\n\n// lib/server-runtime/headers.ts\nimport { splitCookiesString } from \"set-cookie-parser\";\nfunction getDocumentHeaders(context, build) {\n  return getDocumentHeadersImpl(context, m => {\n    let route = build.routes[m.route.id];\n    invariant2(route, `Route with id \"${m.route.id}\" not found in build`);\n    return route.module.headers;\n  });\n}\nfunction getDocumentHeadersImpl(context, getRouteHeadersFn, _defaultHeaders) {\n  let boundaryIdx = context.errors ? context.matches.findIndex(m => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    let {\n      actionHeaders,\n      actionData,\n      loaderHeaders,\n      loaderData\n    } = context;\n    context.matches.slice(boundaryIdx).some(match => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || !actionData.hasOwnProperty(id))) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && !loaderData.hasOwnProperty(id)) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  const defaultHeaders = new Headers(_defaultHeaders);\n  return matches.reduce((parentHeaders, match, idx) => {\n    let {\n      id\n    } = match.route;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n    let includeErrorHeaders = errorHeaders != null && idx === matches.length - 1;\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n    let headersFn = getRouteHeadersFn(match);\n    if (headersFn == null) {\n      let headers2 = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers2);\n      }\n      prependCookies(actionHeaders, headers2);\n      prependCookies(loaderHeaders, headers2);\n      return headers2;\n    }\n    let headers = new Headers(typeof headersFn === \"function\" ? headersFn({\n      loaderHeaders,\n      parentHeaders,\n      actionHeaders,\n      errorHeaders: includeErrorHeaders ? errorHeaders : void 0\n    }) : headersFn);\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers(defaultHeaders));\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    let cookies = splitCookiesString(parentSetCookieString);\n    let childCookies = new Set(childHeaders.getSetCookie());\n    cookies.forEach(cookie => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\n// lib/server-runtime/single-fetch.ts\nvar SERVER_NO_BODY_STATUS_CODES = /* @__PURE__ */new Set([...NO_BODY_STATUS_CODES, 304]);\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...(request.body ? {\n        duplex: \"half\"\n      } : void 0)\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      skipRevalidation: true,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async query => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: {\n        error\n      },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let singleFetchResult;\n    if (context.errors) {\n      singleFetchResult = {\n        error: Object.values(context.errors)[0]\n      };\n    } else {\n      singleFetchResult = {\n        data: Object.values(context.actionData || {})[0]\n      };\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: singleFetchResult,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let routesParam = new URL(request.url).searchParams.get(\"_routes\");\n  let loadRouteIds = routesParam ? new Set(routesParam.split(\",\")) : null;\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      filterMatchesToLoad: m => !loadRouteIds || loadRouteIds.has(m.route.id),\n      skipLoaderErrorBubbling: true,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async query => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: {\n        error\n      },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let results = {};\n    let loadedMatches = new Set(context.matches.filter(m => loadRouteIds ? loadRouteIds.has(m.route.id) : m.route.loader != null).map(m => m.route.id));\n    if (context.errors) {\n      for (let [id, error] of Object.entries(context.errors)) {\n        results[id] = {\n          error\n        };\n      }\n    }\n    for (let [id, data2] of Object.entries(context.loaderData)) {\n      if (!(id in results) && loadedMatches.has(id)) {\n        results[id] = {\n          data: data2\n        };\n      }\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: results,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nfunction generateSingleFetchResponse(request, build, serverMode, {\n  result,\n  headers,\n  status\n}) {\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n  if (SERVER_NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, {\n      status,\n      headers: resultHeaders\n    });\n  }\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n  resultHeaders.delete(\"Content-Length\");\n  return new Response(encodeViaTurboStream(result, request.signal, build.entry.module.streamTimeout, serverMode), {\n    status: status || 200,\n    headers: resultHeaders\n  });\n}\nfunction generateSingleFetchRedirectResponse(redirectResponse, request, build, serverMode) {\n  let redirect2 = getSingleFetchRedirect(redirectResponse.status, redirectResponse.headers, build.basename);\n  let headers = new Headers(redirectResponse.headers);\n  headers.delete(\"Location\");\n  headers.set(\"Content-Type\", \"text/x-script\");\n  return generateSingleFetchResponse(request, build, serverMode, {\n    result: request.method === \"GET\" ? {\n      [SingleFetchRedirectSymbol]: redirect2\n    } : redirect2,\n    headers,\n    status: SINGLE_FETCH_REDIRECT_STATUS\n  });\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect2 = headers.get(\"Location\");\n  if (basename) {\n    redirect2 = stripBasename(redirect2, basename) || redirect2;\n  }\n  return {\n    redirect: redirect2,\n    status,\n    revalidate:\n    // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n    // detail of ?_data requests as our way to tell the front end to revalidate when\n    // we didn't have a response body to include that information in.\n    // With single fetch, we tell the front end via this revalidate boolean field.\n    // However, we're respecting it for now because it may be something folks have\n    // used in their own responses\n    // TODO(v3): Consider removing or making this official public API\n    headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\"),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\nfunction encodeViaTurboStream(data2, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  let timeoutId = setTimeout(() => controller.abort(new Error(\"Server Timeout\")), typeof streamTimeout === \"number\" ? streamTimeout : 4950);\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data2, {\n    signal: controller.signal,\n    plugins: [value => {\n      if (value instanceof Error) {\n        let {\n          name,\n          message,\n          stack\n        } = serverMode === \"production\" /* Production */ ? sanitizeError(value, serverMode) : value;\n        return [\"SanitizedError\", name, message, stack];\n      }\n      if (value instanceof ErrorResponseImpl) {\n        let {\n          data: data3,\n          status,\n          statusText\n        } = value;\n        return [\"ErrorResponse\", data3, status, statusText];\n      }\n      if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n        return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n      }\n    }],\n    postPlugins: [value => {\n      if (!value) return;\n      if (typeof value !== \"object\") return;\n      return [\"SingleFetchClassInstance\", Object.fromEntries(Object.entries(value))];\n    }, () => [\"SingleFetchFallback\"]]\n  });\n}\n\n// lib/server-runtime/server.ts\nfunction derive(build, mode) {\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : \"production\" /* Production */;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename\n  });\n  let errorHandler = build.entry.module.handleError || ((error, {\n    request\n  }) => {\n    if (serverMode !== \"test\" /* Test */ && !request.signal.aborted) {\n      console.error(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      isRouteErrorResponse(error) && error.error ? error.error : error);\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nvar createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, initialContext) {\n    _build = typeof build === \"function\" ? await build() : build;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let params = {};\n    let loadContext;\n    let handleError = error => {\n      if (mode === \"development\" /* Development */) {\n        getDevServerHooks()?.processRequestError?.(error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n    if (_build.future.unstable_middleware) {\n      if (initialContext && !(initialContext instanceof unstable_RouterContextProvider)) {\n        let error = new Error(\"Invalid `context` value provided to `handleRequest`. When middleware is enabled you must return an instance of `unstable_RouterContextProvider` from your `getLoadContext` function.\");\n        handleError(error);\n        return returnLastResortErrorResponse(error, serverMode);\n      }\n      loadContext = initialContext || new unstable_RouterContextProvider();\n    } else {\n      loadContext = initialContext || {};\n    }\n    let url = new URL(request.url);\n    let normalizedBasename = _build.basename || \"/\";\n    let normalizedPath = url.pathname;\n    if (stripBasename(normalizedPath, normalizedBasename) === \"/_root.data\") {\n      normalizedPath = normalizedBasename;\n    } else if (normalizedPath.endsWith(\".data\")) {\n      normalizedPath = normalizedPath.replace(/\\.data$/, \"\");\n    }\n    if (stripBasename(normalizedPath, normalizedBasename) !== \"/\" && normalizedPath.endsWith(\"/\")) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n    let isSpaMode = getBuildTimeHeader(request, \"X-React-Router-SPA-Mode\") === \"yes\";\n    if (!_build.ssr) {\n      let decodedPath = decodeURI(normalizedPath);\n      if (normalizedBasename !== \"/\") {\n        let strippedPath = stripBasename(decodedPath, normalizedBasename);\n        if (strippedPath == null) {\n          errorHandler(new ErrorResponseImpl(404, \"Not Found\", `Refusing to prerender the \\`${decodedPath}\\` path because it does not start with the basename \\`${normalizedBasename}\\``), {\n            context: loadContext,\n            params,\n            request\n          });\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        }\n        decodedPath = strippedPath;\n      }\n      if (_build.prerender.length === 0) {\n        isSpaMode = true;\n      } else if (!_build.prerender.includes(decodedPath) && !_build.prerender.includes(decodedPath + \"/\")) {\n        if (url.pathname.endsWith(\".data\")) {\n          errorHandler(new ErrorResponseImpl(404, \"Not Found\", `Refusing to SSR the path \\`${decodedPath}\\` because \\`ssr:false\\` is set and the path is not included in the \\`prerender\\` config, so in production the path will be a 404.`), {\n            context: loadContext,\n            params,\n            request\n          });\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        } else {\n          isSpaMode = true;\n        }\n      }\n    }\n    let manifestUrl = getManifestPath(_build.routeDiscovery.manifestPath, normalizedBasename);\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", {\n          status: 500\n        });\n      }\n    }\n    let matches = matchServerRoutes(routes, normalizedPath, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = normalizedPath;\n      let singleFetchMatches = matchServerRoutes(routes, handlerUrl.pathname, _build.basename);\n      response = await handleSingleFetchRequest(serverMode, _build, staticHandler, request, handlerUrl, loadContext, handleError);\n      if (isRedirectResponse(response)) {\n        response = generateSingleFetchRedirectResponse(response, request, _build, serverMode);\n      }\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          response = generateSingleFetchRedirectResponse(response, request, _build, serverMode);\n        }\n      }\n    } else if (!isSpaMode && matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(serverMode, _build, staticHandler, matches.slice(-1)[0].route.id, request, loadContext, handleError);\n    } else {\n      let {\n        pathname\n      } = url;\n      let criticalCss = void 0;\n      if (_build.unstable_getCriticalCss) {\n        criticalCss = await _build.unstable_getCriticalCss({\n          pathname\n        });\n      } else if (mode === \"development\" /* Development */ && getDevServerHooks()?.getCriticalCss) {\n        criticalCss = await getDevServerHooks()?.getCriticalCss?.(pathname);\n      }\n      response = await handleDocumentRequest(serverMode, _build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss);\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"p\")) {\n    let paths = /* @__PURE__ */new Set();\n    url.searchParams.getAll(\"p\").forEach(path => {\n      if (!path.startsWith(\"/\")) {\n        path = `/${path}`;\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(`/${partialPath}`);\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          let route = build.assets.routes[routeId];\n          if (route) {\n            patches[routeId] = route;\n          }\n        }\n      }\n    }\n    return Response.json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    });\n  }\n  return new Response(\"Invalid Request\", {\n    status: 400\n  });\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let response = request.method !== \"GET\" ? await singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) : await singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError);\n  return response;\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss) {\n  try {\n    let result = await staticHandler.query(request, {\n      requestContext: loadContext,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async query => {\n        try {\n          let innerResult = await query(request);\n          if (!isResponse(innerResult)) {\n            innerResult = await renderHtml(innerResult, isSpaMode);\n          }\n          return innerResult;\n        } catch (error) {\n          handleError(error);\n          return new Response(null, {\n            status: 500\n          });\n        }\n      } : void 0\n    });\n    if (!isResponse(result)) {\n      result = await renderHtml(result, isSpaMode);\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return new Response(null, {\n      status: 500\n    });\n  }\n  async function renderHtml(context, isSpaMode2) {\n    let headers = getDocumentHeaders(context, build);\n    if (SERVER_NO_BODY_STATUS_CODES.has(context.statusCode)) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors, serverMode)\n    };\n    let baseServerHandoff = {\n      basename: build.basename,\n      future: build.future,\n      routeDiscovery: build.routeDiscovery,\n      ssr: build.ssr,\n      isSpaMode: isSpaMode2\n    };\n    let entryContext = {\n      manifest: build.assets,\n      routeModules: createEntryRouteModules(build.routes),\n      staticHandlerContext: context,\n      criticalCss,\n      serverHandoffString: createServerHandoffString({\n        ...baseServerHandoff,\n        criticalCss\n      }),\n      serverHandoffStream: encodeViaTurboStream(state, request.signal, build.entry.module.streamTimeout, serverMode),\n      renderMeta: {},\n      future: build.future,\n      ssr: build.ssr,\n      routeDiscovery: build.routeDiscovery,\n      isSpaMode: isSpaMode2,\n      serializeError: err => serializeError(err, serverMode)\n    };\n    let handleDocumentRequestFunction = build.entry.module.default;\n    try {\n      return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n    } catch (error) {\n      handleError(error);\n      let errorForSecondRender = error;\n      if (isResponse(error)) {\n        try {\n          let data2 = await unwrapResponse(error);\n          errorForSecondRender = new ErrorResponseImpl(error.status, error.statusText, data2);\n        } catch (e) {}\n      }\n      context = getStaticContextFromError(staticHandler.dataRoutes, context, errorForSecondRender);\n      if (context.errors) {\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let state2 = {\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: serializeErrors(context.errors, serverMode)\n      };\n      entryContext = {\n        ...entryContext,\n        staticHandlerContext: context,\n        serverHandoffString: createServerHandoffString(baseServerHandoff),\n        serverHandoffStream: encodeViaTurboStream(state2, request.signal, build.entry.module.streamTimeout, serverMode),\n        renderMeta: {}\n      };\n      try {\n        return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n      } catch (error2) {\n        handleError(error2);\n        return returnLastResortErrorResponse(error2, serverMode);\n      }\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let result = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async queryRoute => {\n        try {\n          let innerResult = await queryRoute(request);\n          return handleQueryRouteResult(innerResult);\n        } catch (error) {\n          return handleQueryRouteError(error);\n        }\n      } : void 0\n    });\n    return handleQueryRouteResult(result);\n  } catch (error) {\n    return handleQueryRouteError(error);\n  }\n  function handleQueryRouteResult(result) {\n    if (isResponse(result)) {\n      return result;\n    }\n    if (typeof result === \"string\") {\n      return new Response(result);\n    }\n    return Response.json(result);\n  }\n  function handleQueryRouteError(error) {\n    if (isResponse(error)) {\n      error.headers.set(\"X-Remix-Catch\", \"yes\");\n      return error;\n    }\n    if (isRouteErrorResponse(error)) {\n      handleError(error);\n      return errorResponseToJson(error, serverMode);\n    }\n    if (error instanceof Error && error.message === \"Expected a response from queryRoute\") {\n      let newError = new Error(\"Expected a Response to be returned from resource route handler\");\n      handleError(newError);\n      return returnLastResortErrorResponse(newError, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return Response.json(serializeError(\n  // @ts-expect-error This is \"private\" from users but intended for internal use\n  errorResponse.error || new Error(\"Unexpected Server Error\"), serverMode), {\n    status: errorResponse.status,\n    statusText: errorResponse.statusText,\n    headers: {\n      \"X-Remix-Error\": \"yes\"\n    }\n  });\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== \"production\" /* Production */) {\n    message += `\n\n${String(error)}`;\n  }\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\n\n// lib/server-runtime/sessions.ts\nfunction flash(name) {\n  return `__flash_${name}__`;\n}\nvar createSession = (initialData = {}, id = \"\") => {\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return void 0;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\nvar isSession = object => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\nfunction createSessionStorage({\n  cookie: cookieArg,\n  createData,\n  readData,\n  updateData,\n  deleteData\n}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && (await cookie.parse(cookieHeader, options));\n      let data2 = id && (await readData(id));\n      return createSession(data2 || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let {\n        id,\n        data: data2\n      } = session;\n      let expires = options?.maxAge != null ? new Date(Date.now() + options.maxAge * 1e3) : options?.expires != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data2, expires);\n      } else {\n        id = await createData(data2, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */new Date(0)\n      });\n    }\n  };\n}\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(cookie.isSigned, `The \"${cookie.name}\" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.`);\n}\n\n// lib/server-runtime/sessions/cookieStorage.ts\nfunction createCookieSessionStorage({\n  cookie: cookieArg\n} = {}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(cookieHeader && (await cookie.parse(cookieHeader, options)) || {});\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length);\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */new Date(0)\n      });\n    }\n  };\n}\n\n// lib/server-runtime/sessions/memoryStorage.ts\nfunction createMemorySessionStorage({\n  cookie\n} = {}) {\n  let map = /* @__PURE__ */new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data2, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, {\n        data: data2,\n        expires\n      });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let {\n          data: data2,\n          expires\n        } = map.get(id);\n        if (!expires || expires > /* @__PURE__ */new Date()) {\n          return data2;\n        }\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data2, expires) {\n      map.set(id, {\n        data: data2,\n        expires\n      });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n}\n\n// lib/href.ts\nfunction href(path, ...args) {\n  let params = args[0];\n  return path.split(\"/\").map(segment => {\n    if (segment === \"*\") {\n      return params ? params[\"*\"] : void 0;\n    }\n    const match = segment.match(/^:([\\w-]+)(\\?)?/);\n    if (!match) return segment;\n    const param = match[1];\n    const value = params ? params[param] : void 0;\n    const isRequired = match[2] === void 0;\n    if (isRequired && value === void 0) {\n      throw Error(`Path '${path}' requires param '${param}' but it was not provided`);\n    }\n    return value;\n  }).filter(segment => segment !== void 0).join(\"/\");\n}\n\n// lib/rsc/browser.tsx\nimport * as React4 from \"react\";\nimport * as ReactDOM from \"react-dom\";\n\n// lib/dom/ssr/hydration.tsx\nfunction getHydrationData(state, routes, getRouteInfo, location2, basename, isSpaMode) {\n  let hydrationData = {\n    ...state,\n    loaderData: {\n      ...state.loaderData\n    }\n  };\n  let initialMatches = matchRoutes(routes, location2, basename);\n  if (initialMatches) {\n    for (let match of initialMatches) {\n      let routeId = match.route.id;\n      let routeInfo = getRouteInfo(routeId);\n      if (shouldHydrateRouteLoader(routeId, routeInfo.clientLoader, routeInfo.hasLoader, isSpaMode) && (routeInfo.hasHydrateFallback || !routeInfo.hasLoader)) {\n        delete hydrationData.loaderData[routeId];\n      } else if (!routeInfo.hasLoader) {\n        hydrationData.loaderData[routeId] = null;\n      }\n    }\n  }\n  return hydrationData;\n}\n\n// lib/rsc/errorBoundaries.tsx\nimport React3 from \"react\";\nvar RSCRouterGlobalErrorBoundary = class extends React3.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: null,\n      location: props.location\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location) {\n      return {\n        error: null,\n        location: props.location\n      };\n    }\n    return {\n      error: state.error,\n      location: state.location\n    };\n  }\n  render() {\n    if (this.state.error) {\n      return /* @__PURE__ */React3.createElement(RSCDefaultRootErrorBoundaryImpl, {\n        error: this.state.error,\n        renderAppShell: true\n      });\n    } else {\n      return this.props.children;\n    }\n  }\n};\nfunction ErrorWrapper({\n  renderAppShell,\n  title,\n  children\n}) {\n  if (!renderAppShell) {\n    return children;\n  }\n  return /* @__PURE__ */React3.createElement(\"html\", {\n    lang: \"en\"\n  }, /* @__PURE__ */React3.createElement(\"head\", null, /* @__PURE__ */React3.createElement(\"meta\", {\n    charSet: \"utf-8\"\n  }), /* @__PURE__ */React3.createElement(\"meta\", {\n    name: \"viewport\",\n    content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n  }), /* @__PURE__ */React3.createElement(\"title\", null, title)), /* @__PURE__ */React3.createElement(\"body\", null, /* @__PURE__ */React3.createElement(\"main\", {\n    style: {\n      fontFamily: \"system-ui, sans-serif\",\n      padding: \"2rem\"\n    }\n  }, children)));\n}\nfunction RSCDefaultRootErrorBoundaryImpl({\n  error,\n  renderAppShell\n}) {\n  console.error(error);\n  let heyDeveloper = /* @__PURE__ */React3.createElement(\"script\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        console.log(\n          \"\\u{1F4BF} Hey developer \\u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information.\"\n        );\n      `\n    }\n  });\n  if (isRouteErrorResponse(error)) {\n    return /* @__PURE__ */React3.createElement(ErrorWrapper, {\n      renderAppShell,\n      title: \"Unhandled Thrown Response!\"\n    }, /* @__PURE__ */React3.createElement(\"h1\", {\n      style: {\n        fontSize: \"24px\"\n      }\n    }, error.status, \" \", error.statusText), ENABLE_DEV_WARNINGS ? heyDeveloper : null);\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /* @__PURE__ */React3.createElement(ErrorWrapper, {\n    renderAppShell,\n    title: \"Application Error!\"\n  }, /* @__PURE__ */React3.createElement(\"h1\", {\n    style: {\n      fontSize: \"24px\"\n    }\n  }, \"Application Error\"), /* @__PURE__ */React3.createElement(\"pre\", {\n    style: {\n      padding: \"2rem\",\n      background: \"hsla(10, 50%, 50%, 0.1)\",\n      color: \"red\",\n      overflow: \"auto\"\n    }\n  }, errorInstance.stack), heyDeveloper);\n}\nfunction RSCDefaultRootErrorBoundary({\n  hasRootLayout\n}) {\n  let error = useRouteError();\n  if (hasRootLayout === void 0) {\n    throw new Error(\"Missing 'hasRootLayout' prop\");\n  }\n  return /* @__PURE__ */React3.createElement(RSCDefaultRootErrorBoundaryImpl, {\n    renderAppShell: !hasRootLayout,\n    error\n  });\n}\n\n// lib/rsc/route-modules.ts\nfunction createRSCRouteModules(payload) {\n  const routeModules = {};\n  for (const match of payload.matches) {\n    populateRSCRouteModules(routeModules, match);\n  }\n  return routeModules;\n}\nfunction populateRSCRouteModules(routeModules, matches) {\n  matches = Array.isArray(matches) ? matches : [matches];\n  for (const match of matches) {\n    routeModules[match.id] = {\n      links: match.links,\n      meta: match.meta,\n      default: noopComponent\n    };\n  }\n}\nvar noopComponent = () => null;\n\n// lib/rsc/browser.tsx\nfunction createCallServer({\n  createFromReadableStream,\n  createTemporaryReferenceSet,\n  encodeReply,\n  fetch: fetchImplementation = fetch\n}) {\n  const globalVar = window;\n  let landedActionId = 0;\n  return async (id, args) => {\n    let actionId = globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    const temporaryReferences = createTemporaryReferenceSet();\n    const response = await fetchImplementation(new Request(location.href, {\n      body: await encodeReply(args, {\n        temporaryReferences\n      }),\n      method: \"POST\",\n      headers: {\n        Accept: \"text/x-component\",\n        \"rsc-action-id\": id\n      }\n    }));\n    if (!response.body) {\n      throw new Error(\"No response body\");\n    }\n    const payload = await createFromReadableStream(response.body, {\n      temporaryReferences\n    });\n    if (payload.type === \"redirect\") {\n      if (payload.reload) {\n        window.location.href = payload.location;\n        return;\n      }\n      globalVar.__reactRouterDataRouter.navigate(payload.location, {\n        replace: payload.replace\n      });\n      return payload.actionResult;\n    }\n    if (payload.type !== \"action\") {\n      throw new Error(\"Unexpected payload type\");\n    }\n    if (payload.rerender) {\n      React4.startTransition(\n      // @ts-expect-error - We have old react types that don't know this can be async\n      async () => {\n        const rerender = await payload.rerender;\n        if (!rerender) return;\n        if (landedActionId < actionId && globalVar.__routerActionID <= actionId) {\n          landedActionId = actionId;\n          if (rerender.type === \"redirect\") {\n            if (rerender.reload) {\n              window.location.href = rerender.location;\n              return;\n            }\n            globalVar.__reactRouterDataRouter.navigate(rerender.location, {\n              replace: rerender.replace\n            });\n            return;\n          }\n          let lastMatch;\n          for (const match of rerender.matches) {\n            globalVar.__reactRouterDataRouter.patchRoutes(lastMatch?.id ?? null, [createRouteFromServerManifest(match)], true);\n            lastMatch = match;\n          }\n          window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp({});\n          React4.startTransition(() => {\n            window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp({\n              loaderData: Object.assign({}, globalVar.__reactRouterDataRouter.state.loaderData, rerender.loaderData),\n              errors: rerender.errors ? Object.assign({}, globalVar.__reactRouterDataRouter.state.errors, rerender.errors) : null\n            });\n          });\n        }\n      });\n    }\n    return payload.actionResult;\n  };\n}\nfunction createRouterFromPayload({\n  fetchImplementation,\n  createFromReadableStream,\n  unstable_getContext,\n  payload\n}) {\n  const globalVar = window;\n  if (globalVar.__reactRouterDataRouter && globalVar.__reactRouterRouteModules) return {\n    router: globalVar.__reactRouterDataRouter,\n    routeModules: globalVar.__reactRouterRouteModules\n  };\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  globalVar.__reactRouterRouteModules = globalVar.__reactRouterRouteModules ?? {};\n  populateRSCRouteModules(globalVar.__reactRouterRouteModules, payload.matches);\n  let patches = /* @__PURE__ */new Map();\n  payload.patches?.forEach(patch => {\n    invariant(patch.parentId, \"Invalid patch parentId\");\n    if (!patches.has(patch.parentId)) {\n      patches.set(patch.parentId, []);\n    }\n    patches.get(patch.parentId)?.push(patch);\n  });\n  let routes = payload.matches.reduceRight((previous, match) => {\n    const route = createRouteFromServerManifest(match, payload);\n    if (previous.length > 0) {\n      route.children = previous;\n      let childrenToPatch = patches.get(match.id);\n      if (childrenToPatch) {\n        route.children.push(...childrenToPatch.map(r => createRouteFromServerManifest(r)));\n      }\n    }\n    return [route];\n  }, []);\n  globalVar.__reactRouterDataRouter = createRouter({\n    routes,\n    unstable_getContext,\n    basename: payload.basename,\n    history: createBrowserHistory(),\n    hydrationData: getHydrationData({\n      loaderData: payload.loaderData,\n      actionData: payload.actionData,\n      errors: payload.errors\n    }, routes, routeId => {\n      let match = payload.matches.find(m => m.id === routeId);\n      invariant(match, \"Route not found in payload\");\n      return {\n        clientLoader: match.clientLoader,\n        hasLoader: match.hasLoader,\n        hasHydrateFallback: match.hydrateFallbackElement != null\n      };\n    }, payload.location, void 0, false),\n    async patchRoutesOnNavigation({\n      path,\n      signal\n    }) {\n      if (discoveredPaths.has(path)) {\n        return;\n      }\n      await fetchAndApplyManifestPatches([path], createFromReadableStream, fetchImplementation, signal);\n    },\n    // FIXME: Pass `build.ssr` into this function\n    dataStrategy: getRSCSingleFetchDataStrategy(() => globalVar.__reactRouterDataRouter, true, payload.basename, createFromReadableStream, fetchImplementation)\n  });\n  if (globalVar.__reactRouterDataRouter.state.initialized) {\n    globalVar.__routerInitialized = true;\n    globalVar.__reactRouterDataRouter.initialize();\n  } else {\n    globalVar.__routerInitialized = false;\n  }\n  let lastLoaderData = void 0;\n  globalVar.__reactRouterDataRouter.subscribe(({\n    loaderData,\n    actionData\n  }) => {\n    if (lastLoaderData !== loaderData) {\n      globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    }\n  });\n  globalVar.__reactRouterDataRouter._updateRoutesForHMR = routeUpdateByRouteId => {\n    const oldRoutes = window.__reactRouterDataRouter.routes;\n    const newRoutes = [];\n    function walkRoutes(routes2, parentId) {\n      return routes2.map(route => {\n        const routeUpdate = routeUpdateByRouteId.get(route.id);\n        if (routeUpdate) {\n          const {\n            routeModule,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader\n          } = routeUpdate;\n          const newRoute = createRouteFromServerManifest({\n            clientAction: routeModule.clientAction,\n            clientLoader: routeModule.clientLoader,\n            element: route.element,\n            errorElement: route.errorElement,\n            handle: route.handle,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader,\n            hydrateFallbackElement: route.hydrateFallbackElement,\n            id: route.id,\n            index: route.index,\n            links: routeModule.links,\n            meta: routeModule.meta,\n            parentId,\n            path: route.path,\n            shouldRevalidate: routeModule.shouldRevalidate\n          });\n          if (route.children) {\n            newRoute.children = walkRoutes(route.children, route.id);\n          }\n          return newRoute;\n        }\n        const updatedRoute = {\n          ...route\n        };\n        if (route.children) {\n          updatedRoute.children = walkRoutes(route.children, route.id);\n        }\n        return updatedRoute;\n      });\n    }\n    newRoutes.push(...walkRoutes(oldRoutes, void 0));\n    window.__reactRouterDataRouter._internalSetRoutes(newRoutes);\n  };\n  return {\n    router: globalVar.__reactRouterDataRouter,\n    routeModules: globalVar.__reactRouterRouteModules\n  };\n}\nvar renderedRoutesContext = unstable_createContext();\nfunction getRSCSingleFetchDataStrategy(getRouter, ssr, basename, createFromReadableStream, fetchImplementation) {\n  let dataStrategy = getSingleFetchDataStrategyImpl(getRouter, match => {\n    let M = match;\n    return {\n      hasLoader: M.route.hasLoader,\n      hasClientLoader: M.route.hasClientLoader,\n      hasComponent: M.route.hasComponent,\n      hasAction: M.route.hasAction,\n      hasClientAction: M.route.hasClientAction,\n      hasShouldRevalidate: M.route.hasShouldRevalidate\n    };\n  },\n  // pass map into fetchAndDecode so it can add payloads\n  getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation), ssr, basename,\n  // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match => {\n    let M = match;\n    return M.route.hasComponent && !M.route.element;\n  });\n  return async args => args.unstable_runClientMiddleware(async () => {\n    let context = args.context;\n    context.set(renderedRoutesContext, []);\n    let results = await dataStrategy(args);\n    const renderedRoutesById = /* @__PURE__ */new Map();\n    for (const route of context.get(renderedRoutesContext)) {\n      if (!renderedRoutesById.has(route.id)) {\n        renderedRoutesById.set(route.id, []);\n      }\n      renderedRoutesById.get(route.id).push(route);\n    }\n    for (const match of args.matches) {\n      const renderedRoutes = renderedRoutesById.get(match.route.id);\n      if (renderedRoutes) {\n        for (const rendered of renderedRoutes) {\n          window.__reactRouterDataRouter.patchRoutes(rendered.parentId ?? null, [createRouteFromServerManifest(rendered)], true);\n        }\n      }\n    }\n    return results;\n  });\n}\nfunction getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation) {\n  return async (args, basename, targetRoutes) => {\n    let {\n      request,\n      context\n    } = args;\n    let url = singleFetchUrl(request.url, basename, \"rsc\");\n    if (request.method === \"GET\") {\n      url = stripIndexParam(url);\n      if (targetRoutes) {\n        url.searchParams.set(\"_routes\", targetRoutes.join(\",\"));\n      }\n    }\n    let res = await fetchImplementation(new Request(url, await createRequestInit(request)));\n    if (res.status === 404 && !res.headers.has(\"X-Remix-Response\")) {\n      throw new ErrorResponseImpl(404, \"Not Found\", true);\n    }\n    invariant(res.body, \"No response body to decode\");\n    try {\n      const payload = await createFromReadableStream(res.body, {\n        temporaryReferences: void 0\n      });\n      if (payload.type === \"redirect\") {\n        return {\n          status: res.status,\n          data: {\n            redirect: {\n              redirect: payload.location,\n              reload: payload.reload,\n              replace: payload.replace,\n              revalidate: false,\n              status: payload.status\n            }\n          }\n        };\n      }\n      if (payload.type !== \"render\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      context.get(renderedRoutesContext).push(...payload.matches);\n      let results = {\n        routes: {}\n      };\n      const dataKey = isMutationMethod(request.method) ? \"actionData\" : \"loaderData\";\n      for (let [routeId, data2] of Object.entries(payload[dataKey] || {})) {\n        results.routes[routeId] = {\n          data: data2\n        };\n      }\n      if (payload.errors) {\n        for (let [routeId, error] of Object.entries(payload.errors)) {\n          results.routes[routeId] = {\n            error\n          };\n        }\n      }\n      return {\n        status: res.status,\n        data: results\n      };\n    } catch (e) {\n      throw new Error(\"Unable to decode RSC response\");\n    }\n  };\n}\nfunction RSCHydratedRouter({\n  createFromReadableStream,\n  fetch: fetchImplementation = fetch,\n  payload,\n  routeDiscovery = \"eager\",\n  unstable_getContext\n}) {\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let {\n    router,\n    routeModules\n  } = React4.useMemo(() => createRouterFromPayload({\n    payload,\n    fetchImplementation,\n    unstable_getContext,\n    createFromReadableStream\n  }), [createFromReadableStream, payload, fetchImplementation, unstable_getContext]);\n  React4.useEffect(() => {\n    setIsHydrated();\n  }, []);\n  React4.useLayoutEffect(() => {\n    const globalVar = window;\n    if (!globalVar.__routerInitialized) {\n      globalVar.__routerInitialized = true;\n      globalVar.__reactRouterDataRouter.initialize();\n    }\n  }, []);\n  let [location2, setLocation] = React4.useState(router.state.location);\n  React4.useLayoutEffect(() => router.subscribe(newState => {\n    if (newState.location !== location2) {\n      setLocation(newState.location);\n    }\n  }), [router, location2]);\n  React4.useEffect(() => {\n    if (routeDiscovery === \"lazy\" ||\n    // @ts-expect-error - TS doesn't know about this yet\n    window.navigator?.connection?.saveData === true) {\n      return;\n    }\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let pathname = el.tagName === \"A\" ? el.pathname : new URL(path, window.location.origin).pathname;\n      if (!discoveredPaths.has(pathname)) {\n        nextPaths.add(pathname);\n      }\n    }\n    async function fetchPatches() {\n      document.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(registerElement);\n      let paths = Array.from(nextPaths.keys()).filter(path => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (paths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation);\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    fetchPatches();\n    let observer = new MutationObserver(() => debouncedFetchPatches());\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n  }, [routeDiscovery, createFromReadableStream, fetchImplementation]);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: {\n      mode: \"lazy\",\n      manifestPath: \"/__manifest\"\n    },\n    routeModules\n  };\n  return /* @__PURE__ */React4.createElement(RSCRouterContext.Provider, {\n    value: true\n  }, /* @__PURE__ */React4.createElement(RSCRouterGlobalErrorBoundary, {\n    location: location2\n  }, /* @__PURE__ */React4.createElement(FrameworkContext.Provider, {\n    value: frameworkContext\n  }, /* @__PURE__ */React4.createElement(RouterProvider, {\n    router,\n    flushSync: ReactDOM.flushSync\n  }))));\n}\nfunction createRouteFromServerManifest(match, payload) {\n  let hasInitialData = payload && match.id in payload.loaderData;\n  let initialData = payload?.loaderData[match.id];\n  let hasInitialError = payload?.errors && match.id in payload.errors;\n  let initialError = payload?.errors?.[match.id];\n  let isHydrationRequest = match.clientLoader?.hydrate === true || !match.hasLoader ||\n  // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match.hasComponent && !match.element;\n  invariant(window.__reactRouterRouteModules);\n  populateRSCRouteModules(window.__reactRouterRouteModules, match);\n  let dataRoute = {\n    id: match.id,\n    element: match.element,\n    errorElement: match.errorElement,\n    handle: match.handle,\n    hasErrorBoundary: match.hasErrorBoundary,\n    hydrateFallbackElement: match.hydrateFallbackElement,\n    index: match.index,\n    loader: match.clientLoader ? async (args, singleFetch) => {\n      try {\n        let result = await match.clientLoader({\n          ...args,\n          serverLoader: () => {\n            preventInvalidServerHandlerCall(\"loader\", match.id, match.hasLoader);\n            if (isHydrationRequest) {\n              if (hasInitialData) {\n                return initialData;\n              }\n              if (hasInitialError) {\n                throw initialError;\n              }\n            }\n            return callSingleFetch(singleFetch);\n          }\n        });\n        return result;\n      } finally {\n        isHydrationRequest = false;\n      }\n    } :\n    // We always make the call in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    (_, singleFetch) => callSingleFetch(singleFetch),\n    action: match.clientAction ? (args, singleFetch) => match.clientAction({\n      ...args,\n      serverAction: async () => {\n        preventInvalidServerHandlerCall(\"action\", match.id, match.hasLoader);\n        return await callSingleFetch(singleFetch);\n      }\n    }) : match.hasAction ? (_, singleFetch) => callSingleFetch(singleFetch) : () => {\n      throw noActionDefinedError(\"action\", match.id);\n    },\n    path: match.path,\n    shouldRevalidate: match.shouldRevalidate,\n    // We always have a \"loader\" in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    hasLoader: true,\n    hasClientLoader: match.clientLoader != null,\n    hasAction: match.hasAction,\n    hasClientAction: match.clientAction != null,\n    hasShouldRevalidate: match.shouldRevalidate != null\n  };\n  if (typeof dataRoute.loader === \"function\") {\n    dataRoute.loader.hydrate = shouldHydrateRouteLoader(match.id, match.clientLoader, match.hasLoader, false);\n  }\n  return dataRoute;\n}\nfunction callSingleFetch(singleFetch) {\n  invariant(typeof singleFetch === \"function\", \"Invalid singleFetch parameter\");\n  return singleFetch();\n}\nfunction preventInvalidServerHandlerCall(type, routeId, hasHandler) {\n  if (!hasHandler) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You are trying to call ${fn} on a route that does not have a server ${type} (routeId: \"${routeId}\")`;\n    console.error(msg);\n    throw new ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nvar nextPaths = /* @__PURE__ */new Set();\nvar discoveredPathsMaxSize = 1e3;\nvar discoveredPaths = /* @__PURE__ */new Set();\nvar URL_LIMIT = 7680;\nfunction getManifestUrl(paths) {\n  if (paths.length === 0) {\n    return null;\n  }\n  if (paths.length === 1) {\n    return new URL(`${paths[0]}.manifest`, window.location.origin);\n  }\n  const globalVar = window;\n  let basename = (globalVar.__reactRouterDataRouter.basename ?? \"\").replace(/^\\/|\\/$/g, \"\");\n  let url = new URL(`${basename}/.manifest`, window.location.origin);\n  paths.sort().forEach(path => url.searchParams.append(\"p\", path));\n  return url;\n}\nasync function fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation, signal) {\n  let url = getManifestUrl(paths);\n  if (url == null) {\n    return;\n  }\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let response = await fetchImplementation(new Request(url, {\n    signal\n  }));\n  if (!response.body || response.status < 200 || response.status >= 300) {\n    throw new Error(\"Unable to fetch new route matches from the server\");\n  }\n  let payload = await createFromReadableStream(response.body, {\n    temporaryReferences: void 0\n  });\n  if (payload.type !== \"manifest\") {\n    throw new Error(\"Failed to patch routes\");\n  }\n  paths.forEach(p => addToFifoQueue(p, discoveredPaths));\n  payload.patches.forEach(p => {\n    window.__reactRouterDataRouter.patchRoutes(p.parentId ?? null, [createRouteFromServerManifest(p)]);\n  });\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    queue.delete(first);\n  }\n  queue.add(path);\n}\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return (...args) => {\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\n// lib/rsc/server.ssr.tsx\nimport * as React5 from \"react\";\n\n// lib/rsc/html-stream/server.ts\nvar encoder2 = new TextEncoder();\nvar trailer = \"</body></html>\";\nfunction injectRSCPayload(rscStream) {\n  let decoder = new TextDecoder();\n  let resolveFlightDataPromise;\n  let flightDataPromise = new Promise(resolve => resolveFlightDataPromise = resolve);\n  let startedRSC = false;\n  let buffered = [];\n  let timeout = null;\n  function flushBufferedChunks(controller) {\n    for (let chunk of buffered) {\n      let buf = decoder.decode(chunk, {\n        stream: true\n      });\n      if (buf.endsWith(trailer)) {\n        buf = buf.slice(0, -trailer.length);\n      }\n      controller.enqueue(encoder2.encode(buf));\n    }\n    buffered.length = 0;\n    timeout = null;\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      buffered.push(chunk);\n      if (timeout) {\n        return;\n      }\n      timeout = setTimeout(async () => {\n        flushBufferedChunks(controller);\n        if (!startedRSC) {\n          startedRSC = true;\n          writeRSCStream(rscStream, controller).catch(err => controller.error(err)).then(resolveFlightDataPromise);\n        }\n      }, 0);\n    },\n    async flush(controller) {\n      await flightDataPromise;\n      if (timeout) {\n        clearTimeout(timeout);\n        flushBufferedChunks(controller);\n      }\n      controller.enqueue(encoder2.encode(\"</body></html>\"));\n    }\n  });\n}\nasync function writeRSCStream(rscStream, controller) {\n  let decoder = new TextDecoder(\"utf-8\", {\n    fatal: true\n  });\n  const reader = rscStream.getReader();\n  try {\n    let read;\n    while ((read = await reader.read()) && !read.done) {\n      const chunk = read.value;\n      try {\n        writeChunk(JSON.stringify(decoder.decode(chunk, {\n          stream: true\n        })), controller);\n      } catch (err) {\n        let base64 = JSON.stringify(btoa(String.fromCodePoint(...chunk)));\n        writeChunk(`Uint8Array.from(atob(${base64}), m => m.codePointAt(0))`, controller);\n      }\n    }\n  } finally {\n    reader.releaseLock();\n  }\n  let remaining = decoder.decode();\n  if (remaining.length) {\n    writeChunk(JSON.stringify(remaining), controller);\n  }\n}\nfunction writeChunk(chunk, controller) {\n  controller.enqueue(encoder2.encode(`<script>${escapeScript(`(self.__FLIGHT_DATA||=[]).push(${chunk})`)}</script>`));\n}\nfunction escapeScript(script) {\n  return script.replace(/<!--/g, \"<\\\\!--\").replace(/<\\/(script)/gi, \"</\\\\$1\");\n}\n\n// lib/rsc/server.ssr.tsx\nvar REACT_USE = \"use\";\nvar useImpl = React5[REACT_USE];\nfunction useSafe(promise) {\n  if (useImpl) {\n    return useImpl(promise);\n  }\n  throw new Error(\"React Router v7 requires React 19+ for RSC features.\");\n}\nasync function routeRSCServerRequest({\n  request,\n  fetchServer,\n  createFromReadableStream,\n  renderHTML,\n  hydrate = true\n}) {\n  const url = new URL(request.url);\n  const isDataRequest = isReactServerRequest(url);\n  const respondWithRSCPayload = isDataRequest || isManifestRequest(url) || request.headers.has(\"rsc-action-id\");\n  const serverResponse = await fetchServer(request);\n  if (respondWithRSCPayload || serverResponse.headers.get(\"React-Router-Resource\") === \"true\") {\n    return serverResponse;\n  }\n  if (!serverResponse.body) {\n    throw new Error(\"Missing body in server response\");\n  }\n  let serverResponseB = null;\n  if (hydrate) {\n    serverResponseB = serverResponse.clone();\n  }\n  const body = serverResponse.body;\n  let payloadPromise;\n  const getPayload = async () => {\n    if (payloadPromise) return payloadPromise;\n    payloadPromise = createFromReadableStream(body);\n    return payloadPromise;\n  };\n  try {\n    const payload = await getPayload();\n    if (serverResponse.status === SINGLE_FETCH_REDIRECT_STATUS && payload.type === \"redirect\") {\n      const headers2 = new Headers(serverResponse.headers);\n      headers2.delete(\"Content-Encoding\");\n      headers2.delete(\"Content-Length\");\n      headers2.delete(\"Content-Type\");\n      headers2.delete(\"x-remix-response\");\n      headers2.set(\"Location\", payload.location);\n      return new Response(serverResponseB?.body || \"\", {\n        headers: headers2,\n        status: payload.status,\n        statusText: serverResponse.statusText\n      });\n    }\n    const html = await renderHTML(getPayload);\n    const headers = new Headers(serverResponse.headers);\n    headers.set(\"Content-Type\", \"text/html\");\n    if (!hydrate) {\n      return new Response(html, {\n        status: serverResponse.status,\n        headers\n      });\n    }\n    if (!serverResponseB?.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n    return new Response(body2, {\n      status: serverResponse.status,\n      headers\n    });\n  } catch (reason) {\n    if (reason instanceof Response) {\n      return reason;\n    }\n    throw reason;\n  }\n}\nfunction RSCStaticRouter({\n  getPayload\n}) {\n  const payload = useSafe(getPayload());\n  if (payload.type === \"redirect\") {\n    throw new Response(null, {\n      status: payload.status,\n      headers: {\n        Location: payload.location\n      }\n    });\n  }\n  if (payload.type !== \"render\") return null;\n  let patchedLoaderData = {\n    ...payload.loaderData\n  };\n  for (const match of payload.matches) {\n    if (shouldHydrateRouteLoader(match.id, match.clientLoader, match.hasLoader, false) && (match.hydrateFallbackElement || !match.hasLoader)) {\n      delete patchedLoaderData[match.id];\n    }\n  }\n  const context = {\n    actionData: payload.actionData,\n    actionHeaders: {},\n    basename: payload.basename,\n    errors: payload.errors,\n    loaderData: patchedLoaderData,\n    loaderHeaders: {},\n    location: payload.location,\n    statusCode: 200,\n    matches: payload.matches.map(match => ({\n      params: match.params,\n      pathname: match.pathname,\n      pathnameBase: match.pathnameBase,\n      route: {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        handle: match.handle,\n        hasErrorBoundary: match.hasErrorBoundary,\n        loader: match.hasLoader || !!match.clientLoader,\n        index: match.index,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      }\n    }))\n  };\n  const router = createStaticRouter(payload.matches.reduceRight((previous, match) => {\n    const route = {\n      id: match.id,\n      action: match.hasAction || !!match.clientAction,\n      element: match.element,\n      errorElement: match.errorElement,\n      handle: match.handle,\n      hasErrorBoundary: !!match.errorElement,\n      hydrateFallbackElement: match.hydrateFallbackElement,\n      index: match.index,\n      loader: match.hasLoader || !!match.clientLoader,\n      path: match.path,\n      shouldRevalidate: match.shouldRevalidate\n    };\n    if (previous.length > 0) {\n      route.children = previous;\n    }\n    return [route];\n  }, []), context);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: {\n      mode: \"lazy\",\n      manifestPath: \"/__manifest\"\n    },\n    routeModules: createRSCRouteModules(payload)\n  };\n  return /* @__PURE__ */React5.createElement(RSCRouterContext.Provider, {\n    value: true\n  }, /* @__PURE__ */React5.createElement(RSCRouterGlobalErrorBoundary, {\n    location: payload.location\n  }, /* @__PURE__ */React5.createElement(FrameworkContext.Provider, {\n    value: frameworkContext\n  }, /* @__PURE__ */React5.createElement(StaticRouterProvider, {\n    context,\n    router,\n    hydrate: false,\n    nonce: payload.nonce\n  }))));\n}\nfunction isReactServerRequest(url) {\n  return url.pathname.endsWith(\".rsc\");\n}\nfunction isManifestRequest(url) {\n  return url.pathname.endsWith(\".manifest\");\n}\n\n// lib/rsc/html-stream/browser.ts\nfunction getRSCStream() {\n  let encoder3 = new TextEncoder();\n  let streamController = null;\n  let rscStream = new ReadableStream({\n    start(controller) {\n      if (typeof window === \"undefined\") {\n        return;\n      }\n      let handleChunk = chunk => {\n        if (typeof chunk === \"string\") {\n          controller.enqueue(encoder3.encode(chunk));\n        } else {\n          controller.enqueue(chunk);\n        }\n      };\n      window.__FLIGHT_DATA || (window.__FLIGHT_DATA = []);\n      window.__FLIGHT_DATA.forEach(handleChunk);\n      window.__FLIGHT_DATA.push = chunk => {\n        handleChunk(chunk);\n        return 0;\n      };\n      streamController = controller;\n    }\n  });\n  if (typeof document !== \"undefined\" && document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      streamController?.close();\n    });\n  } else {\n    streamController?.close();\n  }\n  return rscStream;\n}\n\n// lib/dom/ssr/errors.ts\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {}\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\nexport { ServerRouter, createRoutesStub, createCookie, isCookie, ServerMode, setDevServerHooks, createRequestHandler, createSession, isSession, createSessionStorage, createCookieSessionStorage, createMemorySessionStorage, href, getHydrationData, RSCDefaultRootErrorBoundary, createCallServer, RSCHydratedRouter, routeRSCServerRequest, RSCStaticRouter, getRSCStream, deserializeErrors };", "map": {"version": 3, "names": ["ENABLE_DEV_WARNINGS", "ErrorResponseImpl", "FrameworkContext", "NO_BODY_STATUS_CODES", "Outlet", "RSCRouterContext", "RemixErrorBoundary", "RouterProvider", "SINGLE_FETCH_REDIRECT_STATUS", "SingleFetchRedirectSymbol", "StaticRouterProvider", "StreamTransfer", "convertRoutesToDataRoutes", "createBrowserHistory", "createMemoryRouter", "createRequestInit", "createRouter", "createServerRoutes", "createStaticHandler", "createStaticRouter", "decodeViaTurboStream", "encode", "getManifestPath", "getSingleFetchDataStrategyImpl", "getStaticContextFromError", "invariant", "isDataWithResponseInit", "isMutationMethod", "isRedirectResponse", "isRedirectStatusCode", "isResponse", "isRouteErrorResponse", "matchRoutes", "noActionDefinedError", "redirect", "redirectDocument", "replace", "setIsHydrated", "shouldHydrateRouteLoader", "singleFetchUrl", "stripBasename", "stripIndexParam", "unstable_RouterContextProvider", "unstable_createContext", "useRouteError", "warnOnce", "withComponentProps", "withErrorBoundaryProps", "withHydrateFallbackProps", "React", "ServerRouter", "context", "url", "nonce", "URL", "manifest", "routeModules", "criticalCss", "serverHandoffString", "routes", "future", "isSpaMode", "staticHandlerContext", "loaderData", "match", "matches", "routeId", "route", "id", "manifestRoute", "clientLoader", "<PERSON><PERSON><PERSON><PERSON>", "HydrateFallback", "router", "createElement", "Fragment", "Provider", "value", "ssr", "routeDiscovery", "serializeError", "renderMeta", "location", "state", "hydrate", "serverHandoffStream", "Suspense", "identifier", "reader", "<PERSON><PERSON><PERSON><PERSON>", "textDecoder", "TextDecoder", "React2", "createRoutesStub", "_context", "RoutesTestStub", "initialEntries", "initialIndex", "hydrationData", "routerRef", "useRef", "frameworkContextRef", "current", "unstable_subResourceIntegrity", "unstable_middleware", "entry", "imports", "module", "version", "mode", "manifestPath", "patched", "processRoutes", "r", "parentId", "map", "Error", "newRoute", "path", "index", "Component", "Error<PERSON>ou<PERSON><PERSON>", "action", "args", "loader", "handle", "shouldRevalidate", "entryRoute", "hasAction", "hasClientAction", "hasClientLoader", "hasClientMiddleware", "hasErrorBou<PERSON>ry", "clientActionModule", "clientLoaderModule", "clientMiddlewareModule", "hydrateFallbackModule", "default", "links", "meta", "children", "parse", "serialize", "encoder", "TextEncoder", "sign", "secret", "data2", "key", "create<PERSON><PERSON>", "signature", "crypto", "subtle", "hash", "btoa", "String", "fromCharCode", "Uint8Array", "unsign", "cookie", "lastIndexOf", "slice", "byteStringToUint8Array", "atob", "valid", "verify", "error", "usages", "importKey", "name", "byteString", "array", "length", "i", "charCodeAt", "createCookie", "cookieOptions", "secrets", "options", "sameSite", "warnOnceAboutExpiresCookie", "expires", "isSigned", "maxAge", "Date", "now", "cookieHeader", "parseOptions", "cookies", "decoded", "decodeCookieValue", "serializeOptions", "encodeCookieValue", "<PERSON><PERSON><PERSON><PERSON>", "object", "encoded", "encodeData", "unsignedValue", "decodeData", "myUnescape", "encodeURIComponent", "JSON", "stringify", "decodeURIComponent", "myEscape", "str", "toString", "result", "chr", "code", "char<PERSON>t", "exec", "hex", "toUpperCase", "part", "parseInt", "createEntryRouteModules", "Object", "keys", "reduce", "memo", "ServerMode", "ServerMode2", "isServerMode", "sanitizeError", "serverMode", "sanitized", "stack", "sanitizeErrors", "errors", "entries", "acc", "assign", "message", "serializeErrors", "serialized", "val", "__type", "__subType", "matchServerRoutes", "pathname", "basename", "params", "callRouteHandler", "handler", "request", "stripRoutesParam", "stripIndexParam2", "init", "status", "Response", "indexValues", "searchParams", "getAll", "delete", "indexValuesToKeep", "indexValue", "push", "<PERSON><PERSON><PERSON>", "append", "method", "body", "headers", "signal", "duplex", "Request", "href", "invariant2", "console", "globalDevServerHooksKey", "setDevServerHooks", "devServerHooks", "globalThis", "getDevServerHooks", "getBuildTimeHeader", "headerName", "process", "env", "IS_RR_BUILD_REQUEST", "get", "e", "groupRoutesByParentId", "values", "for<PERSON>ach", "createRoutes", "routesByParentId", "createStaticHandlerDataRoutes", "commonRoute", "preRenderedData", "decodeURI", "uint8array", "stream", "ReadableStream", "start", "controller", "enqueue", "close", "global", "reload", "data", "caseSensitive", "ESCAPE_LOOKUP", "ESCAPE_REGEX", "escapeHtml", "html", "createServerHandoffString", "serverHandoff", "splitCookiesString", "getDocumentHeaders", "build", "getDocumentHeadersImpl", "m", "getRouteHeadersFn", "_defaultHeaders", "boundaryIdx", "findIndex", "errorHeaders", "actionHeaders", "actionData", "loaderHeaders", "some", "hasOwnProperty", "defaultHeaders", "Headers", "parentHeaders", "idx", "includeErrorHeaders", "includeErrorCookies", "headersFn", "headers2", "prependCookies", "childHeaders", "parentSetCookieString", "childCookies", "Set", "getSetCookie", "has", "SERVER_NO_BODY_STATUS_CODES", "singleFetchAction", "static<PERSON><PERSON><PERSON>", "handlerUrl", "loadContext", "handleError", "handlerRequest", "query", "requestContext", "skipL<PERSON>derError<PERSON><PERSON>bling", "skipRevalidation", "unstable_generateMiddlewareResponse", "innerResult", "handleQueryResult", "handleQueryError", "staticContextToResponse", "generateSingleFetchResponse", "statusCode", "err", "singleFetchResult", "singleFetchLoaders", "routesParam", "loadRouteIds", "split", "filterMatchesToLoad", "results", "loadedMatches", "filter", "resultHeaders", "set", "encodeViaTurboStream", "streamTimeout", "generateSingleFetchRedirectResponse", "redirectResponse", "redirect2", "getSingleFetchRedirect", "revalidate", "requestSignal", "AbortController", "timeoutId", "setTimeout", "abort", "addEventListener", "clearTimeout", "plugins", "data3", "statusText", "postPlugins", "fromEntries", "derive", "dataRoutes", "<PERSON><PERSON><PERSON><PERSON>", "aborted", "createRequestHandler", "_build", "requestHandler", "initialContext", "derived", "processRequestError", "returnLastResortErrorResponse", "normalizedBasename", "normalizedPath", "endsWith", "decodedPath", "<PERSON><PERSON><PERSON>", "prerender", "includes", "manifestUrl", "res", "handleManifestRequest", "response", "singleFetchMatches", "handleSingleFetchRequest", "handleDataRequest", "handleResourceRequest", "unstable_getCriticalCss", "getCriticalCss", "handleDocumentRequest", "assets", "patches", "paths", "startsWith", "segments", "_", "partialPath", "join", "add", "json", "renderHtml", "isSpaMode2", "baseServerHandoff", "entryContext", "handleDocumentRequestFunction", "errorForSecondRender", "unwrapResponse", "state2", "error2", "queryRoute", "handleQueryRouteResult", "handleQueryRouteError", "errorResponseToJson", "newError", "errorResponse", "contentType", "test", "text", "flash", "createSession", "initialData", "Map", "flashName", "unset", "isSession", "createSessionStorage", "cookieArg", "createData", "readData", "updateData", "deleteData", "warnOnceAboutSigningSessionCookie", "getSession", "commitSession", "session", "destroySession", "createCookieSessionStorage", "serializedCookie", "_session", "createMemorySessionStorage", "Math", "random", "substring", "segment", "param", "isRequired", "React4", "ReactDOM", "getHydrationData", "getRouteInfo", "location2", "initialMatches", "routeInfo", "hasHydrateFallback", "React3", "RSCRouterGlobalErrorBoundary", "constructor", "props", "getDerivedStateFromError", "getDerivedStateFromProps", "render", "RSCDefaultRootErrorBoundaryImpl", "renderAppShell", "ErrorWrapper", "title", "lang", "charSet", "content", "style", "fontFamily", "padding", "heyDeveloper", "dangerouslySetInnerHTML", "__html", "fontSize", "errorInstance", "errorString", "background", "color", "overflow", "RSCDefaultRootErrorBoundary", "hasRootLayout", "createRSCRouteModules", "payload", "populateRSCRouteModules", "Array", "isArray", "noopComponent", "createCallServer", "createFromReadableStream", "createTemporaryReferenceSet", "encodeReply", "fetch", "fetchImplementation", "globalVar", "window", "landedActionId", "actionId", "__routerActionID", "temporaryReferences", "Accept", "type", "__reactRouterDataRouter", "navigate", "actionResult", "rerender", "startTransition", "lastMatch", "patchRoutes", "createRouteFromServerManifest", "_internalSetStateDoNotUseOrYouWillBreakYourApp", "createRouterFromPayload", "unstable_getContext", "__reactRouterRouteModules", "patch", "reduceRight", "previous", "childrenToPatch", "history", "find", "hydrateFallbackElement", "patchRoutesOnNavigation", "discoveredPaths", "fetchAndApplyManifestPatches", "dataStrategy", "getRSCSingleFetchDataStrategy", "initialized", "__routerInitialized", "initialize", "lastLoaderData", "subscribe", "_updateRoutesForHMR", "routeUpdateByRouteId", "oldRoutes", "newRoutes", "walkRoutes", "routes2", "routeUpdate", "routeModule", "hasComponent", "clientAction", "element", "errorElement", "updatedRoute", "_internalSetRoutes", "renderedRoutesContext", "getRouter", "M", "hasShouldRevalidate", "getFetchAndDecodeViaRSC", "unstable_runClientMiddleware", "renderedRoutesById", "renderedRoutes", "rendered", "targetRoutes", "dataKey", "RSCHydratedRouter", "useMemo", "useEffect", "useLayoutEffect", "setLocation", "useState", "newState", "navigator", "connection", "saveData", "registerElement", "el", "tagName", "getAttribute", "origin", "nextPaths", "fetchPatches", "document", "querySelectorAll", "from", "debouncedFetchPatches", "debounce", "observer", "MutationObserver", "observe", "documentElement", "subtree", "childList", "attributes", "attributeFilter", "frameworkContext", "flushSync", "hasInitialData", "hasInitialError", "initialError", "isHydrationRequest", "dataRoute", "singleFetch", "serverLoader", "preventInvalidServerHandlerCall", "callSingleFetch", "serverAction", "<PERSON><PERSON><PERSON><PERSON>", "fn", "msg", "discoveredPathsMaxSize", "URL_LIMIT", "getManifestUrl", "sort", "clear", "p", "addToFifoQueue", "queue", "size", "first", "next", "callback", "wait", "React5", "encoder2", "trailer", "injectRSCPayload", "rscStream", "decoder", "resolveFlightDataPromise", "flightDataPromise", "Promise", "resolve", "startedRSC", "buffered", "timeout", "flushBufferedChunks", "chunk", "buf", "decode", "TransformStream", "transform", "writeRSCStream", "catch", "then", "flush", "fatal", "read", "done", "writeChunk", "base64", "fromCodePoint", "releaseLock", "remaining", "escapeScript", "script", "REACT_USE", "useImpl", "useSafe", "promise", "routeRSCServerRequest", "fetchServer", "renderHTML", "isDataRequest", "isReactServerRequest", "respondWithRSCPayload", "isManifestRequest", "serverResponse", "serverResponseB", "clone", "payloadPromise", "getPayload", "body2", "pipeThrough", "reason", "RSCStatic<PERSON><PERSON><PERSON>", "Location", "patchedLoaderData", "pathnameBase", "getRSCStream", "encoder3", "streamController", "handleChunk", "__FLIGHT_DATA", "readyState", "deserializeErrors", "internal", "ErrorConstructor"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-router/dist/development/chunk-5UALIXAM.mjs"], "sourcesContent": ["/**\n * react-router v7.8.2\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport {\n  ENABLE_DEV_WARNINGS,\n  ErrorResponseImpl,\n  FrameworkContext,\n  NO_BODY_STATUS_CODES,\n  Outlet,\n  RSCRouterContext,\n  RemixErrorBoundary,\n  RouterProvider,\n  SINGLE_FETCH_REDIRECT_STATUS,\n  SingleFetchRedirectSymbol,\n  StaticRouterProvider,\n  StreamTransfer,\n  convertRoutesToDataRoutes,\n  createBrowserHistory,\n  createMemoryRouter,\n  createRequestInit,\n  createRouter,\n  createServerRoutes,\n  createStaticHandler,\n  createStaticRouter,\n  decodeViaTurboStream,\n  encode,\n  getManifestPath,\n  getSingleFetchDataStrategyImpl,\n  getStaticContextFromError,\n  invariant,\n  isDataWithResponseInit,\n  isMutationMethod,\n  isRedirectResponse,\n  isRedirectStatusCode,\n  isResponse,\n  isRouteErrorResponse,\n  matchRoutes,\n  noActionDefinedError,\n  redirect,\n  redirectDocument,\n  replace,\n  setIsHydrated,\n  shouldHydrateRouteLoader,\n  singleFetchUrl,\n  stripBasename,\n  stripIndexParam,\n  unstable_RouterContextProvider,\n  unstable_createContext,\n  useRouteError,\n  warnOnce,\n  withComponentProps,\n  withErrorBoundaryProps,\n  withHydrateFallbackProps\n} from \"./chunk-PVWAREVJ.mjs\";\n\n// lib/dom/ssr/server.tsx\nimport * as React from \"react\";\nfunction ServerRouter({\n  context,\n  url,\n  nonce\n}) {\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let { manifest, routeModules, criticalCss, serverHandoffString } = context;\n  let routes = createServerRoutes(\n    manifest.routes,\n    routeModules,\n    context.future,\n    context.isSpaMode\n  );\n  context.staticHandlerContext.loaderData = {\n    ...context.staticHandlerContext.loaderData\n  };\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    if (route && manifestRoute && shouldHydrateRouteLoader(\n      routeId,\n      route.clientLoader,\n      manifestRoute.hasLoader,\n      context.isSpaMode\n    ) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      delete context.staticHandlerContext.loaderData[routeId];\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext);\n  return /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement(\n    FrameworkContext.Provider,\n    {\n      value: {\n        manifest,\n        routeModules,\n        criticalCss,\n        serverHandoffString,\n        future: context.future,\n        ssr: context.ssr,\n        isSpaMode: context.isSpaMode,\n        routeDiscovery: context.routeDiscovery,\n        serializeError: context.serializeError,\n        renderMeta: context.renderMeta\n      }\n    },\n    /* @__PURE__ */ React.createElement(RemixErrorBoundary, { location: router.state.location }, /* @__PURE__ */ React.createElement(\n      StaticRouterProvider,\n      {\n        router,\n        context: context.staticHandlerContext,\n        hydrate: false\n      }\n    ))\n  ), context.serverHandoffStream ? /* @__PURE__ */ React.createElement(React.Suspense, null, /* @__PURE__ */ React.createElement(\n    StreamTransfer,\n    {\n      context,\n      identifier: 0,\n      reader: context.serverHandoffStream.getReader(),\n      textDecoder: new TextDecoder(),\n      nonce\n    }\n  )) : null);\n}\n\n// lib/dom/ssr/routes-test-stub.tsx\nimport * as React2 from \"react\";\nfunction createRoutesStub(routes, _context) {\n  return function RoutesTestStub({\n    initialEntries,\n    initialIndex,\n    hydrationData,\n    future\n  }) {\n    let routerRef = React2.useRef();\n    let frameworkContextRef = React2.useRef();\n    if (routerRef.current == null) {\n      frameworkContextRef.current = {\n        future: {\n          unstable_subResourceIntegrity: future?.unstable_subResourceIntegrity === true,\n          unstable_middleware: future?.unstable_middleware === true\n        },\n        manifest: {\n          routes: {},\n          entry: { imports: [], module: \"\" },\n          url: \"\",\n          version: \"\"\n        },\n        routeModules: {},\n        ssr: false,\n        isSpaMode: false,\n        routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" }\n      };\n      let patched = processRoutes(\n        // @ts-expect-error `StubRouteObject` is stricter about `loader`/`action`\n        // types compared to `AgnosticRouteObject`\n        convertRoutesToDataRoutes(routes, (r) => r),\n        _context !== void 0 ? _context : future?.unstable_middleware ? new unstable_RouterContextProvider() : {},\n        frameworkContextRef.current.manifest,\n        frameworkContextRef.current.routeModules\n      );\n      routerRef.current = createMemoryRouter(patched, {\n        initialEntries,\n        initialIndex,\n        hydrationData\n      });\n    }\n    return /* @__PURE__ */ React2.createElement(FrameworkContext.Provider, { value: frameworkContextRef.current }, /* @__PURE__ */ React2.createElement(RouterProvider, { router: routerRef.current }));\n  };\n}\nfunction processRoutes(routes, context, manifest, routeModules, parentId) {\n  return routes.map((route) => {\n    if (!route.id) {\n      throw new Error(\n        \"Expected a route.id in react-router processRoutes() function\"\n      );\n    }\n    let newRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      Component: route.Component ? withComponentProps(route.Component) : void 0,\n      HydrateFallback: route.HydrateFallback ? withHydrateFallbackProps(route.HydrateFallback) : void 0,\n      ErrorBoundary: route.ErrorBoundary ? withErrorBoundaryProps(route.ErrorBoundary) : void 0,\n      action: route.action ? (args) => route.action({ ...args, context }) : void 0,\n      loader: route.loader ? (args) => route.loader({ ...args, context }) : void 0,\n      handle: route.handle,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    let entryRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      parentId,\n      hasAction: route.action != null,\n      hasLoader: route.loader != null,\n      // When testing routes, you should be stubbing loader/action/middleware,\n      // not trying to re-implement the full loader/clientLoader/SSR/hydration\n      // flow. That is better tested via E2E tests.\n      hasClientAction: false,\n      hasClientLoader: false,\n      hasClientMiddleware: false,\n      hasErrorBoundary: route.ErrorBoundary != null,\n      // any need for these?\n      module: \"build/stub-path-to-module.js\",\n      clientActionModule: void 0,\n      clientLoaderModule: void 0,\n      clientMiddlewareModule: void 0,\n      hydrateFallbackModule: void 0\n    };\n    manifest.routes[newRoute.id] = entryRoute;\n    routeModules[route.id] = {\n      default: newRoute.Component || Outlet,\n      ErrorBoundary: newRoute.ErrorBoundary || void 0,\n      handle: route.handle,\n      links: route.links,\n      meta: route.meta,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    if (route.children) {\n      newRoute.children = processRoutes(\n        route.children,\n        context,\n        manifest,\n        routeModules,\n        newRoute.id\n      );\n    }\n    return newRoute;\n  });\n}\n\n// lib/server-runtime/cookies.ts\nimport { parse, serialize } from \"cookie\";\n\n// lib/server-runtime/crypto.ts\nvar encoder = /* @__PURE__ */ new TextEncoder();\nvar sign = async (value, secret) => {\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"sign\"]);\n  let signature = await crypto.subtle.sign(\"HMAC\", key, data2);\n  let hash = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(\n    /=+$/,\n    \"\"\n  );\n  return value + \".\" + hash;\n};\nvar unsign = async (cookie, secret) => {\n  let index = cookie.lastIndexOf(\".\");\n  let value = cookie.slice(0, index);\n  let hash = cookie.slice(index + 1);\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"verify\"]);\n  try {\n    let signature = byteStringToUint8Array(atob(hash));\n    let valid = await crypto.subtle.verify(\"HMAC\", key, signature, data2);\n    return valid ? value : false;\n  } catch (error) {\n    return false;\n  }\n};\nvar createKey = async (secret, usages) => crypto.subtle.importKey(\n  \"raw\",\n  encoder.encode(secret),\n  { name: \"HMAC\", hash: \"SHA-256\" },\n  false,\n  usages\n);\nfunction byteStringToUint8Array(byteString) {\n  let array = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    array[i] = byteString.charCodeAt(i);\n  }\n  return array;\n}\n\n// lib/server-runtime/cookies.ts\nvar createCookie = (name, cookieOptions = {}) => {\n  let { secrets = [], ...options } = {\n    path: \"/\",\n    sameSite: \"lax\",\n    ...cookieOptions\n  };\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1e3) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, { ...options, ...parseOptions });\n      if (name in cookies) {\n        let value = cookies[name];\n        if (typeof value === \"string\" && value !== \"\") {\n          let decoded = await decodeCookieValue(value, secrets);\n          return decoded;\n        } else {\n          return \"\";\n        }\n      } else {\n        return null;\n      }\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(\n        name,\n        value === \"\" ? \"\" : await encodeCookieValue(value, secrets),\n        {\n          ...options,\n          ...serializeOptions\n        }\n      );\n    }\n  };\n};\nvar isCookie = (object) => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(\n    !expires,\n    `The \"${name}\" cookie has an \"expires\" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use \\`commitSession(session, { expires })\\` if using a session storage object, or \\`cookie.serialize(\"value\", { expires })\\` if you're using the cookie directly.`\n  );\n}\n\n// lib/server-runtime/entry.ts\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo, routeId) => {\n    let route = manifest[routeId];\n    if (route) {\n      memo[routeId] = route.module;\n    }\n    return memo;\n  }, {});\n}\n\n// lib/server-runtime/mode.ts\nvar ServerMode = /* @__PURE__ */ ((ServerMode2) => {\n  ServerMode2[\"Development\"] = \"development\";\n  ServerMode2[\"Production\"] = \"production\";\n  ServerMode2[\"Test\"] = \"test\";\n  return ServerMode2;\n})(ServerMode || {});\nfunction isServerMode(value) {\n  return value === \"development\" /* Development */ || value === \"production\" /* Production */ || value === \"test\" /* Test */;\n}\n\n// lib/server-runtime/errors.ts\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== \"development\" /* Development */) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = void 0;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, [routeId, error]) => {\n    return Object.assign(acc, { [routeId]: sanitizeError(error, serverMode) });\n  }, {});\n}\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = { ...val, __type: \"RouteErrorResponse\" };\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = {\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.  This will only apply\n        // in dev mode since all production errors are sanitized to normal\n        // Error instances\n        ...sanitized.name !== \"Error\" ? {\n          __subType: sanitized.name\n        } : {}\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/server-runtime/routeMatching.ts\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(\n    routes,\n    pathname,\n    basename\n  );\n  if (!matches) return null;\n  return matches.map((match) => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\n// lib/server-runtime/data.ts\nasync function callRouteHandler(handler, args) {\n  let result = await handler({\n    request: stripRoutesParam(stripIndexParam2(args.request)),\n    params: args.params,\n    context: args.context\n  });\n  if (isDataWithResponseInit(result) && result.init && result.init.status && isRedirectStatusCode(result.init.status)) {\n    throw new Response(null, result.init);\n  }\n  return result;\n}\nfunction stripIndexParam2(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\n// lib/server-runtime/invariant.ts\nfunction invariant2(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\n      \"The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose\"\n    );\n    throw new Error(message);\n  }\n}\n\n// lib/server-runtime/dev.ts\nvar globalDevServerHooksKey = \"__reactRouterDevServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  return globalThis[globalDevServerHooksKey];\n}\nfunction getBuildTimeHeader(request, headerName) {\n  if (typeof process !== \"undefined\") {\n    try {\n      if (process.env?.IS_RR_BUILD_REQUEST === \"yes\") {\n        return request.headers.get(headerName);\n      }\n    } catch (e) {\n    }\n  }\n  return null;\n}\n\n// lib/server-runtime/routes.ts\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach((route) => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction createRoutes(manifest, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => ({\n    ...route,\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\nfunction createStaticHandlerDataRoutes(manifest, future, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      unstable_middleware: route.module.unstable_middleware,\n      // Need to use RR's version in the param typed here to permit the optional\n      // context even though we know it'll always be provided in remix\n      loader: route.module.loader ? async (args) => {\n        let preRenderedData = getBuildTimeHeader(\n          args.request,\n          \"X-React-Router-Prerender-Data\"\n        );\n        if (preRenderedData != null) {\n          let encoded = preRenderedData ? decodeURI(preRenderedData) : preRenderedData;\n          invariant2(encoded, \"Missing prerendered data for route\");\n          let uint8array = new TextEncoder().encode(encoded);\n          let stream = new ReadableStream({\n            start(controller) {\n              controller.enqueue(uint8array);\n              controller.close();\n            }\n          });\n          let decoded = await decodeViaTurboStream(stream, global);\n          let data2 = decoded.value;\n          if (data2 && SingleFetchRedirectSymbol in data2) {\n            let result = data2[SingleFetchRedirectSymbol];\n            let init = { status: result.status };\n            if (result.reload) {\n              throw redirectDocument(result.redirect, init);\n            } else if (result.replace) {\n              throw replace(result.redirect, init);\n            } else {\n              throw redirect(result.redirect, init);\n            }\n          } else {\n            invariant2(\n              data2 && route.id in data2,\n              \"Unable to decode prerendered data\"\n            );\n            let result = data2[route.id];\n            invariant2(\n              \"data\" in result,\n              \"Unable to process prerendered data\"\n            );\n            return result.data;\n          }\n        }\n        let val = await callRouteHandler(route.module.loader, args);\n        return val;\n      } : void 0,\n      action: route.module.action ? (args) => callRouteHandler(route.module.action, args) : void 0,\n      handle: route.module.handle\n    };\n    return route.index ? {\n      index: true,\n      ...commonRoute\n    } : {\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(\n        manifest,\n        future,\n        route.id,\n        routesByParentId\n      ),\n      ...commonRoute\n    };\n  });\n}\n\n// lib/server-runtime/markup.ts\nvar ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nvar ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction escapeHtml(html) {\n  return html.replace(ESCAPE_REGEX, (match) => ESCAPE_LOOKUP[match]);\n}\n\n// lib/server-runtime/serverHandoff.ts\nfunction createServerHandoffString(serverHandoff) {\n  return escapeHtml(JSON.stringify(serverHandoff));\n}\n\n// lib/server-runtime/headers.ts\nimport { splitCookiesString } from \"set-cookie-parser\";\nfunction getDocumentHeaders(context, build) {\n  return getDocumentHeadersImpl(context, (m) => {\n    let route = build.routes[m.route.id];\n    invariant2(route, `Route with id \"${m.route.id}\" not found in build`);\n    return route.module.headers;\n  });\n}\nfunction getDocumentHeadersImpl(context, getRouteHeadersFn, _defaultHeaders) {\n  let boundaryIdx = context.errors ? context.matches.findIndex((m) => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    let { actionHeaders, actionData, loaderHeaders, loaderData } = context;\n    context.matches.slice(boundaryIdx).some((match) => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || !actionData.hasOwnProperty(id))) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && !loaderData.hasOwnProperty(id)) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  const defaultHeaders = new Headers(_defaultHeaders);\n  return matches.reduce((parentHeaders, match, idx) => {\n    let { id } = match.route;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n    let includeErrorHeaders = errorHeaders != null && idx === matches.length - 1;\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n    let headersFn = getRouteHeadersFn(match);\n    if (headersFn == null) {\n      let headers2 = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers2);\n      }\n      prependCookies(actionHeaders, headers2);\n      prependCookies(loaderHeaders, headers2);\n      return headers2;\n    }\n    let headers = new Headers(\n      typeof headersFn === \"function\" ? headersFn({\n        loaderHeaders,\n        parentHeaders,\n        actionHeaders,\n        errorHeaders: includeErrorHeaders ? errorHeaders : void 0\n      }) : headersFn\n    );\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers(defaultHeaders));\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    let cookies = splitCookiesString(parentSetCookieString);\n    let childCookies = new Set(childHeaders.getSetCookie());\n    cookies.forEach((cookie) => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\n// lib/server-runtime/single-fetch.ts\nvar SERVER_NO_BODY_STATUS_CODES = /* @__PURE__ */ new Set([\n  ...NO_BODY_STATUS_CODES,\n  304\n]);\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...request.body ? { duplex: \"half\" } : void 0\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      skipRevalidation: true,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async (query) => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { error },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let singleFetchResult;\n    if (context.errors) {\n      singleFetchResult = { error: Object.values(context.errors)[0] };\n    } else {\n      singleFetchResult = {\n        data: Object.values(context.actionData || {})[0]\n      };\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: singleFetchResult,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let routesParam = new URL(request.url).searchParams.get(\"_routes\");\n  let loadRouteIds = routesParam ? new Set(routesParam.split(\",\")) : null;\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      filterMatchesToLoad: (m) => !loadRouteIds || loadRouteIds.has(m.route.id),\n      skipLoaderErrorBubbling: true,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async (query) => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { error },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let results = {};\n    let loadedMatches = new Set(\n      context.matches.filter(\n        (m) => loadRouteIds ? loadRouteIds.has(m.route.id) : m.route.loader != null\n      ).map((m) => m.route.id)\n    );\n    if (context.errors) {\n      for (let [id, error] of Object.entries(context.errors)) {\n        results[id] = { error };\n      }\n    }\n    for (let [id, data2] of Object.entries(context.loaderData)) {\n      if (!(id in results) && loadedMatches.has(id)) {\n        results[id] = { data: data2 };\n      }\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: results,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nfunction generateSingleFetchResponse(request, build, serverMode, {\n  result,\n  headers,\n  status\n}) {\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n  if (SERVER_NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, { status, headers: resultHeaders });\n  }\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n  resultHeaders.delete(\"Content-Length\");\n  return new Response(\n    encodeViaTurboStream(\n      result,\n      request.signal,\n      build.entry.module.streamTimeout,\n      serverMode\n    ),\n    {\n      status: status || 200,\n      headers: resultHeaders\n    }\n  );\n}\nfunction generateSingleFetchRedirectResponse(redirectResponse, request, build, serverMode) {\n  let redirect2 = getSingleFetchRedirect(\n    redirectResponse.status,\n    redirectResponse.headers,\n    build.basename\n  );\n  let headers = new Headers(redirectResponse.headers);\n  headers.delete(\"Location\");\n  headers.set(\"Content-Type\", \"text/x-script\");\n  return generateSingleFetchResponse(request, build, serverMode, {\n    result: request.method === \"GET\" ? { [SingleFetchRedirectSymbol]: redirect2 } : redirect2,\n    headers,\n    status: SINGLE_FETCH_REDIRECT_STATUS\n  });\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect2 = headers.get(\"Location\");\n  if (basename) {\n    redirect2 = stripBasename(redirect2, basename) || redirect2;\n  }\n  return {\n    redirect: redirect2,\n    status,\n    revalidate: (\n      // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n      // detail of ?_data requests as our way to tell the front end to revalidate when\n      // we didn't have a response body to include that information in.\n      // With single fetch, we tell the front end via this revalidate boolean field.\n      // However, we're respecting it for now because it may be something folks have\n      // used in their own responses\n      // TODO(v3): Consider removing or making this official public API\n      headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\")\n    ),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\nfunction encodeViaTurboStream(data2, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  let timeoutId = setTimeout(\n    () => controller.abort(new Error(\"Server Timeout\")),\n    typeof streamTimeout === \"number\" ? streamTimeout : 4950\n  );\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data2, {\n    signal: controller.signal,\n    plugins: [\n      (value) => {\n        if (value instanceof Error) {\n          let { name, message, stack } = serverMode === \"production\" /* Production */ ? sanitizeError(value, serverMode) : value;\n          return [\"SanitizedError\", name, message, stack];\n        }\n        if (value instanceof ErrorResponseImpl) {\n          let { data: data3, status, statusText } = value;\n          return [\"ErrorResponse\", data3, status, statusText];\n        }\n        if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n          return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n        }\n      }\n    ],\n    postPlugins: [\n      (value) => {\n        if (!value) return;\n        if (typeof value !== \"object\") return;\n        return [\n          \"SingleFetchClassInstance\",\n          Object.fromEntries(Object.entries(value))\n        ];\n      },\n      () => [\"SingleFetchFallback\"]\n    ]\n  });\n}\n\n// lib/server-runtime/server.ts\nfunction derive(build, mode) {\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : \"production\" /* Production */;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename\n  });\n  let errorHandler = build.entry.module.handleError || ((error, { request }) => {\n    if (serverMode !== \"test\" /* Test */ && !request.signal.aborted) {\n      console.error(\n        // @ts-expect-error This is \"private\" from users but intended for internal use\n        isRouteErrorResponse(error) && error.error ? error.error : error\n      );\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nvar createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, initialContext) {\n    _build = typeof build === \"function\" ? await build() : build;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let params = {};\n    let loadContext;\n    let handleError = (error) => {\n      if (mode === \"development\" /* Development */) {\n        getDevServerHooks()?.processRequestError?.(error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n    if (_build.future.unstable_middleware) {\n      if (initialContext && !(initialContext instanceof unstable_RouterContextProvider)) {\n        let error = new Error(\n          \"Invalid `context` value provided to `handleRequest`. When middleware is enabled you must return an instance of `unstable_RouterContextProvider` from your `getLoadContext` function.\"\n        );\n        handleError(error);\n        return returnLastResortErrorResponse(error, serverMode);\n      }\n      loadContext = initialContext || new unstable_RouterContextProvider();\n    } else {\n      loadContext = initialContext || {};\n    }\n    let url = new URL(request.url);\n    let normalizedBasename = _build.basename || \"/\";\n    let normalizedPath = url.pathname;\n    if (stripBasename(normalizedPath, normalizedBasename) === \"/_root.data\") {\n      normalizedPath = normalizedBasename;\n    } else if (normalizedPath.endsWith(\".data\")) {\n      normalizedPath = normalizedPath.replace(/\\.data$/, \"\");\n    }\n    if (stripBasename(normalizedPath, normalizedBasename) !== \"/\" && normalizedPath.endsWith(\"/\")) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n    let isSpaMode = getBuildTimeHeader(request, \"X-React-Router-SPA-Mode\") === \"yes\";\n    if (!_build.ssr) {\n      let decodedPath = decodeURI(normalizedPath);\n      if (normalizedBasename !== \"/\") {\n        let strippedPath = stripBasename(decodedPath, normalizedBasename);\n        if (strippedPath == null) {\n          errorHandler(\n            new ErrorResponseImpl(\n              404,\n              \"Not Found\",\n              `Refusing to prerender the \\`${decodedPath}\\` path because it does not start with the basename \\`${normalizedBasename}\\``\n            ),\n            {\n              context: loadContext,\n              params,\n              request\n            }\n          );\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        }\n        decodedPath = strippedPath;\n      }\n      if (_build.prerender.length === 0) {\n        isSpaMode = true;\n      } else if (!_build.prerender.includes(decodedPath) && !_build.prerender.includes(decodedPath + \"/\")) {\n        if (url.pathname.endsWith(\".data\")) {\n          errorHandler(\n            new ErrorResponseImpl(\n              404,\n              \"Not Found\",\n              `Refusing to SSR the path \\`${decodedPath}\\` because \\`ssr:false\\` is set and the path is not included in the \\`prerender\\` config, so in production the path will be a 404.`\n            ),\n            {\n              context: loadContext,\n              params,\n              request\n            }\n          );\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        } else {\n          isSpaMode = true;\n        }\n      }\n    }\n    let manifestUrl = getManifestPath(\n      _build.routeDiscovery.manifestPath,\n      normalizedBasename\n    );\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", { status: 500 });\n      }\n    }\n    let matches = matchServerRoutes(routes, normalizedPath, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = normalizedPath;\n      let singleFetchMatches = matchServerRoutes(\n        routes,\n        handlerUrl.pathname,\n        _build.basename\n      );\n      response = await handleSingleFetchRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        handlerUrl,\n        loadContext,\n        handleError\n      );\n      if (isRedirectResponse(response)) {\n        response = generateSingleFetchRedirectResponse(\n          response,\n          request,\n          _build,\n          serverMode\n        );\n      }\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          response = generateSingleFetchRedirectResponse(\n            response,\n            request,\n            _build,\n            serverMode\n          );\n        }\n      }\n    } else if (!isSpaMode && matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        matches.slice(-1)[0].route.id,\n        request,\n        loadContext,\n        handleError\n      );\n    } else {\n      let { pathname } = url;\n      let criticalCss = void 0;\n      if (_build.unstable_getCriticalCss) {\n        criticalCss = await _build.unstable_getCriticalCss({ pathname });\n      } else if (mode === \"development\" /* Development */ && getDevServerHooks()?.getCriticalCss) {\n        criticalCss = await getDevServerHooks()?.getCriticalCss?.(pathname);\n      }\n      response = await handleDocumentRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        loadContext,\n        handleError,\n        isSpaMode,\n        criticalCss\n      );\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"p\")) {\n    let paths = /* @__PURE__ */ new Set();\n    url.searchParams.getAll(\"p\").forEach((path) => {\n      if (!path.startsWith(\"/\")) {\n        path = `/${path}`;\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(`/${partialPath}`);\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          let route = build.assets.routes[routeId];\n          if (route) {\n            patches[routeId] = route;\n          }\n        }\n      }\n    }\n    return Response.json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    });\n  }\n  return new Response(\"Invalid Request\", { status: 400 });\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let response = request.method !== \"GET\" ? await singleFetchAction(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  ) : await singleFetchLoaders(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  );\n  return response;\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss) {\n  try {\n    let result = await staticHandler.query(request, {\n      requestContext: loadContext,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async (query) => {\n        try {\n          let innerResult = await query(request);\n          if (!isResponse(innerResult)) {\n            innerResult = await renderHtml(innerResult, isSpaMode);\n          }\n          return innerResult;\n        } catch (error) {\n          handleError(error);\n          return new Response(null, { status: 500 });\n        }\n      } : void 0\n    });\n    if (!isResponse(result)) {\n      result = await renderHtml(result, isSpaMode);\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return new Response(null, { status: 500 });\n  }\n  async function renderHtml(context, isSpaMode2) {\n    let headers = getDocumentHeaders(context, build);\n    if (SERVER_NO_BODY_STATUS_CODES.has(context.statusCode)) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors, serverMode)\n    };\n    let baseServerHandoff = {\n      basename: build.basename,\n      future: build.future,\n      routeDiscovery: build.routeDiscovery,\n      ssr: build.ssr,\n      isSpaMode: isSpaMode2\n    };\n    let entryContext = {\n      manifest: build.assets,\n      routeModules: createEntryRouteModules(build.routes),\n      staticHandlerContext: context,\n      criticalCss,\n      serverHandoffString: createServerHandoffString({\n        ...baseServerHandoff,\n        criticalCss\n      }),\n      serverHandoffStream: encodeViaTurboStream(\n        state,\n        request.signal,\n        build.entry.module.streamTimeout,\n        serverMode\n      ),\n      renderMeta: {},\n      future: build.future,\n      ssr: build.ssr,\n      routeDiscovery: build.routeDiscovery,\n      isSpaMode: isSpaMode2,\n      serializeError: (err) => serializeError(err, serverMode)\n    };\n    let handleDocumentRequestFunction = build.entry.module.default;\n    try {\n      return await handleDocumentRequestFunction(\n        request,\n        context.statusCode,\n        headers,\n        entryContext,\n        loadContext\n      );\n    } catch (error) {\n      handleError(error);\n      let errorForSecondRender = error;\n      if (isResponse(error)) {\n        try {\n          let data2 = await unwrapResponse(error);\n          errorForSecondRender = new ErrorResponseImpl(\n            error.status,\n            error.statusText,\n            data2\n          );\n        } catch (e) {\n        }\n      }\n      context = getStaticContextFromError(\n        staticHandler.dataRoutes,\n        context,\n        errorForSecondRender\n      );\n      if (context.errors) {\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let state2 = {\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: serializeErrors(context.errors, serverMode)\n      };\n      entryContext = {\n        ...entryContext,\n        staticHandlerContext: context,\n        serverHandoffString: createServerHandoffString(baseServerHandoff),\n        serverHandoffStream: encodeViaTurboStream(\n          state2,\n          request.signal,\n          build.entry.module.streamTimeout,\n          serverMode\n        ),\n        renderMeta: {}\n      };\n      try {\n        return await handleDocumentRequestFunction(\n          request,\n          context.statusCode,\n          headers,\n          entryContext,\n          loadContext\n        );\n      } catch (error2) {\n        handleError(error2);\n        return returnLastResortErrorResponse(error2, serverMode);\n      }\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let result = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async (queryRoute) => {\n        try {\n          let innerResult = await queryRoute(request);\n          return handleQueryRouteResult(innerResult);\n        } catch (error) {\n          return handleQueryRouteError(error);\n        }\n      } : void 0\n    });\n    return handleQueryRouteResult(result);\n  } catch (error) {\n    return handleQueryRouteError(error);\n  }\n  function handleQueryRouteResult(result) {\n    if (isResponse(result)) {\n      return result;\n    }\n    if (typeof result === \"string\") {\n      return new Response(result);\n    }\n    return Response.json(result);\n  }\n  function handleQueryRouteError(error) {\n    if (isResponse(error)) {\n      error.headers.set(\"X-Remix-Catch\", \"yes\");\n      return error;\n    }\n    if (isRouteErrorResponse(error)) {\n      handleError(error);\n      return errorResponseToJson(error, serverMode);\n    }\n    if (error instanceof Error && error.message === \"Expected a response from queryRoute\") {\n      let newError = new Error(\n        \"Expected a Response to be returned from resource route handler\"\n      );\n      handleError(newError);\n      return returnLastResortErrorResponse(newError, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return Response.json(\n    serializeError(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      errorResponse.error || new Error(\"Unexpected Server Error\"),\n      serverMode\n    ),\n    {\n      status: errorResponse.status,\n      statusText: errorResponse.statusText,\n      headers: {\n        \"X-Remix-Error\": \"yes\"\n      }\n    }\n  );\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== \"production\" /* Production */) {\n    message += `\n\n${String(error)}`;\n  }\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\n\n// lib/server-runtime/sessions.ts\nfunction flash(name) {\n  return `__flash_${name}__`;\n}\nvar createSession = (initialData = {}, id = \"\") => {\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return void 0;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\nvar isSession = (object) => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\nfunction createSessionStorage({\n  cookie: cookieArg,\n  createData,\n  readData,\n  updateData,\n  deleteData\n}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && await cookie.parse(cookieHeader, options);\n      let data2 = id && await readData(id);\n      return createSession(data2 || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let { id, data: data2 } = session;\n      let expires = options?.maxAge != null ? new Date(Date.now() + options.maxAge * 1e3) : options?.expires != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data2, expires);\n      } else {\n        id = await createData(data2, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(\n    cookie.isSigned,\n    `The \"${cookie.name}\" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.`\n  );\n}\n\n// lib/server-runtime/sessions/cookieStorage.ts\nfunction createCookieSessionStorage({ cookie: cookieArg } = {}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(\n        cookieHeader && await cookie.parse(cookieHeader, options) || {}\n      );\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\n          \"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length\n        );\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\n\n// lib/server-runtime/sessions/memoryStorage.ts\nfunction createMemorySessionStorage({ cookie } = {}) {\n  let map = /* @__PURE__ */ new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data2, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, { data: data2, expires });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let { data: data2, expires } = map.get(id);\n        if (!expires || expires > /* @__PURE__ */ new Date()) {\n          return data2;\n        }\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data2, expires) {\n      map.set(id, { data: data2, expires });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n}\n\n// lib/href.ts\nfunction href(path, ...args) {\n  let params = args[0];\n  return path.split(\"/\").map((segment) => {\n    if (segment === \"*\") {\n      return params ? params[\"*\"] : void 0;\n    }\n    const match = segment.match(/^:([\\w-]+)(\\?)?/);\n    if (!match) return segment;\n    const param = match[1];\n    const value = params ? params[param] : void 0;\n    const isRequired = match[2] === void 0;\n    if (isRequired && value === void 0) {\n      throw Error(\n        `Path '${path}' requires param '${param}' but it was not provided`\n      );\n    }\n    return value;\n  }).filter((segment) => segment !== void 0).join(\"/\");\n}\n\n// lib/rsc/browser.tsx\nimport * as React4 from \"react\";\nimport * as ReactDOM from \"react-dom\";\n\n// lib/dom/ssr/hydration.tsx\nfunction getHydrationData(state, routes, getRouteInfo, location2, basename, isSpaMode) {\n  let hydrationData = {\n    ...state,\n    loaderData: { ...state.loaderData }\n  };\n  let initialMatches = matchRoutes(routes, location2, basename);\n  if (initialMatches) {\n    for (let match of initialMatches) {\n      let routeId = match.route.id;\n      let routeInfo = getRouteInfo(routeId);\n      if (shouldHydrateRouteLoader(\n        routeId,\n        routeInfo.clientLoader,\n        routeInfo.hasLoader,\n        isSpaMode\n      ) && (routeInfo.hasHydrateFallback || !routeInfo.hasLoader)) {\n        delete hydrationData.loaderData[routeId];\n      } else if (!routeInfo.hasLoader) {\n        hydrationData.loaderData[routeId] = null;\n      }\n    }\n  }\n  return hydrationData;\n}\n\n// lib/rsc/errorBoundaries.tsx\nimport React3 from \"react\";\nvar RSCRouterGlobalErrorBoundary = class extends React3.Component {\n  constructor(props) {\n    super(props);\n    this.state = { error: null, location: props.location };\n  }\n  static getDerivedStateFromError(error) {\n    return { error };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location) {\n      return { error: null, location: props.location };\n    }\n    return { error: state.error, location: state.location };\n  }\n  render() {\n    if (this.state.error) {\n      return /* @__PURE__ */ React3.createElement(\n        RSCDefaultRootErrorBoundaryImpl,\n        {\n          error: this.state.error,\n          renderAppShell: true\n        }\n      );\n    } else {\n      return this.props.children;\n    }\n  }\n};\nfunction ErrorWrapper({\n  renderAppShell,\n  title,\n  children\n}) {\n  if (!renderAppShell) {\n    return children;\n  }\n  return /* @__PURE__ */ React3.createElement(\"html\", { lang: \"en\" }, /* @__PURE__ */ React3.createElement(\"head\", null, /* @__PURE__ */ React3.createElement(\"meta\", { charSet: \"utf-8\" }), /* @__PURE__ */ React3.createElement(\n    \"meta\",\n    {\n      name: \"viewport\",\n      content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n    }\n  ), /* @__PURE__ */ React3.createElement(\"title\", null, title)), /* @__PURE__ */ React3.createElement(\"body\", null, /* @__PURE__ */ React3.createElement(\"main\", { style: { fontFamily: \"system-ui, sans-serif\", padding: \"2rem\" } }, children)));\n}\nfunction RSCDefaultRootErrorBoundaryImpl({\n  error,\n  renderAppShell\n}) {\n  console.error(error);\n  let heyDeveloper = /* @__PURE__ */ React3.createElement(\n    \"script\",\n    {\n      dangerouslySetInnerHTML: {\n        __html: `\n        console.log(\n          \"\\u{1F4BF} Hey developer \\u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information.\"\n        );\n      `\n      }\n    }\n  );\n  if (isRouteErrorResponse(error)) {\n    return /* @__PURE__ */ React3.createElement(\n      ErrorWrapper,\n      {\n        renderAppShell,\n        title: \"Unhandled Thrown Response!\"\n      },\n      /* @__PURE__ */ React3.createElement(\"h1\", { style: { fontSize: \"24px\" } }, error.status, \" \", error.statusText),\n      ENABLE_DEV_WARNINGS ? heyDeveloper : null\n    );\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /* @__PURE__ */ React3.createElement(ErrorWrapper, { renderAppShell, title: \"Application Error!\" }, /* @__PURE__ */ React3.createElement(\"h1\", { style: { fontSize: \"24px\" } }, \"Application Error\"), /* @__PURE__ */ React3.createElement(\n    \"pre\",\n    {\n      style: {\n        padding: \"2rem\",\n        background: \"hsla(10, 50%, 50%, 0.1)\",\n        color: \"red\",\n        overflow: \"auto\"\n      }\n    },\n    errorInstance.stack\n  ), heyDeveloper);\n}\nfunction RSCDefaultRootErrorBoundary({\n  hasRootLayout\n}) {\n  let error = useRouteError();\n  if (hasRootLayout === void 0) {\n    throw new Error(\"Missing 'hasRootLayout' prop\");\n  }\n  return /* @__PURE__ */ React3.createElement(\n    RSCDefaultRootErrorBoundaryImpl,\n    {\n      renderAppShell: !hasRootLayout,\n      error\n    }\n  );\n}\n\n// lib/rsc/route-modules.ts\nfunction createRSCRouteModules(payload) {\n  const routeModules = {};\n  for (const match of payload.matches) {\n    populateRSCRouteModules(routeModules, match);\n  }\n  return routeModules;\n}\nfunction populateRSCRouteModules(routeModules, matches) {\n  matches = Array.isArray(matches) ? matches : [matches];\n  for (const match of matches) {\n    routeModules[match.id] = {\n      links: match.links,\n      meta: match.meta,\n      default: noopComponent\n    };\n  }\n}\nvar noopComponent = () => null;\n\n// lib/rsc/browser.tsx\nfunction createCallServer({\n  createFromReadableStream,\n  createTemporaryReferenceSet,\n  encodeReply,\n  fetch: fetchImplementation = fetch\n}) {\n  const globalVar = window;\n  let landedActionId = 0;\n  return async (id, args) => {\n    let actionId = globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    const temporaryReferences = createTemporaryReferenceSet();\n    const response = await fetchImplementation(\n      new Request(location.href, {\n        body: await encodeReply(args, { temporaryReferences }),\n        method: \"POST\",\n        headers: {\n          Accept: \"text/x-component\",\n          \"rsc-action-id\": id\n        }\n      })\n    );\n    if (!response.body) {\n      throw new Error(\"No response body\");\n    }\n    const payload = await createFromReadableStream(response.body, {\n      temporaryReferences\n    });\n    if (payload.type === \"redirect\") {\n      if (payload.reload) {\n        window.location.href = payload.location;\n        return;\n      }\n      globalVar.__reactRouterDataRouter.navigate(payload.location, {\n        replace: payload.replace\n      });\n      return payload.actionResult;\n    }\n    if (payload.type !== \"action\") {\n      throw new Error(\"Unexpected payload type\");\n    }\n    if (payload.rerender) {\n      React4.startTransition(\n        // @ts-expect-error - We have old react types that don't know this can be async\n        async () => {\n          const rerender = await payload.rerender;\n          if (!rerender) return;\n          if (landedActionId < actionId && globalVar.__routerActionID <= actionId) {\n            landedActionId = actionId;\n            if (rerender.type === \"redirect\") {\n              if (rerender.reload) {\n                window.location.href = rerender.location;\n                return;\n              }\n              globalVar.__reactRouterDataRouter.navigate(rerender.location, {\n                replace: rerender.replace\n              });\n              return;\n            }\n            let lastMatch;\n            for (const match of rerender.matches) {\n              globalVar.__reactRouterDataRouter.patchRoutes(\n                lastMatch?.id ?? null,\n                [createRouteFromServerManifest(match)],\n                true\n              );\n              lastMatch = match;\n            }\n            window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp(\n              {}\n            );\n            React4.startTransition(() => {\n              window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp(\n                {\n                  loaderData: Object.assign(\n                    {},\n                    globalVar.__reactRouterDataRouter.state.loaderData,\n                    rerender.loaderData\n                  ),\n                  errors: rerender.errors ? Object.assign(\n                    {},\n                    globalVar.__reactRouterDataRouter.state.errors,\n                    rerender.errors\n                  ) : null\n                }\n              );\n            });\n          }\n        }\n      );\n    }\n    return payload.actionResult;\n  };\n}\nfunction createRouterFromPayload({\n  fetchImplementation,\n  createFromReadableStream,\n  unstable_getContext,\n  payload\n}) {\n  const globalVar = window;\n  if (globalVar.__reactRouterDataRouter && globalVar.__reactRouterRouteModules)\n    return {\n      router: globalVar.__reactRouterDataRouter,\n      routeModules: globalVar.__reactRouterRouteModules\n    };\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  globalVar.__reactRouterRouteModules = globalVar.__reactRouterRouteModules ?? {};\n  populateRSCRouteModules(globalVar.__reactRouterRouteModules, payload.matches);\n  let patches = /* @__PURE__ */ new Map();\n  payload.patches?.forEach((patch) => {\n    invariant(patch.parentId, \"Invalid patch parentId\");\n    if (!patches.has(patch.parentId)) {\n      patches.set(patch.parentId, []);\n    }\n    patches.get(patch.parentId)?.push(patch);\n  });\n  let routes = payload.matches.reduceRight((previous, match) => {\n    const route = createRouteFromServerManifest(\n      match,\n      payload\n    );\n    if (previous.length > 0) {\n      route.children = previous;\n      let childrenToPatch = patches.get(match.id);\n      if (childrenToPatch) {\n        route.children.push(\n          ...childrenToPatch.map((r) => createRouteFromServerManifest(r))\n        );\n      }\n    }\n    return [route];\n  }, []);\n  globalVar.__reactRouterDataRouter = createRouter({\n    routes,\n    unstable_getContext,\n    basename: payload.basename,\n    history: createBrowserHistory(),\n    hydrationData: getHydrationData(\n      {\n        loaderData: payload.loaderData,\n        actionData: payload.actionData,\n        errors: payload.errors\n      },\n      routes,\n      (routeId) => {\n        let match = payload.matches.find((m) => m.id === routeId);\n        invariant(match, \"Route not found in payload\");\n        return {\n          clientLoader: match.clientLoader,\n          hasLoader: match.hasLoader,\n          hasHydrateFallback: match.hydrateFallbackElement != null\n        };\n      },\n      payload.location,\n      void 0,\n      false\n    ),\n    async patchRoutesOnNavigation({ path, signal }) {\n      if (discoveredPaths.has(path)) {\n        return;\n      }\n      await fetchAndApplyManifestPatches(\n        [path],\n        createFromReadableStream,\n        fetchImplementation,\n        signal\n      );\n    },\n    // FIXME: Pass `build.ssr` into this function\n    dataStrategy: getRSCSingleFetchDataStrategy(\n      () => globalVar.__reactRouterDataRouter,\n      true,\n      payload.basename,\n      createFromReadableStream,\n      fetchImplementation\n    )\n  });\n  if (globalVar.__reactRouterDataRouter.state.initialized) {\n    globalVar.__routerInitialized = true;\n    globalVar.__reactRouterDataRouter.initialize();\n  } else {\n    globalVar.__routerInitialized = false;\n  }\n  let lastLoaderData = void 0;\n  globalVar.__reactRouterDataRouter.subscribe(({ loaderData, actionData }) => {\n    if (lastLoaderData !== loaderData) {\n      globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    }\n  });\n  globalVar.__reactRouterDataRouter._updateRoutesForHMR = (routeUpdateByRouteId) => {\n    const oldRoutes = window.__reactRouterDataRouter.routes;\n    const newRoutes = [];\n    function walkRoutes(routes2, parentId) {\n      return routes2.map((route) => {\n        const routeUpdate = routeUpdateByRouteId.get(route.id);\n        if (routeUpdate) {\n          const {\n            routeModule,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader\n          } = routeUpdate;\n          const newRoute = createRouteFromServerManifest({\n            clientAction: routeModule.clientAction,\n            clientLoader: routeModule.clientLoader,\n            element: route.element,\n            errorElement: route.errorElement,\n            handle: route.handle,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader,\n            hydrateFallbackElement: route.hydrateFallbackElement,\n            id: route.id,\n            index: route.index,\n            links: routeModule.links,\n            meta: routeModule.meta,\n            parentId,\n            path: route.path,\n            shouldRevalidate: routeModule.shouldRevalidate\n          });\n          if (route.children) {\n            newRoute.children = walkRoutes(route.children, route.id);\n          }\n          return newRoute;\n        }\n        const updatedRoute = { ...route };\n        if (route.children) {\n          updatedRoute.children = walkRoutes(route.children, route.id);\n        }\n        return updatedRoute;\n      });\n    }\n    newRoutes.push(\n      ...walkRoutes(oldRoutes, void 0)\n    );\n    window.__reactRouterDataRouter._internalSetRoutes(newRoutes);\n  };\n  return {\n    router: globalVar.__reactRouterDataRouter,\n    routeModules: globalVar.__reactRouterRouteModules\n  };\n}\nvar renderedRoutesContext = unstable_createContext();\nfunction getRSCSingleFetchDataStrategy(getRouter, ssr, basename, createFromReadableStream, fetchImplementation) {\n  let dataStrategy = getSingleFetchDataStrategyImpl(\n    getRouter,\n    (match) => {\n      let M = match;\n      return {\n        hasLoader: M.route.hasLoader,\n        hasClientLoader: M.route.hasClientLoader,\n        hasComponent: M.route.hasComponent,\n        hasAction: M.route.hasAction,\n        hasClientAction: M.route.hasClientAction,\n        hasShouldRevalidate: M.route.hasShouldRevalidate\n      };\n    },\n    // pass map into fetchAndDecode so it can add payloads\n    getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation),\n    ssr,\n    basename,\n    // If the route has a component but we don't have an element, we need to hit\n    // the server loader flow regardless of whether the client loader calls\n    // `serverLoader` or not, otherwise we'll have nothing to render.\n    (match) => {\n      let M = match;\n      return M.route.hasComponent && !M.route.element;\n    }\n  );\n  return async (args) => args.unstable_runClientMiddleware(async () => {\n    let context = args.context;\n    context.set(renderedRoutesContext, []);\n    let results = await dataStrategy(args);\n    const renderedRoutesById = /* @__PURE__ */ new Map();\n    for (const route of context.get(renderedRoutesContext)) {\n      if (!renderedRoutesById.has(route.id)) {\n        renderedRoutesById.set(route.id, []);\n      }\n      renderedRoutesById.get(route.id).push(route);\n    }\n    for (const match of args.matches) {\n      const renderedRoutes = renderedRoutesById.get(match.route.id);\n      if (renderedRoutes) {\n        for (const rendered of renderedRoutes) {\n          window.__reactRouterDataRouter.patchRoutes(\n            rendered.parentId ?? null,\n            [createRouteFromServerManifest(rendered)],\n            true\n          );\n        }\n      }\n    }\n    return results;\n  });\n}\nfunction getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation) {\n  return async (args, basename, targetRoutes) => {\n    let { request, context } = args;\n    let url = singleFetchUrl(request.url, basename, \"rsc\");\n    if (request.method === \"GET\") {\n      url = stripIndexParam(url);\n      if (targetRoutes) {\n        url.searchParams.set(\"_routes\", targetRoutes.join(\",\"));\n      }\n    }\n    let res = await fetchImplementation(\n      new Request(url, await createRequestInit(request))\n    );\n    if (res.status === 404 && !res.headers.has(\"X-Remix-Response\")) {\n      throw new ErrorResponseImpl(404, \"Not Found\", true);\n    }\n    invariant(res.body, \"No response body to decode\");\n    try {\n      const payload = await createFromReadableStream(res.body, {\n        temporaryReferences: void 0\n      });\n      if (payload.type === \"redirect\") {\n        return {\n          status: res.status,\n          data: {\n            redirect: {\n              redirect: payload.location,\n              reload: payload.reload,\n              replace: payload.replace,\n              revalidate: false,\n              status: payload.status\n            }\n          }\n        };\n      }\n      if (payload.type !== \"render\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      context.get(renderedRoutesContext).push(...payload.matches);\n      let results = { routes: {} };\n      const dataKey = isMutationMethod(request.method) ? \"actionData\" : \"loaderData\";\n      for (let [routeId, data2] of Object.entries(payload[dataKey] || {})) {\n        results.routes[routeId] = { data: data2 };\n      }\n      if (payload.errors) {\n        for (let [routeId, error] of Object.entries(payload.errors)) {\n          results.routes[routeId] = { error };\n        }\n      }\n      return { status: res.status, data: results };\n    } catch (e) {\n      throw new Error(\"Unable to decode RSC response\");\n    }\n  };\n}\nfunction RSCHydratedRouter({\n  createFromReadableStream,\n  fetch: fetchImplementation = fetch,\n  payload,\n  routeDiscovery = \"eager\",\n  unstable_getContext\n}) {\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let { router, routeModules } = React4.useMemo(\n    () => createRouterFromPayload({\n      payload,\n      fetchImplementation,\n      unstable_getContext,\n      createFromReadableStream\n    }),\n    [\n      createFromReadableStream,\n      payload,\n      fetchImplementation,\n      unstable_getContext\n    ]\n  );\n  React4.useEffect(() => {\n    setIsHydrated();\n  }, []);\n  React4.useLayoutEffect(() => {\n    const globalVar = window;\n    if (!globalVar.__routerInitialized) {\n      globalVar.__routerInitialized = true;\n      globalVar.__reactRouterDataRouter.initialize();\n    }\n  }, []);\n  let [location2, setLocation] = React4.useState(router.state.location);\n  React4.useLayoutEffect(\n    () => router.subscribe((newState) => {\n      if (newState.location !== location2) {\n        setLocation(newState.location);\n      }\n    }),\n    [router, location2]\n  );\n  React4.useEffect(() => {\n    if (routeDiscovery === \"lazy\" || // @ts-expect-error - TS doesn't know about this yet\n    window.navigator?.connection?.saveData === true) {\n      return;\n    }\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let pathname = el.tagName === \"A\" ? el.pathname : new URL(path, window.location.origin).pathname;\n      if (!discoveredPaths.has(pathname)) {\n        nextPaths.add(pathname);\n      }\n    }\n    async function fetchPatches() {\n      document.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(registerElement);\n      let paths = Array.from(nextPaths.keys()).filter((path) => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (paths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(\n          paths,\n          createFromReadableStream,\n          fetchImplementation\n        );\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    fetchPatches();\n    let observer = new MutationObserver(() => debouncedFetchPatches());\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n  }, [routeDiscovery, createFromReadableStream, fetchImplementation]);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" },\n    routeModules\n  };\n  return /* @__PURE__ */ React4.createElement(RSCRouterContext.Provider, { value: true }, /* @__PURE__ */ React4.createElement(RSCRouterGlobalErrorBoundary, { location: location2 }, /* @__PURE__ */ React4.createElement(FrameworkContext.Provider, { value: frameworkContext }, /* @__PURE__ */ React4.createElement(RouterProvider, { router, flushSync: ReactDOM.flushSync }))));\n}\nfunction createRouteFromServerManifest(match, payload) {\n  let hasInitialData = payload && match.id in payload.loaderData;\n  let initialData = payload?.loaderData[match.id];\n  let hasInitialError = payload?.errors && match.id in payload.errors;\n  let initialError = payload?.errors?.[match.id];\n  let isHydrationRequest = match.clientLoader?.hydrate === true || !match.hasLoader || // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match.hasComponent && !match.element;\n  invariant(window.__reactRouterRouteModules);\n  populateRSCRouteModules(window.__reactRouterRouteModules, match);\n  let dataRoute = {\n    id: match.id,\n    element: match.element,\n    errorElement: match.errorElement,\n    handle: match.handle,\n    hasErrorBoundary: match.hasErrorBoundary,\n    hydrateFallbackElement: match.hydrateFallbackElement,\n    index: match.index,\n    loader: match.clientLoader ? async (args, singleFetch) => {\n      try {\n        let result = await match.clientLoader({\n          ...args,\n          serverLoader: () => {\n            preventInvalidServerHandlerCall(\n              \"loader\",\n              match.id,\n              match.hasLoader\n            );\n            if (isHydrationRequest) {\n              if (hasInitialData) {\n                return initialData;\n              }\n              if (hasInitialError) {\n                throw initialError;\n              }\n            }\n            return callSingleFetch(singleFetch);\n          }\n        });\n        return result;\n      } finally {\n        isHydrationRequest = false;\n      }\n    } : (\n      // We always make the call in this RSC world since even if we don't\n      // have a `loader` we may need to get the `element` implementation\n      (_, singleFetch) => callSingleFetch(singleFetch)\n    ),\n    action: match.clientAction ? (args, singleFetch) => match.clientAction({\n      ...args,\n      serverAction: async () => {\n        preventInvalidServerHandlerCall(\n          \"action\",\n          match.id,\n          match.hasLoader\n        );\n        return await callSingleFetch(singleFetch);\n      }\n    }) : match.hasAction ? (_, singleFetch) => callSingleFetch(singleFetch) : () => {\n      throw noActionDefinedError(\"action\", match.id);\n    },\n    path: match.path,\n    shouldRevalidate: match.shouldRevalidate,\n    // We always have a \"loader\" in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    hasLoader: true,\n    hasClientLoader: match.clientLoader != null,\n    hasAction: match.hasAction,\n    hasClientAction: match.clientAction != null,\n    hasShouldRevalidate: match.shouldRevalidate != null\n  };\n  if (typeof dataRoute.loader === \"function\") {\n    dataRoute.loader.hydrate = shouldHydrateRouteLoader(\n      match.id,\n      match.clientLoader,\n      match.hasLoader,\n      false\n    );\n  }\n  return dataRoute;\n}\nfunction callSingleFetch(singleFetch) {\n  invariant(typeof singleFetch === \"function\", \"Invalid singleFetch parameter\");\n  return singleFetch();\n}\nfunction preventInvalidServerHandlerCall(type, routeId, hasHandler) {\n  if (!hasHandler) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You are trying to call ${fn} on a route that does not have a server ${type} (routeId: \"${routeId}\")`;\n    console.error(msg);\n    throw new ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nvar nextPaths = /* @__PURE__ */ new Set();\nvar discoveredPathsMaxSize = 1e3;\nvar discoveredPaths = /* @__PURE__ */ new Set();\nvar URL_LIMIT = 7680;\nfunction getManifestUrl(paths) {\n  if (paths.length === 0) {\n    return null;\n  }\n  if (paths.length === 1) {\n    return new URL(`${paths[0]}.manifest`, window.location.origin);\n  }\n  const globalVar = window;\n  let basename = (globalVar.__reactRouterDataRouter.basename ?? \"\").replace(\n    /^\\/|\\/$/g,\n    \"\"\n  );\n  let url = new URL(`${basename}/.manifest`, window.location.origin);\n  paths.sort().forEach((path) => url.searchParams.append(\"p\", path));\n  return url;\n}\nasync function fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation, signal) {\n  let url = getManifestUrl(paths);\n  if (url == null) {\n    return;\n  }\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let response = await fetchImplementation(new Request(url, { signal }));\n  if (!response.body || response.status < 200 || response.status >= 300) {\n    throw new Error(\"Unable to fetch new route matches from the server\");\n  }\n  let payload = await createFromReadableStream(response.body, {\n    temporaryReferences: void 0\n  });\n  if (payload.type !== \"manifest\") {\n    throw new Error(\"Failed to patch routes\");\n  }\n  paths.forEach((p) => addToFifoQueue(p, discoveredPaths));\n  payload.patches.forEach((p) => {\n    window.__reactRouterDataRouter.patchRoutes(\n      p.parentId ?? null,\n      [createRouteFromServerManifest(p)]\n    );\n  });\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    queue.delete(first);\n  }\n  queue.add(path);\n}\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return (...args) => {\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\n// lib/rsc/server.ssr.tsx\nimport * as React5 from \"react\";\n\n// lib/rsc/html-stream/server.ts\nvar encoder2 = new TextEncoder();\nvar trailer = \"</body></html>\";\nfunction injectRSCPayload(rscStream) {\n  let decoder = new TextDecoder();\n  let resolveFlightDataPromise;\n  let flightDataPromise = new Promise(\n    (resolve) => resolveFlightDataPromise = resolve\n  );\n  let startedRSC = false;\n  let buffered = [];\n  let timeout = null;\n  function flushBufferedChunks(controller) {\n    for (let chunk of buffered) {\n      let buf = decoder.decode(chunk, { stream: true });\n      if (buf.endsWith(trailer)) {\n        buf = buf.slice(0, -trailer.length);\n      }\n      controller.enqueue(encoder2.encode(buf));\n    }\n    buffered.length = 0;\n    timeout = null;\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      buffered.push(chunk);\n      if (timeout) {\n        return;\n      }\n      timeout = setTimeout(async () => {\n        flushBufferedChunks(controller);\n        if (!startedRSC) {\n          startedRSC = true;\n          writeRSCStream(rscStream, controller).catch((err) => controller.error(err)).then(resolveFlightDataPromise);\n        }\n      }, 0);\n    },\n    async flush(controller) {\n      await flightDataPromise;\n      if (timeout) {\n        clearTimeout(timeout);\n        flushBufferedChunks(controller);\n      }\n      controller.enqueue(encoder2.encode(\"</body></html>\"));\n    }\n  });\n}\nasync function writeRSCStream(rscStream, controller) {\n  let decoder = new TextDecoder(\"utf-8\", { fatal: true });\n  const reader = rscStream.getReader();\n  try {\n    let read;\n    while ((read = await reader.read()) && !read.done) {\n      const chunk = read.value;\n      try {\n        writeChunk(\n          JSON.stringify(decoder.decode(chunk, { stream: true })),\n          controller\n        );\n      } catch (err) {\n        let base64 = JSON.stringify(btoa(String.fromCodePoint(...chunk)));\n        writeChunk(\n          `Uint8Array.from(atob(${base64}), m => m.codePointAt(0))`,\n          controller\n        );\n      }\n    }\n  } finally {\n    reader.releaseLock();\n  }\n  let remaining = decoder.decode();\n  if (remaining.length) {\n    writeChunk(JSON.stringify(remaining), controller);\n  }\n}\nfunction writeChunk(chunk, controller) {\n  controller.enqueue(\n    encoder2.encode(\n      `<script>${escapeScript(\n        `(self.__FLIGHT_DATA||=[]).push(${chunk})`\n      )}</script>`\n    )\n  );\n}\nfunction escapeScript(script) {\n  return script.replace(/<!--/g, \"<\\\\!--\").replace(/<\\/(script)/gi, \"</\\\\$1\");\n}\n\n// lib/rsc/server.ssr.tsx\nvar REACT_USE = \"use\";\nvar useImpl = React5[REACT_USE];\nfunction useSafe(promise) {\n  if (useImpl) {\n    return useImpl(promise);\n  }\n  throw new Error(\"React Router v7 requires React 19+ for RSC features.\");\n}\nasync function routeRSCServerRequest({\n  request,\n  fetchServer,\n  createFromReadableStream,\n  renderHTML,\n  hydrate = true\n}) {\n  const url = new URL(request.url);\n  const isDataRequest = isReactServerRequest(url);\n  const respondWithRSCPayload = isDataRequest || isManifestRequest(url) || request.headers.has(\"rsc-action-id\");\n  const serverResponse = await fetchServer(request);\n  if (respondWithRSCPayload || serverResponse.headers.get(\"React-Router-Resource\") === \"true\") {\n    return serverResponse;\n  }\n  if (!serverResponse.body) {\n    throw new Error(\"Missing body in server response\");\n  }\n  let serverResponseB = null;\n  if (hydrate) {\n    serverResponseB = serverResponse.clone();\n  }\n  const body = serverResponse.body;\n  let payloadPromise;\n  const getPayload = async () => {\n    if (payloadPromise) return payloadPromise;\n    payloadPromise = createFromReadableStream(body);\n    return payloadPromise;\n  };\n  try {\n    const payload = await getPayload();\n    if (serverResponse.status === SINGLE_FETCH_REDIRECT_STATUS && payload.type === \"redirect\") {\n      const headers2 = new Headers(serverResponse.headers);\n      headers2.delete(\"Content-Encoding\");\n      headers2.delete(\"Content-Length\");\n      headers2.delete(\"Content-Type\");\n      headers2.delete(\"x-remix-response\");\n      headers2.set(\"Location\", payload.location);\n      return new Response(serverResponseB?.body || \"\", {\n        headers: headers2,\n        status: payload.status,\n        statusText: serverResponse.statusText\n      });\n    }\n    const html = await renderHTML(getPayload);\n    const headers = new Headers(serverResponse.headers);\n    headers.set(\"Content-Type\", \"text/html\");\n    if (!hydrate) {\n      return new Response(html, {\n        status: serverResponse.status,\n        headers\n      });\n    }\n    if (!serverResponseB?.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n    return new Response(body2, {\n      status: serverResponse.status,\n      headers\n    });\n  } catch (reason) {\n    if (reason instanceof Response) {\n      return reason;\n    }\n    throw reason;\n  }\n}\nfunction RSCStaticRouter({ getPayload }) {\n  const payload = useSafe(getPayload());\n  if (payload.type === \"redirect\") {\n    throw new Response(null, {\n      status: payload.status,\n      headers: {\n        Location: payload.location\n      }\n    });\n  }\n  if (payload.type !== \"render\") return null;\n  let patchedLoaderData = { ...payload.loaderData };\n  for (const match of payload.matches) {\n    if (shouldHydrateRouteLoader(\n      match.id,\n      match.clientLoader,\n      match.hasLoader,\n      false\n    ) && (match.hydrateFallbackElement || !match.hasLoader)) {\n      delete patchedLoaderData[match.id];\n    }\n  }\n  const context = {\n    actionData: payload.actionData,\n    actionHeaders: {},\n    basename: payload.basename,\n    errors: payload.errors,\n    loaderData: patchedLoaderData,\n    loaderHeaders: {},\n    location: payload.location,\n    statusCode: 200,\n    matches: payload.matches.map((match) => ({\n      params: match.params,\n      pathname: match.pathname,\n      pathnameBase: match.pathnameBase,\n      route: {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        handle: match.handle,\n        hasErrorBoundary: match.hasErrorBoundary,\n        loader: match.hasLoader || !!match.clientLoader,\n        index: match.index,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      }\n    }))\n  };\n  const router = createStaticRouter(\n    payload.matches.reduceRight((previous, match) => {\n      const route = {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        element: match.element,\n        errorElement: match.errorElement,\n        handle: match.handle,\n        hasErrorBoundary: !!match.errorElement,\n        hydrateFallbackElement: match.hydrateFallbackElement,\n        index: match.index,\n        loader: match.hasLoader || !!match.clientLoader,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      };\n      if (previous.length > 0) {\n        route.children = previous;\n      }\n      return [route];\n    }, []),\n    context\n  );\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" },\n    routeModules: createRSCRouteModules(payload)\n  };\n  return /* @__PURE__ */ React5.createElement(RSCRouterContext.Provider, { value: true }, /* @__PURE__ */ React5.createElement(RSCRouterGlobalErrorBoundary, { location: payload.location }, /* @__PURE__ */ React5.createElement(FrameworkContext.Provider, { value: frameworkContext }, /* @__PURE__ */ React5.createElement(\n    StaticRouterProvider,\n    {\n      context,\n      router,\n      hydrate: false,\n      nonce: payload.nonce\n    }\n  ))));\n}\nfunction isReactServerRequest(url) {\n  return url.pathname.endsWith(\".rsc\");\n}\nfunction isManifestRequest(url) {\n  return url.pathname.endsWith(\".manifest\");\n}\n\n// lib/rsc/html-stream/browser.ts\nfunction getRSCStream() {\n  let encoder3 = new TextEncoder();\n  let streamController = null;\n  let rscStream = new ReadableStream({\n    start(controller) {\n      if (typeof window === \"undefined\") {\n        return;\n      }\n      let handleChunk = (chunk) => {\n        if (typeof chunk === \"string\") {\n          controller.enqueue(encoder3.encode(chunk));\n        } else {\n          controller.enqueue(chunk);\n        }\n      };\n      window.__FLIGHT_DATA || (window.__FLIGHT_DATA = []);\n      window.__FLIGHT_DATA.forEach(handleChunk);\n      window.__FLIGHT_DATA.push = (chunk) => {\n        handleChunk(chunk);\n        return 0;\n      };\n      streamController = controller;\n    }\n  });\n  if (typeof document !== \"undefined\" && document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      streamController?.close();\n    });\n  } else {\n    streamController?.close();\n  }\n  return rscStream;\n}\n\n// lib/dom/ssr/errors.ts\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\nexport {\n  ServerRouter,\n  createRoutesStub,\n  createCookie,\n  isCookie,\n  ServerMode,\n  setDevServerHooks,\n  createRequestHandler,\n  createSession,\n  isSession,\n  createSessionStorage,\n  createCookieSessionStorage,\n  createMemorySessionStorage,\n  href,\n  getHydrationData,\n  RSCDefaultRootErrorBoundary,\n  createCallServer,\n  RSCHydratedRouter,\n  routeRSCServerRequest,\n  RSCStaticRouter,\n  getRSCStream,\n  deserializeErrors\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SACEA,mBAAmB,EACnBC,iBAAiB,EACjBC,gBAAgB,EAChBC,oBAAoB,EACpBC,MAAM,EACNC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,4BAA4B,EAC5BC,yBAAyB,EACzBC,oBAAoB,EACpBC,cAAc,EACdC,yBAAyB,EACzBC,oBAAoB,EACpBC,kBAAkB,EAClBC,iBAAiB,EACjBC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,kBAAkB,EAClBC,oBAAoB,EACpBC,MAAM,EACNC,eAAe,EACfC,8BAA8B,EAC9BC,yBAAyB,EACzBC,SAAS,EACTC,sBAAsB,EACtBC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,UAAU,EACVC,oBAAoB,EACpBC,WAAW,EACXC,oBAAoB,EACpBC,QAAQ,EACRC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,wBAAwB,EACxBC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,8BAA8B,EAC9BC,sBAAsB,EACtBC,aAAa,EACbC,QAAQ,EACRC,kBAAkB,EAClBC,sBAAsB,EACtBC,wBAAwB,QACnB,sBAAsB;;AAE7B;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAYA,CAAC;EACpBC,OAAO;EACPC,GAAG;EACHC;AACF,CAAC,EAAE;EACD,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAG,IAAIE,GAAG,CAACF,GAAG,CAAC;EACpB;EACA,IAAI;IAAEG,QAAQ;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAoB,CAAC,GAAGP,OAAO;EAC1E,IAAIQ,MAAM,GAAG1C,kBAAkB,CAC7BsC,QAAQ,CAACI,MAAM,EACfH,YAAY,EACZL,OAAO,CAACS,MAAM,EACdT,OAAO,CAACU,SACV,CAAC;EACDV,OAAO,CAACW,oBAAoB,CAACC,UAAU,GAAG;IACxC,GAAGZ,OAAO,CAACW,oBAAoB,CAACC;EAClC,CAAC;EACD,KAAK,IAAIC,KAAK,IAAIb,OAAO,CAACW,oBAAoB,CAACG,OAAO,EAAE;IACtD,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;IAC5B,IAAID,KAAK,GAAGX,YAAY,CAACU,OAAO,CAAC;IACjC,IAAIG,aAAa,GAAGlB,OAAO,CAACI,QAAQ,CAACI,MAAM,CAACO,OAAO,CAAC;IACpD,IAAIC,KAAK,IAAIE,aAAa,IAAI/B,wBAAwB,CACpD4B,OAAO,EACPC,KAAK,CAACG,YAAY,EAClBD,aAAa,CAACE,SAAS,EACvBpB,OAAO,CAACU,SACV,CAAC,KAAKM,KAAK,CAACK,eAAe,IAAI,CAACH,aAAa,CAACE,SAAS,CAAC,EAAE;MACxD,OAAOpB,OAAO,CAACW,oBAAoB,CAACC,UAAU,CAACG,OAAO,CAAC;IACzD;EACF;EACA,IAAIO,MAAM,GAAGtD,kBAAkB,CAACwC,MAAM,EAAER,OAAO,CAACW,oBAAoB,CAAC;EACrE,OAAO,eAAgBb,KAAK,CAACyB,aAAa,CAACzB,KAAK,CAAC0B,QAAQ,EAAE,IAAI,EAAE,eAAgB1B,KAAK,CAACyB,aAAa,CAClGxE,gBAAgB,CAAC0E,QAAQ,EACzB;IACEC,KAAK,EAAE;MACLtB,QAAQ;MACRC,YAAY;MACZC,WAAW;MACXC,mBAAmB;MACnBE,MAAM,EAAET,OAAO,CAACS,MAAM;MACtBkB,GAAG,EAAE3B,OAAO,CAAC2B,GAAG;MAChBjB,SAAS,EAAEV,OAAO,CAACU,SAAS;MAC5BkB,cAAc,EAAE5B,OAAO,CAAC4B,cAAc;MACtCC,cAAc,EAAE7B,OAAO,CAAC6B,cAAc;MACtCC,UAAU,EAAE9B,OAAO,CAAC8B;IACtB;EACF,CAAC,EACD,eAAgBhC,KAAK,CAACyB,aAAa,CAACpE,kBAAkB,EAAE;IAAE4E,QAAQ,EAAET,MAAM,CAACU,KAAK,CAACD;EAAS,CAAC,EAAE,eAAgBjC,KAAK,CAACyB,aAAa,CAC9HhE,oBAAoB,EACpB;IACE+D,MAAM;IACNtB,OAAO,EAAEA,OAAO,CAACW,oBAAoB;IACrCsB,OAAO,EAAE;EACX,CACF,CAAC,CACH,CAAC,EAAEjC,OAAO,CAACkC,mBAAmB,GAAG,eAAgBpC,KAAK,CAACyB,aAAa,CAACzB,KAAK,CAACqC,QAAQ,EAAE,IAAI,EAAE,eAAgBrC,KAAK,CAACyB,aAAa,CAC5H/D,cAAc,EACd;IACEwC,OAAO;IACPoC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAErC,OAAO,CAACkC,mBAAmB,CAACI,SAAS,CAAC,CAAC;IAC/CC,WAAW,EAAE,IAAIC,WAAW,CAAC,CAAC;IAC9BtC;EACF,CACF,CAAC,CAAC,GAAG,IAAI,CAAC;AACZ;;AAEA;AACA,OAAO,KAAKuC,MAAM,MAAM,OAAO;AAC/B,SAASC,gBAAgBA,CAAClC,MAAM,EAAEmC,QAAQ,EAAE;EAC1C,OAAO,SAASC,cAAcA,CAAC;IAC7BC,cAAc;IACdC,YAAY;IACZC,aAAa;IACbtC;EACF,CAAC,EAAE;IACD,IAAIuC,SAAS,GAAGP,MAAM,CAACQ,MAAM,CAAC,CAAC;IAC/B,IAAIC,mBAAmB,GAAGT,MAAM,CAACQ,MAAM,CAAC,CAAC;IACzC,IAAID,SAAS,CAACG,OAAO,IAAI,IAAI,EAAE;MAC7BD,mBAAmB,CAACC,OAAO,GAAG;QAC5B1C,MAAM,EAAE;UACN2C,6BAA6B,EAAE3C,MAAM,EAAE2C,6BAA6B,KAAK,IAAI;UAC7EC,mBAAmB,EAAE5C,MAAM,EAAE4C,mBAAmB,KAAK;QACvD,CAAC;QACDjD,QAAQ,EAAE;UACRI,MAAM,EAAE,CAAC,CAAC;UACV8C,KAAK,EAAE;YAAEC,OAAO,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAC;UAClCvD,GAAG,EAAE,EAAE;UACPwD,OAAO,EAAE;QACX,CAAC;QACDpD,YAAY,EAAE,CAAC,CAAC;QAChBsB,GAAG,EAAE,KAAK;QACVjB,SAAS,EAAE,KAAK;QAChBkB,cAAc,EAAE;UAAE8B,IAAI,EAAE,MAAM;UAAEC,YAAY,EAAE;QAAc;MAC9D,CAAC;MACD,IAAIC,OAAO,GAAGC,aAAa;MACzB;MACA;MACApG,yBAAyB,CAAC+C,MAAM,EAAGsD,CAAC,IAAKA,CAAC,CAAC,EAC3CnB,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGlC,MAAM,EAAE4C,mBAAmB,GAAG,IAAI9D,8BAA8B,CAAC,CAAC,GAAG,CAAC,CAAC,EACxG2D,mBAAmB,CAACC,OAAO,CAAC/C,QAAQ,EACpC8C,mBAAmB,CAACC,OAAO,CAAC9C,YAC9B,CAAC;MACD2C,SAAS,CAACG,OAAO,GAAGxF,kBAAkB,CAACiG,OAAO,EAAE;QAC9Cf,cAAc;QACdC,YAAY;QACZC;MACF,CAAC,CAAC;IACJ;IACA,OAAO,eAAgBN,MAAM,CAAClB,aAAa,CAACxE,gBAAgB,CAAC0E,QAAQ,EAAE;MAAEC,KAAK,EAAEwB,mBAAmB,CAACC;IAAQ,CAAC,EAAE,eAAgBV,MAAM,CAAClB,aAAa,CAACnE,cAAc,EAAE;MAAEkE,MAAM,EAAE0B,SAAS,CAACG;IAAQ,CAAC,CAAC,CAAC;EACrM,CAAC;AACH;AACA,SAASU,aAAaA,CAACrD,MAAM,EAAER,OAAO,EAAEI,QAAQ,EAAEC,YAAY,EAAE0D,QAAQ,EAAE;EACxE,OAAOvD,MAAM,CAACwD,GAAG,CAAEhD,KAAK,IAAK;IAC3B,IAAI,CAACA,KAAK,CAACC,EAAE,EAAE;MACb,MAAM,IAAIgD,KAAK,CACb,8DACF,CAAC;IACH;IACA,IAAIC,QAAQ,GAAG;MACbjD,EAAE,EAAED,KAAK,CAACC,EAAE;MACZkD,IAAI,EAAEnD,KAAK,CAACmD,IAAI;MAChBC,KAAK,EAAEpD,KAAK,CAACoD,KAAK;MAClBC,SAAS,EAAErD,KAAK,CAACqD,SAAS,GAAG1E,kBAAkB,CAACqB,KAAK,CAACqD,SAAS,CAAC,GAAG,KAAK,CAAC;MACzEhD,eAAe,EAAEL,KAAK,CAACK,eAAe,GAAGxB,wBAAwB,CAACmB,KAAK,CAACK,eAAe,CAAC,GAAG,KAAK,CAAC;MACjGiD,aAAa,EAAEtD,KAAK,CAACsD,aAAa,GAAG1E,sBAAsB,CAACoB,KAAK,CAACsD,aAAa,CAAC,GAAG,KAAK,CAAC;MACzFC,MAAM,EAAEvD,KAAK,CAACuD,MAAM,GAAIC,IAAI,IAAKxD,KAAK,CAACuD,MAAM,CAAC;QAAE,GAAGC,IAAI;QAAExE;MAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;MAC5EyE,MAAM,EAAEzD,KAAK,CAACyD,MAAM,GAAID,IAAI,IAAKxD,KAAK,CAACyD,MAAM,CAAC;QAAE,GAAGD,IAAI;QAAExE;MAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;MAC5E0E,MAAM,EAAE1D,KAAK,CAAC0D,MAAM;MACpBC,gBAAgB,EAAE3D,KAAK,CAAC2D;IAC1B,CAAC;IACD,IAAIC,UAAU,GAAG;MACf3D,EAAE,EAAED,KAAK,CAACC,EAAE;MACZkD,IAAI,EAAEnD,KAAK,CAACmD,IAAI;MAChBC,KAAK,EAAEpD,KAAK,CAACoD,KAAK;MAClBL,QAAQ;MACRc,SAAS,EAAE7D,KAAK,CAACuD,MAAM,IAAI,IAAI;MAC/BnD,SAAS,EAAEJ,KAAK,CAACyD,MAAM,IAAI,IAAI;MAC/B;MACA;MACA;MACAK,eAAe,EAAE,KAAK;MACtBC,eAAe,EAAE,KAAK;MACtBC,mBAAmB,EAAE,KAAK;MAC1BC,gBAAgB,EAAEjE,KAAK,CAACsD,aAAa,IAAI,IAAI;MAC7C;MACAd,MAAM,EAAE,8BAA8B;MACtC0B,kBAAkB,EAAE,KAAK,CAAC;MAC1BC,kBAAkB,EAAE,KAAK,CAAC;MAC1BC,sBAAsB,EAAE,KAAK,CAAC;MAC9BC,qBAAqB,EAAE,KAAK;IAC9B,CAAC;IACDjF,QAAQ,CAACI,MAAM,CAAC0D,QAAQ,CAACjD,EAAE,CAAC,GAAG2D,UAAU;IACzCvE,YAAY,CAACW,KAAK,CAACC,EAAE,CAAC,GAAG;MACvBqE,OAAO,EAAEpB,QAAQ,CAACG,SAAS,IAAIpH,MAAM;MACrCqH,aAAa,EAAEJ,QAAQ,CAACI,aAAa,IAAI,KAAK,CAAC;MAC/CI,MAAM,EAAE1D,KAAK,CAAC0D,MAAM;MACpBa,KAAK,EAAEvE,KAAK,CAACuE,KAAK;MAClBC,IAAI,EAAExE,KAAK,CAACwE,IAAI;MAChBb,gBAAgB,EAAE3D,KAAK,CAAC2D;IAC1B,CAAC;IACD,IAAI3D,KAAK,CAACyE,QAAQ,EAAE;MAClBvB,QAAQ,CAACuB,QAAQ,GAAG5B,aAAa,CAC/B7C,KAAK,CAACyE,QAAQ,EACdzF,OAAO,EACPI,QAAQ,EACRC,YAAY,EACZ6D,QAAQ,CAACjD,EACX,CAAC;IACH;IACA,OAAOiD,QAAQ;EACjB,CAAC,CAAC;AACJ;;AAEA;AACA,SAASwB,KAAK,EAAEC,SAAS,QAAQ,QAAQ;;AAEzC;AACA,IAAIC,OAAO,GAAG,eAAgB,IAAIC,WAAW,CAAC,CAAC;AAC/C,IAAIC,IAAI,GAAG,MAAAA,CAAOpE,KAAK,EAAEqE,MAAM,KAAK;EAClC,IAAIC,KAAK,GAAGJ,OAAO,CAAC1H,MAAM,CAACwD,KAAK,CAAC;EACjC,IAAIuE,GAAG,GAAG,MAAMC,SAAS,CAACH,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;EAC3C,IAAII,SAAS,GAAG,MAAMC,MAAM,CAACC,MAAM,CAACP,IAAI,CAAC,MAAM,EAAEG,GAAG,EAAED,KAAK,CAAC;EAC5D,IAAIM,IAAI,GAAGC,IAAI,CAACC,MAAM,CAACC,YAAY,CAAC,GAAG,IAAIC,UAAU,CAACP,SAAS,CAAC,CAAC,CAAC,CAAClH,OAAO,CACxE,KAAK,EACL,EACF,CAAC;EACD,OAAOyC,KAAK,GAAG,GAAG,GAAG4E,IAAI;AAC3B,CAAC;AACD,IAAIK,MAAM,GAAG,MAAAA,CAAOC,MAAM,EAAEb,MAAM,KAAK;EACrC,IAAI3B,KAAK,GAAGwC,MAAM,CAACC,WAAW,CAAC,GAAG,CAAC;EACnC,IAAInF,KAAK,GAAGkF,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE1C,KAAK,CAAC;EAClC,IAAIkC,IAAI,GAAGM,MAAM,CAACE,KAAK,CAAC1C,KAAK,GAAG,CAAC,CAAC;EAClC,IAAI4B,KAAK,GAAGJ,OAAO,CAAC1H,MAAM,CAACwD,KAAK,CAAC;EACjC,IAAIuE,GAAG,GAAG,MAAMC,SAAS,CAACH,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC;EAC7C,IAAI;IACF,IAAII,SAAS,GAAGY,sBAAsB,CAACC,IAAI,CAACV,IAAI,CAAC,CAAC;IAClD,IAAIW,KAAK,GAAG,MAAMb,MAAM,CAACC,MAAM,CAACa,MAAM,CAAC,MAAM,EAAEjB,GAAG,EAAEE,SAAS,EAAEH,KAAK,CAAC;IACrE,OAAOiB,KAAK,GAAGvF,KAAK,GAAG,KAAK;EAC9B,CAAC,CAAC,OAAOyF,KAAK,EAAE;IACd,OAAO,KAAK;EACd;AACF,CAAC;AACD,IAAIjB,SAAS,GAAG,MAAAA,CAAOH,MAAM,EAAEqB,MAAM,KAAKhB,MAAM,CAACC,MAAM,CAACgB,SAAS,CAC/D,KAAK,EACLzB,OAAO,CAAC1H,MAAM,CAAC6H,MAAM,CAAC,EACtB;EAAEuB,IAAI,EAAE,MAAM;EAAEhB,IAAI,EAAE;AAAU,CAAC,EACjC,KAAK,EACLc,MACF,CAAC;AACD,SAASL,sBAAsBA,CAACQ,UAAU,EAAE;EAC1C,IAAIC,KAAK,GAAG,IAAId,UAAU,CAACa,UAAU,CAACE,MAAM,CAAC;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC1CF,KAAK,CAACE,CAAC,CAAC,GAAGH,UAAU,CAACI,UAAU,CAACD,CAAC,CAAC;EACrC;EACA,OAAOF,KAAK;AACd;;AAEA;AACA,IAAII,YAAY,GAAGA,CAACN,IAAI,EAAEO,aAAa,GAAG,CAAC,CAAC,KAAK;EAC/C,IAAI;IAAEC,OAAO,GAAG,EAAE;IAAE,GAAGC;EAAQ,CAAC,GAAG;IACjC5D,IAAI,EAAE,GAAG;IACT6D,QAAQ,EAAE,KAAK;IACf,GAAGH;EACL,CAAC;EACDI,0BAA0B,CAACX,IAAI,EAAES,OAAO,CAACG,OAAO,CAAC;EACjD,OAAO;IACL,IAAIZ,IAAIA,CAAA,EAAG;MACT,OAAOA,IAAI;IACb,CAAC;IACD,IAAIa,QAAQA,CAAA,EAAG;MACb,OAAOL,OAAO,CAACL,MAAM,GAAG,CAAC;IAC3B,CAAC;IACD,IAAIS,OAAOA,CAAA,EAAG;MACZ,OAAO,OAAOH,OAAO,CAACK,MAAM,KAAK,WAAW,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,OAAO,CAACK,MAAM,GAAG,GAAG,CAAC,GAAGL,OAAO,CAACG,OAAO;IAC9G,CAAC;IACD,MAAMxC,KAAKA,CAAC6C,YAAY,EAAEC,YAAY,EAAE;MACtC,IAAI,CAACD,YAAY,EAAE,OAAO,IAAI;MAC9B,IAAIE,OAAO,GAAG/C,KAAK,CAAC6C,YAAY,EAAE;QAAE,GAAGR,OAAO;QAAE,GAAGS;MAAa,CAAC,CAAC;MAClE,IAAIlB,IAAI,IAAImB,OAAO,EAAE;QACnB,IAAI/G,KAAK,GAAG+G,OAAO,CAACnB,IAAI,CAAC;QACzB,IAAI,OAAO5F,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,EAAE,EAAE;UAC7C,IAAIgH,OAAO,GAAG,MAAMC,iBAAiB,CAACjH,KAAK,EAAEoG,OAAO,CAAC;UACrD,OAAOY,OAAO;QAChB,CAAC,MAAM;UACL,OAAO,EAAE;QACX;MACF,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC;IACD,MAAM/C,SAASA,CAACjE,KAAK,EAAEkH,gBAAgB,EAAE;MACvC,OAAOjD,SAAS,CACd2B,IAAI,EACJ5F,KAAK,KAAK,EAAE,GAAG,EAAE,GAAG,MAAMmH,iBAAiB,CAACnH,KAAK,EAAEoG,OAAO,CAAC,EAC3D;QACE,GAAGC,OAAO;QACV,GAAGa;MACL,CACF,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD,IAAIE,QAAQ,GAAIC,MAAM,IAAK;EACzB,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACzB,IAAI,KAAK,QAAQ,IAAI,OAAOyB,MAAM,CAACZ,QAAQ,KAAK,SAAS,IAAI,OAAOY,MAAM,CAACrD,KAAK,KAAK,UAAU,IAAI,OAAOqD,MAAM,CAACpD,SAAS,KAAK,UAAU;AAClL,CAAC;AACD,eAAekD,iBAAiBA,CAACnH,KAAK,EAAEoG,OAAO,EAAE;EAC/C,IAAIkB,OAAO,GAAGC,UAAU,CAACvH,KAAK,CAAC;EAC/B,IAAIoG,OAAO,CAACL,MAAM,GAAG,CAAC,EAAE;IACtBuB,OAAO,GAAG,MAAMlD,IAAI,CAACkD,OAAO,EAAElB,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3C;EACA,OAAOkB,OAAO;AAChB;AACA,eAAeL,iBAAiBA,CAACjH,KAAK,EAAEoG,OAAO,EAAE;EAC/C,IAAIA,OAAO,CAACL,MAAM,GAAG,CAAC,EAAE;IACtB,KAAK,IAAI1B,MAAM,IAAI+B,OAAO,EAAE;MAC1B,IAAIoB,aAAa,GAAG,MAAMvC,MAAM,CAACjF,KAAK,EAAEqE,MAAM,CAAC;MAC/C,IAAImD,aAAa,KAAK,KAAK,EAAE;QAC3B,OAAOC,UAAU,CAACD,aAAa,CAAC;MAClC;IACF;IACA,OAAO,IAAI;EACb;EACA,OAAOC,UAAU,CAACzH,KAAK,CAAC;AAC1B;AACA,SAASuH,UAAUA,CAACvH,KAAK,EAAE;EACzB,OAAO6E,IAAI,CAAC6C,UAAU,CAACC,kBAAkB,CAACC,IAAI,CAACC,SAAS,CAAC7H,KAAK,CAAC,CAAC,CAAC,CAAC;AACpE;AACA,SAASyH,UAAUA,CAACzH,KAAK,EAAE;EACzB,IAAI;IACF,OAAO4H,IAAI,CAAC5D,KAAK,CAAC8D,kBAAkB,CAACC,QAAQ,CAACzC,IAAI,CAACtF,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC,OAAOyF,KAAK,EAAE;IACd,OAAO,CAAC,CAAC;EACX;AACF;AACA,SAASsC,QAAQA,CAAC/H,KAAK,EAAE;EACvB,IAAIgI,GAAG,GAAGhI,KAAK,CAACiI,QAAQ,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIxF,KAAK,GAAG,CAAC;EACb,IAAIyF,GAAG,EAAEC,IAAI;EACb,OAAO1F,KAAK,GAAGsF,GAAG,CAACjC,MAAM,EAAE;IACzBoC,GAAG,GAAGH,GAAG,CAACK,MAAM,CAAC3F,KAAK,EAAE,CAAC;IACzB,IAAI,aAAa,CAAC4F,IAAI,CAACH,GAAG,CAAC,EAAE;MAC3BD,MAAM,IAAIC,GAAG;IACf,CAAC,MAAM;MACLC,IAAI,GAAGD,GAAG,CAAClC,UAAU,CAAC,CAAC,CAAC;MACxB,IAAImC,IAAI,GAAG,GAAG,EAAE;QACdF,MAAM,IAAI,GAAG,GAAGK,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC;MAC9B,CAAC,MAAM;QACLF,MAAM,IAAI,IAAI,GAAGK,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;MAC7C;IACF;EACF;EACA,OAAON,MAAM;AACf;AACA,SAASK,GAAGA,CAACH,IAAI,EAAErC,MAAM,EAAE;EACzB,IAAImC,MAAM,GAAGE,IAAI,CAACH,QAAQ,CAAC,EAAE,CAAC;EAC9B,OAAOC,MAAM,CAACnC,MAAM,GAAGA,MAAM,EAAEmC,MAAM,GAAG,GAAG,GAAGA,MAAM;EACpD,OAAOA,MAAM;AACf;AACA,SAASR,UAAUA,CAAC1H,KAAK,EAAE;EACzB,IAAIgI,GAAG,GAAGhI,KAAK,CAACiI,QAAQ,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIxF,KAAK,GAAG,CAAC;EACb,IAAIyF,GAAG,EAAEM,IAAI;EACb,OAAO/F,KAAK,GAAGsF,GAAG,CAACjC,MAAM,EAAE;IACzBoC,GAAG,GAAGH,GAAG,CAACK,MAAM,CAAC3F,KAAK,EAAE,CAAC;IACzB,IAAIyF,GAAG,KAAK,GAAG,EAAE;MACf,IAAIH,GAAG,CAACK,MAAM,CAAC3F,KAAK,CAAC,KAAK,GAAG,EAAE;QAC7B+F,IAAI,GAAGT,GAAG,CAAC5C,KAAK,CAAC1C,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,CAAC;QACtC,IAAI,eAAe,CAAC4F,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9BP,MAAM,IAAIpD,MAAM,CAACC,YAAY,CAAC2D,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,CAAC;UACjD/F,KAAK,IAAI,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL+F,IAAI,GAAGT,GAAG,CAAC5C,KAAK,CAAC1C,KAAK,EAAEA,KAAK,GAAG,CAAC,CAAC;QAClC,IAAI,eAAe,CAAC4F,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9BP,MAAM,IAAIpD,MAAM,CAACC,YAAY,CAAC2D,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,CAAC;UACjD/F,KAAK,IAAI,CAAC;UACV;QACF;MACF;IACF;IACAwF,MAAM,IAAIC,GAAG;EACf;EACA,OAAOD,MAAM;AACf;AACA,SAAS3B,0BAA0BA,CAACX,IAAI,EAAEY,OAAO,EAAE;EACjDxI,QAAQ,CACN,CAACwI,OAAO,EACR,QAAQZ,IAAI,6WACd,CAAC;AACH;;AAEA;AACA,SAAS+C,uBAAuBA,CAACjK,QAAQ,EAAE;EACzC,OAAOkK,MAAM,CAACC,IAAI,CAACnK,QAAQ,CAAC,CAACoK,MAAM,CAAC,CAACC,IAAI,EAAE1J,OAAO,KAAK;IACrD,IAAIC,KAAK,GAAGZ,QAAQ,CAACW,OAAO,CAAC;IAC7B,IAAIC,KAAK,EAAE;MACTyJ,IAAI,CAAC1J,OAAO,CAAC,GAAGC,KAAK,CAACwC,MAAM;IAC9B;IACA,OAAOiH,IAAI;EACb,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;;AAEA;AACA,IAAIC,UAAU,GAAG,eAAgB,CAAEC,WAAW,IAAK;EACjDA,WAAW,CAAC,aAAa,CAAC,GAAG,aAAa;EAC1CA,WAAW,CAAC,YAAY,CAAC,GAAG,YAAY;EACxCA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;EAC5B,OAAOA,WAAW;AACpB,CAAC,EAAED,UAAU,IAAI,CAAC,CAAC,CAAC;AACpB,SAASE,YAAYA,CAAClJ,KAAK,EAAE;EAC3B,OAAOA,KAAK,KAAK,aAAa,CAAC,qBAAqBA,KAAK,KAAK,YAAY,CAAC,oBAAoBA,KAAK,KAAK,MAAM,CAAC;AAClH;;AAEA;AACA,SAASmJ,aAAaA,CAAC1D,KAAK,EAAE2D,UAAU,EAAE;EACxC,IAAI3D,KAAK,YAAYlD,KAAK,IAAI6G,UAAU,KAAK,aAAa,CAAC,mBAAmB;IAC5E,IAAIC,SAAS,GAAG,IAAI9G,KAAK,CAAC,yBAAyB,CAAC;IACpD8G,SAAS,CAACC,KAAK,GAAG,KAAK,CAAC;IACxB,OAAOD,SAAS;EAClB;EACA,OAAO5D,KAAK;AACd;AACA,SAAS8D,cAAcA,CAACC,MAAM,EAAEJ,UAAU,EAAE;EAC1C,OAAOR,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC,CAACV,MAAM,CAAC,CAACY,GAAG,EAAE,CAACrK,OAAO,EAAEoG,KAAK,CAAC,KAAK;IAC9D,OAAOmD,MAAM,CAACe,MAAM,CAACD,GAAG,EAAE;MAAE,CAACrK,OAAO,GAAG8J,aAAa,CAAC1D,KAAK,EAAE2D,UAAU;IAAE,CAAC,CAAC;EAC5E,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,SAASjJ,cAAcA,CAACsF,KAAK,EAAE2D,UAAU,EAAE;EACzC,IAAIC,SAAS,GAAGF,aAAa,CAAC1D,KAAK,EAAE2D,UAAU,CAAC;EAChD,OAAO;IACLQ,OAAO,EAAEP,SAAS,CAACO,OAAO;IAC1BN,KAAK,EAAED,SAAS,CAACC;EACnB,CAAC;AACH;AACA,SAASO,eAAeA,CAACL,MAAM,EAAEJ,UAAU,EAAE;EAC3C,IAAI,CAACI,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIC,OAAO,GAAGb,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC;EACpC,IAAIM,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI,CAACvF,GAAG,EAAEwF,GAAG,CAAC,IAAIN,OAAO,EAAE;IAC9B,IAAIvM,oBAAoB,CAAC6M,GAAG,CAAC,EAAE;MAC7BD,UAAU,CAACvF,GAAG,CAAC,GAAG;QAAE,GAAGwF,GAAG;QAAEC,MAAM,EAAE;MAAqB,CAAC;IAC5D,CAAC,MAAM,IAAID,GAAG,YAAYxH,KAAK,EAAE;MAC/B,IAAI8G,SAAS,GAAGF,aAAa,CAACY,GAAG,EAAEX,UAAU,CAAC;MAC9CU,UAAU,CAACvF,GAAG,CAAC,GAAG;QAChBqF,OAAO,EAAEP,SAAS,CAACO,OAAO;QAC1BN,KAAK,EAAED,SAAS,CAACC,KAAK;QACtBU,MAAM,EAAE,OAAO;QACf;QACA;QACA;QACA;QACA,IAAGX,SAAS,CAACzD,IAAI,KAAK,OAAO,GAAG;UAC9BqE,SAAS,EAAEZ,SAAS,CAACzD;QACvB,CAAC,GAAG,CAAC,CAAC;MACR,CAAC;IACH,CAAC,MAAM;MACLkE,UAAU,CAACvF,GAAG,CAAC,GAAGwF,GAAG;IACvB;EACF;EACA,OAAOD,UAAU;AACnB;;AAEA;AACA,SAASI,iBAAiBA,CAACpL,MAAM,EAAEqL,QAAQ,EAAEC,QAAQ,EAAE;EACrD,IAAIhL,OAAO,GAAGjC,WAAW,CACvB2B,MAAM,EACNqL,QAAQ,EACRC,QACF,CAAC;EACD,IAAI,CAAChL,OAAO,EAAE,OAAO,IAAI;EACzB,OAAOA,OAAO,CAACkD,GAAG,CAAEnD,KAAK,KAAM;IAC7BkL,MAAM,EAAElL,KAAK,CAACkL,MAAM;IACpBF,QAAQ,EAAEhL,KAAK,CAACgL,QAAQ;IACxB7K,KAAK,EAAEH,KAAK,CAACG;EACf,CAAC,CAAC,CAAC;AACL;;AAEA;AACA,eAAegL,gBAAgBA,CAACC,OAAO,EAAEzH,IAAI,EAAE;EAC7C,IAAIoF,MAAM,GAAG,MAAMqC,OAAO,CAAC;IACzBC,OAAO,EAAEC,gBAAgB,CAACC,gBAAgB,CAAC5H,IAAI,CAAC0H,OAAO,CAAC,CAAC;IACzDH,MAAM,EAAEvH,IAAI,CAACuH,MAAM;IACnB/L,OAAO,EAAEwE,IAAI,CAACxE;EAChB,CAAC,CAAC;EACF,IAAIzB,sBAAsB,CAACqL,MAAM,CAAC,IAAIA,MAAM,CAACyC,IAAI,IAAIzC,MAAM,CAACyC,IAAI,CAACC,MAAM,IAAI5N,oBAAoB,CAACkL,MAAM,CAACyC,IAAI,CAACC,MAAM,CAAC,EAAE;IACnH,MAAM,IAAIC,QAAQ,CAAC,IAAI,EAAE3C,MAAM,CAACyC,IAAI,CAAC;EACvC;EACA,OAAOzC,MAAM;AACf;AACA,SAASwC,gBAAgBA,CAACF,OAAO,EAAE;EACjC,IAAIjM,GAAG,GAAG,IAAIE,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;EAC9B,IAAIuM,WAAW,GAAGvM,GAAG,CAACwM,YAAY,CAACC,MAAM,CAAC,OAAO,CAAC;EAClDzM,GAAG,CAACwM,YAAY,CAACE,MAAM,CAAC,OAAO,CAAC;EAChC,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,KAAK,IAAIC,UAAU,IAAIL,WAAW,EAAE;IAClC,IAAIK,UAAU,EAAE;MACdD,iBAAiB,CAACE,IAAI,CAACD,UAAU,CAAC;IACpC;EACF;EACA,KAAK,IAAIE,MAAM,IAAIH,iBAAiB,EAAE;IACpC3M,GAAG,CAACwM,YAAY,CAACO,MAAM,CAAC,OAAO,EAAED,MAAM,CAAC;EAC1C;EACA,IAAIV,IAAI,GAAG;IACTY,MAAM,EAAEf,OAAO,CAACe,MAAM;IACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;IAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;IACxBC,MAAM,EAAElB,OAAO,CAACkB;EAClB,CAAC;EACD,IAAIf,IAAI,CAACa,IAAI,EAAE;IACbb,IAAI,CAACgB,MAAM,GAAG,MAAM;EACtB;EACA,OAAO,IAAIC,OAAO,CAACrN,GAAG,CAACsN,IAAI,EAAElB,IAAI,CAAC;AACpC;AACA,SAASF,gBAAgBA,CAACD,OAAO,EAAE;EACjC,IAAIjM,GAAG,GAAG,IAAIE,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;EAC9BA,GAAG,CAACwM,YAAY,CAACE,MAAM,CAAC,SAAS,CAAC;EAClC,IAAIN,IAAI,GAAG;IACTY,MAAM,EAAEf,OAAO,CAACe,MAAM;IACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;IAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;IACxBC,MAAM,EAAElB,OAAO,CAACkB;EAClB,CAAC;EACD,IAAIf,IAAI,CAACa,IAAI,EAAE;IACbb,IAAI,CAACgB,MAAM,GAAG,MAAM;EACtB;EACA,OAAO,IAAIC,OAAO,CAACrN,GAAG,CAACsN,IAAI,EAAElB,IAAI,CAAC;AACpC;;AAEA;AACA,SAASmB,UAAUA,CAAC9L,KAAK,EAAE4J,OAAO,EAAE;EAClC,IAAI5J,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;IACrE+L,OAAO,CAACtG,KAAK,CACX,iIACF,CAAC;IACD,MAAM,IAAIlD,KAAK,CAACqH,OAAO,CAAC;EAC1B;AACF;;AAEA;AACA,IAAIoC,uBAAuB,GAAG,6BAA6B;AAC3D,SAASC,iBAAiBA,CAACC,cAAc,EAAE;EACzCC,UAAU,CAACH,uBAAuB,CAAC,GAAGE,cAAc;AACtD;AACA,SAASE,iBAAiBA,CAAA,EAAG;EAC3B,OAAOD,UAAU,CAACH,uBAAuB,CAAC;AAC5C;AACA,SAASK,kBAAkBA,CAAC7B,OAAO,EAAE8B,UAAU,EAAE;EAC/C,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;IAClC,IAAI;MACF,IAAIA,OAAO,CAACC,GAAG,EAAEC,mBAAmB,KAAK,KAAK,EAAE;QAC9C,OAAOjC,OAAO,CAACiB,OAAO,CAACiB,GAAG,CAACJ,UAAU,CAAC;MACxC;IACF,CAAC,CAAC,OAAOK,CAAC,EAAE,CACZ;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAASC,qBAAqBA,CAAClO,QAAQ,EAAE;EACvC,IAAII,MAAM,GAAG,CAAC,CAAC;EACf8J,MAAM,CAACiE,MAAM,CAACnO,QAAQ,CAAC,CAACoO,OAAO,CAAExN,KAAK,IAAK;IACzC,IAAIA,KAAK,EAAE;MACT,IAAI+C,QAAQ,GAAG/C,KAAK,CAAC+C,QAAQ,IAAI,EAAE;MACnC,IAAI,CAACvD,MAAM,CAACuD,QAAQ,CAAC,EAAE;QACrBvD,MAAM,CAACuD,QAAQ,CAAC,GAAG,EAAE;MACvB;MACAvD,MAAM,CAACuD,QAAQ,CAAC,CAAC+I,IAAI,CAAC9L,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,OAAOR,MAAM;AACf;AACA,SAASiO,YAAYA,CAACrO,QAAQ,EAAE2D,QAAQ,GAAG,EAAE,EAAE2K,gBAAgB,GAAGJ,qBAAqB,CAAClO,QAAQ,CAAC,EAAE;EACjG,OAAO,CAACsO,gBAAgB,CAAC3K,QAAQ,CAAC,IAAI,EAAE,EAAEC,GAAG,CAAEhD,KAAK,KAAM;IACxD,GAAGA,KAAK;IACRyE,QAAQ,EAAEgJ,YAAY,CAACrO,QAAQ,EAAEY,KAAK,CAACC,EAAE,EAAEyN,gBAAgB;EAC7D,CAAC,CAAC,CAAC;AACL;AACA,SAASC,6BAA6BA,CAACvO,QAAQ,EAAEK,MAAM,EAAEsD,QAAQ,GAAG,EAAE,EAAE2K,gBAAgB,GAAGJ,qBAAqB,CAAClO,QAAQ,CAAC,EAAE;EAC1H,OAAO,CAACsO,gBAAgB,CAAC3K,QAAQ,CAAC,IAAI,EAAE,EAAEC,GAAG,CAAEhD,KAAK,IAAK;IACvD,IAAI4N,WAAW,GAAG;MAChB;MACA3J,gBAAgB,EAAEjE,KAAK,CAACC,EAAE,KAAK,MAAM,IAAID,KAAK,CAACwC,MAAM,CAACc,aAAa,IAAI,IAAI;MAC3ErD,EAAE,EAAED,KAAK,CAACC,EAAE;MACZkD,IAAI,EAAEnD,KAAK,CAACmD,IAAI;MAChBd,mBAAmB,EAAErC,KAAK,CAACwC,MAAM,CAACH,mBAAmB;MACrD;MACA;MACAoB,MAAM,EAAEzD,KAAK,CAACwC,MAAM,CAACiB,MAAM,GAAG,MAAOD,IAAI,IAAK;QAC5C,IAAIqK,eAAe,GAAGd,kBAAkB,CACtCvJ,IAAI,CAAC0H,OAAO,EACZ,+BACF,CAAC;QACD,IAAI2C,eAAe,IAAI,IAAI,EAAE;UAC3B,IAAI7F,OAAO,GAAG6F,eAAe,GAAGC,SAAS,CAACD,eAAe,CAAC,GAAGA,eAAe;UAC5ErB,UAAU,CAACxE,OAAO,EAAE,oCAAoC,CAAC;UACzD,IAAI+F,UAAU,GAAG,IAAIlJ,WAAW,CAAC,CAAC,CAAC3H,MAAM,CAAC8K,OAAO,CAAC;UAClD,IAAIgG,MAAM,GAAG,IAAIC,cAAc,CAAC;YAC9BC,KAAKA,CAACC,UAAU,EAAE;cAChBA,UAAU,CAACC,OAAO,CAACL,UAAU,CAAC;cAC9BI,UAAU,CAACE,KAAK,CAAC,CAAC;YACpB;UACF,CAAC,CAAC;UACF,IAAI3G,OAAO,GAAG,MAAMzK,oBAAoB,CAAC+Q,MAAM,EAAEM,MAAM,CAAC;UACxD,IAAItJ,KAAK,GAAG0C,OAAO,CAAChH,KAAK;UACzB,IAAIsE,KAAK,IAAI1I,yBAAyB,IAAI0I,KAAK,EAAE;YAC/C,IAAI4D,MAAM,GAAG5D,KAAK,CAAC1I,yBAAyB,CAAC;YAC7C,IAAI+O,IAAI,GAAG;cAAEC,MAAM,EAAE1C,MAAM,CAAC0C;YAAO,CAAC;YACpC,IAAI1C,MAAM,CAAC2F,MAAM,EAAE;cACjB,MAAMvQ,gBAAgB,CAAC4K,MAAM,CAAC7K,QAAQ,EAAEsN,IAAI,CAAC;YAC/C,CAAC,MAAM,IAAIzC,MAAM,CAAC3K,OAAO,EAAE;cACzB,MAAMA,OAAO,CAAC2K,MAAM,CAAC7K,QAAQ,EAAEsN,IAAI,CAAC;YACtC,CAAC,MAAM;cACL,MAAMtN,QAAQ,CAAC6K,MAAM,CAAC7K,QAAQ,EAAEsN,IAAI,CAAC;YACvC;UACF,CAAC,MAAM;YACLmB,UAAU,CACRxH,KAAK,IAAIhF,KAAK,CAACC,EAAE,IAAI+E,KAAK,EAC1B,mCACF,CAAC;YACD,IAAI4D,MAAM,GAAG5D,KAAK,CAAChF,KAAK,CAACC,EAAE,CAAC;YAC5BuM,UAAU,CACR,MAAM,IAAI5D,MAAM,EAChB,oCACF,CAAC;YACD,OAAOA,MAAM,CAAC4F,IAAI;UACpB;QACF;QACA,IAAI/D,GAAG,GAAG,MAAMO,gBAAgB,CAAChL,KAAK,CAACwC,MAAM,CAACiB,MAAM,EAAED,IAAI,CAAC;QAC3D,OAAOiH,GAAG;MACZ,CAAC,GAAG,KAAK,CAAC;MACVlH,MAAM,EAAEvD,KAAK,CAACwC,MAAM,CAACe,MAAM,GAAIC,IAAI,IAAKwH,gBAAgB,CAAChL,KAAK,CAACwC,MAAM,CAACe,MAAM,EAAEC,IAAI,CAAC,GAAG,KAAK,CAAC;MAC5FE,MAAM,EAAE1D,KAAK,CAACwC,MAAM,CAACkB;IACvB,CAAC;IACD,OAAO1D,KAAK,CAACoD,KAAK,GAAG;MACnBA,KAAK,EAAE,IAAI;MACX,GAAGwK;IACL,CAAC,GAAG;MACFa,aAAa,EAAEzO,KAAK,CAACyO,aAAa;MAClChK,QAAQ,EAAEkJ,6BAA6B,CACrCvO,QAAQ,EACRK,MAAM,EACNO,KAAK,CAACC,EAAE,EACRyN,gBACF,CAAC;MACD,GAAGE;IACL,CAAC;EACH,CAAC,CAAC;AACJ;;AAEA;AACA,IAAIc,aAAa,GAAG;EAClB,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,QAAQ,EAAE,SAAS;EACnB,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIC,YAAY,GAAG,oBAAoB;AACvC,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAOA,IAAI,CAAC5Q,OAAO,CAAC0Q,YAAY,EAAG9O,KAAK,IAAK6O,aAAa,CAAC7O,KAAK,CAAC,CAAC;AACpE;;AAEA;AACA,SAASiP,yBAAyBA,CAACC,aAAa,EAAE;EAChD,OAAOH,UAAU,CAACtG,IAAI,CAACC,SAAS,CAACwG,aAAa,CAAC,CAAC;AAClD;;AAEA;AACA,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,SAASC,kBAAkBA,CAACjQ,OAAO,EAAEkQ,KAAK,EAAE;EAC1C,OAAOC,sBAAsB,CAACnQ,OAAO,EAAGoQ,CAAC,IAAK;IAC5C,IAAIpP,KAAK,GAAGkP,KAAK,CAAC1P,MAAM,CAAC4P,CAAC,CAACpP,KAAK,CAACC,EAAE,CAAC;IACpCuM,UAAU,CAACxM,KAAK,EAAE,kBAAkBoP,CAAC,CAACpP,KAAK,CAACC,EAAE,sBAAsB,CAAC;IACrE,OAAOD,KAAK,CAACwC,MAAM,CAAC2J,OAAO;EAC7B,CAAC,CAAC;AACJ;AACA,SAASgD,sBAAsBA,CAACnQ,OAAO,EAAEqQ,iBAAiB,EAAEC,eAAe,EAAE;EAC3E,IAAIC,WAAW,GAAGvQ,OAAO,CAACkL,MAAM,GAAGlL,OAAO,CAACc,OAAO,CAAC0P,SAAS,CAAEJ,CAAC,IAAKpQ,OAAO,CAACkL,MAAM,CAACkF,CAAC,CAACpP,KAAK,CAACC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;EACpG,IAAIH,OAAO,GAAGyP,WAAW,IAAI,CAAC,GAAGvQ,OAAO,CAACc,OAAO,CAACgG,KAAK,CAAC,CAAC,EAAEyJ,WAAW,GAAG,CAAC,CAAC,GAAGvQ,OAAO,CAACc,OAAO;EAC5F,IAAI2P,YAAY;EAChB,IAAIF,WAAW,IAAI,CAAC,EAAE;IACpB,IAAI;MAAEG,aAAa;MAAEC,UAAU;MAAEC,aAAa;MAAEhQ;IAAW,CAAC,GAAGZ,OAAO;IACtEA,OAAO,CAACc,OAAO,CAACgG,KAAK,CAACyJ,WAAW,CAAC,CAACM,IAAI,CAAEhQ,KAAK,IAAK;MACjD,IAAII,EAAE,GAAGJ,KAAK,CAACG,KAAK,CAACC,EAAE;MACvB,IAAIyP,aAAa,CAACzP,EAAE,CAAC,KAAK,CAAC0P,UAAU,IAAI,CAACA,UAAU,CAACG,cAAc,CAAC7P,EAAE,CAAC,CAAC,EAAE;QACxEwP,YAAY,GAAGC,aAAa,CAACzP,EAAE,CAAC;MAClC,CAAC,MAAM,IAAI2P,aAAa,CAAC3P,EAAE,CAAC,IAAI,CAACL,UAAU,CAACkQ,cAAc,CAAC7P,EAAE,CAAC,EAAE;QAC9DwP,YAAY,GAAGG,aAAa,CAAC3P,EAAE,CAAC;MAClC;MACA,OAAOwP,YAAY,IAAI,IAAI;IAC7B,CAAC,CAAC;EACJ;EACA,MAAMM,cAAc,GAAG,IAAIC,OAAO,CAACV,eAAe,CAAC;EACnD,OAAOxP,OAAO,CAAC0J,MAAM,CAAC,CAACyG,aAAa,EAAEpQ,KAAK,EAAEqQ,GAAG,KAAK;IACnD,IAAI;MAAEjQ;IAAG,CAAC,GAAGJ,KAAK,CAACG,KAAK;IACxB,IAAI4P,aAAa,GAAG5Q,OAAO,CAAC4Q,aAAa,CAAC3P,EAAE,CAAC,IAAI,IAAI+P,OAAO,CAAC,CAAC;IAC9D,IAAIN,aAAa,GAAG1Q,OAAO,CAAC0Q,aAAa,CAACzP,EAAE,CAAC,IAAI,IAAI+P,OAAO,CAAC,CAAC;IAC9D,IAAIG,mBAAmB,GAAGV,YAAY,IAAI,IAAI,IAAIS,GAAG,KAAKpQ,OAAO,CAAC2G,MAAM,GAAG,CAAC;IAC5E,IAAI2J,mBAAmB,GAAGD,mBAAmB,IAAIV,YAAY,KAAKG,aAAa,IAAIH,YAAY,KAAKC,aAAa;IACjH,IAAIW,SAAS,GAAGhB,iBAAiB,CAACxP,KAAK,CAAC;IACxC,IAAIwQ,SAAS,IAAI,IAAI,EAAE;MACrB,IAAIC,QAAQ,GAAG,IAAIN,OAAO,CAACC,aAAa,CAAC;MACzC,IAAIG,mBAAmB,EAAE;QACvBG,cAAc,CAACd,YAAY,EAAEa,QAAQ,CAAC;MACxC;MACAC,cAAc,CAACb,aAAa,EAAEY,QAAQ,CAAC;MACvCC,cAAc,CAACX,aAAa,EAAEU,QAAQ,CAAC;MACvC,OAAOA,QAAQ;IACjB;IACA,IAAInE,OAAO,GAAG,IAAI6D,OAAO,CACvB,OAAOK,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC;MAC1CT,aAAa;MACbK,aAAa;MACbP,aAAa;MACbD,YAAY,EAAEU,mBAAmB,GAAGV,YAAY,GAAG,KAAK;IAC1D,CAAC,CAAC,GAAGY,SACP,CAAC;IACD,IAAID,mBAAmB,EAAE;MACvBG,cAAc,CAACd,YAAY,EAAEtD,OAAO,CAAC;IACvC;IACAoE,cAAc,CAACb,aAAa,EAAEvD,OAAO,CAAC;IACtCoE,cAAc,CAACX,aAAa,EAAEzD,OAAO,CAAC;IACtCoE,cAAc,CAACN,aAAa,EAAE9D,OAAO,CAAC;IACtC,OAAOA,OAAO;EAChB,CAAC,EAAE,IAAI6D,OAAO,CAACD,cAAc,CAAC,CAAC;AACjC;AACA,SAASQ,cAAcA,CAACN,aAAa,EAAEO,YAAY,EAAE;EACnD,IAAIC,qBAAqB,GAAGR,aAAa,CAAC7C,GAAG,CAAC,YAAY,CAAC;EAC3D,IAAIqD,qBAAqB,EAAE;IACzB,IAAIhJ,OAAO,GAAGuH,kBAAkB,CAACyB,qBAAqB,CAAC;IACvD,IAAIC,YAAY,GAAG,IAAIC,GAAG,CAACH,YAAY,CAACI,YAAY,CAAC,CAAC,CAAC;IACvDnJ,OAAO,CAAC+F,OAAO,CAAE5H,MAAM,IAAK;MAC1B,IAAI,CAAC8K,YAAY,CAACG,GAAG,CAACjL,MAAM,CAAC,EAAE;QAC7B4K,YAAY,CAACxE,MAAM,CAAC,YAAY,EAAEpG,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,IAAIkL,2BAA2B,GAAG,eAAgB,IAAIH,GAAG,CAAC,CACxD,GAAG3U,oBAAoB,EACvB,GAAG,CACJ,CAAC;AACF,eAAe+U,iBAAiBA,CAAC7B,KAAK,EAAEpF,UAAU,EAAEkH,aAAa,EAAE9F,OAAO,EAAE+F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EAChH,IAAI;IACF,IAAIC,cAAc,GAAG,IAAI9E,OAAO,CAAC2E,UAAU,EAAE;MAC3ChF,MAAM,EAAEf,OAAO,CAACe,MAAM;MACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;MAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;MACxBC,MAAM,EAAElB,OAAO,CAACkB,MAAM;MACtB,IAAGlB,OAAO,CAACgB,IAAI,GAAG;QAAEG,MAAM,EAAE;MAAO,CAAC,GAAG,KAAK,CAAC;IAC/C,CAAC,CAAC;IACF,IAAIzD,MAAM,GAAG,MAAMoI,aAAa,CAACK,KAAK,CAACD,cAAc,EAAE;MACrDE,cAAc,EAAEJ,WAAW;MAC3BK,uBAAuB,EAAE,IAAI;MAC7BC,gBAAgB,EAAE,IAAI;MACtBC,mCAAmC,EAAEvC,KAAK,CAACzP,MAAM,CAAC4C,mBAAmB,GAAG,MAAOgP,KAAK,IAAK;QACvF,IAAI;UACF,IAAIK,WAAW,GAAG,MAAML,KAAK,CAACD,cAAc,CAAC;UAC7C,OAAOO,iBAAiB,CAACD,WAAW,CAAC;QACvC,CAAC,CAAC,OAAOvL,KAAK,EAAE;UACd,OAAOyL,gBAAgB,CAACzL,KAAK,CAAC;QAChC;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,OAAOwL,iBAAiB,CAAC/I,MAAM,CAAC;EAClC,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd,OAAOyL,gBAAgB,CAACzL,KAAK,CAAC;EAChC;EACA,SAASwL,iBAAiBA,CAAC/I,MAAM,EAAE;IACjC,OAAOjL,UAAU,CAACiL,MAAM,CAAC,GAAGA,MAAM,GAAGiJ,uBAAuB,CAACjJ,MAAM,CAAC;EACtE;EACA,SAASgJ,gBAAgBA,CAACzL,KAAK,EAAE;IAC/BgL,WAAW,CAAChL,KAAK,CAAC;IAClB,OAAO2L,2BAA2B,CAAC5G,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;MAC7DlB,MAAM,EAAE;QAAEzC;MAAM,CAAC;MACjBgG,OAAO,EAAE,IAAI6D,OAAO,CAAC,CAAC;MACtB1E,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EACA,SAASuG,uBAAuBA,CAAC7S,OAAO,EAAE;IACxC,IAAImN,OAAO,GAAG8C,kBAAkB,CAACjQ,OAAO,EAAEkQ,KAAK,CAAC;IAChD,IAAIxR,oBAAoB,CAACsB,OAAO,CAAC+S,UAAU,CAAC,IAAI5F,OAAO,CAAC0E,GAAG,CAAC,UAAU,CAAC,EAAE;MACvE,OAAO,IAAItF,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAEtM,OAAO,CAAC+S,UAAU;QAAE5F;MAAQ,CAAC,CAAC;IACpE;IACA,IAAInN,OAAO,CAACkL,MAAM,EAAE;MAClBZ,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAACkL,MAAM,CAAC,CAACsD,OAAO,CAAEwE,GAAG,IAAK;QAC7C,IAAI,CAACpU,oBAAoB,CAACoU,GAAG,CAAC,IAAIA,GAAG,CAAC7L,KAAK,EAAE;UAC3CgL,WAAW,CAACa,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACFhT,OAAO,CAACkL,MAAM,GAAGD,cAAc,CAACjL,OAAO,CAACkL,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAImI,iBAAiB;IACrB,IAAIjT,OAAO,CAACkL,MAAM,EAAE;MAClB+H,iBAAiB,GAAG;QAAE9L,KAAK,EAAEmD,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAACkL,MAAM,CAAC,CAAC,CAAC;MAAE,CAAC;IACjE,CAAC,MAAM;MACL+H,iBAAiB,GAAG;QAClBzD,IAAI,EAAElF,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAAC2Q,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC;IACH;IACA,OAAOmC,2BAA2B,CAAC5G,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;MAC7DlB,MAAM,EAAEqJ,iBAAiB;MACzB9F,OAAO;MACPb,MAAM,EAAEtM,OAAO,CAAC+S;IAClB,CAAC,CAAC;EACJ;AACF;AACA,eAAeG,kBAAkBA,CAAChD,KAAK,EAAEpF,UAAU,EAAEkH,aAAa,EAAE9F,OAAO,EAAE+F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACjH,IAAIgB,WAAW,GAAG,IAAIhT,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC,CAACwM,YAAY,CAAC2B,GAAG,CAAC,SAAS,CAAC;EAClE,IAAIgF,YAAY,GAAGD,WAAW,GAAG,IAAIxB,GAAG,CAACwB,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;EACvE,IAAI;IACF,IAAIjB,cAAc,GAAG,IAAI9E,OAAO,CAAC2E,UAAU,EAAE;MAC3C9E,OAAO,EAAEjB,OAAO,CAACiB,OAAO;MACxBC,MAAM,EAAElB,OAAO,CAACkB;IAClB,CAAC,CAAC;IACF,IAAIxD,MAAM,GAAG,MAAMoI,aAAa,CAACK,KAAK,CAACD,cAAc,EAAE;MACrDE,cAAc,EAAEJ,WAAW;MAC3BoB,mBAAmB,EAAGlD,CAAC,IAAK,CAACgD,YAAY,IAAIA,YAAY,CAACvB,GAAG,CAACzB,CAAC,CAACpP,KAAK,CAACC,EAAE,CAAC;MACzEsR,uBAAuB,EAAE,IAAI;MAC7BE,mCAAmC,EAAEvC,KAAK,CAACzP,MAAM,CAAC4C,mBAAmB,GAAG,MAAOgP,KAAK,IAAK;QACvF,IAAI;UACF,IAAIK,WAAW,GAAG,MAAML,KAAK,CAACD,cAAc,CAAC;UAC7C,OAAOO,iBAAiB,CAACD,WAAW,CAAC;QACvC,CAAC,CAAC,OAAOvL,KAAK,EAAE;UACd,OAAOyL,gBAAgB,CAACzL,KAAK,CAAC;QAChC;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,OAAOwL,iBAAiB,CAAC/I,MAAM,CAAC;EAClC,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd,OAAOyL,gBAAgB,CAACzL,KAAK,CAAC;EAChC;EACA,SAASwL,iBAAiBA,CAAC/I,MAAM,EAAE;IACjC,OAAOjL,UAAU,CAACiL,MAAM,CAAC,GAAGA,MAAM,GAAGiJ,uBAAuB,CAACjJ,MAAM,CAAC;EACtE;EACA,SAASgJ,gBAAgBA,CAACzL,KAAK,EAAE;IAC/BgL,WAAW,CAAChL,KAAK,CAAC;IAClB,OAAO2L,2BAA2B,CAAC5G,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;MAC7DlB,MAAM,EAAE;QAAEzC;MAAM,CAAC;MACjBgG,OAAO,EAAE,IAAI6D,OAAO,CAAC,CAAC;MACtB1E,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EACA,SAASuG,uBAAuBA,CAAC7S,OAAO,EAAE;IACxC,IAAImN,OAAO,GAAG8C,kBAAkB,CAACjQ,OAAO,EAAEkQ,KAAK,CAAC;IAChD,IAAIxR,oBAAoB,CAACsB,OAAO,CAAC+S,UAAU,CAAC,IAAI5F,OAAO,CAAC0E,GAAG,CAAC,UAAU,CAAC,EAAE;MACvE,OAAO,IAAItF,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAEtM,OAAO,CAAC+S,UAAU;QAAE5F;MAAQ,CAAC,CAAC;IACpE;IACA,IAAInN,OAAO,CAACkL,MAAM,EAAE;MAClBZ,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAACkL,MAAM,CAAC,CAACsD,OAAO,CAAEwE,GAAG,IAAK;QAC7C,IAAI,CAACpU,oBAAoB,CAACoU,GAAG,CAAC,IAAIA,GAAG,CAAC7L,KAAK,EAAE;UAC3CgL,WAAW,CAACa,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACFhT,OAAO,CAACkL,MAAM,GAAGD,cAAc,CAACjL,OAAO,CAACkL,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAIyI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIC,aAAa,GAAG,IAAI7B,GAAG,CACzB3R,OAAO,CAACc,OAAO,CAAC2S,MAAM,CACnBrD,CAAC,IAAKgD,YAAY,GAAGA,YAAY,CAACvB,GAAG,CAACzB,CAAC,CAACpP,KAAK,CAACC,EAAE,CAAC,GAAGmP,CAAC,CAACpP,KAAK,CAACyD,MAAM,IAAI,IACzE,CAAC,CAACT,GAAG,CAAEoM,CAAC,IAAKA,CAAC,CAACpP,KAAK,CAACC,EAAE,CACzB,CAAC;IACD,IAAIjB,OAAO,CAACkL,MAAM,EAAE;MAClB,KAAK,IAAI,CAACjK,EAAE,EAAEkG,KAAK,CAAC,IAAImD,MAAM,CAACa,OAAO,CAACnL,OAAO,CAACkL,MAAM,CAAC,EAAE;QACtDqI,OAAO,CAACtS,EAAE,CAAC,GAAG;UAAEkG;QAAM,CAAC;MACzB;IACF;IACA,KAAK,IAAI,CAAClG,EAAE,EAAE+E,KAAK,CAAC,IAAIsE,MAAM,CAACa,OAAO,CAACnL,OAAO,CAACY,UAAU,CAAC,EAAE;MAC1D,IAAI,EAAEK,EAAE,IAAIsS,OAAO,CAAC,IAAIC,aAAa,CAAC3B,GAAG,CAAC5Q,EAAE,CAAC,EAAE;QAC7CsS,OAAO,CAACtS,EAAE,CAAC,GAAG;UAAEuO,IAAI,EAAExJ;QAAM,CAAC;MAC/B;IACF;IACA,OAAO8M,2BAA2B,CAAC5G,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;MAC7DlB,MAAM,EAAE2J,OAAO;MACfpG,OAAO;MACPb,MAAM,EAAEtM,OAAO,CAAC+S;IAClB,CAAC,CAAC;EACJ;AACF;AACA,SAASD,2BAA2BA,CAAC5G,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;EAC/DlB,MAAM;EACNuD,OAAO;EACPb;AACF,CAAC,EAAE;EACD,IAAIoH,aAAa,GAAG,IAAI1C,OAAO,CAAC7D,OAAO,CAAC;EACxCuG,aAAa,CAACC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC;EAC5C,IAAI7B,2BAA2B,CAACD,GAAG,CAACvF,MAAM,CAAC,EAAE;IAC3C,OAAO,IAAIC,QAAQ,CAAC,IAAI,EAAE;MAAED,MAAM;MAAEa,OAAO,EAAEuG;IAAc,CAAC,CAAC;EAC/D;EACAA,aAAa,CAACC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;EAClDD,aAAa,CAAC/G,MAAM,CAAC,gBAAgB,CAAC;EACtC,OAAO,IAAIJ,QAAQ,CACjBqH,oBAAoB,CAClBhK,MAAM,EACNsC,OAAO,CAACkB,MAAM,EACd8C,KAAK,CAAC5M,KAAK,CAACE,MAAM,CAACqQ,aAAa,EAChC/I,UACF,CAAC,EACD;IACEwB,MAAM,EAAEA,MAAM,IAAI,GAAG;IACrBa,OAAO,EAAEuG;EACX,CACF,CAAC;AACH;AACA,SAASI,mCAAmCA,CAACC,gBAAgB,EAAE7H,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;EACzF,IAAIkJ,SAAS,GAAGC,sBAAsB,CACpCF,gBAAgB,CAACzH,MAAM,EACvByH,gBAAgB,CAAC5G,OAAO,EACxB+C,KAAK,CAACpE,QACR,CAAC;EACD,IAAIqB,OAAO,GAAG,IAAI6D,OAAO,CAAC+C,gBAAgB,CAAC5G,OAAO,CAAC;EACnDA,OAAO,CAACR,MAAM,CAAC,UAAU,CAAC;EAC1BQ,OAAO,CAACwG,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;EAC5C,OAAOb,2BAA2B,CAAC5G,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;IAC7DlB,MAAM,EAAEsC,OAAO,CAACe,MAAM,KAAK,KAAK,GAAG;MAAE,CAAC3P,yBAAyB,GAAG0W;IAAU,CAAC,GAAGA,SAAS;IACzF7G,OAAO;IACPb,MAAM,EAAEjP;EACV,CAAC,CAAC;AACJ;AACA,SAAS4W,sBAAsBA,CAAC3H,MAAM,EAAEa,OAAO,EAAErB,QAAQ,EAAE;EACzD,IAAIkI,SAAS,GAAG7G,OAAO,CAACiB,GAAG,CAAC,UAAU,CAAC;EACvC,IAAItC,QAAQ,EAAE;IACZkI,SAAS,GAAG3U,aAAa,CAAC2U,SAAS,EAAElI,QAAQ,CAAC,IAAIkI,SAAS;EAC7D;EACA,OAAO;IACLjV,QAAQ,EAAEiV,SAAS;IACnB1H,MAAM;IACN4H,UAAU;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA/G,OAAO,CAAC0E,GAAG,CAAC,oBAAoB,CAAC,IAAI1E,OAAO,CAAC0E,GAAG,CAAC,YAAY,CAC9D;IACDtC,MAAM,EAAEpC,OAAO,CAAC0E,GAAG,CAAC,yBAAyB,CAAC;IAC9C5S,OAAO,EAAEkO,OAAO,CAAC0E,GAAG,CAAC,iBAAiB;EACxC,CAAC;AACH;AACA,SAAS+B,oBAAoBA,CAAC5N,KAAK,EAAEmO,aAAa,EAAEN,aAAa,EAAE/I,UAAU,EAAE;EAC7E,IAAIqE,UAAU,GAAG,IAAIiF,eAAe,CAAC,CAAC;EACtC,IAAIC,SAAS,GAAGC,UAAU,CACxB,MAAMnF,UAAU,CAACoF,KAAK,CAAC,IAAItQ,KAAK,CAAC,gBAAgB,CAAC,CAAC,EACnD,OAAO4P,aAAa,KAAK,QAAQ,GAAGA,aAAa,GAAG,IACtD,CAAC;EACDM,aAAa,CAACK,gBAAgB,CAAC,OAAO,EAAE,MAAMC,YAAY,CAACJ,SAAS,CAAC,CAAC;EACtE,OAAOnW,MAAM,CAAC8H,KAAK,EAAE;IACnBoH,MAAM,EAAE+B,UAAU,CAAC/B,MAAM;IACzBsH,OAAO,EAAE,CACNhT,KAAK,IAAK;MACT,IAAIA,KAAK,YAAYuC,KAAK,EAAE;QAC1B,IAAI;UAAEqD,IAAI;UAAEgE,OAAO;UAAEN;QAAM,CAAC,GAAGF,UAAU,KAAK,YAAY,CAAC,mBAAmBD,aAAa,CAACnJ,KAAK,EAAEoJ,UAAU,CAAC,GAAGpJ,KAAK;QACtH,OAAO,CAAC,gBAAgB,EAAE4F,IAAI,EAAEgE,OAAO,EAAEN,KAAK,CAAC;MACjD;MACA,IAAItJ,KAAK,YAAY5E,iBAAiB,EAAE;QACtC,IAAI;UAAE0S,IAAI,EAAEmF,KAAK;UAAErI,MAAM;UAAEsI;QAAW,CAAC,GAAGlT,KAAK;QAC/C,OAAO,CAAC,eAAe,EAAEiT,KAAK,EAAErI,MAAM,EAAEsI,UAAU,CAAC;MACrD;MACA,IAAIlT,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIpE,yBAAyB,IAAIoE,KAAK,EAAE;QAC5E,OAAO,CAAC,qBAAqB,EAAEA,KAAK,CAACpE,yBAAyB,CAAC,CAAC;MAClE;IACF,CAAC,CACF;IACDuX,WAAW,EAAE,CACVnT,KAAK,IAAK;MACT,IAAI,CAACA,KAAK,EAAE;MACZ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC/B,OAAO,CACL,0BAA0B,EAC1B4I,MAAM,CAACwK,WAAW,CAACxK,MAAM,CAACa,OAAO,CAACzJ,KAAK,CAAC,CAAC,CAC1C;IACH,CAAC,EACD,MAAM,CAAC,qBAAqB,CAAC;EAEjC,CAAC,CAAC;AACJ;;AAEA;AACA,SAASqT,MAAMA,CAAC7E,KAAK,EAAExM,IAAI,EAAE;EAC3B,IAAIlD,MAAM,GAAGiO,YAAY,CAACyB,KAAK,CAAC1P,MAAM,CAAC;EACvC,IAAIwU,UAAU,GAAGrG,6BAA6B,CAACuB,KAAK,CAAC1P,MAAM,EAAE0P,KAAK,CAACzP,MAAM,CAAC;EAC1E,IAAIqK,UAAU,GAAGF,YAAY,CAAClH,IAAI,CAAC,GAAGA,IAAI,GAAG,YAAY,CAAC;EAC1D,IAAIsO,aAAa,GAAGjU,mBAAmB,CAACiX,UAAU,EAAE;IAClDlJ,QAAQ,EAAEoE,KAAK,CAACpE;EAClB,CAAC,CAAC;EACF,IAAImJ,YAAY,GAAG/E,KAAK,CAAC5M,KAAK,CAACE,MAAM,CAAC2O,WAAW,KAAK,CAAChL,KAAK,EAAE;IAAE+E;EAAQ,CAAC,KAAK;IAC5E,IAAIpB,UAAU,KAAK,MAAM,CAAC,cAAc,CAACoB,OAAO,CAACkB,MAAM,CAAC8H,OAAO,EAAE;MAC/DzH,OAAO,CAACtG,KAAK;MACX;MACAvI,oBAAoB,CAACuI,KAAK,CAAC,IAAIA,KAAK,CAACA,KAAK,GAAGA,KAAK,CAACA,KAAK,GAAGA,KAC7D,CAAC;IACH;EACF,CAAC,CAAC;EACF,OAAO;IACL3G,MAAM;IACNwU,UAAU;IACVlK,UAAU;IACVkH,aAAa;IACbiD;EACF,CAAC;AACH;AACA,IAAIE,oBAAoB,GAAGA,CAACjF,KAAK,EAAExM,IAAI,KAAK;EAC1C,IAAI0R,MAAM;EACV,IAAI5U,MAAM;EACV,IAAIsK,UAAU;EACd,IAAIkH,aAAa;EACjB,IAAIiD,YAAY;EAChB,OAAO,eAAeI,cAAcA,CAACnJ,OAAO,EAAEoJ,cAAc,EAAE;IAC5DF,MAAM,GAAG,OAAOlF,KAAK,KAAK,UAAU,GAAG,MAAMA,KAAK,CAAC,CAAC,GAAGA,KAAK;IAC5D,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAC/B,IAAIqF,OAAO,GAAGR,MAAM,CAACK,MAAM,EAAE1R,IAAI,CAAC;MAClClD,MAAM,GAAG+U,OAAO,CAAC/U,MAAM;MACvBsK,UAAU,GAAGyK,OAAO,CAACzK,UAAU;MAC/BkH,aAAa,GAAGuD,OAAO,CAACvD,aAAa;MACrCiD,YAAY,GAAGM,OAAO,CAACN,YAAY;IACrC,CAAC,MAAM,IAAI,CAACzU,MAAM,IAAI,CAACsK,UAAU,IAAI,CAACkH,aAAa,IAAI,CAACiD,YAAY,EAAE;MACpE,IAAIM,OAAO,GAAGR,MAAM,CAACK,MAAM,EAAE1R,IAAI,CAAC;MAClClD,MAAM,GAAG+U,OAAO,CAAC/U,MAAM;MACvBsK,UAAU,GAAGyK,OAAO,CAACzK,UAAU;MAC/BkH,aAAa,GAAGuD,OAAO,CAACvD,aAAa;MACrCiD,YAAY,GAAGM,OAAO,CAACN,YAAY;IACrC;IACA,IAAIlJ,MAAM,GAAG,CAAC,CAAC;IACf,IAAImG,WAAW;IACf,IAAIC,WAAW,GAAIhL,KAAK,IAAK;MAC3B,IAAIzD,IAAI,KAAK,aAAa,CAAC,mBAAmB;QAC5CoK,iBAAiB,CAAC,CAAC,EAAE0H,mBAAmB,GAAGrO,KAAK,CAAC;MACnD;MACA8N,YAAY,CAAC9N,KAAK,EAAE;QAClBnH,OAAO,EAAEkS,WAAW;QACpBnG,MAAM;QACNG;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAIkJ,MAAM,CAAC3U,MAAM,CAAC4C,mBAAmB,EAAE;MACrC,IAAIiS,cAAc,IAAI,EAAEA,cAAc,YAAY/V,8BAA8B,CAAC,EAAE;QACjF,IAAI4H,KAAK,GAAG,IAAIlD,KAAK,CACnB,sLACF,CAAC;QACDkO,WAAW,CAAChL,KAAK,CAAC;QAClB,OAAOsO,6BAA6B,CAACtO,KAAK,EAAE2D,UAAU,CAAC;MACzD;MACAoH,WAAW,GAAGoD,cAAc,IAAI,IAAI/V,8BAA8B,CAAC,CAAC;IACtE,CAAC,MAAM;MACL2S,WAAW,GAAGoD,cAAc,IAAI,CAAC,CAAC;IACpC;IACA,IAAIrV,GAAG,GAAG,IAAIE,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;IAC9B,IAAIyV,kBAAkB,GAAGN,MAAM,CAACtJ,QAAQ,IAAI,GAAG;IAC/C,IAAI6J,cAAc,GAAG1V,GAAG,CAAC4L,QAAQ;IACjC,IAAIxM,aAAa,CAACsW,cAAc,EAAED,kBAAkB,CAAC,KAAK,aAAa,EAAE;MACvEC,cAAc,GAAGD,kBAAkB;IACrC,CAAC,MAAM,IAAIC,cAAc,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC3CD,cAAc,GAAGA,cAAc,CAAC1W,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IACxD;IACA,IAAII,aAAa,CAACsW,cAAc,EAAED,kBAAkB,CAAC,KAAK,GAAG,IAAIC,cAAc,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7FD,cAAc,GAAGA,cAAc,CAAC7O,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C;IACA,IAAIpG,SAAS,GAAGqN,kBAAkB,CAAC7B,OAAO,EAAE,yBAAyB,CAAC,KAAK,KAAK;IAChF,IAAI,CAACkJ,MAAM,CAACzT,GAAG,EAAE;MACf,IAAIkU,WAAW,GAAG/G,SAAS,CAAC6G,cAAc,CAAC;MAC3C,IAAID,kBAAkB,KAAK,GAAG,EAAE;QAC9B,IAAII,YAAY,GAAGzW,aAAa,CAACwW,WAAW,EAAEH,kBAAkB,CAAC;QACjE,IAAII,YAAY,IAAI,IAAI,EAAE;UACxBb,YAAY,CACV,IAAInY,iBAAiB,CACnB,GAAG,EACH,WAAW,EACX,+BAA+B+Y,WAAW,yDAAyDH,kBAAkB,IACvH,CAAC,EACD;YACE1V,OAAO,EAAEkS,WAAW;YACpBnG,MAAM;YACNG;UACF,CACF,CAAC;UACD,OAAO,IAAIK,QAAQ,CAAC,WAAW,EAAE;YAC/BD,MAAM,EAAE,GAAG;YACXsI,UAAU,EAAE;UACd,CAAC,CAAC;QACJ;QACAiB,WAAW,GAAGC,YAAY;MAC5B;MACA,IAAIV,MAAM,CAACW,SAAS,CAACtO,MAAM,KAAK,CAAC,EAAE;QACjC/G,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM,IAAI,CAAC0U,MAAM,CAACW,SAAS,CAACC,QAAQ,CAACH,WAAW,CAAC,IAAI,CAACT,MAAM,CAACW,SAAS,CAACC,QAAQ,CAACH,WAAW,GAAG,GAAG,CAAC,EAAE;QACnG,IAAI5V,GAAG,CAAC4L,QAAQ,CAAC+J,QAAQ,CAAC,OAAO,CAAC,EAAE;UAClCX,YAAY,CACV,IAAInY,iBAAiB,CACnB,GAAG,EACH,WAAW,EACX,8BAA8B+Y,WAAW,oIAC3C,CAAC,EACD;YACE7V,OAAO,EAAEkS,WAAW;YACpBnG,MAAM;YACNG;UACF,CACF,CAAC;UACD,OAAO,IAAIK,QAAQ,CAAC,WAAW,EAAE;YAC/BD,MAAM,EAAE,GAAG;YACXsI,UAAU,EAAE;UACd,CAAC,CAAC;QACJ,CAAC,MAAM;UACLlU,SAAS,GAAG,IAAI;QAClB;MACF;IACF;IACA,IAAIuV,WAAW,GAAG9X,eAAe,CAC/BiX,MAAM,CAACxT,cAAc,CAAC+B,YAAY,EAClC+R,kBACF,CAAC;IACD,IAAIzV,GAAG,CAAC4L,QAAQ,KAAKoK,WAAW,EAAE;MAChC,IAAI;QACF,IAAIC,GAAG,GAAG,MAAMC,qBAAqB,CAACf,MAAM,EAAE5U,MAAM,EAAEP,GAAG,CAAC;QAC1D,OAAOiW,GAAG;MACZ,CAAC,CAAC,OAAO7H,CAAC,EAAE;QACV8D,WAAW,CAAC9D,CAAC,CAAC;QACd,OAAO,IAAI9B,QAAQ,CAAC,sBAAsB,EAAE;UAAED,MAAM,EAAE;QAAI,CAAC,CAAC;MAC9D;IACF;IACA,IAAIxL,OAAO,GAAG8K,iBAAiB,CAACpL,MAAM,EAAEmV,cAAc,EAAEP,MAAM,CAACtJ,QAAQ,CAAC;IACxE,IAAIhL,OAAO,IAAIA,OAAO,CAAC2G,MAAM,GAAG,CAAC,EAAE;MACjC6C,MAAM,CAACe,MAAM,CAACU,MAAM,EAAEjL,OAAO,CAAC,CAAC,CAAC,CAACiL,MAAM,CAAC;IAC1C;IACA,IAAIqK,QAAQ;IACZ,IAAInW,GAAG,CAAC4L,QAAQ,CAAC+J,QAAQ,CAAC,OAAO,CAAC,EAAE;MAClC,IAAI3D,UAAU,GAAG,IAAI9R,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;MACrCgS,UAAU,CAACpG,QAAQ,GAAG8J,cAAc;MACpC,IAAIU,kBAAkB,GAAGzK,iBAAiB,CACxCpL,MAAM,EACNyR,UAAU,CAACpG,QAAQ,EACnBuJ,MAAM,CAACtJ,QACT,CAAC;MACDsK,QAAQ,GAAG,MAAME,wBAAwB,CACvCxL,UAAU,EACVsK,MAAM,EACNpD,aAAa,EACb9F,OAAO,EACP+F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC;MACD,IAAI1T,kBAAkB,CAAC2X,QAAQ,CAAC,EAAE;QAChCA,QAAQ,GAAGtC,mCAAmC,CAC5CsC,QAAQ,EACRlK,OAAO,EACPkJ,MAAM,EACNtK,UACF,CAAC;MACH;MACA,IAAIsK,MAAM,CAAC9R,KAAK,CAACE,MAAM,CAAC+S,iBAAiB,EAAE;QACzCH,QAAQ,GAAG,MAAMhB,MAAM,CAAC9R,KAAK,CAACE,MAAM,CAAC+S,iBAAiB,CAACH,QAAQ,EAAE;UAC/DpW,OAAO,EAAEkS,WAAW;UACpBnG,MAAM,EAAEsK,kBAAkB,GAAGA,kBAAkB,CAAC,CAAC,CAAC,CAACtK,MAAM,GAAG,CAAC,CAAC;UAC9DG;QACF,CAAC,CAAC;QACF,IAAIzN,kBAAkB,CAAC2X,QAAQ,CAAC,EAAE;UAChCA,QAAQ,GAAGtC,mCAAmC,CAC5CsC,QAAQ,EACRlK,OAAO,EACPkJ,MAAM,EACNtK,UACF,CAAC;QACH;MACF;IACF,CAAC,MAAM,IAAI,CAACpK,SAAS,IAAII,OAAO,IAAIA,OAAO,CAACA,OAAO,CAAC2G,MAAM,GAAG,CAAC,CAAC,CAACzG,KAAK,CAACwC,MAAM,CAAC8B,OAAO,IAAI,IAAI,IAAIxE,OAAO,CAACA,OAAO,CAAC2G,MAAM,GAAG,CAAC,CAAC,CAACzG,KAAK,CAACwC,MAAM,CAACc,aAAa,IAAI,IAAI,EAAE;MAC9J8R,QAAQ,GAAG,MAAMI,qBAAqB,CACpC1L,UAAU,EACVsK,MAAM,EACNpD,aAAa,EACblR,OAAO,CAACgG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC9F,KAAK,CAACC,EAAE,EAC7BiL,OAAO,EACPgG,WAAW,EACXC,WACF,CAAC;IACH,CAAC,MAAM;MACL,IAAI;QAAEtG;MAAS,CAAC,GAAG5L,GAAG;MACtB,IAAIK,WAAW,GAAG,KAAK,CAAC;MACxB,IAAI8U,MAAM,CAACqB,uBAAuB,EAAE;QAClCnW,WAAW,GAAG,MAAM8U,MAAM,CAACqB,uBAAuB,CAAC;UAAE5K;QAAS,CAAC,CAAC;MAClE,CAAC,MAAM,IAAInI,IAAI,KAAK,aAAa,CAAC,qBAAqBoK,iBAAiB,CAAC,CAAC,EAAE4I,cAAc,EAAE;QAC1FpW,WAAW,GAAG,MAAMwN,iBAAiB,CAAC,CAAC,EAAE4I,cAAc,GAAG7K,QAAQ,CAAC;MACrE;MACAuK,QAAQ,GAAG,MAAMO,qBAAqB,CACpC7L,UAAU,EACVsK,MAAM,EACNpD,aAAa,EACb9F,OAAO,EACPgG,WAAW,EACXC,WAAW,EACXzR,SAAS,EACTJ,WACF,CAAC;IACH;IACA,IAAI4L,OAAO,CAACe,MAAM,KAAK,MAAM,EAAE;MAC7B,OAAO,IAAIV,QAAQ,CAAC,IAAI,EAAE;QACxBY,OAAO,EAAEiJ,QAAQ,CAACjJ,OAAO;QACzBb,MAAM,EAAE8J,QAAQ,CAAC9J,MAAM;QACvBsI,UAAU,EAAEwB,QAAQ,CAACxB;MACvB,CAAC,CAAC;IACJ;IACA,OAAOwB,QAAQ;EACjB,CAAC;AACH,CAAC;AACD,eAAeD,qBAAqBA,CAACjG,KAAK,EAAE1P,MAAM,EAAEP,GAAG,EAAE;EACvD,IAAIiQ,KAAK,CAAC0G,MAAM,CAACnT,OAAO,KAAKxD,GAAG,CAACwM,YAAY,CAAC2B,GAAG,CAAC,SAAS,CAAC,EAAE;IAC5D,OAAO,IAAI7B,QAAQ,CAAC,IAAI,EAAE;MACxBD,MAAM,EAAE,GAAG;MACXa,OAAO,EAAE;QACP,yBAAyB,EAAE;MAC7B;IACF,CAAC,CAAC;EACJ;EACA,IAAI0J,OAAO,GAAG,CAAC,CAAC;EAChB,IAAI5W,GAAG,CAACwM,YAAY,CAACoF,GAAG,CAAC,GAAG,CAAC,EAAE;IAC7B,IAAIiF,KAAK,GAAG,eAAgB,IAAInF,GAAG,CAAC,CAAC;IACrC1R,GAAG,CAACwM,YAAY,CAACC,MAAM,CAAC,GAAG,CAAC,CAAC8B,OAAO,CAAErK,IAAI,IAAK;MAC7C,IAAI,CAACA,IAAI,CAAC4S,UAAU,CAAC,GAAG,CAAC,EAAE;QACzB5S,IAAI,GAAG,IAAIA,IAAI,EAAE;MACnB;MACA,IAAI6S,QAAQ,GAAG7S,IAAI,CAACkP,KAAK,CAAC,GAAG,CAAC,CAACvM,KAAK,CAAC,CAAC,CAAC;MACvCkQ,QAAQ,CAACxI,OAAO,CAAC,CAACyI,CAAC,EAAEvP,CAAC,KAAK;QACzB,IAAIwP,WAAW,GAAGF,QAAQ,CAAClQ,KAAK,CAAC,CAAC,EAAEY,CAAC,GAAG,CAAC,CAAC,CAACyP,IAAI,CAAC,GAAG,CAAC;QACpDL,KAAK,CAACM,GAAG,CAAC,IAAIF,WAAW,EAAE,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,KAAK,IAAI/S,IAAI,IAAI2S,KAAK,EAAE;MACtB,IAAIhW,OAAO,GAAG8K,iBAAiB,CAACpL,MAAM,EAAE2D,IAAI,EAAE+L,KAAK,CAACpE,QAAQ,CAAC;MAC7D,IAAIhL,OAAO,EAAE;QACX,KAAK,IAAID,KAAK,IAAIC,OAAO,EAAE;UACzB,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;UAC5B,IAAID,KAAK,GAAGkP,KAAK,CAAC0G,MAAM,CAACpW,MAAM,CAACO,OAAO,CAAC;UACxC,IAAIC,KAAK,EAAE;YACT6V,OAAO,CAAC9V,OAAO,CAAC,GAAGC,KAAK;UAC1B;QACF;MACF;IACF;IACA,OAAOuL,QAAQ,CAAC8K,IAAI,CAACR,OAAO,EAAE;MAC5B1J,OAAO,EAAE;QACP,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;EACJ;EACA,OAAO,IAAIZ,QAAQ,CAAC,iBAAiB,EAAE;IAAED,MAAM,EAAE;EAAI,CAAC,CAAC;AACzD;AACA,eAAegK,wBAAwBA,CAACxL,UAAU,EAAEoF,KAAK,EAAE8B,aAAa,EAAE9F,OAAO,EAAE+F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACvH,IAAIiE,QAAQ,GAAGlK,OAAO,CAACe,MAAM,KAAK,KAAK,GAAG,MAAM8E,iBAAiB,CAC/D7B,KAAK,EACLpF,UAAU,EACVkH,aAAa,EACb9F,OAAO,EACP+F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC,GAAG,MAAMe,kBAAkB,CAC1BhD,KAAK,EACLpF,UAAU,EACVkH,aAAa,EACb9F,OAAO,EACP+F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC;EACD,OAAOiE,QAAQ;AACjB;AACA,eAAeO,qBAAqBA,CAAC7L,UAAU,EAAEoF,KAAK,EAAE8B,aAAa,EAAE9F,OAAO,EAAEgG,WAAW,EAAEC,WAAW,EAAEzR,SAAS,EAAEJ,WAAW,EAAE;EAChI,IAAI;IACF,IAAIsJ,MAAM,GAAG,MAAMoI,aAAa,CAACK,KAAK,CAACnG,OAAO,EAAE;MAC9CoG,cAAc,EAAEJ,WAAW;MAC3BO,mCAAmC,EAAEvC,KAAK,CAACzP,MAAM,CAAC4C,mBAAmB,GAAG,MAAOgP,KAAK,IAAK;QACvF,IAAI;UACF,IAAIK,WAAW,GAAG,MAAML,KAAK,CAACnG,OAAO,CAAC;UACtC,IAAI,CAACvN,UAAU,CAAC+T,WAAW,CAAC,EAAE;YAC5BA,WAAW,GAAG,MAAM4E,UAAU,CAAC5E,WAAW,EAAEhS,SAAS,CAAC;UACxD;UACA,OAAOgS,WAAW;QACpB,CAAC,CAAC,OAAOvL,KAAK,EAAE;UACdgL,WAAW,CAAChL,KAAK,CAAC;UAClB,OAAO,IAAIoF,QAAQ,CAAC,IAAI,EAAE;YAAED,MAAM,EAAE;UAAI,CAAC,CAAC;QAC5C;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,IAAI,CAAC3N,UAAU,CAACiL,MAAM,CAAC,EAAE;MACvBA,MAAM,GAAG,MAAM0N,UAAU,CAAC1N,MAAM,EAAElJ,SAAS,CAAC;IAC9C;IACA,OAAOkJ,MAAM;EACf,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACdgL,WAAW,CAAChL,KAAK,CAAC;IAClB,OAAO,IAAIoF,QAAQ,CAAC,IAAI,EAAE;MAAED,MAAM,EAAE;IAAI,CAAC,CAAC;EAC5C;EACA,eAAegL,UAAUA,CAACtX,OAAO,EAAEuX,UAAU,EAAE;IAC7C,IAAIpK,OAAO,GAAG8C,kBAAkB,CAACjQ,OAAO,EAAEkQ,KAAK,CAAC;IAChD,IAAI4B,2BAA2B,CAACD,GAAG,CAAC7R,OAAO,CAAC+S,UAAU,CAAC,EAAE;MACvD,OAAO,IAAIxG,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAEtM,OAAO,CAAC+S,UAAU;QAAE5F;MAAQ,CAAC,CAAC;IACpE;IACA,IAAInN,OAAO,CAACkL,MAAM,EAAE;MAClBZ,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAACkL,MAAM,CAAC,CAACsD,OAAO,CAAEwE,GAAG,IAAK;QAC7C,IAAI,CAACpU,oBAAoB,CAACoU,GAAG,CAAC,IAAIA,GAAG,CAAC7L,KAAK,EAAE;UAC3CgL,WAAW,CAACa,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACFhT,OAAO,CAACkL,MAAM,GAAGD,cAAc,CAACjL,OAAO,CAACkL,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAI9I,KAAK,GAAG;MACVpB,UAAU,EAAEZ,OAAO,CAACY,UAAU;MAC9B+P,UAAU,EAAE3Q,OAAO,CAAC2Q,UAAU;MAC9BzF,MAAM,EAAEK,eAAe,CAACvL,OAAO,CAACkL,MAAM,EAAEJ,UAAU;IACpD,CAAC;IACD,IAAI0M,iBAAiB,GAAG;MACtB1L,QAAQ,EAAEoE,KAAK,CAACpE,QAAQ;MACxBrL,MAAM,EAAEyP,KAAK,CAACzP,MAAM;MACpBmB,cAAc,EAAEsO,KAAK,CAACtO,cAAc;MACpCD,GAAG,EAAEuO,KAAK,CAACvO,GAAG;MACdjB,SAAS,EAAE6W;IACb,CAAC;IACD,IAAIE,YAAY,GAAG;MACjBrX,QAAQ,EAAE8P,KAAK,CAAC0G,MAAM;MACtBvW,YAAY,EAAEgK,uBAAuB,CAAC6F,KAAK,CAAC1P,MAAM,CAAC;MACnDG,oBAAoB,EAAEX,OAAO;MAC7BM,WAAW;MACXC,mBAAmB,EAAEuP,yBAAyB,CAAC;QAC7C,GAAG0H,iBAAiB;QACpBlX;MACF,CAAC,CAAC;MACF4B,mBAAmB,EAAE0R,oBAAoB,CACvC5R,KAAK,EACLkK,OAAO,CAACkB,MAAM,EACd8C,KAAK,CAAC5M,KAAK,CAACE,MAAM,CAACqQ,aAAa,EAChC/I,UACF,CAAC;MACDhJ,UAAU,EAAE,CAAC,CAAC;MACdrB,MAAM,EAAEyP,KAAK,CAACzP,MAAM;MACpBkB,GAAG,EAAEuO,KAAK,CAACvO,GAAG;MACdC,cAAc,EAAEsO,KAAK,CAACtO,cAAc;MACpClB,SAAS,EAAE6W,UAAU;MACrB1V,cAAc,EAAGmR,GAAG,IAAKnR,cAAc,CAACmR,GAAG,EAAElI,UAAU;IACzD,CAAC;IACD,IAAI4M,6BAA6B,GAAGxH,KAAK,CAAC5M,KAAK,CAACE,MAAM,CAAC8B,OAAO;IAC9D,IAAI;MACF,OAAO,MAAMoS,6BAA6B,CACxCxL,OAAO,EACPlM,OAAO,CAAC+S,UAAU,EAClB5F,OAAO,EACPsK,YAAY,EACZvF,WACF,CAAC;IACH,CAAC,CAAC,OAAO/K,KAAK,EAAE;MACdgL,WAAW,CAAChL,KAAK,CAAC;MAClB,IAAIwQ,oBAAoB,GAAGxQ,KAAK;MAChC,IAAIxI,UAAU,CAACwI,KAAK,CAAC,EAAE;QACrB,IAAI;UACF,IAAInB,KAAK,GAAG,MAAM4R,cAAc,CAACzQ,KAAK,CAAC;UACvCwQ,oBAAoB,GAAG,IAAI7a,iBAAiB,CAC1CqK,KAAK,CAACmF,MAAM,EACZnF,KAAK,CAACyN,UAAU,EAChB5O,KACF,CAAC;QACH,CAAC,CAAC,OAAOqI,CAAC,EAAE,CACZ;MACF;MACArO,OAAO,GAAG3B,yBAAyB,CACjC2T,aAAa,CAACgD,UAAU,EACxBhV,OAAO,EACP2X,oBACF,CAAC;MACD,IAAI3X,OAAO,CAACkL,MAAM,EAAE;QAClBlL,OAAO,CAACkL,MAAM,GAAGD,cAAc,CAACjL,OAAO,CAACkL,MAAM,EAAEJ,UAAU,CAAC;MAC7D;MACA,IAAI+M,MAAM,GAAG;QACXjX,UAAU,EAAEZ,OAAO,CAACY,UAAU;QAC9B+P,UAAU,EAAE3Q,OAAO,CAAC2Q,UAAU;QAC9BzF,MAAM,EAAEK,eAAe,CAACvL,OAAO,CAACkL,MAAM,EAAEJ,UAAU;MACpD,CAAC;MACD2M,YAAY,GAAG;QACb,GAAGA,YAAY;QACf9W,oBAAoB,EAAEX,OAAO;QAC7BO,mBAAmB,EAAEuP,yBAAyB,CAAC0H,iBAAiB,CAAC;QACjEtV,mBAAmB,EAAE0R,oBAAoB,CACvCiE,MAAM,EACN3L,OAAO,CAACkB,MAAM,EACd8C,KAAK,CAAC5M,KAAK,CAACE,MAAM,CAACqQ,aAAa,EAChC/I,UACF,CAAC;QACDhJ,UAAU,EAAE,CAAC;MACf,CAAC;MACD,IAAI;QACF,OAAO,MAAM4V,6BAA6B,CACxCxL,OAAO,EACPlM,OAAO,CAAC+S,UAAU,EAClB5F,OAAO,EACPsK,YAAY,EACZvF,WACF,CAAC;MACH,CAAC,CAAC,OAAO4F,MAAM,EAAE;QACf3F,WAAW,CAAC2F,MAAM,CAAC;QACnB,OAAOrC,6BAA6B,CAACqC,MAAM,EAAEhN,UAAU,CAAC;MAC1D;IACF;EACF;AACF;AACA,eAAe0L,qBAAqBA,CAAC1L,UAAU,EAAEoF,KAAK,EAAE8B,aAAa,EAAEjR,OAAO,EAAEmL,OAAO,EAAEgG,WAAW,EAAEC,WAAW,EAAE;EACjH,IAAI;IACF,IAAIvI,MAAM,GAAG,MAAMoI,aAAa,CAAC+F,UAAU,CAAC7L,OAAO,EAAE;MACnDnL,OAAO;MACPuR,cAAc,EAAEJ,WAAW;MAC3BO,mCAAmC,EAAEvC,KAAK,CAACzP,MAAM,CAAC4C,mBAAmB,GAAG,MAAO0U,UAAU,IAAK;QAC5F,IAAI;UACF,IAAIrF,WAAW,GAAG,MAAMqF,UAAU,CAAC7L,OAAO,CAAC;UAC3C,OAAO8L,sBAAsB,CAACtF,WAAW,CAAC;QAC5C,CAAC,CAAC,OAAOvL,KAAK,EAAE;UACd,OAAO8Q,qBAAqB,CAAC9Q,KAAK,CAAC;QACrC;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,OAAO6Q,sBAAsB,CAACpO,MAAM,CAAC;EACvC,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd,OAAO8Q,qBAAqB,CAAC9Q,KAAK,CAAC;EACrC;EACA,SAAS6Q,sBAAsBA,CAACpO,MAAM,EAAE;IACtC,IAAIjL,UAAU,CAACiL,MAAM,CAAC,EAAE;MACtB,OAAOA,MAAM;IACf;IACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAO,IAAI2C,QAAQ,CAAC3C,MAAM,CAAC;IAC7B;IACA,OAAO2C,QAAQ,CAAC8K,IAAI,CAACzN,MAAM,CAAC;EAC9B;EACA,SAASqO,qBAAqBA,CAAC9Q,KAAK,EAAE;IACpC,IAAIxI,UAAU,CAACwI,KAAK,CAAC,EAAE;MACrBA,KAAK,CAACgG,OAAO,CAACwG,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;MACzC,OAAOxM,KAAK;IACd;IACA,IAAIvI,oBAAoB,CAACuI,KAAK,CAAC,EAAE;MAC/BgL,WAAW,CAAChL,KAAK,CAAC;MAClB,OAAO+Q,mBAAmB,CAAC/Q,KAAK,EAAE2D,UAAU,CAAC;IAC/C;IACA,IAAI3D,KAAK,YAAYlD,KAAK,IAAIkD,KAAK,CAACmE,OAAO,KAAK,qCAAqC,EAAE;MACrF,IAAI6M,QAAQ,GAAG,IAAIlU,KAAK,CACtB,gEACF,CAAC;MACDkO,WAAW,CAACgG,QAAQ,CAAC;MACrB,OAAO1C,6BAA6B,CAAC0C,QAAQ,EAAErN,UAAU,CAAC;IAC5D;IACAqH,WAAW,CAAChL,KAAK,CAAC;IAClB,OAAOsO,6BAA6B,CAACtO,KAAK,EAAE2D,UAAU,CAAC;EACzD;AACF;AACA,SAASoN,mBAAmBA,CAACE,aAAa,EAAEtN,UAAU,EAAE;EACtD,OAAOyB,QAAQ,CAAC8K,IAAI,CAClBxV,cAAc;EACZ;EACAuW,aAAa,CAACjR,KAAK,IAAI,IAAIlD,KAAK,CAAC,yBAAyB,CAAC,EAC3D6G,UACF,CAAC,EACD;IACEwB,MAAM,EAAE8L,aAAa,CAAC9L,MAAM;IAC5BsI,UAAU,EAAEwD,aAAa,CAACxD,UAAU;IACpCzH,OAAO,EAAE;MACP,eAAe,EAAE;IACnB;EACF,CACF,CAAC;AACH;AACA,SAASsI,6BAA6BA,CAACtO,KAAK,EAAE2D,UAAU,EAAE;EACxD,IAAIQ,OAAO,GAAG,yBAAyB;EACvC,IAAIR,UAAU,KAAK,YAAY,CAAC,kBAAkB;IAChDQ,OAAO,IAAI;AACf;AACA,EAAE9E,MAAM,CAACW,KAAK,CAAC,EAAE;EACf;EACA,OAAO,IAAIoF,QAAQ,CAACjB,OAAO,EAAE;IAC3BgB,MAAM,EAAE,GAAG;IACXa,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ;AACA,SAASyK,cAAcA,CAACxB,QAAQ,EAAE;EAChC,IAAIiC,WAAW,GAAGjC,QAAQ,CAACjJ,OAAO,CAACiB,GAAG,CAAC,cAAc,CAAC;EACtD,OAAOiK,WAAW,IAAI,uBAAuB,CAACC,IAAI,CAACD,WAAW,CAAC,GAAGjC,QAAQ,CAAClJ,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGkJ,QAAQ,CAACiB,IAAI,CAAC,CAAC,GAAGjB,QAAQ,CAACmC,IAAI,CAAC,CAAC;AACpI;;AAEA;AACA,SAASC,KAAKA,CAAClR,IAAI,EAAE;EACnB,OAAO,WAAWA,IAAI,IAAI;AAC5B;AACA,IAAImR,aAAa,GAAGA,CAACC,WAAW,GAAG,CAAC,CAAC,EAAEzX,EAAE,GAAG,EAAE,KAAK;EACjD,IAAI+C,GAAG,GAAG,IAAI2U,GAAG,CAACrO,MAAM,CAACa,OAAO,CAACuN,WAAW,CAAC,CAAC;EAC9C,OAAO;IACL,IAAIzX,EAAEA,CAAA,EAAG;MACP,OAAOA,EAAE;IACX,CAAC;IACD,IAAIuO,IAAIA,CAAA,EAAG;MACT,OAAOlF,MAAM,CAACwK,WAAW,CAAC9Q,GAAG,CAAC;IAChC,CAAC;IACD6N,GAAGA,CAACvK,IAAI,EAAE;MACR,OAAOtD,GAAG,CAAC6N,GAAG,CAACvK,IAAI,CAAC,IAAItD,GAAG,CAAC6N,GAAG,CAAC2G,KAAK,CAAClR,IAAI,CAAC,CAAC;IAC9C,CAAC;IACD8G,GAAGA,CAAC9G,IAAI,EAAE;MACR,IAAItD,GAAG,CAAC6N,GAAG,CAACvK,IAAI,CAAC,EAAE,OAAOtD,GAAG,CAACoK,GAAG,CAAC9G,IAAI,CAAC;MACvC,IAAIsR,SAAS,GAAGJ,KAAK,CAAClR,IAAI,CAAC;MAC3B,IAAItD,GAAG,CAAC6N,GAAG,CAAC+G,SAAS,CAAC,EAAE;QACtB,IAAIlX,KAAK,GAAGsC,GAAG,CAACoK,GAAG,CAACwK,SAAS,CAAC;QAC9B5U,GAAG,CAAC2I,MAAM,CAACiM,SAAS,CAAC;QACrB,OAAOlX,KAAK;MACd;MACA,OAAO,KAAK,CAAC;IACf,CAAC;IACDiS,GAAGA,CAACrM,IAAI,EAAE5F,KAAK,EAAE;MACfsC,GAAG,CAAC2P,GAAG,CAACrM,IAAI,EAAE5F,KAAK,CAAC;IACtB,CAAC;IACD8W,KAAKA,CAAClR,IAAI,EAAE5F,KAAK,EAAE;MACjBsC,GAAG,CAAC2P,GAAG,CAAC6E,KAAK,CAAClR,IAAI,CAAC,EAAE5F,KAAK,CAAC;IAC7B,CAAC;IACDmX,KAAKA,CAACvR,IAAI,EAAE;MACVtD,GAAG,CAAC2I,MAAM,CAACrF,IAAI,CAAC;IAClB;EACF,CAAC;AACH,CAAC;AACD,IAAIwR,SAAS,GAAI/P,MAAM,IAAK;EAC1B,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAAC9H,EAAE,KAAK,QAAQ,IAAI,OAAO8H,MAAM,CAACyG,IAAI,KAAK,WAAW,IAAI,OAAOzG,MAAM,CAAC8I,GAAG,KAAK,UAAU,IAAI,OAAO9I,MAAM,CAACqF,GAAG,KAAK,UAAU,IAAI,OAAOrF,MAAM,CAAC4K,GAAG,KAAK,UAAU,IAAI,OAAO5K,MAAM,CAACyP,KAAK,KAAK,UAAU,IAAI,OAAOzP,MAAM,CAAC8P,KAAK,KAAK,UAAU;AACtR,CAAC;AACD,SAASE,oBAAoBA,CAAC;EAC5BnS,MAAM,EAAEoS,SAAS;EACjBC,UAAU;EACVC,QAAQ;EACRC,UAAU;EACVC;AACF,CAAC,EAAE;EACD,IAAIxS,MAAM,GAAGkC,QAAQ,CAACkQ,SAAS,CAAC,GAAGA,SAAS,GAAGpR,YAAY,CAACoR,SAAS,EAAE1R,IAAI,IAAI,WAAW,EAAE0R,SAAS,CAAC;EACtGK,iCAAiC,CAACzS,MAAM,CAAC;EACzC,OAAO;IACL,MAAM0S,UAAUA,CAAC/Q,YAAY,EAAER,OAAO,EAAE;MACtC,IAAI9G,EAAE,GAAGsH,YAAY,KAAI,MAAM3B,MAAM,CAAClB,KAAK,CAAC6C,YAAY,EAAER,OAAO,CAAC;MAClE,IAAI/B,KAAK,GAAG/E,EAAE,KAAI,MAAMiY,QAAQ,CAACjY,EAAE,CAAC;MACpC,OAAOwX,aAAa,CAACzS,KAAK,IAAI,CAAC,CAAC,EAAE/E,EAAE,IAAI,EAAE,CAAC;IAC7C,CAAC;IACD,MAAMsY,aAAaA,CAACC,OAAO,EAAEzR,OAAO,EAAE;MACpC,IAAI;QAAE9G,EAAE;QAAEuO,IAAI,EAAExJ;MAAM,CAAC,GAAGwT,OAAO;MACjC,IAAItR,OAAO,GAAGH,OAAO,EAAEK,MAAM,IAAI,IAAI,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,OAAO,CAACK,MAAM,GAAG,GAAG,CAAC,GAAGL,OAAO,EAAEG,OAAO,IAAI,IAAI,GAAGH,OAAO,CAACG,OAAO,GAAGtB,MAAM,CAACsB,OAAO;MACjJ,IAAIjH,EAAE,EAAE;QACN,MAAMkY,UAAU,CAAClY,EAAE,EAAE+E,KAAK,EAAEkC,OAAO,CAAC;MACtC,CAAC,MAAM;QACLjH,EAAE,GAAG,MAAMgY,UAAU,CAACjT,KAAK,EAAEkC,OAAO,CAAC;MACvC;MACA,OAAOtB,MAAM,CAACjB,SAAS,CAAC1E,EAAE,EAAE8G,OAAO,CAAC;IACtC,CAAC;IACD,MAAM0R,cAAcA,CAACD,OAAO,EAAEzR,OAAO,EAAE;MACrC,MAAMqR,UAAU,CAACI,OAAO,CAACvY,EAAE,CAAC;MAC5B,OAAO2F,MAAM,CAACjB,SAAS,CAAC,EAAE,EAAE;QAC1B,GAAGoC,OAAO;QACVK,MAAM,EAAE,KAAK,CAAC;QACdF,OAAO,EAAE,eAAgB,IAAIG,IAAI,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC;AACH;AACA,SAASgR,iCAAiCA,CAACzS,MAAM,EAAE;EACjDlH,QAAQ,CACNkH,MAAM,CAACuB,QAAQ,EACf,QAAQvB,MAAM,CAACU,IAAI,6OACrB,CAAC;AACH;;AAEA;AACA,SAASoS,0BAA0BA,CAAC;EAAE9S,MAAM,EAAEoS;AAAU,CAAC,GAAG,CAAC,CAAC,EAAE;EAC9D,IAAIpS,MAAM,GAAGkC,QAAQ,CAACkQ,SAAS,CAAC,GAAGA,SAAS,GAAGpR,YAAY,CAACoR,SAAS,EAAE1R,IAAI,IAAI,WAAW,EAAE0R,SAAS,CAAC;EACtGK,iCAAiC,CAACzS,MAAM,CAAC;EACzC,OAAO;IACL,MAAM0S,UAAUA,CAAC/Q,YAAY,EAAER,OAAO,EAAE;MACtC,OAAO0Q,aAAa,CAClBlQ,YAAY,KAAI,MAAM3B,MAAM,CAAClB,KAAK,CAAC6C,YAAY,EAAER,OAAO,CAAC,KAAI,CAAC,CAChE,CAAC;IACH,CAAC;IACD,MAAMwR,aAAaA,CAACC,OAAO,EAAEzR,OAAO,EAAE;MACpC,IAAI4R,gBAAgB,GAAG,MAAM/S,MAAM,CAACjB,SAAS,CAAC6T,OAAO,CAAChK,IAAI,EAAEzH,OAAO,CAAC;MACpE,IAAI4R,gBAAgB,CAAClS,MAAM,GAAG,IAAI,EAAE;QAClC,MAAM,IAAIxD,KAAK,CACb,qDAAqD,GAAG0V,gBAAgB,CAAClS,MAC3E,CAAC;MACH;MACA,OAAOkS,gBAAgB;IACzB,CAAC;IACD,MAAMF,cAAcA,CAACG,QAAQ,EAAE7R,OAAO,EAAE;MACtC,OAAOnB,MAAM,CAACjB,SAAS,CAAC,EAAE,EAAE;QAC1B,GAAGoC,OAAO;QACVK,MAAM,EAAE,KAAK,CAAC;QACdF,OAAO,EAAE,eAAgB,IAAIG,IAAI,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC;AACH;;AAEA;AACA,SAASwR,0BAA0BA,CAAC;EAAEjT;AAAO,CAAC,GAAG,CAAC,CAAC,EAAE;EACnD,IAAI5C,GAAG,GAAG,eAAgB,IAAI2U,GAAG,CAAC,CAAC;EACnC,OAAOI,oBAAoB,CAAC;IAC1BnS,MAAM;IACN,MAAMqS,UAAUA,CAACjT,KAAK,EAAEkC,OAAO,EAAE;MAC/B,IAAIjH,EAAE,GAAG6Y,IAAI,CAACC,MAAM,CAAC,CAAC,CAACpQ,QAAQ,CAAC,EAAE,CAAC,CAACqQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;MACpDhW,GAAG,CAAC2P,GAAG,CAAC1S,EAAE,EAAE;QAAEuO,IAAI,EAAExJ,KAAK;QAAEkC;MAAQ,CAAC,CAAC;MACrC,OAAOjH,EAAE;IACX,CAAC;IACD,MAAMiY,QAAQA,CAACjY,EAAE,EAAE;MACjB,IAAI+C,GAAG,CAAC6N,GAAG,CAAC5Q,EAAE,CAAC,EAAE;QACf,IAAI;UAAEuO,IAAI,EAAExJ,KAAK;UAAEkC;QAAQ,CAAC,GAAGlE,GAAG,CAACoK,GAAG,CAACnN,EAAE,CAAC;QAC1C,IAAI,CAACiH,OAAO,IAAIA,OAAO,GAAG,eAAgB,IAAIG,IAAI,CAAC,CAAC,EAAE;UACpD,OAAOrC,KAAK;QACd;QACA,IAAIkC,OAAO,EAAElE,GAAG,CAAC2I,MAAM,CAAC1L,EAAE,CAAC;MAC7B;MACA,OAAO,IAAI;IACb,CAAC;IACD,MAAMkY,UAAUA,CAAClY,EAAE,EAAE+E,KAAK,EAAEkC,OAAO,EAAE;MACnClE,GAAG,CAAC2P,GAAG,CAAC1S,EAAE,EAAE;QAAEuO,IAAI,EAAExJ,KAAK;QAAEkC;MAAQ,CAAC,CAAC;IACvC,CAAC;IACD,MAAMkR,UAAUA,CAACnY,EAAE,EAAE;MACnB+C,GAAG,CAAC2I,MAAM,CAAC1L,EAAE,CAAC;IAChB;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,SAASsM,IAAIA,CAACpJ,IAAI,EAAE,GAAGK,IAAI,EAAE;EAC3B,IAAIuH,MAAM,GAAGvH,IAAI,CAAC,CAAC,CAAC;EACpB,OAAOL,IAAI,CAACkP,KAAK,CAAC,GAAG,CAAC,CAACrP,GAAG,CAAEiW,OAAO,IAAK;IACtC,IAAIA,OAAO,KAAK,GAAG,EAAE;MACnB,OAAOlO,MAAM,GAAGA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACtC;IACA,MAAMlL,KAAK,GAAGoZ,OAAO,CAACpZ,KAAK,CAAC,iBAAiB,CAAC;IAC9C,IAAI,CAACA,KAAK,EAAE,OAAOoZ,OAAO;IAC1B,MAAMC,KAAK,GAAGrZ,KAAK,CAAC,CAAC,CAAC;IACtB,MAAMa,KAAK,GAAGqK,MAAM,GAAGA,MAAM,CAACmO,KAAK,CAAC,GAAG,KAAK,CAAC;IAC7C,MAAMC,UAAU,GAAGtZ,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC;IACtC,IAAIsZ,UAAU,IAAIzY,KAAK,KAAK,KAAK,CAAC,EAAE;MAClC,MAAMuC,KAAK,CACT,SAASE,IAAI,qBAAqB+V,KAAK,2BACzC,CAAC;IACH;IACA,OAAOxY,KAAK;EACd,CAAC,CAAC,CAAC+R,MAAM,CAAEwG,OAAO,IAAKA,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC9C,IAAI,CAAC,GAAG,CAAC;AACtD;;AAEA;AACA,OAAO,KAAKiD,MAAM,MAAM,OAAO;AAC/B,OAAO,KAAKC,QAAQ,MAAM,WAAW;;AAErC;AACA,SAASC,gBAAgBA,CAACtY,KAAK,EAAExB,MAAM,EAAE+Z,YAAY,EAAEC,SAAS,EAAE1O,QAAQ,EAAEpL,SAAS,EAAE;EACrF,IAAIqC,aAAa,GAAG;IAClB,GAAGf,KAAK;IACRpB,UAAU,EAAE;MAAE,GAAGoB,KAAK,CAACpB;IAAW;EACpC,CAAC;EACD,IAAI6Z,cAAc,GAAG5b,WAAW,CAAC2B,MAAM,EAAEga,SAAS,EAAE1O,QAAQ,CAAC;EAC7D,IAAI2O,cAAc,EAAE;IAClB,KAAK,IAAI5Z,KAAK,IAAI4Z,cAAc,EAAE;MAChC,IAAI1Z,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;MAC5B,IAAIyZ,SAAS,GAAGH,YAAY,CAACxZ,OAAO,CAAC;MACrC,IAAI5B,wBAAwB,CAC1B4B,OAAO,EACP2Z,SAAS,CAACvZ,YAAY,EACtBuZ,SAAS,CAACtZ,SAAS,EACnBV,SACF,CAAC,KAAKga,SAAS,CAACC,kBAAkB,IAAI,CAACD,SAAS,CAACtZ,SAAS,CAAC,EAAE;QAC3D,OAAO2B,aAAa,CAACnC,UAAU,CAACG,OAAO,CAAC;MAC1C,CAAC,MAAM,IAAI,CAAC2Z,SAAS,CAACtZ,SAAS,EAAE;QAC/B2B,aAAa,CAACnC,UAAU,CAACG,OAAO,CAAC,GAAG,IAAI;MAC1C;IACF;EACF;EACA,OAAOgC,aAAa;AACtB;;AAEA;AACA,OAAO6X,MAAM,MAAM,OAAO;AAC1B,IAAIC,4BAA4B,GAAG,cAAcD,MAAM,CAACvW,SAAS,CAAC;EAChEyW,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAAC/Y,KAAK,GAAG;MAAEmF,KAAK,EAAE,IAAI;MAAEpF,QAAQ,EAAEgZ,KAAK,CAAChZ;IAAS,CAAC;EACxD;EACA,OAAOiZ,wBAAwBA,CAAC7T,KAAK,EAAE;IACrC,OAAO;MAAEA;IAAM,CAAC;EAClB;EACA,OAAO8T,wBAAwBA,CAACF,KAAK,EAAE/Y,KAAK,EAAE;IAC5C,IAAIA,KAAK,CAACD,QAAQ,KAAKgZ,KAAK,CAAChZ,QAAQ,EAAE;MACrC,OAAO;QAAEoF,KAAK,EAAE,IAAI;QAAEpF,QAAQ,EAAEgZ,KAAK,CAAChZ;MAAS,CAAC;IAClD;IACA,OAAO;MAAEoF,KAAK,EAAEnF,KAAK,CAACmF,KAAK;MAAEpF,QAAQ,EAAEC,KAAK,CAACD;IAAS,CAAC;EACzD;EACAmZ,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAAClZ,KAAK,CAACmF,KAAK,EAAE;MACpB,OAAO,eAAgByT,MAAM,CAACrZ,aAAa,CACzC4Z,+BAA+B,EAC/B;QACEhU,KAAK,EAAE,IAAI,CAACnF,KAAK,CAACmF,KAAK;QACvBiU,cAAc,EAAE;MAClB,CACF,CAAC;IACH,CAAC,MAAM;MACL,OAAO,IAAI,CAACL,KAAK,CAACtV,QAAQ;IAC5B;EACF;AACF,CAAC;AACD,SAAS4V,YAAYA,CAAC;EACpBD,cAAc;EACdE,KAAK;EACL7V;AACF,CAAC,EAAE;EACD,IAAI,CAAC2V,cAAc,EAAE;IACnB,OAAO3V,QAAQ;EACjB;EACA,OAAO,eAAgBmV,MAAM,CAACrZ,aAAa,CAAC,MAAM,EAAE;IAAEga,IAAI,EAAE;EAAK,CAAC,EAAE,eAAgBX,MAAM,CAACrZ,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,eAAgBqZ,MAAM,CAACrZ,aAAa,CAAC,MAAM,EAAE;IAAEia,OAAO,EAAE;EAAQ,CAAC,CAAC,EAAE,eAAgBZ,MAAM,CAACrZ,aAAa,CAC7N,MAAM,EACN;IACE+F,IAAI,EAAE,UAAU;IAChBmU,OAAO,EAAE;EACX,CACF,CAAC,EAAE,eAAgBb,MAAM,CAACrZ,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE+Z,KAAK,CAAC,CAAC,EAAE,eAAgBV,MAAM,CAACrZ,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,eAAgBqZ,MAAM,CAACrZ,aAAa,CAAC,MAAM,EAAE;IAAEma,KAAK,EAAE;MAAEC,UAAU,EAAE,uBAAuB;MAAEC,OAAO,EAAE;IAAO;EAAE,CAAC,EAAEnW,QAAQ,CAAC,CAAC,CAAC;AAClP;AACA,SAAS0V,+BAA+BA,CAAC;EACvChU,KAAK;EACLiU;AACF,CAAC,EAAE;EACD3N,OAAO,CAACtG,KAAK,CAACA,KAAK,CAAC;EACpB,IAAI0U,YAAY,GAAG,eAAgBjB,MAAM,CAACrZ,aAAa,CACrD,QAAQ,EACR;IACEua,uBAAuB,EAAE;MACvBC,MAAM,EAAE;AAChB;AACA;AACA;AACA;IACM;EACF,CACF,CAAC;EACD,IAAInd,oBAAoB,CAACuI,KAAK,CAAC,EAAE;IAC/B,OAAO,eAAgByT,MAAM,CAACrZ,aAAa,CACzC8Z,YAAY,EACZ;MACED,cAAc;MACdE,KAAK,EAAE;IACT,CAAC,EACD,eAAgBV,MAAM,CAACrZ,aAAa,CAAC,IAAI,EAAE;MAAEma,KAAK,EAAE;QAAEM,QAAQ,EAAE;MAAO;IAAE,CAAC,EAAE7U,KAAK,CAACmF,MAAM,EAAE,GAAG,EAAEnF,KAAK,CAACyN,UAAU,CAAC,EAChH/X,mBAAmB,GAAGgf,YAAY,GAAG,IACvC,CAAC;EACH;EACA,IAAII,aAAa;EACjB,IAAI9U,KAAK,YAAYlD,KAAK,EAAE;IAC1BgY,aAAa,GAAG9U,KAAK;EACvB,CAAC,MAAM;IACL,IAAI+U,WAAW,GAAG/U,KAAK,IAAI,IAAI,GAAG,eAAe,GAAG,OAAOA,KAAK,KAAK,QAAQ,IAAI,UAAU,IAAIA,KAAK,GAAGA,KAAK,CAACwC,QAAQ,CAAC,CAAC,GAAGL,IAAI,CAACC,SAAS,CAACpC,KAAK,CAAC;IAC/I8U,aAAa,GAAG,IAAIhY,KAAK,CAACiY,WAAW,CAAC;EACxC;EACA,OAAO,eAAgBtB,MAAM,CAACrZ,aAAa,CAAC8Z,YAAY,EAAE;IAAED,cAAc;IAAEE,KAAK,EAAE;EAAqB,CAAC,EAAE,eAAgBV,MAAM,CAACrZ,aAAa,CAAC,IAAI,EAAE;IAAEma,KAAK,EAAE;MAAEM,QAAQ,EAAE;IAAO;EAAE,CAAC,EAAE,mBAAmB,CAAC,EAAE,eAAgBpB,MAAM,CAACrZ,aAAa,CAC/O,KAAK,EACL;IACEma,KAAK,EAAE;MACLE,OAAO,EAAE,MAAM;MACfO,UAAU,EAAE,yBAAyB;MACrCC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC,EACDJ,aAAa,CAACjR,KAChB,CAAC,EAAE6Q,YAAY,CAAC;AAClB;AACA,SAASS,2BAA2BA,CAAC;EACnCC;AACF,CAAC,EAAE;EACD,IAAIpV,KAAK,GAAG1H,aAAa,CAAC,CAAC;EAC3B,IAAI8c,aAAa,KAAK,KAAK,CAAC,EAAE;IAC5B,MAAM,IAAItY,KAAK,CAAC,8BAA8B,CAAC;EACjD;EACA,OAAO,eAAgB2W,MAAM,CAACrZ,aAAa,CACzC4Z,+BAA+B,EAC/B;IACEC,cAAc,EAAE,CAACmB,aAAa;IAC9BpV;EACF,CACF,CAAC;AACH;;AAEA;AACA,SAASqV,qBAAqBA,CAACC,OAAO,EAAE;EACtC,MAAMpc,YAAY,GAAG,CAAC,CAAC;EACvB,KAAK,MAAMQ,KAAK,IAAI4b,OAAO,CAAC3b,OAAO,EAAE;IACnC4b,uBAAuB,CAACrc,YAAY,EAAEQ,KAAK,CAAC;EAC9C;EACA,OAAOR,YAAY;AACrB;AACA,SAASqc,uBAAuBA,CAACrc,YAAY,EAAES,OAAO,EAAE;EACtDA,OAAO,GAAG6b,KAAK,CAACC,OAAO,CAAC9b,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EACtD,KAAK,MAAMD,KAAK,IAAIC,OAAO,EAAE;IAC3BT,YAAY,CAACQ,KAAK,CAACI,EAAE,CAAC,GAAG;MACvBsE,KAAK,EAAE1E,KAAK,CAAC0E,KAAK;MAClBC,IAAI,EAAE3E,KAAK,CAAC2E,IAAI;MAChBF,OAAO,EAAEuX;IACX,CAAC;EACH;AACF;AACA,IAAIA,aAAa,GAAGA,CAAA,KAAM,IAAI;;AAE9B;AACA,SAASC,gBAAgBA,CAAC;EACxBC,wBAAwB;EACxBC,2BAA2B;EAC3BC,WAAW;EACXC,KAAK,EAAEC,mBAAmB,GAAGD;AAC/B,CAAC,EAAE;EACD,MAAME,SAAS,GAAGC,MAAM;EACxB,IAAIC,cAAc,GAAG,CAAC;EACtB,OAAO,OAAOrc,EAAE,EAAEuD,IAAI,KAAK;IACzB,IAAI+Y,QAAQ,GAAGH,SAAS,CAACI,gBAAgB,GAAG,CAACJ,SAAS,CAACI,gBAAgB,KAAKJ,SAAS,CAACI,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC;IAChH,MAAMC,mBAAmB,GAAGT,2BAA2B,CAAC,CAAC;IACzD,MAAM5G,QAAQ,GAAG,MAAM+G,mBAAmB,CACxC,IAAI7P,OAAO,CAACvL,QAAQ,CAACwL,IAAI,EAAE;MACzBL,IAAI,EAAE,MAAM+P,WAAW,CAACzY,IAAI,EAAE;QAAEiZ;MAAoB,CAAC,CAAC;MACtDxQ,MAAM,EAAE,MAAM;MACdE,OAAO,EAAE;QACPuQ,MAAM,EAAE,kBAAkB;QAC1B,eAAe,EAAEzc;MACnB;IACF,CAAC,CACH,CAAC;IACD,IAAI,CAACmV,QAAQ,CAAClJ,IAAI,EAAE;MAClB,MAAM,IAAIjJ,KAAK,CAAC,kBAAkB,CAAC;IACrC;IACA,MAAMwY,OAAO,GAAG,MAAMM,wBAAwB,CAAC3G,QAAQ,CAAClJ,IAAI,EAAE;MAC5DuQ;IACF,CAAC,CAAC;IACF,IAAIhB,OAAO,CAACkB,IAAI,KAAK,UAAU,EAAE;MAC/B,IAAIlB,OAAO,CAAClN,MAAM,EAAE;QAClB8N,MAAM,CAACtb,QAAQ,CAACwL,IAAI,GAAGkP,OAAO,CAAC1a,QAAQ;QACvC;MACF;MACAqb,SAAS,CAACQ,uBAAuB,CAACC,QAAQ,CAACpB,OAAO,CAAC1a,QAAQ,EAAE;QAC3D9C,OAAO,EAAEwd,OAAO,CAACxd;MACnB,CAAC,CAAC;MACF,OAAOwd,OAAO,CAACqB,YAAY;IAC7B;IACA,IAAIrB,OAAO,CAACkB,IAAI,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAI1Z,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IACA,IAAIwY,OAAO,CAACsB,QAAQ,EAAE;MACpB3D,MAAM,CAAC4D,eAAe;MACpB;MACA,YAAY;QACV,MAAMD,QAAQ,GAAG,MAAMtB,OAAO,CAACsB,QAAQ;QACvC,IAAI,CAACA,QAAQ,EAAE;QACf,IAAIT,cAAc,GAAGC,QAAQ,IAAIH,SAAS,CAACI,gBAAgB,IAAID,QAAQ,EAAE;UACvED,cAAc,GAAGC,QAAQ;UACzB,IAAIQ,QAAQ,CAACJ,IAAI,KAAK,UAAU,EAAE;YAChC,IAAII,QAAQ,CAACxO,MAAM,EAAE;cACnB8N,MAAM,CAACtb,QAAQ,CAACwL,IAAI,GAAGwQ,QAAQ,CAAChc,QAAQ;cACxC;YACF;YACAqb,SAAS,CAACQ,uBAAuB,CAACC,QAAQ,CAACE,QAAQ,CAAChc,QAAQ,EAAE;cAC5D9C,OAAO,EAAE8e,QAAQ,CAAC9e;YACpB,CAAC,CAAC;YACF;UACF;UACA,IAAIgf,SAAS;UACb,KAAK,MAAMpd,KAAK,IAAIkd,QAAQ,CAACjd,OAAO,EAAE;YACpCsc,SAAS,CAACQ,uBAAuB,CAACM,WAAW,CAC3CD,SAAS,EAAEhd,EAAE,IAAI,IAAI,EACrB,CAACkd,6BAA6B,CAACtd,KAAK,CAAC,CAAC,EACtC,IACF,CAAC;YACDod,SAAS,GAAGpd,KAAK;UACnB;UACAwc,MAAM,CAACO,uBAAuB,CAACQ,8CAA8C,CAC3E,CAAC,CACH,CAAC;UACDhE,MAAM,CAAC4D,eAAe,CAAC,MAAM;YAC3BX,MAAM,CAACO,uBAAuB,CAACQ,8CAA8C,CAC3E;cACExd,UAAU,EAAE0J,MAAM,CAACe,MAAM,CACvB,CAAC,CAAC,EACF+R,SAAS,CAACQ,uBAAuB,CAAC5b,KAAK,CAACpB,UAAU,EAClDmd,QAAQ,CAACnd,UACX,CAAC;cACDsK,MAAM,EAAE6S,QAAQ,CAAC7S,MAAM,GAAGZ,MAAM,CAACe,MAAM,CACrC,CAAC,CAAC,EACF+R,SAAS,CAACQ,uBAAuB,CAAC5b,KAAK,CAACkJ,MAAM,EAC9C6S,QAAQ,CAAC7S,MACX,CAAC,GAAG;YACN,CACF,CAAC;UACH,CAAC,CAAC;QACJ;MACF,CACF,CAAC;IACH;IACA,OAAOuR,OAAO,CAACqB,YAAY;EAC7B,CAAC;AACH;AACA,SAASO,uBAAuBA,CAAC;EAC/BlB,mBAAmB;EACnBJ,wBAAwB;EACxBuB,mBAAmB;EACnB7B;AACF,CAAC,EAAE;EACD,MAAMW,SAAS,GAAGC,MAAM;EACxB,IAAID,SAAS,CAACQ,uBAAuB,IAAIR,SAAS,CAACmB,yBAAyB,EAC1E,OAAO;IACLjd,MAAM,EAAE8b,SAAS,CAACQ,uBAAuB;IACzCvd,YAAY,EAAE+c,SAAS,CAACmB;EAC1B,CAAC;EACH,IAAI9B,OAAO,CAACkB,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAI1Z,KAAK,CAAC,sBAAsB,CAAC;EACtEmZ,SAAS,CAACmB,yBAAyB,GAAGnB,SAAS,CAACmB,yBAAyB,IAAI,CAAC,CAAC;EAC/E7B,uBAAuB,CAACU,SAAS,CAACmB,yBAAyB,EAAE9B,OAAO,CAAC3b,OAAO,CAAC;EAC7E,IAAI+V,OAAO,GAAG,eAAgB,IAAI8B,GAAG,CAAC,CAAC;EACvC8D,OAAO,CAAC5F,OAAO,EAAErI,OAAO,CAAEgQ,KAAK,IAAK;IAClClgB,SAAS,CAACkgB,KAAK,CAACza,QAAQ,EAAE,wBAAwB,CAAC;IACnD,IAAI,CAAC8S,OAAO,CAAChF,GAAG,CAAC2M,KAAK,CAACza,QAAQ,CAAC,EAAE;MAChC8S,OAAO,CAAClD,GAAG,CAAC6K,KAAK,CAACza,QAAQ,EAAE,EAAE,CAAC;IACjC;IACA8S,OAAO,CAACzI,GAAG,CAACoQ,KAAK,CAACza,QAAQ,CAAC,EAAE+I,IAAI,CAAC0R,KAAK,CAAC;EAC1C,CAAC,CAAC;EACF,IAAIhe,MAAM,GAAGic,OAAO,CAAC3b,OAAO,CAAC2d,WAAW,CAAC,CAACC,QAAQ,EAAE7d,KAAK,KAAK;IAC5D,MAAMG,KAAK,GAAGmd,6BAA6B,CACzCtd,KAAK,EACL4b,OACF,CAAC;IACD,IAAIiC,QAAQ,CAACjX,MAAM,GAAG,CAAC,EAAE;MACvBzG,KAAK,CAACyE,QAAQ,GAAGiZ,QAAQ;MACzB,IAAIC,eAAe,GAAG9H,OAAO,CAACzI,GAAG,CAACvN,KAAK,CAACI,EAAE,CAAC;MAC3C,IAAI0d,eAAe,EAAE;QACnB3d,KAAK,CAACyE,QAAQ,CAACqH,IAAI,CACjB,GAAG6R,eAAe,CAAC3a,GAAG,CAAEF,CAAC,IAAKqa,6BAA6B,CAACra,CAAC,CAAC,CAChE,CAAC;MACH;IACF;IACA,OAAO,CAAC9C,KAAK,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EACNoc,SAAS,CAACQ,uBAAuB,GAAG/f,YAAY,CAAC;IAC/C2C,MAAM;IACN8d,mBAAmB;IACnBxS,QAAQ,EAAE2Q,OAAO,CAAC3Q,QAAQ;IAC1B8S,OAAO,EAAElhB,oBAAoB,CAAC,CAAC;IAC/BqF,aAAa,EAAEuX,gBAAgB,CAC7B;MACE1Z,UAAU,EAAE6b,OAAO,CAAC7b,UAAU;MAC9B+P,UAAU,EAAE8L,OAAO,CAAC9L,UAAU;MAC9BzF,MAAM,EAAEuR,OAAO,CAACvR;IAClB,CAAC,EACD1K,MAAM,EACLO,OAAO,IAAK;MACX,IAAIF,KAAK,GAAG4b,OAAO,CAAC3b,OAAO,CAAC+d,IAAI,CAAEzO,CAAC,IAAKA,CAAC,CAACnP,EAAE,KAAKF,OAAO,CAAC;MACzDzC,SAAS,CAACuC,KAAK,EAAE,4BAA4B,CAAC;MAC9C,OAAO;QACLM,YAAY,EAAEN,KAAK,CAACM,YAAY;QAChCC,SAAS,EAAEP,KAAK,CAACO,SAAS;QAC1BuZ,kBAAkB,EAAE9Z,KAAK,CAACie,sBAAsB,IAAI;MACtD,CAAC;IACH,CAAC,EACDrC,OAAO,CAAC1a,QAAQ,EAChB,KAAK,CAAC,EACN,KACF,CAAC;IACD,MAAMgd,uBAAuBA,CAAC;MAAE5a,IAAI;MAAEiJ;IAAO,CAAC,EAAE;MAC9C,IAAI4R,eAAe,CAACnN,GAAG,CAAC1N,IAAI,CAAC,EAAE;QAC7B;MACF;MACA,MAAM8a,4BAA4B,CAChC,CAAC9a,IAAI,CAAC,EACN4Y,wBAAwB,EACxBI,mBAAmB,EACnB/P,MACF,CAAC;IACH,CAAC;IACD;IACA8R,YAAY,EAAEC,6BAA6B,CACzC,MAAM/B,SAAS,CAACQ,uBAAuB,EACvC,IAAI,EACJnB,OAAO,CAAC3Q,QAAQ,EAChBiR,wBAAwB,EACxBI,mBACF;EACF,CAAC,CAAC;EACF,IAAIC,SAAS,CAACQ,uBAAuB,CAAC5b,KAAK,CAACod,WAAW,EAAE;IACvDhC,SAAS,CAACiC,mBAAmB,GAAG,IAAI;IACpCjC,SAAS,CAACQ,uBAAuB,CAAC0B,UAAU,CAAC,CAAC;EAChD,CAAC,MAAM;IACLlC,SAAS,CAACiC,mBAAmB,GAAG,KAAK;EACvC;EACA,IAAIE,cAAc,GAAG,KAAK,CAAC;EAC3BnC,SAAS,CAACQ,uBAAuB,CAAC4B,SAAS,CAAC,CAAC;IAAE5e,UAAU;IAAE+P;EAAW,CAAC,KAAK;IAC1E,IAAI4O,cAAc,KAAK3e,UAAU,EAAE;MACjCwc,SAAS,CAACI,gBAAgB,GAAG,CAACJ,SAAS,CAACI,gBAAgB,KAAKJ,SAAS,CAACI,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC;IACnG;EACF,CAAC,CAAC;EACFJ,SAAS,CAACQ,uBAAuB,CAAC6B,mBAAmB,GAAIC,oBAAoB,IAAK;IAChF,MAAMC,SAAS,GAAGtC,MAAM,CAACO,uBAAuB,CAACpd,MAAM;IACvD,MAAMof,SAAS,GAAG,EAAE;IACpB,SAASC,UAAUA,CAACC,OAAO,EAAE/b,QAAQ,EAAE;MACrC,OAAO+b,OAAO,CAAC9b,GAAG,CAAEhD,KAAK,IAAK;QAC5B,MAAM+e,WAAW,GAAGL,oBAAoB,CAACtR,GAAG,CAACpN,KAAK,CAACC,EAAE,CAAC;QACtD,IAAI8e,WAAW,EAAE;UACf,MAAM;YACJC,WAAW;YACXnb,SAAS;YACTob,YAAY;YACZhb,gBAAgB;YAChB7D;UACF,CAAC,GAAG2e,WAAW;UACf,MAAM7b,QAAQ,GAAGia,6BAA6B,CAAC;YAC7C+B,YAAY,EAAEF,WAAW,CAACE,YAAY;YACtC/e,YAAY,EAAE6e,WAAW,CAAC7e,YAAY;YACtCgf,OAAO,EAAEnf,KAAK,CAACmf,OAAO;YACtBC,YAAY,EAAEpf,KAAK,CAACof,YAAY;YAChC1b,MAAM,EAAE1D,KAAK,CAAC0D,MAAM;YACpBG,SAAS;YACTob,YAAY;YACZhb,gBAAgB;YAChB7D,SAAS;YACT0d,sBAAsB,EAAE9d,KAAK,CAAC8d,sBAAsB;YACpD7d,EAAE,EAAED,KAAK,CAACC,EAAE;YACZmD,KAAK,EAAEpD,KAAK,CAACoD,KAAK;YAClBmB,KAAK,EAAEya,WAAW,CAACza,KAAK;YACxBC,IAAI,EAAEwa,WAAW,CAACxa,IAAI;YACtBzB,QAAQ;YACRI,IAAI,EAAEnD,KAAK,CAACmD,IAAI;YAChBQ,gBAAgB,EAAEqb,WAAW,CAACrb;UAChC,CAAC,CAAC;UACF,IAAI3D,KAAK,CAACyE,QAAQ,EAAE;YAClBvB,QAAQ,CAACuB,QAAQ,GAAGoa,UAAU,CAAC7e,KAAK,CAACyE,QAAQ,EAAEzE,KAAK,CAACC,EAAE,CAAC;UAC1D;UACA,OAAOiD,QAAQ;QACjB;QACA,MAAMmc,YAAY,GAAG;UAAE,GAAGrf;QAAM,CAAC;QACjC,IAAIA,KAAK,CAACyE,QAAQ,EAAE;UAClB4a,YAAY,CAAC5a,QAAQ,GAAGoa,UAAU,CAAC7e,KAAK,CAACyE,QAAQ,EAAEzE,KAAK,CAACC,EAAE,CAAC;QAC9D;QACA,OAAOof,YAAY;MACrB,CAAC,CAAC;IACJ;IACAT,SAAS,CAAC9S,IAAI,CACZ,GAAG+S,UAAU,CAACF,SAAS,EAAE,KAAK,CAAC,CACjC,CAAC;IACDtC,MAAM,CAACO,uBAAuB,CAAC0C,kBAAkB,CAACV,SAAS,CAAC;EAC9D,CAAC;EACD,OAAO;IACLte,MAAM,EAAE8b,SAAS,CAACQ,uBAAuB;IACzCvd,YAAY,EAAE+c,SAAS,CAACmB;EAC1B,CAAC;AACH;AACA,IAAIgC,qBAAqB,GAAG/gB,sBAAsB,CAAC,CAAC;AACpD,SAAS2f,6BAA6BA,CAACqB,SAAS,EAAE7e,GAAG,EAAEmK,QAAQ,EAAEiR,wBAAwB,EAAEI,mBAAmB,EAAE;EAC9G,IAAI+B,YAAY,GAAG9gB,8BAA8B,CAC/CoiB,SAAS,EACR3f,KAAK,IAAK;IACT,IAAI4f,CAAC,GAAG5f,KAAK;IACb,OAAO;MACLO,SAAS,EAAEqf,CAAC,CAACzf,KAAK,CAACI,SAAS;MAC5B2D,eAAe,EAAE0b,CAAC,CAACzf,KAAK,CAAC+D,eAAe;MACxCkb,YAAY,EAAEQ,CAAC,CAACzf,KAAK,CAACif,YAAY;MAClCpb,SAAS,EAAE4b,CAAC,CAACzf,KAAK,CAAC6D,SAAS;MAC5BC,eAAe,EAAE2b,CAAC,CAACzf,KAAK,CAAC8D,eAAe;MACxC4b,mBAAmB,EAAED,CAAC,CAACzf,KAAK,CAAC0f;IAC/B,CAAC;EACH,CAAC;EACD;EACAC,uBAAuB,CAAC5D,wBAAwB,EAAEI,mBAAmB,CAAC,EACtExb,GAAG,EACHmK,QAAQ;EACR;EACA;EACA;EACCjL,KAAK,IAAK;IACT,IAAI4f,CAAC,GAAG5f,KAAK;IACb,OAAO4f,CAAC,CAACzf,KAAK,CAACif,YAAY,IAAI,CAACQ,CAAC,CAACzf,KAAK,CAACmf,OAAO;EACjD,CACF,CAAC;EACD,OAAO,MAAO3b,IAAI,IAAKA,IAAI,CAACoc,4BAA4B,CAAC,YAAY;IACnE,IAAI5gB,OAAO,GAAGwE,IAAI,CAACxE,OAAO;IAC1BA,OAAO,CAAC2T,GAAG,CAAC4M,qBAAqB,EAAE,EAAE,CAAC;IACtC,IAAIhN,OAAO,GAAG,MAAM2L,YAAY,CAAC1a,IAAI,CAAC;IACtC,MAAMqc,kBAAkB,GAAG,eAAgB,IAAIlI,GAAG,CAAC,CAAC;IACpD,KAAK,MAAM3X,KAAK,IAAIhB,OAAO,CAACoO,GAAG,CAACmS,qBAAqB,CAAC,EAAE;MACtD,IAAI,CAACM,kBAAkB,CAAChP,GAAG,CAAC7Q,KAAK,CAACC,EAAE,CAAC,EAAE;QACrC4f,kBAAkB,CAAClN,GAAG,CAAC3S,KAAK,CAACC,EAAE,EAAE,EAAE,CAAC;MACtC;MACA4f,kBAAkB,CAACzS,GAAG,CAACpN,KAAK,CAACC,EAAE,CAAC,CAAC6L,IAAI,CAAC9L,KAAK,CAAC;IAC9C;IACA,KAAK,MAAMH,KAAK,IAAI2D,IAAI,CAAC1D,OAAO,EAAE;MAChC,MAAMggB,cAAc,GAAGD,kBAAkB,CAACzS,GAAG,CAACvN,KAAK,CAACG,KAAK,CAACC,EAAE,CAAC;MAC7D,IAAI6f,cAAc,EAAE;QAClB,KAAK,MAAMC,QAAQ,IAAID,cAAc,EAAE;UACrCzD,MAAM,CAACO,uBAAuB,CAACM,WAAW,CACxC6C,QAAQ,CAAChd,QAAQ,IAAI,IAAI,EACzB,CAACoa,6BAA6B,CAAC4C,QAAQ,CAAC,CAAC,EACzC,IACF,CAAC;QACH;MACF;IACF;IACA,OAAOxN,OAAO;EAChB,CAAC,CAAC;AACJ;AACA,SAASoN,uBAAuBA,CAAC5D,wBAAwB,EAAEI,mBAAmB,EAAE;EAC9E,OAAO,OAAO3Y,IAAI,EAAEsH,QAAQ,EAAEkV,YAAY,KAAK;IAC7C,IAAI;MAAE9U,OAAO;MAAElM;IAAQ,CAAC,GAAGwE,IAAI;IAC/B,IAAIvE,GAAG,GAAGb,cAAc,CAAC8M,OAAO,CAACjM,GAAG,EAAE6L,QAAQ,EAAE,KAAK,CAAC;IACtD,IAAII,OAAO,CAACe,MAAM,KAAK,KAAK,EAAE;MAC5BhN,GAAG,GAAGX,eAAe,CAACW,GAAG,CAAC;MAC1B,IAAI+gB,YAAY,EAAE;QAChB/gB,GAAG,CAACwM,YAAY,CAACkH,GAAG,CAAC,SAAS,EAAEqN,YAAY,CAAC7J,IAAI,CAAC,GAAG,CAAC,CAAC;MACzD;IACF;IACA,IAAIjB,GAAG,GAAG,MAAMiH,mBAAmB,CACjC,IAAI7P,OAAO,CAACrN,GAAG,EAAE,MAAMrC,iBAAiB,CAACsO,OAAO,CAAC,CACnD,CAAC;IACD,IAAIgK,GAAG,CAAC5J,MAAM,KAAK,GAAG,IAAI,CAAC4J,GAAG,CAAC/I,OAAO,CAAC0E,GAAG,CAAC,kBAAkB,CAAC,EAAE;MAC9D,MAAM,IAAI/U,iBAAiB,CAAC,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC;IACrD;IACAwB,SAAS,CAAC4X,GAAG,CAAChJ,IAAI,EAAE,4BAA4B,CAAC;IACjD,IAAI;MACF,MAAMuP,OAAO,GAAG,MAAMM,wBAAwB,CAAC7G,GAAG,CAAChJ,IAAI,EAAE;QACvDuQ,mBAAmB,EAAE,KAAK;MAC5B,CAAC,CAAC;MACF,IAAIhB,OAAO,CAACkB,IAAI,KAAK,UAAU,EAAE;QAC/B,OAAO;UACLrR,MAAM,EAAE4J,GAAG,CAAC5J,MAAM;UAClBkD,IAAI,EAAE;YACJzQ,QAAQ,EAAE;cACRA,QAAQ,EAAE0d,OAAO,CAAC1a,QAAQ;cAC1BwN,MAAM,EAAEkN,OAAO,CAAClN,MAAM;cACtBtQ,OAAO,EAAEwd,OAAO,CAACxd,OAAO;cACxBiV,UAAU,EAAE,KAAK;cACjB5H,MAAM,EAAEmQ,OAAO,CAACnQ;YAClB;UACF;QACF,CAAC;MACH;MACA,IAAImQ,OAAO,CAACkB,IAAI,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAI1Z,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MACAjE,OAAO,CAACoO,GAAG,CAACmS,qBAAqB,CAAC,CAACzT,IAAI,CAAC,GAAG2P,OAAO,CAAC3b,OAAO,CAAC;MAC3D,IAAIyS,OAAO,GAAG;QAAE/S,MAAM,EAAE,CAAC;MAAE,CAAC;MAC5B,MAAMygB,OAAO,GAAGziB,gBAAgB,CAAC0N,OAAO,CAACe,MAAM,CAAC,GAAG,YAAY,GAAG,YAAY;MAC9E,KAAK,IAAI,CAAClM,OAAO,EAAEiF,KAAK,CAAC,IAAIsE,MAAM,CAACa,OAAO,CAACsR,OAAO,CAACwE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QACnE1N,OAAO,CAAC/S,MAAM,CAACO,OAAO,CAAC,GAAG;UAAEyO,IAAI,EAAExJ;QAAM,CAAC;MAC3C;MACA,IAAIyW,OAAO,CAACvR,MAAM,EAAE;QAClB,KAAK,IAAI,CAACnK,OAAO,EAAEoG,KAAK,CAAC,IAAImD,MAAM,CAACa,OAAO,CAACsR,OAAO,CAACvR,MAAM,CAAC,EAAE;UAC3DqI,OAAO,CAAC/S,MAAM,CAACO,OAAO,CAAC,GAAG;YAAEoG;UAAM,CAAC;QACrC;MACF;MACA,OAAO;QAAEmF,MAAM,EAAE4J,GAAG,CAAC5J,MAAM;QAAEkD,IAAI,EAAE+D;MAAQ,CAAC;IAC9C,CAAC,CAAC,OAAOlF,CAAC,EAAE;MACV,MAAM,IAAIpK,KAAK,CAAC,+BAA+B,CAAC;IAClD;EACF,CAAC;AACH;AACA,SAASid,iBAAiBA,CAAC;EACzBnE,wBAAwB;EACxBG,KAAK,EAAEC,mBAAmB,GAAGD,KAAK;EAClCT,OAAO;EACP7a,cAAc,GAAG,OAAO;EACxB0c;AACF,CAAC,EAAE;EACD,IAAI7B,OAAO,CAACkB,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAI1Z,KAAK,CAAC,sBAAsB,CAAC;EACtE,IAAI;IAAE3C,MAAM;IAAEjB;EAAa,CAAC,GAAG+Z,MAAM,CAAC+G,OAAO,CAC3C,MAAM9C,uBAAuB,CAAC;IAC5B5B,OAAO;IACPU,mBAAmB;IACnBmB,mBAAmB;IACnBvB;EACF,CAAC,CAAC,EACF,CACEA,wBAAwB,EACxBN,OAAO,EACPU,mBAAmB,EACnBmB,mBAAmB,CAEvB,CAAC;EACDlE,MAAM,CAACgH,SAAS,CAAC,MAAM;IACrBliB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EACNkb,MAAM,CAACiH,eAAe,CAAC,MAAM;IAC3B,MAAMjE,SAAS,GAAGC,MAAM;IACxB,IAAI,CAACD,SAAS,CAACiC,mBAAmB,EAAE;MAClCjC,SAAS,CAACiC,mBAAmB,GAAG,IAAI;MACpCjC,SAAS,CAACQ,uBAAuB,CAAC0B,UAAU,CAAC,CAAC;IAChD;EACF,CAAC,EAAE,EAAE,CAAC;EACN,IAAI,CAAC9E,SAAS,EAAE8G,WAAW,CAAC,GAAGlH,MAAM,CAACmH,QAAQ,CAACjgB,MAAM,CAACU,KAAK,CAACD,QAAQ,CAAC;EACrEqY,MAAM,CAACiH,eAAe,CACpB,MAAM/f,MAAM,CAACke,SAAS,CAAEgC,QAAQ,IAAK;IACnC,IAAIA,QAAQ,CAACzf,QAAQ,KAAKyY,SAAS,EAAE;MACnC8G,WAAW,CAACE,QAAQ,CAACzf,QAAQ,CAAC;IAChC;EACF,CAAC,CAAC,EACF,CAACT,MAAM,EAAEkZ,SAAS,CACpB,CAAC;EACDJ,MAAM,CAACgH,SAAS,CAAC,MAAM;IACrB,IAAIxf,cAAc,KAAK,MAAM;IAAI;IACjCyb,MAAM,CAACoE,SAAS,EAAEC,UAAU,EAAEC,QAAQ,KAAK,IAAI,EAAE;MAC/C;IACF;IACA,SAASC,eAAeA,CAACC,EAAE,EAAE;MAC3B,IAAI1d,IAAI,GAAG0d,EAAE,CAACC,OAAO,KAAK,MAAM,GAAGD,EAAE,CAACE,YAAY,CAAC,QAAQ,CAAC,GAAGF,EAAE,CAACE,YAAY,CAAC,MAAM,CAAC;MACtF,IAAI,CAAC5d,IAAI,EAAE;QACT;MACF;MACA,IAAI0H,QAAQ,GAAGgW,EAAE,CAACC,OAAO,KAAK,GAAG,GAAGD,EAAE,CAAChW,QAAQ,GAAG,IAAI1L,GAAG,CAACgE,IAAI,EAAEkZ,MAAM,CAACtb,QAAQ,CAACigB,MAAM,CAAC,CAACnW,QAAQ;MAChG,IAAI,CAACmT,eAAe,CAACnN,GAAG,CAAChG,QAAQ,CAAC,EAAE;QAClCoW,SAAS,CAAC7K,GAAG,CAACvL,QAAQ,CAAC;MACzB;IACF;IACA,eAAeqW,YAAYA,CAAA,EAAG;MAC5BC,QAAQ,CAACC,gBAAgB,CAAC,uCAAuC,CAAC,CAAC5T,OAAO,CAACoT,eAAe,CAAC;MAC3F,IAAI9K,KAAK,GAAG6F,KAAK,CAAC0F,IAAI,CAACJ,SAAS,CAAC1X,IAAI,CAAC,CAAC,CAAC,CAACkJ,MAAM,CAAEtP,IAAI,IAAK;QACxD,IAAI6a,eAAe,CAACnN,GAAG,CAAC1N,IAAI,CAAC,EAAE;UAC7B8d,SAAS,CAACtV,MAAM,CAACxI,IAAI,CAAC;UACtB,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAI2S,KAAK,CAACrP,MAAM,KAAK,CAAC,EAAE;QACtB;MACF;MACA,IAAI;QACF,MAAMwX,4BAA4B,CAChCnI,KAAK,EACLiG,wBAAwB,EACxBI,mBACF,CAAC;MACH,CAAC,CAAC,OAAO9O,CAAC,EAAE;QACVZ,OAAO,CAACtG,KAAK,CAAC,kCAAkC,EAAEkH,CAAC,CAAC;MACtD;IACF;IACA,IAAIiU,qBAAqB,GAAGC,QAAQ,CAACL,YAAY,EAAE,GAAG,CAAC;IACvDA,YAAY,CAAC,CAAC;IACd,IAAIM,QAAQ,GAAG,IAAIC,gBAAgB,CAAC,MAAMH,qBAAqB,CAAC,CAAC,CAAC;IAClEE,QAAQ,CAACE,OAAO,CAACP,QAAQ,CAACQ,eAAe,EAAE;MACzCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,CAAC,eAAe,EAAE,MAAM,EAAE,QAAQ;IACrD,CAAC,CAAC;EACJ,CAAC,EAAE,CAACnhB,cAAc,EAAEmb,wBAAwB,EAAEI,mBAAmB,CAAC,CAAC;EACnE,MAAM6F,gBAAgB,GAAG;IACvBviB,MAAM,EAAE;MACN;MACA;MACA4C,mBAAmB,EAAE,KAAK;MAC1BD,6BAA6B,EAAE;IACjC,CAAC;IACD1C,SAAS,EAAE,KAAK;IAChBiB,GAAG,EAAE,IAAI;IACTrB,WAAW,EAAE,EAAE;IACfF,QAAQ,EAAE;MACRI,MAAM,EAAE,CAAC,CAAC;MACViD,OAAO,EAAE,GAAG;MACZxD,GAAG,EAAE,EAAE;MACPqD,KAAK,EAAE;QACLE,MAAM,EAAE,EAAE;QACVD,OAAO,EAAE;MACX;IACF,CAAC;IACD3B,cAAc,EAAE;MAAE8B,IAAI,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAc,CAAC;IAC7DtD;EACF,CAAC;EACD,OAAO,eAAgB+Z,MAAM,CAAC7Y,aAAa,CAACrE,gBAAgB,CAACuE,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAAE,eAAgB0Y,MAAM,CAAC7Y,aAAa,CAACsZ,4BAA4B,EAAE;IAAE9Y,QAAQ,EAAEyY;EAAU,CAAC,EAAE,eAAgBJ,MAAM,CAAC7Y,aAAa,CAACxE,gBAAgB,CAAC0E,QAAQ,EAAE;IAAEC,KAAK,EAAEshB;EAAiB,CAAC,EAAE,eAAgB5I,MAAM,CAAC7Y,aAAa,CAACnE,cAAc,EAAE;IAAEkE,MAAM;IAAE2hB,SAAS,EAAE5I,QAAQ,CAAC4I;EAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACrX;AACA,SAAS9E,6BAA6BA,CAACtd,KAAK,EAAE4b,OAAO,EAAE;EACrD,IAAIyG,cAAc,GAAGzG,OAAO,IAAI5b,KAAK,CAACI,EAAE,IAAIwb,OAAO,CAAC7b,UAAU;EAC9D,IAAI8X,WAAW,GAAG+D,OAAO,EAAE7b,UAAU,CAACC,KAAK,CAACI,EAAE,CAAC;EAC/C,IAAIkiB,eAAe,GAAG1G,OAAO,EAAEvR,MAAM,IAAIrK,KAAK,CAACI,EAAE,IAAIwb,OAAO,CAACvR,MAAM;EACnE,IAAIkY,YAAY,GAAG3G,OAAO,EAAEvR,MAAM,GAAGrK,KAAK,CAACI,EAAE,CAAC;EAC9C,IAAIoiB,kBAAkB,GAAGxiB,KAAK,CAACM,YAAY,EAAEc,OAAO,KAAK,IAAI,IAAI,CAACpB,KAAK,CAACO,SAAS;EAAI;EACrF;EACA;EACAP,KAAK,CAACof,YAAY,IAAI,CAACpf,KAAK,CAACsf,OAAO;EACpC7hB,SAAS,CAAC+e,MAAM,CAACkB,yBAAyB,CAAC;EAC3C7B,uBAAuB,CAACW,MAAM,CAACkB,yBAAyB,EAAE1d,KAAK,CAAC;EAChE,IAAIyiB,SAAS,GAAG;IACdriB,EAAE,EAAEJ,KAAK,CAACI,EAAE;IACZkf,OAAO,EAAEtf,KAAK,CAACsf,OAAO;IACtBC,YAAY,EAAEvf,KAAK,CAACuf,YAAY;IAChC1b,MAAM,EAAE7D,KAAK,CAAC6D,MAAM;IACpBO,gBAAgB,EAAEpE,KAAK,CAACoE,gBAAgB;IACxC6Z,sBAAsB,EAAEje,KAAK,CAACie,sBAAsB;IACpD1a,KAAK,EAAEvD,KAAK,CAACuD,KAAK;IAClBK,MAAM,EAAE5D,KAAK,CAACM,YAAY,GAAG,OAAOqD,IAAI,EAAE+e,WAAW,KAAK;MACxD,IAAI;QACF,IAAI3Z,MAAM,GAAG,MAAM/I,KAAK,CAACM,YAAY,CAAC;UACpC,GAAGqD,IAAI;UACPgf,YAAY,EAAEA,CAAA,KAAM;YAClBC,+BAA+B,CAC7B,QAAQ,EACR5iB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACO,SACR,CAAC;YACD,IAAIiiB,kBAAkB,EAAE;cACtB,IAAIH,cAAc,EAAE;gBAClB,OAAOxK,WAAW;cACpB;cACA,IAAIyK,eAAe,EAAE;gBACnB,MAAMC,YAAY;cACpB;YACF;YACA,OAAOM,eAAe,CAACH,WAAW,CAAC;UACrC;QACF,CAAC,CAAC;QACF,OAAO3Z,MAAM;MACf,CAAC,SAAS;QACRyZ,kBAAkB,GAAG,KAAK;MAC5B;IACF,CAAC;IACC;IACA;IACA,CAACpM,CAAC,EAAEsM,WAAW,KAAKG,eAAe,CAACH,WAAW,CAChD;IACDhf,MAAM,EAAE1D,KAAK,CAACqf,YAAY,GAAG,CAAC1b,IAAI,EAAE+e,WAAW,KAAK1iB,KAAK,CAACqf,YAAY,CAAC;MACrE,GAAG1b,IAAI;MACPmf,YAAY,EAAE,MAAAA,CAAA,KAAY;QACxBF,+BAA+B,CAC7B,QAAQ,EACR5iB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACO,SACR,CAAC;QACD,OAAO,MAAMsiB,eAAe,CAACH,WAAW,CAAC;MAC3C;IACF,CAAC,CAAC,GAAG1iB,KAAK,CAACgE,SAAS,GAAG,CAACoS,CAAC,EAAEsM,WAAW,KAAKG,eAAe,CAACH,WAAW,CAAC,GAAG,MAAM;MAC9E,MAAMzkB,oBAAoB,CAAC,QAAQ,EAAE+B,KAAK,CAACI,EAAE,CAAC;IAChD,CAAC;IACDkD,IAAI,EAAEtD,KAAK,CAACsD,IAAI;IAChBQ,gBAAgB,EAAE9D,KAAK,CAAC8D,gBAAgB;IACxC;IACA;IACAvD,SAAS,EAAE,IAAI;IACf2D,eAAe,EAAElE,KAAK,CAACM,YAAY,IAAI,IAAI;IAC3C0D,SAAS,EAAEhE,KAAK,CAACgE,SAAS;IAC1BC,eAAe,EAAEjE,KAAK,CAACqf,YAAY,IAAI,IAAI;IAC3CQ,mBAAmB,EAAE7f,KAAK,CAAC8D,gBAAgB,IAAI;EACjD,CAAC;EACD,IAAI,OAAO2e,SAAS,CAAC7e,MAAM,KAAK,UAAU,EAAE;IAC1C6e,SAAS,CAAC7e,MAAM,CAACxC,OAAO,GAAG9C,wBAAwB,CACjD0B,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACM,YAAY,EAClBN,KAAK,CAACO,SAAS,EACf,KACF,CAAC;EACH;EACA,OAAOkiB,SAAS;AAClB;AACA,SAASI,eAAeA,CAACH,WAAW,EAAE;EACpCjlB,SAAS,CAAC,OAAOilB,WAAW,KAAK,UAAU,EAAE,+BAA+B,CAAC;EAC7E,OAAOA,WAAW,CAAC,CAAC;AACtB;AACA,SAASE,+BAA+BA,CAAC9F,IAAI,EAAE5c,OAAO,EAAE6iB,UAAU,EAAE;EAClE,IAAI,CAACA,UAAU,EAAE;IACf,IAAIC,EAAE,GAAGlG,IAAI,KAAK,QAAQ,GAAG,gBAAgB,GAAG,gBAAgB;IAChE,IAAImG,GAAG,GAAG,0BAA0BD,EAAE,2CAA2ClG,IAAI,eAAe5c,OAAO,IAAI;IAC/G0M,OAAO,CAACtG,KAAK,CAAC2c,GAAG,CAAC;IAClB,MAAM,IAAIhnB,iBAAiB,CAAC,GAAG,EAAE,aAAa,EAAE,IAAImH,KAAK,CAAC6f,GAAG,CAAC,EAAE,IAAI,CAAC;EACvE;AACF;AACA,IAAI7B,SAAS,GAAG,eAAgB,IAAItQ,GAAG,CAAC,CAAC;AACzC,IAAIoS,sBAAsB,GAAG,GAAG;AAChC,IAAI/E,eAAe,GAAG,eAAgB,IAAIrN,GAAG,CAAC,CAAC;AAC/C,IAAIqS,SAAS,GAAG,IAAI;AACpB,SAASC,cAAcA,CAACnN,KAAK,EAAE;EAC7B,IAAIA,KAAK,CAACrP,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;EACb;EACA,IAAIqP,KAAK,CAACrP,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAItH,GAAG,CAAC,GAAG2W,KAAK,CAAC,CAAC,CAAC,WAAW,EAAEuG,MAAM,CAACtb,QAAQ,CAACigB,MAAM,CAAC;EAChE;EACA,MAAM5E,SAAS,GAAGC,MAAM;EACxB,IAAIvR,QAAQ,GAAG,CAACsR,SAAS,CAACQ,uBAAuB,CAAC9R,QAAQ,IAAI,EAAE,EAAE7M,OAAO,CACvE,UAAU,EACV,EACF,CAAC;EACD,IAAIgB,GAAG,GAAG,IAAIE,GAAG,CAAC,GAAG2L,QAAQ,YAAY,EAAEuR,MAAM,CAACtb,QAAQ,CAACigB,MAAM,CAAC;EAClElL,KAAK,CAACoN,IAAI,CAAC,CAAC,CAAC1V,OAAO,CAAErK,IAAI,IAAKlE,GAAG,CAACwM,YAAY,CAACO,MAAM,CAAC,GAAG,EAAE7I,IAAI,CAAC,CAAC;EAClE,OAAOlE,GAAG;AACZ;AACA,eAAegf,4BAA4BA,CAACnI,KAAK,EAAEiG,wBAAwB,EAAEI,mBAAmB,EAAE/P,MAAM,EAAE;EACxG,IAAInN,GAAG,GAAGgkB,cAAc,CAACnN,KAAK,CAAC;EAC/B,IAAI7W,GAAG,IAAI,IAAI,EAAE;IACf;EACF;EACA,IAAIA,GAAG,CAAC0J,QAAQ,CAAC,CAAC,CAAClC,MAAM,GAAGuc,SAAS,EAAE;IACrC/B,SAAS,CAACkC,KAAK,CAAC,CAAC;IACjB;EACF;EACA,IAAI/N,QAAQ,GAAG,MAAM+G,mBAAmB,CAAC,IAAI7P,OAAO,CAACrN,GAAG,EAAE;IAAEmN;EAAO,CAAC,CAAC,CAAC;EACtE,IAAI,CAACgJ,QAAQ,CAAClJ,IAAI,IAAIkJ,QAAQ,CAAC9J,MAAM,GAAG,GAAG,IAAI8J,QAAQ,CAAC9J,MAAM,IAAI,GAAG,EAAE;IACrE,MAAM,IAAIrI,KAAK,CAAC,mDAAmD,CAAC;EACtE;EACA,IAAIwY,OAAO,GAAG,MAAMM,wBAAwB,CAAC3G,QAAQ,CAAClJ,IAAI,EAAE;IAC1DuQ,mBAAmB,EAAE,KAAK;EAC5B,CAAC,CAAC;EACF,IAAIhB,OAAO,CAACkB,IAAI,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAI1Z,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EACA6S,KAAK,CAACtI,OAAO,CAAE4V,CAAC,IAAKC,cAAc,CAACD,CAAC,EAAEpF,eAAe,CAAC,CAAC;EACxDvC,OAAO,CAAC5F,OAAO,CAACrI,OAAO,CAAE4V,CAAC,IAAK;IAC7B/G,MAAM,CAACO,uBAAuB,CAACM,WAAW,CACxCkG,CAAC,CAACrgB,QAAQ,IAAI,IAAI,EAClB,CAACoa,6BAA6B,CAACiG,CAAC,CAAC,CACnC,CAAC;EACH,CAAC,CAAC;AACJ;AACA,SAASC,cAAcA,CAAClgB,IAAI,EAAEmgB,KAAK,EAAE;EACnC,IAAIA,KAAK,CAACC,IAAI,IAAIR,sBAAsB,EAAE;IACxC,IAAIS,KAAK,GAAGF,KAAK,CAAC/V,MAAM,CAAC,CAAC,CAACkW,IAAI,CAAC,CAAC,CAAC/iB,KAAK;IACvC4iB,KAAK,CAAC3X,MAAM,CAAC6X,KAAK,CAAC;EACrB;EACAF,KAAK,CAAClN,GAAG,CAACjT,IAAI,CAAC;AACjB;AACA,SAASoe,QAAQA,CAACmC,QAAQ,EAAEC,IAAI,EAAE;EAChC,IAAItQ,SAAS;EACb,OAAO,CAAC,GAAG7P,IAAI,KAAK;IAClB6Y,MAAM,CAAC5I,YAAY,CAACJ,SAAS,CAAC;IAC9BA,SAAS,GAAGgJ,MAAM,CAAC/I,UAAU,CAAC,MAAMoQ,QAAQ,CAAC,GAAGlgB,IAAI,CAAC,EAAEmgB,IAAI,CAAC;EAC9D,CAAC;AACH;;AAEA;AACA,OAAO,KAAKC,MAAM,MAAM,OAAO;;AAE/B;AACA,IAAIC,QAAQ,GAAG,IAAIhf,WAAW,CAAC,CAAC;AAChC,IAAIif,OAAO,GAAG,gBAAgB;AAC9B,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EACnC,IAAIC,OAAO,GAAG,IAAIziB,WAAW,CAAC,CAAC;EAC/B,IAAI0iB,wBAAwB;EAC5B,IAAIC,iBAAiB,GAAG,IAAIC,OAAO,CAChCC,OAAO,IAAKH,wBAAwB,GAAGG,OAC1C,CAAC;EACD,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,OAAO,GAAG,IAAI;EAClB,SAASC,mBAAmBA,CAACtW,UAAU,EAAE;IACvC,KAAK,IAAIuW,KAAK,IAAIH,QAAQ,EAAE;MAC1B,IAAII,GAAG,GAAGV,OAAO,CAACW,MAAM,CAACF,KAAK,EAAE;QAAE1W,MAAM,EAAE;MAAK,CAAC,CAAC;MACjD,IAAI2W,GAAG,CAAC/P,QAAQ,CAACkP,OAAO,CAAC,EAAE;QACzBa,GAAG,GAAGA,GAAG,CAAC7e,KAAK,CAAC,CAAC,EAAE,CAACge,OAAO,CAACrd,MAAM,CAAC;MACrC;MACA0H,UAAU,CAACC,OAAO,CAACyV,QAAQ,CAAC3mB,MAAM,CAACynB,GAAG,CAAC,CAAC;IAC1C;IACAJ,QAAQ,CAAC9d,MAAM,GAAG,CAAC;IACnB+d,OAAO,GAAG,IAAI;EAChB;EACA,OAAO,IAAIK,eAAe,CAAC;IACzBC,SAASA,CAACJ,KAAK,EAAEvW,UAAU,EAAE;MAC3BoW,QAAQ,CAACzY,IAAI,CAAC4Y,KAAK,CAAC;MACpB,IAAIF,OAAO,EAAE;QACX;MACF;MACAA,OAAO,GAAGlR,UAAU,CAAC,YAAY;QAC/BmR,mBAAmB,CAACtW,UAAU,CAAC;QAC/B,IAAI,CAACmW,UAAU,EAAE;UACfA,UAAU,GAAG,IAAI;UACjBS,cAAc,CAACf,SAAS,EAAE7V,UAAU,CAAC,CAAC6W,KAAK,CAAEhT,GAAG,IAAK7D,UAAU,CAAChI,KAAK,CAAC6L,GAAG,CAAC,CAAC,CAACiT,IAAI,CAACf,wBAAwB,CAAC;QAC5G;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IACD,MAAMgB,KAAKA,CAAC/W,UAAU,EAAE;MACtB,MAAMgW,iBAAiB;MACvB,IAAIK,OAAO,EAAE;QACX/Q,YAAY,CAAC+Q,OAAO,CAAC;QACrBC,mBAAmB,CAACtW,UAAU,CAAC;MACjC;MACAA,UAAU,CAACC,OAAO,CAACyV,QAAQ,CAAC3mB,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACvD;EACF,CAAC,CAAC;AACJ;AACA,eAAe6nB,cAAcA,CAACf,SAAS,EAAE7V,UAAU,EAAE;EACnD,IAAI8V,OAAO,GAAG,IAAIziB,WAAW,CAAC,OAAO,EAAE;IAAE2jB,KAAK,EAAE;EAAK,CAAC,CAAC;EACvD,MAAM9jB,MAAM,GAAG2iB,SAAS,CAAC1iB,SAAS,CAAC,CAAC;EACpC,IAAI;IACF,IAAI8jB,IAAI;IACR,OAAO,CAACA,IAAI,GAAG,MAAM/jB,MAAM,CAAC+jB,IAAI,CAAC,CAAC,KAAK,CAACA,IAAI,CAACC,IAAI,EAAE;MACjD,MAAMX,KAAK,GAAGU,IAAI,CAAC1kB,KAAK;MACxB,IAAI;QACF4kB,UAAU,CACRhd,IAAI,CAACC,SAAS,CAAC0b,OAAO,CAACW,MAAM,CAACF,KAAK,EAAE;UAAE1W,MAAM,EAAE;QAAK,CAAC,CAAC,CAAC,EACvDG,UACF,CAAC;MACH,CAAC,CAAC,OAAO6D,GAAG,EAAE;QACZ,IAAIuT,MAAM,GAAGjd,IAAI,CAACC,SAAS,CAAChD,IAAI,CAACC,MAAM,CAACggB,aAAa,CAAC,GAAGd,KAAK,CAAC,CAAC,CAAC;QACjEY,UAAU,CACR,wBAAwBC,MAAM,2BAA2B,EACzDpX,UACF,CAAC;MACH;IACF;EACF,CAAC,SAAS;IACR9M,MAAM,CAACokB,WAAW,CAAC,CAAC;EACtB;EACA,IAAIC,SAAS,GAAGzB,OAAO,CAACW,MAAM,CAAC,CAAC;EAChC,IAAIc,SAAS,CAACjf,MAAM,EAAE;IACpB6e,UAAU,CAAChd,IAAI,CAACC,SAAS,CAACmd,SAAS,CAAC,EAAEvX,UAAU,CAAC;EACnD;AACF;AACA,SAASmX,UAAUA,CAACZ,KAAK,EAAEvW,UAAU,EAAE;EACrCA,UAAU,CAACC,OAAO,CAChByV,QAAQ,CAAC3mB,MAAM,CACb,WAAWyoB,YAAY,CACrB,kCAAkCjB,KAAK,GACzC,CAAC,WACH,CACF,CAAC;AACH;AACA,SAASiB,YAAYA,CAACC,MAAM,EAAE;EAC5B,OAAOA,MAAM,CAAC3nB,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,eAAe,EAAE,QAAQ,CAAC;AAC7E;;AAEA;AACA,IAAI4nB,SAAS,GAAG,KAAK;AACrB,IAAIC,OAAO,GAAGlC,MAAM,CAACiC,SAAS,CAAC;AAC/B,SAASE,OAAOA,CAACC,OAAO,EAAE;EACxB,IAAIF,OAAO,EAAE;IACX,OAAOA,OAAO,CAACE,OAAO,CAAC;EACzB;EACA,MAAM,IAAI/iB,KAAK,CAAC,sDAAsD,CAAC;AACzE;AACA,eAAegjB,qBAAqBA,CAAC;EACnC/a,OAAO;EACPgb,WAAW;EACXnK,wBAAwB;EACxBoK,UAAU;EACVllB,OAAO,GAAG;AACZ,CAAC,EAAE;EACD,MAAMhC,GAAG,GAAG,IAAIE,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;EAChC,MAAMmnB,aAAa,GAAGC,oBAAoB,CAACpnB,GAAG,CAAC;EAC/C,MAAMqnB,qBAAqB,GAAGF,aAAa,IAAIG,iBAAiB,CAACtnB,GAAG,CAAC,IAAIiM,OAAO,CAACiB,OAAO,CAAC0E,GAAG,CAAC,eAAe,CAAC;EAC7G,MAAM2V,cAAc,GAAG,MAAMN,WAAW,CAAChb,OAAO,CAAC;EACjD,IAAIob,qBAAqB,IAAIE,cAAc,CAACra,OAAO,CAACiB,GAAG,CAAC,uBAAuB,CAAC,KAAK,MAAM,EAAE;IAC3F,OAAOoZ,cAAc;EACvB;EACA,IAAI,CAACA,cAAc,CAACta,IAAI,EAAE;IACxB,MAAM,IAAIjJ,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACA,IAAIwjB,eAAe,GAAG,IAAI;EAC1B,IAAIxlB,OAAO,EAAE;IACXwlB,eAAe,GAAGD,cAAc,CAACE,KAAK,CAAC,CAAC;EAC1C;EACA,MAAMxa,IAAI,GAAGsa,cAAc,CAACta,IAAI;EAChC,IAAIya,cAAc;EAClB,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAID,cAAc,EAAE,OAAOA,cAAc;IACzCA,cAAc,GAAG5K,wBAAwB,CAAC7P,IAAI,CAAC;IAC/C,OAAOya,cAAc;EACvB,CAAC;EACD,IAAI;IACF,MAAMlL,OAAO,GAAG,MAAMmL,UAAU,CAAC,CAAC;IAClC,IAAIJ,cAAc,CAAClb,MAAM,KAAKjP,4BAA4B,IAAIof,OAAO,CAACkB,IAAI,KAAK,UAAU,EAAE;MACzF,MAAMrM,QAAQ,GAAG,IAAIN,OAAO,CAACwW,cAAc,CAACra,OAAO,CAAC;MACpDmE,QAAQ,CAAC3E,MAAM,CAAC,kBAAkB,CAAC;MACnC2E,QAAQ,CAAC3E,MAAM,CAAC,gBAAgB,CAAC;MACjC2E,QAAQ,CAAC3E,MAAM,CAAC,cAAc,CAAC;MAC/B2E,QAAQ,CAAC3E,MAAM,CAAC,kBAAkB,CAAC;MACnC2E,QAAQ,CAACqC,GAAG,CAAC,UAAU,EAAE8I,OAAO,CAAC1a,QAAQ,CAAC;MAC1C,OAAO,IAAIwK,QAAQ,CAACkb,eAAe,EAAEva,IAAI,IAAI,EAAE,EAAE;QAC/CC,OAAO,EAAEmE,QAAQ;QACjBhF,MAAM,EAAEmQ,OAAO,CAACnQ,MAAM;QACtBsI,UAAU,EAAE4S,cAAc,CAAC5S;MAC7B,CAAC,CAAC;IACJ;IACA,MAAM/E,IAAI,GAAG,MAAMsX,UAAU,CAACS,UAAU,CAAC;IACzC,MAAMza,OAAO,GAAG,IAAI6D,OAAO,CAACwW,cAAc,CAACra,OAAO,CAAC;IACnDA,OAAO,CAACwG,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;IACxC,IAAI,CAAC1R,OAAO,EAAE;MACZ,OAAO,IAAIsK,QAAQ,CAACsD,IAAI,EAAE;QACxBvD,MAAM,EAAEkb,cAAc,CAAClb,MAAM;QAC7Ba;MACF,CAAC,CAAC;IACJ;IACA,IAAI,CAACsa,eAAe,EAAEva,IAAI,EAAE;MAC1B,MAAM,IAAIjJ,KAAK,CAAC,iCAAiC,CAAC;IACpD;IACA,MAAM4jB,KAAK,GAAGhY,IAAI,CAACiY,WAAW,CAAC/C,gBAAgB,CAAC0C,eAAe,CAACva,IAAI,CAAC,CAAC;IACtE,OAAO,IAAIX,QAAQ,CAACsb,KAAK,EAAE;MACzBvb,MAAM,EAAEkb,cAAc,CAAClb,MAAM;MAC7Ba;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAO4a,MAAM,EAAE;IACf,IAAIA,MAAM,YAAYxb,QAAQ,EAAE;MAC9B,OAAOwb,MAAM;IACf;IACA,MAAMA,MAAM;EACd;AACF;AACA,SAASC,eAAeA,CAAC;EAAEJ;AAAW,CAAC,EAAE;EACvC,MAAMnL,OAAO,GAAGsK,OAAO,CAACa,UAAU,CAAC,CAAC,CAAC;EACrC,IAAInL,OAAO,CAACkB,IAAI,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAIpR,QAAQ,CAAC,IAAI,EAAE;MACvBD,MAAM,EAAEmQ,OAAO,CAACnQ,MAAM;MACtBa,OAAO,EAAE;QACP8a,QAAQ,EAAExL,OAAO,CAAC1a;MACpB;IACF,CAAC,CAAC;EACJ;EACA,IAAI0a,OAAO,CAACkB,IAAI,KAAK,QAAQ,EAAE,OAAO,IAAI;EAC1C,IAAIuK,iBAAiB,GAAG;IAAE,GAAGzL,OAAO,CAAC7b;EAAW,CAAC;EACjD,KAAK,MAAMC,KAAK,IAAI4b,OAAO,CAAC3b,OAAO,EAAE;IACnC,IAAI3B,wBAAwB,CAC1B0B,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACM,YAAY,EAClBN,KAAK,CAACO,SAAS,EACf,KACF,CAAC,KAAKP,KAAK,CAACie,sBAAsB,IAAI,CAACje,KAAK,CAACO,SAAS,CAAC,EAAE;MACvD,OAAO8mB,iBAAiB,CAACrnB,KAAK,CAACI,EAAE,CAAC;IACpC;EACF;EACA,MAAMjB,OAAO,GAAG;IACd2Q,UAAU,EAAE8L,OAAO,CAAC9L,UAAU;IAC9BD,aAAa,EAAE,CAAC,CAAC;IACjB5E,QAAQ,EAAE2Q,OAAO,CAAC3Q,QAAQ;IAC1BZ,MAAM,EAAEuR,OAAO,CAACvR,MAAM;IACtBtK,UAAU,EAAEsnB,iBAAiB;IAC7BtX,aAAa,EAAE,CAAC,CAAC;IACjB7O,QAAQ,EAAE0a,OAAO,CAAC1a,QAAQ;IAC1BgR,UAAU,EAAE,GAAG;IACfjS,OAAO,EAAE2b,OAAO,CAAC3b,OAAO,CAACkD,GAAG,CAAEnD,KAAK,KAAM;MACvCkL,MAAM,EAAElL,KAAK,CAACkL,MAAM;MACpBF,QAAQ,EAAEhL,KAAK,CAACgL,QAAQ;MACxBsc,YAAY,EAAEtnB,KAAK,CAACsnB,YAAY;MAChCnnB,KAAK,EAAE;QACLC,EAAE,EAAEJ,KAAK,CAACI,EAAE;QACZsD,MAAM,EAAE1D,KAAK,CAACgE,SAAS,IAAI,CAAC,CAAChE,KAAK,CAACqf,YAAY;QAC/Cxb,MAAM,EAAE7D,KAAK,CAAC6D,MAAM;QACpBO,gBAAgB,EAAEpE,KAAK,CAACoE,gBAAgB;QACxCR,MAAM,EAAE5D,KAAK,CAACO,SAAS,IAAI,CAAC,CAACP,KAAK,CAACM,YAAY;QAC/CiD,KAAK,EAAEvD,KAAK,CAACuD,KAAK;QAClBD,IAAI,EAAEtD,KAAK,CAACsD,IAAI;QAChBQ,gBAAgB,EAAE9D,KAAK,CAAC8D;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EACD,MAAMrD,MAAM,GAAGtD,kBAAkB,CAC/Bye,OAAO,CAAC3b,OAAO,CAAC2d,WAAW,CAAC,CAACC,QAAQ,EAAE7d,KAAK,KAAK;IAC/C,MAAMG,KAAK,GAAG;MACZC,EAAE,EAAEJ,KAAK,CAACI,EAAE;MACZsD,MAAM,EAAE1D,KAAK,CAACgE,SAAS,IAAI,CAAC,CAAChE,KAAK,CAACqf,YAAY;MAC/CC,OAAO,EAAEtf,KAAK,CAACsf,OAAO;MACtBC,YAAY,EAAEvf,KAAK,CAACuf,YAAY;MAChC1b,MAAM,EAAE7D,KAAK,CAAC6D,MAAM;MACpBO,gBAAgB,EAAE,CAAC,CAACpE,KAAK,CAACuf,YAAY;MACtCtB,sBAAsB,EAAEje,KAAK,CAACie,sBAAsB;MACpD1a,KAAK,EAAEvD,KAAK,CAACuD,KAAK;MAClBK,MAAM,EAAE5D,KAAK,CAACO,SAAS,IAAI,CAAC,CAACP,KAAK,CAACM,YAAY;MAC/CgD,IAAI,EAAEtD,KAAK,CAACsD,IAAI;MAChBQ,gBAAgB,EAAE9D,KAAK,CAAC8D;IAC1B,CAAC;IACD,IAAI+Z,QAAQ,CAACjX,MAAM,GAAG,CAAC,EAAE;MACvBzG,KAAK,CAACyE,QAAQ,GAAGiZ,QAAQ;IAC3B;IACA,OAAO,CAAC1d,KAAK,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC,EACNhB,OACF,CAAC;EACD,MAAMgjB,gBAAgB,GAAG;IACvBviB,MAAM,EAAE;MACN;MACA;MACA4C,mBAAmB,EAAE,KAAK;MAC1BD,6BAA6B,EAAE;IACjC,CAAC;IACD1C,SAAS,EAAE,KAAK;IAChBiB,GAAG,EAAE,IAAI;IACTrB,WAAW,EAAE,EAAE;IACfF,QAAQ,EAAE;MACRI,MAAM,EAAE,CAAC,CAAC;MACViD,OAAO,EAAE,GAAG;MACZxD,GAAG,EAAE,EAAE;MACPqD,KAAK,EAAE;QACLE,MAAM,EAAE,EAAE;QACVD,OAAO,EAAE;MACX;IACF,CAAC;IACD3B,cAAc,EAAE;MAAE8B,IAAI,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAc,CAAC;IAC7DtD,YAAY,EAAEmc,qBAAqB,CAACC,OAAO;EAC7C,CAAC;EACD,OAAO,eAAgBmI,MAAM,CAACrjB,aAAa,CAACrE,gBAAgB,CAACuE,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAAE,eAAgBkjB,MAAM,CAACrjB,aAAa,CAACsZ,4BAA4B,EAAE;IAAE9Y,QAAQ,EAAE0a,OAAO,CAAC1a;EAAS,CAAC,EAAE,eAAgB6iB,MAAM,CAACrjB,aAAa,CAACxE,gBAAgB,CAAC0E,QAAQ,EAAE;IAAEC,KAAK,EAAEshB;EAAiB,CAAC,EAAE,eAAgB4B,MAAM,CAACrjB,aAAa,CAC1ThE,oBAAoB,EACpB;IACEyC,OAAO;IACPsB,MAAM;IACNW,OAAO,EAAE,KAAK;IACd/B,KAAK,EAAEuc,OAAO,CAACvc;EACjB,CACF,CAAC,CAAC,CAAC,CAAC;AACN;AACA,SAASmnB,oBAAoBA,CAACpnB,GAAG,EAAE;EACjC,OAAOA,GAAG,CAAC4L,QAAQ,CAAC+J,QAAQ,CAAC,MAAM,CAAC;AACtC;AACA,SAAS2R,iBAAiBA,CAACtnB,GAAG,EAAE;EAC9B,OAAOA,GAAG,CAAC4L,QAAQ,CAAC+J,QAAQ,CAAC,WAAW,CAAC;AAC3C;;AAEA;AACA,SAASwS,YAAYA,CAAA,EAAG;EACtB,IAAIC,QAAQ,GAAG,IAAIxiB,WAAW,CAAC,CAAC;EAChC,IAAIyiB,gBAAgB,GAAG,IAAI;EAC3B,IAAItD,SAAS,GAAG,IAAI/V,cAAc,CAAC;IACjCC,KAAKA,CAACC,UAAU,EAAE;MAChB,IAAI,OAAOkO,MAAM,KAAK,WAAW,EAAE;QACjC;MACF;MACA,IAAIkL,WAAW,GAAI7C,KAAK,IAAK;QAC3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC7BvW,UAAU,CAACC,OAAO,CAACiZ,QAAQ,CAACnqB,MAAM,CAACwnB,KAAK,CAAC,CAAC;QAC5C,CAAC,MAAM;UACLvW,UAAU,CAACC,OAAO,CAACsW,KAAK,CAAC;QAC3B;MACF,CAAC;MACDrI,MAAM,CAACmL,aAAa,KAAKnL,MAAM,CAACmL,aAAa,GAAG,EAAE,CAAC;MACnDnL,MAAM,CAACmL,aAAa,CAACha,OAAO,CAAC+Z,WAAW,CAAC;MACzClL,MAAM,CAACmL,aAAa,CAAC1b,IAAI,GAAI4Y,KAAK,IAAK;QACrC6C,WAAW,CAAC7C,KAAK,CAAC;QAClB,OAAO,CAAC;MACV,CAAC;MACD4C,gBAAgB,GAAGnZ,UAAU;IAC/B;EACF,CAAC,CAAC;EACF,IAAI,OAAOgT,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAACsG,UAAU,KAAK,SAAS,EAAE;IACxEtG,QAAQ,CAAC3N,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;MAClD8T,gBAAgB,EAAEjZ,KAAK,CAAC,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,MAAM;IACLiZ,gBAAgB,EAAEjZ,KAAK,CAAC,CAAC;EAC3B;EACA,OAAO2V,SAAS;AAClB;;AAEA;AACA,SAAS0D,iBAAiBA,CAACxd,MAAM,EAAE;EACjC,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIC,OAAO,GAAGb,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC;EACpC,IAAIM,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI,CAACvF,GAAG,EAAEwF,GAAG,CAAC,IAAIN,OAAO,EAAE;IAC9B,IAAIM,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,oBAAoB,EAAE;MAC9CF,UAAU,CAACvF,GAAG,CAAC,GAAG,IAAInJ,iBAAiB,CACrC2O,GAAG,CAACa,MAAM,EACVb,GAAG,CAACmJ,UAAU,EACdnJ,GAAG,CAAC+D,IAAI,EACR/D,GAAG,CAACkd,QAAQ,KAAK,IACnB,CAAC;IACH,CAAC,MAAM,IAAIld,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,OAAO,EAAE;MACxC,IAAID,GAAG,CAACE,SAAS,EAAE;QACjB,IAAIid,gBAAgB,GAAGvL,MAAM,CAAC5R,GAAG,CAACE,SAAS,CAAC;QAC5C,IAAI,OAAOid,gBAAgB,KAAK,UAAU,EAAE;UAC1C,IAAI;YACF,IAAIzhB,KAAK,GAAG,IAAIyhB,gBAAgB,CAACnd,GAAG,CAACH,OAAO,CAAC;YAC7CnE,KAAK,CAAC6D,KAAK,GAAGS,GAAG,CAACT,KAAK;YACvBQ,UAAU,CAACvF,GAAG,CAAC,GAAGkB,KAAK;UACzB,CAAC,CAAC,OAAOkH,CAAC,EAAE,CACZ;QACF;MACF;MACA,IAAI7C,UAAU,CAACvF,GAAG,CAAC,IAAI,IAAI,EAAE;QAC3B,IAAIkB,KAAK,GAAG,IAAIlD,KAAK,CAACwH,GAAG,CAACH,OAAO,CAAC;QAClCnE,KAAK,CAAC6D,KAAK,GAAGS,GAAG,CAACT,KAAK;QACvBQ,UAAU,CAACvF,GAAG,CAAC,GAAGkB,KAAK;MACzB;IACF,CAAC,MAAM;MACLqE,UAAU,CAACvF,GAAG,CAAC,GAAGwF,GAAG;IACvB;EACF;EACA,OAAOD,UAAU;AACnB;AAEA,SACEzL,YAAY,EACZ2C,gBAAgB,EAChBkF,YAAY,EACZkB,QAAQ,EACR4B,UAAU,EACViD,iBAAiB,EACjBwH,oBAAoB,EACpBsD,aAAa,EACbK,SAAS,EACTC,oBAAoB,EACpBW,0BAA0B,EAC1BG,0BAA0B,EAC1BtM,IAAI,EACJ+M,gBAAgB,EAChBgC,2BAA2B,EAC3BQ,gBAAgB,EAChBoE,iBAAiB,EACjB+F,qBAAqB,EACrBe,eAAe,EACfI,YAAY,EACZM,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}