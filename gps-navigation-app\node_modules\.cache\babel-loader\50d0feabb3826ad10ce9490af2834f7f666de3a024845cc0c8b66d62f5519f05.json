{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getLatitude = _interopRequireDefault(require(\"./getLatitude\"));\nvar _getLongitude = _interopRequireDefault(require(\"./getLongitude\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar getBounds = function getBounds(points) {\n  if (Array.isArray(points) === false || points.length === 0) {\n    throw new Error(\"No points were given.\");\n  }\n  return points.reduce(function (stats, point) {\n    var latitude = (0, _getLatitude.default)(point);\n    var longitude = (0, _getLongitude.default)(point);\n    return {\n      maxLat: Math.max(latitude, stats.maxLat),\n      minLat: Math.min(latitude, stats.minLat),\n      maxLng: Math.max(longitude, stats.maxLng),\n      minLng: Math.min(longitude, stats.minLng)\n    };\n  }, {\n    maxLat: -Infinity,\n    minLat: Infinity,\n    maxLng: -Infinity,\n    minLng: Infinity\n  });\n};\nvar _default = getBounds;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getLatitude", "_interopRequireDefault", "require", "_getLongitude", "obj", "__esModule", "getBounds", "points", "Array", "isArray", "length", "Error", "reduce", "stats", "point", "latitude", "longitude", "maxLat", "Math", "max", "minLat", "min", "maxLng", "minLng", "Infinity", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getBounds.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getLatitude=_interopRequireDefault(require(\"./getLatitude\"));var _getLongitude=_interopRequireDefault(require(\"./getLongitude\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var getBounds=function getBounds(points){if(Array.isArray(points)===false||points.length===0){throw new Error(\"No points were given.\")}return points.reduce(function(stats,point){var latitude=(0,_getLatitude.default)(point);var longitude=(0,_getLongitude.default)(point);return{maxLat:Math.max(latitude,stats.maxLat),minLat:Math.min(latitude,stats.minLat),maxLng:Math.max(longitude,stats.maxLng),minLng:Math.min(longitude,stats.minLng)}},{maxLat:-Infinity,minLat:Infinity,maxLng:-Infinity,minLng:Infinity})};var _default=getBounds;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,IAAIC,aAAa,GAACF,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACG,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACL,OAAO,EAACK;EAAG,CAAC;AAAA;AAAC,IAAIE,SAAS,GAAC,SAASA,SAASA,CAACC,MAAM,EAAC;EAAC,IAAGC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,KAAG,KAAK,IAAEA,MAAM,CAACG,MAAM,KAAG,CAAC,EAAC;IAAC,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;EAAA;EAAC,OAAOJ,MAAM,CAACK,MAAM,CAAC,UAASC,KAAK,EAACC,KAAK,EAAC;IAAC,IAAIC,QAAQ,GAAC,CAAC,CAAC,EAACf,YAAY,CAACD,OAAO,EAAEe,KAAK,CAAC;IAAC,IAAIE,SAAS,GAAC,CAAC,CAAC,EAACb,aAAa,CAACJ,OAAO,EAAEe,KAAK,CAAC;IAAC,OAAM;MAACG,MAAM,EAACC,IAAI,CAACC,GAAG,CAACJ,QAAQ,EAACF,KAAK,CAACI,MAAM,CAAC;MAACG,MAAM,EAACF,IAAI,CAACG,GAAG,CAACN,QAAQ,EAACF,KAAK,CAACO,MAAM,CAAC;MAACE,MAAM,EAACJ,IAAI,CAACC,GAAG,CAACH,SAAS,EAACH,KAAK,CAACS,MAAM,CAAC;MAACC,MAAM,EAACL,IAAI,CAACG,GAAG,CAACL,SAAS,EAACH,KAAK,CAACU,MAAM;IAAC,CAAC;EAAA,CAAC,EAAC;IAACN,MAAM,EAAC,CAACO,QAAQ;IAACJ,MAAM,EAACI,QAAQ;IAACF,MAAM,EAAC,CAACE,QAAQ;IAACD,MAAM,EAACC;EAAQ,CAAC,CAAC;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACnB,SAAS;AAACT,OAAO,CAACE,OAAO,GAAC0B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}