{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getDistance = _interopRequireDefault(require(\"./getDistance\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar orderByDistance = function orderByDistance(point, coords) {\n  var distanceFn = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : _getDistance.default;\n  distanceFn = typeof distanceFn === \"function\" ? distanceFn : _getDistance.default;\n  return coords.slice().sort(function (a, b) {\n    return distanceFn(point, a) - distanceFn(point, b);\n  });\n};\nvar _default = orderByDistance;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getDistance", "_interopRequireDefault", "require", "obj", "__esModule", "orderByDistance", "point", "coords", "distanceFn", "arguments", "length", "undefined", "slice", "sort", "a", "b", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/orderByDistance.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getDistance=_interopRequireDefault(require(\"./getDistance\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var orderByDistance=function orderByDistance(point,coords){var distanceFn=arguments.length>2&&arguments[2]!==undefined?arguments[2]:_getDistance.default;distanceFn=typeof distanceFn===\"function\"?distanceFn:_getDistance.default;return coords.slice().sort(function(a,b){return distanceFn(point,a)-distanceFn(point,b)})};var _default=orderByDistance;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACE,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACJ,OAAO,EAACI;EAAG,CAAC;AAAA;AAAC,IAAIE,eAAe,GAAC,SAASA,eAAeA,CAACC,KAAK,EAACC,MAAM,EAAC;EAAC,IAAIC,UAAU,GAACC,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAGE,SAAS,GAACF,SAAS,CAAC,CAAC,CAAC,GAACT,YAAY,CAACD,OAAO;EAACS,UAAU,GAAC,OAAOA,UAAU,KAAG,UAAU,GAACA,UAAU,GAACR,YAAY,CAACD,OAAO;EAAC,OAAOQ,MAAM,CAACK,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,UAASC,CAAC,EAACC,CAAC,EAAC;IAAC,OAAOP,UAAU,CAACF,KAAK,EAACQ,CAAC,CAAC,GAACN,UAAU,CAACF,KAAK,EAACS,CAAC,CAAC;EAAA,CAAC,CAAC;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACX,eAAe;AAACR,OAAO,CAACE,OAAO,GAACiB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}