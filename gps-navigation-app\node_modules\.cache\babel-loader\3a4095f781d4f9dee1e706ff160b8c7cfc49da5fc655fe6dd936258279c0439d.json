{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"children\"];\nimport { createElementObject, createLayerComponent, extendContext } from '@react-leaflet/core';\nimport { LayerGroup as LeafletLayerGroup } from 'leaflet';\nexport const LayerGroup = createLayerComponent(function createLayerGroup(_ref, ctx) {\n  let {\n      children: _c\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const group = new LeafletLayerGroup([], options);\n  return createElementObject(group, extendContext(ctx, {\n    layerContainer: group\n  }));\n});", "map": {"version": 3, "names": ["createElementObject", "createLayerComponent", "extendContext", "LayerGroup", "LeafletLayerGroup", "createLayerGroup", "_ref", "ctx", "children", "_c", "options", "_objectWithoutProperties", "_excluded", "group", "layerContainer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/LayerGroup.js"], "sourcesContent": ["import { createElementObject, createLayerComponent, extendContext } from '@react-leaflet/core';\nimport { LayerGroup as LeafletLayerGroup } from 'leaflet';\nexport const LayerGroup = createLayerComponent(function createLayerGroup({ children: _c, ...options }, ctx) {\n    const group = new LeafletLayerGroup([], options);\n    return createElementObject(group, extendContext(ctx, {\n        layerContainer: group\n    }));\n});\n"], "mappings": ";;AAAA,SAASA,mBAAmB,EAAEC,oBAAoB,EAAEC,aAAa,QAAQ,qBAAqB;AAC9F,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,SAAS;AACzD,OAAO,MAAMD,UAAU,GAAGF,oBAAoB,CAAC,SAASI,gBAAgBA,CAAAC,IAAA,EAA+BC,GAAG,EAAE;EAAA,IAAnC;MAAEC,QAAQ,EAAEC;IAAe,CAAC,GAAAH,IAAA;IAATI,OAAO,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EAC/F,MAAMC,KAAK,GAAG,IAAIT,iBAAiB,CAAC,EAAE,EAAEM,OAAO,CAAC;EAChD,OAAOV,mBAAmB,CAACa,KAAK,EAAEX,aAAa,CAACK,GAAG,EAAE;IACjDO,cAAc,EAAED;EACpB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}