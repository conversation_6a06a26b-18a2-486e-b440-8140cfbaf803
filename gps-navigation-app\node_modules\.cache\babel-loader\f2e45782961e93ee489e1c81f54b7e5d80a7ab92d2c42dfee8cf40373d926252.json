{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\GPS\\\\gps-navigation-app\\\\src\\\\components\\\\TestComponent.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestComponent = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      backgroundColor: '#1a1a1a',\n      color: 'white',\n      fontSize: '24px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDDFA\\uFE0F \\u0633\\u06CC\\u0633\\u062A\\u0645 GPS \\u0646\\u0627\\u0648\\u0628\\u0631\\u06CC\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u0633\\u06CC\\u0633\\u062A\\u0645 \\u0646\\u0627\\u0648\\u0628\\u0631\\u06CC GPS \\u0628\\u0631\\u0627\\u06CC \\u062A\\u0628\\u0644\\u062A \\u0622\\u0645\\u0627\\u062F\\u0647 \\u0627\\u0633\\u062A!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '20px',\n          fontSize: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2705 \\u0646\\u0642\\u0634\\u0647 \\u062A\\u0639\\u0627\\u0645\\u0644\\u06CC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2705 \\u062C\\u0633\\u062A\\u062C\\u0648\\u06CC \\u0645\\u06A9\\u0627\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2705 \\u0645\\u0633\\u06CC\\u0631\\u06CC\\u0627\\u0628\\u06CC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2705 \\u0631\\u0627\\u0628\\u0637 \\u06A9\\u0627\\u0631\\u0628\\u0631\\u06CC \\u0628\\u0647\\u06CC\\u0646\\u0647 \\u0634\\u062F\\u0647 \\u0628\\u0631\\u0627\\u06CC \\u062A\\u0628\\u0644\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2705 \\u067E\\u0634\\u062A\\u06CC\\u0628\\u0627\\u0646\\u06CC \\u0627\\u0632 \\u0632\\u0628\\u0627\\u0646 \\u0641\\u0627\\u0631\\u0633\\u06CC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = TestComponent;\nexport default TestComponent;\nvar _c;\n$RefreshReg$(_c, \"TestComponent\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "TestComponent", "style", "height", "display", "alignItems", "justifyContent", "backgroundColor", "color", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/src/components/TestComponent.tsx"], "sourcesContent": ["import React from 'react';\n\nconst TestComponent: React.FC = () => {\n  return (\n    <div style={{ \n      height: '100vh', \n      display: 'flex', \n      alignItems: 'center', \n      justifyContent: 'center',\n      backgroundColor: '#1a1a1a',\n      color: 'white',\n      fontSize: '24px'\n    }}>\n      <div>\n        <h1>🗺️ سیستم GPS ناوبری</h1>\n        <p>سیستم ناوبری GPS برای تبلت آماده است!</p>\n        <div style={{ marginTop: '20px', fontSize: '16px' }}>\n          <p>✅ نقشه تعاملی</p>\n          <p>✅ جستجوی مکان</p>\n          <p>✅ مسیریابی</p>\n          <p>✅ رابط کاربری بهینه شده برای تبلت</p>\n          <p>✅ پشتیبانی از زبان فارسی</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TestComponent;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EACpC,oBACED,OAAA;IAAKE,KAAK,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,eAAe,EAAE,SAAS;MAC1BC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,eACAV,OAAA;MAAAU,QAAA,gBACEV,OAAA;QAAAU,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7Bd,OAAA;QAAAU,QAAA,EAAG;MAAqC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5Cd,OAAA;QAAKE,KAAK,EAAE;UAAEa,SAAS,EAAE,MAAM;UAAEN,QAAQ,EAAE;QAAO,CAAE;QAAAC,QAAA,gBAClDV,OAAA;UAAAU,QAAA,EAAG;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpBd,OAAA;UAAAU,QAAA,EAAG;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpBd,OAAA;UAAAU,QAAA,EAAG;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjBd,OAAA;UAAAU,QAAA,EAAG;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxCd,OAAA;UAAAU,QAAA,EAAG;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GAxBIf,aAAuB;AA0B7B,eAAeA,aAAa;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}