{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getLatitude = _interopRequireDefault(require(\"./getLatitude\"));\nvar _getLongitude = _interopRequireDefault(require(\"./getLongitude\"));\nvar _toRad = _interopRequireDefault(require(\"./toRad\"));\nvar _robustAcos = _interopRequireDefault(require(\"./robustAcos\"));\nvar _constants = require(\"./constants\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar getDistance = function getDistance(from, to) {\n  var accuracy = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  accuracy = typeof accuracy !== \"undefined\" && !isNaN(accuracy) ? accuracy : 1;\n  var fromLat = (0, _getLatitude.default)(from);\n  var fromLon = (0, _getLongitude.default)(from);\n  var toLat = (0, _getLatitude.default)(to);\n  var toLon = (0, _getLongitude.default)(to);\n  var distance = Math.acos((0, _robustAcos.default)(Math.sin((0, _toRad.default)(toLat)) * Math.sin((0, _toRad.default)(fromLat)) + Math.cos((0, _toRad.default)(toLat)) * Math.cos((0, _toRad.default)(fromLat)) * Math.cos((0, _toRad.default)(fromLon) - (0, _toRad.default)(toLon)))) * _constants.earthRadius;\n  return Math.round(distance / accuracy) * accuracy;\n};\nvar _default = getDistance;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getLatitude", "_interopRequireDefault", "require", "_getLongitude", "_toRad", "_robustAcos", "_constants", "obj", "__esModule", "getDistance", "from", "to", "accuracy", "arguments", "length", "undefined", "isNaN", "fromLat", "fromLon", "toLat", "toLon", "distance", "Math", "acos", "sin", "cos", "earthRadius", "round", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getDistance.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getLatitude=_interopRequireDefault(require(\"./getLatitude\"));var _getLongitude=_interopRequireDefault(require(\"./getLongitude\"));var _toRad=_interopRequireDefault(require(\"./toRad\"));var _robustAcos=_interopRequireDefault(require(\"./robustAcos\"));var _constants=require(\"./constants\");function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var getDistance=function getDistance(from,to){var accuracy=arguments.length>2&&arguments[2]!==undefined?arguments[2]:1;accuracy=typeof accuracy!==\"undefined\"&&!isNaN(accuracy)?accuracy:1;var fromLat=(0,_getLatitude.default)(from);var fromLon=(0,_getLongitude.default)(from);var toLat=(0,_getLatitude.default)(to);var toLon=(0,_getLongitude.default)(to);var distance=Math.acos((0,_robustAcos.default)(Math.sin((0,_toRad.default)(toLat))*Math.sin((0,_toRad.default)(fromLat))+Math.cos((0,_toRad.default)(toLat))*Math.cos((0,_toRad.default)(fromLat))*Math.cos((0,_toRad.default)(fromLon)-(0,_toRad.default)(toLon))))*_constants.earthRadius;return Math.round(distance/accuracy)*accuracy};var _default=getDistance;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,IAAIC,aAAa,GAACF,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAAC,IAAIE,MAAM,GAACH,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAAC,IAAIG,WAAW,GAACJ,sBAAsB,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;AAAC,IAAII,UAAU,GAACJ,OAAO,CAAC,aAAa,CAAC;AAAC,SAASD,sBAAsBA,CAACM,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACR,OAAO,EAACQ;EAAG,CAAC;AAAA;AAAC,IAAIE,WAAW,GAAC,SAASA,WAAWA,CAACC,IAAI,EAACC,EAAE,EAAC;EAAC,IAAIC,QAAQ,GAACC,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAGE,SAAS,GAACF,SAAS,CAAC,CAAC,CAAC,GAAC,CAAC;EAACD,QAAQ,GAAC,OAAOA,QAAQ,KAAG,WAAW,IAAE,CAACI,KAAK,CAACJ,QAAQ,CAAC,GAACA,QAAQ,GAAC,CAAC;EAAC,IAAIK,OAAO,GAAC,CAAC,CAAC,EAACjB,YAAY,CAACD,OAAO,EAAEW,IAAI,CAAC;EAAC,IAAIQ,OAAO,GAAC,CAAC,CAAC,EAACf,aAAa,CAACJ,OAAO,EAAEW,IAAI,CAAC;EAAC,IAAIS,KAAK,GAAC,CAAC,CAAC,EAACnB,YAAY,CAACD,OAAO,EAAEY,EAAE,CAAC;EAAC,IAAIS,KAAK,GAAC,CAAC,CAAC,EAACjB,aAAa,CAACJ,OAAO,EAAEY,EAAE,CAAC;EAAC,IAAIU,QAAQ,GAACC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAClB,WAAW,CAACN,OAAO,EAAEuB,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,EAACpB,MAAM,CAACL,OAAO,EAAEoB,KAAK,CAAC,CAAC,GAACG,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,EAACpB,MAAM,CAACL,OAAO,EAAEkB,OAAO,CAAC,CAAC,GAACK,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,EAACrB,MAAM,CAACL,OAAO,EAAEoB,KAAK,CAAC,CAAC,GAACG,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,EAACrB,MAAM,CAACL,OAAO,EAAEkB,OAAO,CAAC,CAAC,GAACK,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,EAACrB,MAAM,CAACL,OAAO,EAAEmB,OAAO,CAAC,GAAC,CAAC,CAAC,EAACd,MAAM,CAACL,OAAO,EAAEqB,KAAK,CAAC,CAAC,CAAC,CAAC,GAACd,UAAU,CAACoB,WAAW;EAAC,OAAOJ,IAAI,CAACK,KAAK,CAACN,QAAQ,GAACT,QAAQ,CAAC,GAACA,QAAQ;AAAA,CAAC;AAAC,IAAIgB,QAAQ,GAACnB,WAAW;AAACZ,OAAO,CAACE,OAAO,GAAC6B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}