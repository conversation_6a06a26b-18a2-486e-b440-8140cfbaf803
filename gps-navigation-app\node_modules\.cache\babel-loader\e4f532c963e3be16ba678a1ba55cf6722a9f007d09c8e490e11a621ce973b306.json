{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.areaConversion = exports.timeConversion = exports.distanceConversion = exports.altitudeKeys = exports.latitudeKeys = exports.longitudeKeys = exports.MAXLON = exports.MINLON = exports.MAXLAT = exports.MINLAT = exports.earthRadius = exports.sexagesimalPattern = void 0;\nvar sexagesimalPattern = /^([0-9]{1,3})°\\s*([0-9]{1,3}(?:\\.(?:[0-9]{1,}))?)['′]\\s*(([0-9]{1,3}(\\.([0-9]{1,}))?)[\"″]\\s*)?([NEOSW]?)$/;\nexports.sexagesimalPattern = sexagesimalPattern;\nvar earthRadius = 6378137;\nexports.earthRadius = earthRadius;\nvar MINLAT = -90;\nexports.MINLAT = MINLAT;\nvar MAXLAT = 90;\nexports.MAXLAT = MAXLAT;\nvar MINLON = -180;\nexports.MINLON = MINLON;\nvar MAXLON = 180;\nexports.MAXLON = MAXLON;\nvar longitudeKeys = [\"lng\", \"lon\", \"longitude\", 0];\nexports.longitudeKeys = longitudeKeys;\nvar latitudeKeys = [\"lat\", \"latitude\", 1];\nexports.latitudeKeys = latitudeKeys;\nvar altitudeKeys = [\"alt\", \"altitude\", \"elevation\", \"elev\", 2];\nexports.altitudeKeys = altitudeKeys;\nvar distanceConversion = {\n  m: 1,\n  km: 0.001,\n  cm: 100,\n  mm: 1000,\n  mi: 1 / 1609.344,\n  sm: 1 / 1852.216,\n  ft: 100 / 30.48,\n  in: 100 / 2.54,\n  yd: 1 / 0.9144\n};\nexports.distanceConversion = distanceConversion;\nvar timeConversion = {\n  m: 60,\n  h: 3600,\n  d: 86400\n};\nexports.timeConversion = timeConversion;\nvar areaConversion = {\n  m2: 1,\n  km2: 0.000001,\n  ha: 0.0001,\n  a: 0.01,\n  ft2: 10.763911,\n  yd2: 1.19599,\n  in2: 1550.0031\n};\nexports.areaConversion = areaConversion;\nareaConversion.sqm = areaConversion.m2;\nareaConversion.sqkm = areaConversion.km2;\nareaConversion.sqft = areaConversion.ft2;\nareaConversion.sqyd = areaConversion.yd2;\nareaConversion.sqin = areaConversion.in2;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "areaConversion", "timeConversion", "distanceConversion", "altitudeKeys", "latitudeKeys", "longitudeKeys", "MAXLON", "MINLON", "MAXLAT", "MINLAT", "earthRadius", "sexagesimalPattern", "m", "km", "cm", "mm", "mi", "sm", "ft", "in", "yd", "h", "d", "m2", "km2", "ha", "a", "ft2", "yd2", "in2", "sqm", "sqkm", "sqft", "sqyd", "sqin"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/constants.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.areaConversion=exports.timeConversion=exports.distanceConversion=exports.altitudeKeys=exports.latitudeKeys=exports.longitudeKeys=exports.MAXLON=exports.MINLON=exports.MAXLAT=exports.MINLAT=exports.earthRadius=exports.sexagesimalPattern=void 0;var sexagesimalPattern=/^([0-9]{1,3})°\\s*([0-9]{1,3}(?:\\.(?:[0-9]{1,}))?)['′]\\s*(([0-9]{1,3}(\\.([0-9]{1,}))?)[\"″]\\s*)?([NEOSW]?)$/;exports.sexagesimalPattern=sexagesimalPattern;var earthRadius=6378137;exports.earthRadius=earthRadius;var MINLAT=-90;exports.MINLAT=MINLAT;var MAXLAT=90;exports.MAXLAT=MAXLAT;var MINLON=-180;exports.MINLON=MINLON;var MAXLON=180;exports.MAXLON=MAXLON;var longitudeKeys=[\"lng\",\"lon\",\"longitude\",0];exports.longitudeKeys=longitudeKeys;var latitudeKeys=[\"lat\",\"latitude\",1];exports.latitudeKeys=latitudeKeys;var altitudeKeys=[\"alt\",\"altitude\",\"elevation\",\"elev\",2];exports.altitudeKeys=altitudeKeys;var distanceConversion={m:1,km:0.001,cm:100,mm:1000,mi:1/1609.344,sm:1/1852.216,ft:100/30.48,in:100/2.54,yd:1/0.9144};exports.distanceConversion=distanceConversion;var timeConversion={m:60,h:3600,d:86400};exports.timeConversion=timeConversion;var areaConversion={m2:1,km2:0.000001,ha:0.0001,a:0.01,ft2:10.763911,yd2:1.19599,in2:1550.0031};exports.areaConversion=areaConversion;areaConversion.sqm=areaConversion.m2;areaConversion.sqkm=areaConversion.km2;areaConversion.sqft=areaConversion.ft2;areaConversion.sqyd=areaConversion.yd2;areaConversion.sqin=areaConversion.in2;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,cAAc,GAACF,OAAO,CAACG,cAAc,GAACH,OAAO,CAACI,kBAAkB,GAACJ,OAAO,CAACK,YAAY,GAACL,OAAO,CAACM,YAAY,GAACN,OAAO,CAACO,aAAa,GAACP,OAAO,CAACQ,MAAM,GAACR,OAAO,CAACS,MAAM,GAACT,OAAO,CAACU,MAAM,GAACV,OAAO,CAACW,MAAM,GAACX,OAAO,CAACY,WAAW,GAACZ,OAAO,CAACa,kBAAkB,GAAC,KAAK,CAAC;AAAC,IAAIA,kBAAkB,GAAC,2GAA2G;AAACb,OAAO,CAACa,kBAAkB,GAACA,kBAAkB;AAAC,IAAID,WAAW,GAAC,OAAO;AAACZ,OAAO,CAACY,WAAW,GAACA,WAAW;AAAC,IAAID,MAAM,GAAC,CAAC,EAAE;AAACX,OAAO,CAACW,MAAM,GAACA,MAAM;AAAC,IAAID,MAAM,GAAC,EAAE;AAACV,OAAO,CAACU,MAAM,GAACA,MAAM;AAAC,IAAID,MAAM,GAAC,CAAC,GAAG;AAACT,OAAO,CAACS,MAAM,GAACA,MAAM;AAAC,IAAID,MAAM,GAAC,GAAG;AAACR,OAAO,CAACQ,MAAM,GAACA,MAAM;AAAC,IAAID,aAAa,GAAC,CAAC,KAAK,EAAC,KAAK,EAAC,WAAW,EAAC,CAAC,CAAC;AAACP,OAAO,CAACO,aAAa,GAACA,aAAa;AAAC,IAAID,YAAY,GAAC,CAAC,KAAK,EAAC,UAAU,EAAC,CAAC,CAAC;AAACN,OAAO,CAACM,YAAY,GAACA,YAAY;AAAC,IAAID,YAAY,GAAC,CAAC,KAAK,EAAC,UAAU,EAAC,WAAW,EAAC,MAAM,EAAC,CAAC,CAAC;AAACL,OAAO,CAACK,YAAY,GAACA,YAAY;AAAC,IAAID,kBAAkB,GAAC;EAACU,CAAC,EAAC,CAAC;EAACC,EAAE,EAAC,KAAK;EAACC,EAAE,EAAC,GAAG;EAACC,EAAE,EAAC,IAAI;EAACC,EAAE,EAAC,CAAC,GAAC,QAAQ;EAACC,EAAE,EAAC,CAAC,GAAC,QAAQ;EAACC,EAAE,EAAC,GAAG,GAAC,KAAK;EAACC,EAAE,EAAC,GAAG,GAAC,IAAI;EAACC,EAAE,EAAC,CAAC,GAAC;AAAM,CAAC;AAACtB,OAAO,CAACI,kBAAkB,GAACA,kBAAkB;AAAC,IAAID,cAAc,GAAC;EAACW,CAAC,EAAC,EAAE;EAACS,CAAC,EAAC,IAAI;EAACC,CAAC,EAAC;AAAK,CAAC;AAACxB,OAAO,CAACG,cAAc,GAACA,cAAc;AAAC,IAAID,cAAc,GAAC;EAACuB,EAAE,EAAC,CAAC;EAACC,GAAG,EAAC,QAAQ;EAACC,EAAE,EAAC,MAAM;EAACC,CAAC,EAAC,IAAI;EAACC,GAAG,EAAC,SAAS;EAACC,GAAG,EAAC,OAAO;EAACC,GAAG,EAAC;AAAS,CAAC;AAAC/B,OAAO,CAACE,cAAc,GAACA,cAAc;AAACA,cAAc,CAAC8B,GAAG,GAAC9B,cAAc,CAACuB,EAAE;AAACvB,cAAc,CAAC+B,IAAI,GAAC/B,cAAc,CAACwB,GAAG;AAACxB,cAAc,CAACgC,IAAI,GAAChC,cAAc,CAAC2B,GAAG;AAAC3B,cAAc,CAACiC,IAAI,GAACjC,cAAc,CAAC4B,GAAG;AAAC5B,cAAc,CAACkC,IAAI,GAAClC,cAAc,CAAC6B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}