{"ast": null, "code": "import { createElementObject, createLayerComponent, extendContext } from '@react-leaflet/core';\nimport { LayerGroup as LeafletLayerGroup } from 'leaflet';\nexport const LayerGroup = createLayerComponent(function createLayerGroup({\n  children: _c,\n  ...options\n}, ctx) {\n  const group = new LeafletLayerGroup([], options);\n  return createElementObject(group, extendContext(ctx, {\n    layerContainer: group\n  }));\n});", "map": {"version": 3, "names": ["createElementObject", "createLayerComponent", "extendContext", "LayerGroup", "LeafletLayerGroup", "createLayerGroup", "children", "_c", "options", "ctx", "group", "layerContainer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/LayerGroup.js"], "sourcesContent": ["import { createElementObject, createLayerComponent, extendContext } from '@react-leaflet/core';\nimport { LayerGroup as LeafletLayerGroup } from 'leaflet';\nexport const LayerGroup = createLayerComponent(function createLayerGroup({ children: _c, ...options }, ctx) {\n    const group = new LeafletLayerGroup([], options);\n    return createElementObject(group, extendContext(ctx, {\n        layerContainer: group\n    }));\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,oBAAoB,EAAEC,aAAa,QAAQ,qBAAqB;AAC9F,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,SAAS;AACzD,OAAO,MAAMD,UAAU,GAAGF,oBAAoB,CAAC,SAASI,gBAAgBA,CAAC;EAAEC,QAAQ,EAAEC,EAAE;EAAE,GAAGC;AAAQ,CAAC,EAAEC,GAAG,EAAE;EACxG,MAAMC,KAAK,GAAG,IAAIN,iBAAiB,CAAC,EAAE,EAAEI,OAAO,CAAC;EAChD,OAAOR,mBAAmB,CAACU,KAAK,EAAER,aAAa,CAACO,GAAG,EAAE;IACjDE,cAAc,EAAED;EACpB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}