{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nimport { bodyRegExps, namedReferences } from './named-references.js';\nimport { numericUnicodeMap } from './numeric-unicode-map.js';\nimport { fromCodePoint, getCodePoint } from './surrogate-pairs.js';\nvar allNamedReferences = __assign(__assign({}, namedReferences), {\n  all: namedReferences.html5\n});\nvar encodeRegExps = {\n  specialChars: /[<>'\"&]/g,\n  nonAscii: /[<>'\"&\\u0080-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g,\n  nonAsciiPrintable: /[<>'\"&\\x01-\\x08\\x11-\\x15\\x17-\\x1F\\x7f-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g,\n  nonAsciiPrintableOnly: /[\\x01-\\x08\\x11-\\x15\\x17-\\x1F\\x7f-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g,\n  extensive: /[\\x01-\\x0c\\x0e-\\x1f\\x21-\\x2c\\x2e-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\x7d\\x7f-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g\n};\nvar defaultEncodeOptions = {\n  mode: 'specialChars',\n  level: 'all',\n  numeric: 'decimal'\n};\n/** Encodes all the necessary (specified by `level`) characters in the text */\nexport function encode(text, _a) {\n  var _b = _a === void 0 ? defaultEncodeOptions : _a,\n    _c = _b.mode,\n    mode = _c === void 0 ? 'specialChars' : _c,\n    _d = _b.numeric,\n    numeric = _d === void 0 ? 'decimal' : _d,\n    _e = _b.level,\n    level = _e === void 0 ? 'all' : _e;\n  if (!text) {\n    return '';\n  }\n  var encodeRegExp = encodeRegExps[mode];\n  var references = allNamedReferences[level].characters;\n  var isHex = numeric === 'hexadecimal';\n  return String.prototype.replace.call(text, encodeRegExp, function (input) {\n    var result = references[input];\n    if (!result) {\n      var code = input.length > 1 ? getCodePoint(input, 0) : input.charCodeAt(0);\n      result = (isHex ? '&#x' + code.toString(16) : '&#' + code) + ';';\n    }\n    return result;\n  });\n}\nvar defaultDecodeOptions = {\n  scope: 'body',\n  level: 'all'\n};\nvar strict = /&(?:#\\d+|#[xX][\\da-fA-F]+|[0-9a-zA-Z]+);/g;\nvar attribute = /&(?:#\\d+|#[xX][\\da-fA-F]+|[0-9a-zA-Z]+)[;=]?/g;\nvar baseDecodeRegExps = {\n  xml: {\n    strict: strict,\n    attribute: attribute,\n    body: bodyRegExps.xml\n  },\n  html4: {\n    strict: strict,\n    attribute: attribute,\n    body: bodyRegExps.html4\n  },\n  html5: {\n    strict: strict,\n    attribute: attribute,\n    body: bodyRegExps.html5\n  }\n};\nvar decodeRegExps = __assign(__assign({}, baseDecodeRegExps), {\n  all: baseDecodeRegExps.html5\n});\nvar fromCharCode = String.fromCharCode;\nvar outOfBoundsChar = fromCharCode(65533);\nvar defaultDecodeEntityOptions = {\n  level: 'all'\n};\nfunction getDecodedEntity(entity, references, isAttribute, isStrict) {\n  var decodeResult = entity;\n  var decodeEntityLastChar = entity[entity.length - 1];\n  if (isAttribute && decodeEntityLastChar === '=') {\n    decodeResult = entity;\n  } else if (isStrict && decodeEntityLastChar !== ';') {\n    decodeResult = entity;\n  } else {\n    var decodeResultByReference = references[entity];\n    if (decodeResultByReference) {\n      decodeResult = decodeResultByReference;\n    } else if (entity[0] === '&' && entity[1] === '#') {\n      var decodeSecondChar = entity[2];\n      var decodeCode = decodeSecondChar == 'x' || decodeSecondChar == 'X' ? parseInt(entity.substr(3), 16) : parseInt(entity.substr(2));\n      decodeResult = decodeCode >= 0x10ffff ? outOfBoundsChar : decodeCode > 65535 ? fromCodePoint(decodeCode) : fromCharCode(numericUnicodeMap[decodeCode] || decodeCode);\n    }\n  }\n  return decodeResult;\n}\n/** Decodes a single entity */\nexport function decodeEntity(entity, _a) {\n  var _b = _a === void 0 ? defaultDecodeEntityOptions : _a,\n    _c = _b.level,\n    level = _c === void 0 ? 'all' : _c;\n  if (!entity) {\n    return '';\n  }\n  return getDecodedEntity(entity, allNamedReferences[level].entities, false, false);\n}\n/** Decodes all entities in the text */\nexport function decode(text, _a) {\n  var _b = _a === void 0 ? defaultDecodeOptions : _a,\n    _c = _b.level,\n    level = _c === void 0 ? 'all' : _c,\n    _d = _b.scope,\n    scope = _d === void 0 ? level === 'xml' ? 'strict' : 'body' : _d;\n  if (!text) {\n    return '';\n  }\n  var decodeRegExp = decodeRegExps[level][scope];\n  var references = allNamedReferences[level].entities;\n  var isAttribute = scope === 'attribute';\n  var isStrict = scope === 'strict';\n  return text.replace(decodeRegExp, function (entity) {\n    return getDecodedEntity(entity, references, isAttribute, isStrict);\n  });\n}", "map": {"version": 3, "names": ["bodyRegExps", "namedReferences", "numericUnicodeMap", "fromCodePoint", "getCodePoint", "allNamedReferences", "__assign", "all", "html5", "encodeRegExps", "specialChars", "non<PERSON><PERSON><PERSON>", "nonAsciiPrintable", "nonAsciiPrintableOnly", "extensive", "defaultEncodeOptions", "mode", "level", "numeric", "encode", "text", "_a", "_b", "_c", "_d", "_e", "encodeRegExp", "references", "characters", "isHex", "String", "prototype", "replace", "call", "input", "result", "code", "length", "charCodeAt", "toString", "defaultDecodeOptions", "scope", "strict", "attribute", "baseDecodeRegExps", "xml", "body", "html4", "decodeRegExps", "fromCharCode", "outOfBoundsChar", "defaultDecodeEntityOptions", "getDecodedEntity", "entity", "isAttribute", "isStrict", "decodeResult", "decodeEntityLastChar", "decodeResultByReference", "decodeSecondChar", "decodeCode", "parseInt", "substr", "decodeEntity", "entities", "decode", "decodeRegExp"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\node_modules\\html-entities\\src\\index.ts"], "sourcesContent": ["import {bodyRegExps, namedReferences} from './named-references.js';\nimport {numericUnicodeMap} from './numeric-unicode-map.js';\nimport {fromCodePoint, getCodePoint} from './surrogate-pairs.js';\n\nconst allNamedReferences = {\n    ...namedReferences,\n    all: namedReferences.html5\n};\n\nexport type Level = 'xml' | 'html4' | 'html5' | 'all';\n\ninterface CommonOptions {\n    level?: Level;\n}\n\nexport type EncodeMode = 'specialChars' | 'nonAscii' | 'nonAsciiPrintable' | 'nonAsciiPrintableOnly' | 'extensive';\n\nexport interface EncodeOptions extends CommonOptions {\n    mode?: EncodeMode;\n    numeric?: 'decimal' | 'hexadecimal';\n}\n\nexport type DecodeScope = 'strict' | 'body' | 'attribute';\n\nexport interface DecodeOptions extends CommonOptions {\n    scope?: DecodeScope;\n}\n\nconst encodeRegExps: Record<EncodeMode, RegExp> = {\n    specialChars: /[<>'\"&]/g,\n    nonAscii: /[<>'\"&\\u0080-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g,\n    nonAsciiPrintable: /[<>'\"&\\x01-\\x08\\x11-\\x15\\x17-\\x1F\\x7f-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g,\n    nonAsciiPrintableOnly: /[\\x01-\\x08\\x11-\\x15\\x17-\\x1F\\x7f-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g,\n    extensive: /[\\x01-\\x0c\\x0e-\\x1f\\x21-\\x2c\\x2e-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\x7d\\x7f-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g\n};\n\nconst defaultEncodeOptions: EncodeOptions = {\n    mode: 'specialChars',\n    level: 'all',\n    numeric: 'decimal'\n};\n\n/** Encodes all the necessary (specified by `level`) characters in the text */\nexport function encode(\n    text: string | undefined | null,\n    {mode = 'specialChars', numeric = 'decimal', level = 'all'}: EncodeOptions = defaultEncodeOptions\n) {\n    if (!text) {\n        return '';\n    }\n\n    const encodeRegExp = encodeRegExps[mode];\n    const references = allNamedReferences[level].characters;\n    const isHex = numeric === 'hexadecimal';\n\n    return String.prototype.replace.call(text, encodeRegExp, (input) => {\n        let result = references[input];\n        if (!result) {\n            const code = input.length > 1 ? getCodePoint(input, 0)! : input.charCodeAt(0);\n            result = (isHex ? '&#x' + code.toString(16) : '&#' + code) + ';';\n        }\n        return result;\n    });\n}\n\nconst defaultDecodeOptions: DecodeOptions = {\n    scope: 'body',\n    level: 'all'\n};\n\nconst strict = /&(?:#\\d+|#[xX][\\da-fA-F]+|[0-9a-zA-Z]+);/g;\nconst attribute = /&(?:#\\d+|#[xX][\\da-fA-F]+|[0-9a-zA-Z]+)[;=]?/g;\n\nconst baseDecodeRegExps: Record<Exclude<Level, 'all'>, Record<DecodeScope, RegExp>> = {\n    xml: {\n        strict,\n        attribute,\n        body: bodyRegExps.xml\n    },\n    html4: {\n        strict,\n        attribute,\n        body: bodyRegExps.html4\n    },\n    html5: {\n        strict,\n        attribute,\n        body: bodyRegExps.html5\n    }\n};\n\nconst decodeRegExps: Record<Level, Record<DecodeScope, RegExp>> = {\n    ...baseDecodeRegExps,\n    all: baseDecodeRegExps.html5\n};\n\nconst fromCharCode = String.fromCharCode;\nconst outOfBoundsChar = fromCharCode(65533);\n\nconst defaultDecodeEntityOptions: CommonOptions = {\n    level: 'all'\n};\n\nfunction getDecodedEntity(\n    entity: string,\n    references: Record<string, string>,\n    isAttribute: boolean,\n    isStrict: boolean\n): string {\n    let decodeResult = entity;\n    const decodeEntityLastChar = entity[entity.length - 1];\n    if (isAttribute && decodeEntityLastChar === '=') {\n        decodeResult = entity;\n    } else if (isStrict && decodeEntityLastChar !== ';') {\n        decodeResult = entity;\n    } else {\n        const decodeResultByReference = references[entity];\n        if (decodeResultByReference) {\n            decodeResult = decodeResultByReference;\n        } else if (entity[0] === '&' && entity[1] === '#') {\n            const decodeSecondChar = entity[2];\n            const decodeCode =\n                decodeSecondChar == 'x' || decodeSecondChar == 'X'\n                    ? parseInt(entity.substr(3), 16)\n                    : parseInt(entity.substr(2));\n\n            decodeResult =\n                decodeCode >= 0x10ffff\n                    ? outOfBoundsChar\n                    : decodeCode > 65535\n                    ? fromCodePoint(decodeCode)\n                    : fromCharCode(numericUnicodeMap[decodeCode] || decodeCode);\n        }\n    }\n    return decodeResult;\n}\n\n/** Decodes a single entity */\nexport function decodeEntity(\n    entity: string | undefined | null,\n    {level = 'all'}: CommonOptions = defaultDecodeEntityOptions\n): string {\n    if (!entity) {\n        return '';\n    }\n    return getDecodedEntity(entity, allNamedReferences[level].entities, false, false);\n}\n\n/** Decodes all entities in the text */\nexport function decode(\n    text: string | undefined | null,\n    {level = 'all', scope = level === 'xml' ? 'strict' : 'body'}: DecodeOptions = defaultDecodeOptions\n) {\n    if (!text) {\n        return '';\n    }\n\n    const decodeRegExp = decodeRegExps[level][scope];\n    const references = allNamedReferences[level].entities;\n    const isAttribute = scope === 'attribute';\n    const isStrict = scope === 'strict';\n\n    return text.replace(decodeRegExp, (entity) => getDecodedEntity(entity, references, isAttribute, isStrict));\n}\n"], "mappings": ";;;;;;;;;;AAAA,SAAQA,WAAW,EAAEC,eAAe,QAAO,uBAAuB;AAClE,SAAQC,iBAAiB,QAAO,0BAA0B;AAC1D,SAAQC,aAAa,EAAEC,YAAY,QAAO,sBAAsB;AAEhE,IAAMC,kBAAkB,GAAAC,QAAA,CAAAA,QAAA,KACjBL,eAAe;EAClBM,GAAG,EAAEN,eAAe,CAACO;AAAK,EAC7B;AAqBD,IAAMC,aAAa,GAA+B;EAC9CC,YAAY,EAAE,UAAU;EACxBC,QAAQ,EAAE,iFAAiF;EAC3FC,iBAAiB,EAAE,0GAA0G;EAC7HC,qBAAqB,EAAE,qGAAqG;EAC5HC,SAAS,EAAE;CACd;AAED,IAAMC,oBAAoB,GAAkB;EACxCC,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,KAAK;EACZC,OAAO,EAAE;CACZ;AAED;AACA,OAAM,SAAUC,MAAMA,CAClBC,IAA+B,EAC/BC,EAAiG;MAAjGC,EAAA,GAAAD,EAAA,cAA6EN,oBAAoB,GAAAM,EAAA;IAAhGE,EAAA,GAAAD,EAAA,CAAAN,IAAqB;IAArBA,IAAI,GAAAO,EAAA,cAAG,cAAc,GAAAA,EAAA;IAAEC,EAAA,GAAAF,EAAA,CAAAJ,OAAmB;IAAnBA,OAAO,GAAAM,EAAA,cAAG,SAAS,GAAAA,EAAA;IAAEC,EAAA,GAAAH,EAAA,CAAAL,KAAa;IAAbA,KAAK,GAAAQ,EAAA,cAAG,KAAK,GAAAA,EAAA;EAE1D,IAAI,CAACL,IAAI,EAAE;IACP,OAAO,EAAE;EACb;EAEA,IAAMM,YAAY,GAAGjB,aAAa,CAACO,IAAI,CAAC;EACxC,IAAMW,UAAU,GAAGtB,kBAAkB,CAACY,KAAK,CAAC,CAACW,UAAU;EACvD,IAAMC,KAAK,GAAGX,OAAO,KAAK,aAAa;EAEvC,OAAOY,MAAM,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAACb,IAAI,EAAEM,YAAY,EAAE,UAACQ,KAAK;IAC3D,IAAIC,MAAM,GAAGR,UAAU,CAACO,KAAK,CAAC;IAC9B,IAAI,CAACC,MAAM,EAAE;MACT,IAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,GAAG,CAAC,GAAGjC,YAAY,CAAC8B,KAAK,EAAE,CAAC,CAAE,GAAGA,KAAK,CAACI,UAAU,CAAC,CAAC,CAAC;MAC7EH,MAAM,GAAG,CAACN,KAAK,GAAG,KAAK,GAAGO,IAAI,CAACG,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGH,IAAI,IAAI,GAAG;IACpE;IACA,OAAOD,MAAM;EACjB,CAAC,CAAC;AACN;AAEA,IAAMK,oBAAoB,GAAkB;EACxCC,KAAK,EAAE,MAAM;EACbxB,KAAK,EAAE;CACV;AAED,IAAMyB,MAAM,GAAG,2CAA2C;AAC1D,IAAMC,SAAS,GAAG,+CAA+C;AAEjE,IAAMC,iBAAiB,GAA+D;EAClFC,GAAG,EAAE;IACDH,MAAM,EAAAA,MAAA;IACNC,SAAS,EAAAA,SAAA;IACTG,IAAI,EAAE9C,WAAW,CAAC6C;GACrB;EACDE,KAAK,EAAE;IACHL,MAAM,EAAAA,MAAA;IACNC,SAAS,EAAAA,SAAA;IACTG,IAAI,EAAE9C,WAAW,CAAC+C;GACrB;EACDvC,KAAK,EAAE;IACHkC,MAAM,EAAAA,MAAA;IACNC,SAAS,EAAAA,SAAA;IACTG,IAAI,EAAE9C,WAAW,CAACQ;;CAEzB;AAED,IAAMwC,aAAa,GAAA1C,QAAA,CAAAA,QAAA,KACZsC,iBAAiB;EACpBrC,GAAG,EAAEqC,iBAAiB,CAACpC;AAAK,EAC/B;AAED,IAAMyC,YAAY,GAAGnB,MAAM,CAACmB,YAAY;AACxC,IAAMC,eAAe,GAAGD,YAAY,CAAC,KAAK,CAAC;AAE3C,IAAME,0BAA0B,GAAkB;EAC9ClC,KAAK,EAAE;CACV;AAED,SAASmC,gBAAgBA,CACrBC,MAAc,EACd1B,UAAkC,EAClC2B,WAAoB,EACpBC,QAAiB;EAEjB,IAAIC,YAAY,GAAGH,MAAM;EACzB,IAAMI,oBAAoB,GAAGJ,MAAM,CAACA,MAAM,CAAChB,MAAM,GAAG,CAAC,CAAC;EACtD,IAAIiB,WAAW,IAAIG,oBAAoB,KAAK,GAAG,EAAE;IAC7CD,YAAY,GAAGH,MAAM;EACzB,CAAC,MAAM,IAAIE,QAAQ,IAAIE,oBAAoB,KAAK,GAAG,EAAE;IACjDD,YAAY,GAAGH,MAAM;EACzB,CAAC,MAAM;IACH,IAAMK,uBAAuB,GAAG/B,UAAU,CAAC0B,MAAM,CAAC;IAClD,IAAIK,uBAAuB,EAAE;MACzBF,YAAY,GAAGE,uBAAuB;IAC1C,CAAC,MAAM,IAAIL,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC/C,IAAMM,gBAAgB,GAAGN,MAAM,CAAC,CAAC,CAAC;MAClC,IAAMO,UAAU,GACZD,gBAAgB,IAAI,GAAG,IAAIA,gBAAgB,IAAI,GAAG,GAC5CE,QAAQ,CAACR,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAC9BD,QAAQ,CAACR,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC,CAAC;MAEpCN,YAAY,GACRI,UAAU,IAAI,QAAQ,GAChBV,eAAe,GACfU,UAAU,GAAG,KAAK,GAClBzD,aAAa,CAACyD,UAAU,CAAC,GACzBX,YAAY,CAAC/C,iBAAiB,CAAC0D,UAAU,CAAC,IAAIA,UAAU,CAAC;IACvE;EACJ;EACA,OAAOJ,YAAY;AACvB;AAEA;AACA,OAAM,SAAUO,YAAYA,CACxBV,MAAiC,EACjChC,EAA2D;MAA3DC,EAAA,GAAAD,EAAA,cAAiC8B,0BAA0B,GAAA9B,EAAA;IAA1DE,EAAA,GAAAD,EAAA,CAAAL,KAAa;IAAbA,KAAK,GAAAM,EAAA,cAAG,KAAK,GAAAA,EAAA;EAEd,IAAI,CAAC8B,MAAM,EAAE;IACT,OAAO,EAAE;EACb;EACA,OAAOD,gBAAgB,CAACC,MAAM,EAAEhD,kBAAkB,CAACY,KAAK,CAAC,CAAC+C,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC;AACrF;AAEA;AACA,OAAM,SAAUC,MAAMA,CAClB7C,IAA+B,EAC/BC,EAAkG;MAAlGC,EAAA,GAAAD,EAAA,cAA8EmB,oBAAoB,GAAAnB,EAAA;IAAjGE,EAAA,GAAAD,EAAA,CAAAL,KAAa;IAAbA,KAAK,GAAAM,EAAA,cAAG,KAAK,GAAAA,EAAA;IAAEC,EAAA,GAAAF,EAAA,CAAAmB,KAA2C;IAA3CA,KAAK,GAAAjB,EAAA,cAAGP,KAAK,KAAK,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAAO,EAAA;EAE3D,IAAI,CAACJ,IAAI,EAAE;IACP,OAAO,EAAE;EACb;EAEA,IAAM8C,YAAY,GAAGlB,aAAa,CAAC/B,KAAK,CAAC,CAACwB,KAAK,CAAC;EAChD,IAAMd,UAAU,GAAGtB,kBAAkB,CAACY,KAAK,CAAC,CAAC+C,QAAQ;EACrD,IAAMV,WAAW,GAAGb,KAAK,KAAK,WAAW;EACzC,IAAMc,QAAQ,GAAGd,KAAK,KAAK,QAAQ;EAEnC,OAAOrB,IAAI,CAACY,OAAO,CAACkC,YAAY,EAAE,UAACb,MAAM;IAAK,OAAAD,gBAAgB,CAACC,MAAM,EAAE1B,UAAU,EAAE2B,WAAW,EAAEC,QAAQ,CAAC;EAA3D,CAA2D,CAAC;AAC9G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}