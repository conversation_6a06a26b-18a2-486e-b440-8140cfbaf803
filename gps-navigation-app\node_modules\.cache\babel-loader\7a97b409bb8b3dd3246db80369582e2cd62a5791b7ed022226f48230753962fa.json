{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"url\"];\nimport { createElementObject, createTileLayerComponent, updateGridLayer, withPane } from '@react-leaflet/core';\nimport { TileLayer as LeafletTileLayer } from 'leaflet';\nexport const TileLayer = createTileLayerComponent(function createTileLayer(_ref, context) {\n  let {\n      url\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const layer = new LeafletTileLayer(url, withPane(options, context));\n  return createElementObject(layer, context);\n}, function updateTileLayer(layer, props, prevProps) {\n  updateGridLayer(layer, props, prevProps);\n  const {\n    url\n  } = props;\n  if (url != null && url !== prevProps.url) {\n    layer.setUrl(url);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createTileLayerComponent", "updateGridLayer", "with<PERSON>ane", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LeafletTileLayer", "createTileLayer", "_ref", "context", "url", "options", "_objectWithoutProperties", "_excluded", "layer", "updateTileLayer", "props", "prevProps", "setUrl"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/TileLayer.js"], "sourcesContent": ["import { createElementObject, createTileLayerComponent, updateGridLayer, withPane } from '@react-leaflet/core';\nimport { TileLayer as LeafletTileLayer } from 'leaflet';\nexport const TileLayer = createTileLayerComponent(function createTileLayer({ url, ...options }, context) {\n    const layer = new LeafletTileLayer(url, withPane(options, context));\n    return createElementObject(layer, context);\n}, function updateTileLayer(layer, props, prevProps) {\n    updateGridLayer(layer, props, prevProps);\n    const { url } = props;\n    if (url != null && url !== prevProps.url) {\n        layer.setUrl(url);\n    }\n});\n"], "mappings": ";;AAAA,SAASA,mBAAmB,EAAEC,wBAAwB,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,qBAAqB;AAC9G,SAASC,SAAS,IAAIC,gBAAgB,QAAQ,SAAS;AACvD,OAAO,MAAMD,SAAS,GAAGH,wBAAwB,CAAC,SAASK,eAAeA,CAAAC,IAAA,EAAsBC,OAAO,EAAE;EAAA,IAA9B;MAAEC;IAAgB,CAAC,GAAAF,IAAA;IAATG,OAAO,GAAAC,wBAAA,CAAAJ,IAAA,EAAAK,SAAA;EACxF,MAAMC,KAAK,GAAG,IAAIR,gBAAgB,CAACI,GAAG,EAAEN,QAAQ,CAACO,OAAO,EAAEF,OAAO,CAAC,CAAC;EACnE,OAAOR,mBAAmB,CAACa,KAAK,EAAEL,OAAO,CAAC;AAC9C,CAAC,EAAE,SAASM,eAAeA,CAACD,KAAK,EAAEE,KAAK,EAAEC,SAAS,EAAE;EACjDd,eAAe,CAACW,KAAK,EAAEE,KAAK,EAAEC,SAAS,CAAC;EACxC,MAAM;IAAEP;EAAI,CAAC,GAAGM,KAAK;EACrB,IAAIN,GAAG,IAAI,IAAI,IAAIA,GAAG,KAAKO,SAAS,CAACP,GAAG,EAAE;IACtCI,KAAK,CAACI,MAAM,CAACR,GAAG,CAAC;EACrB;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}