{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"bounds\", \"url\"];\nimport { createElementObject, createLayerComponent, extendContext, updateMediaOverlay } from '@react-leaflet/core';\nimport { LatLngBounds, ImageOverlay as LeafletImageOverlay } from 'leaflet';\nexport const ImageOverlay = createLayerComponent(function createImageOverlay(_ref, ctx) {\n  let {\n      bounds,\n      url\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const overlay = new LeafletImageOverlay(url, bounds, options);\n  return createElementObject(overlay, extendContext(ctx, {\n    overlayContainer: overlay\n  }));\n}, function updateImageOverlay(overlay, props, prevProps) {\n  updateMediaOverlay(overlay, props, prevProps);\n  if (props.bounds !== prevProps.bounds) {\n    const bounds = props.bounds instanceof LatLngBounds ? props.bounds : new LatLngBounds(props.bounds);\n    overlay.setBounds(bounds);\n  }\n  if (props.url !== prevProps.url) {\n    overlay.setUrl(props.url);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createLayerComponent", "extendContext", "updateMediaOverlay", "LatLngBounds", "ImageOverlay", "LeafletImageOverlay", "createImageOverlay", "_ref", "ctx", "bounds", "url", "options", "_objectWithoutProperties", "_excluded", "overlay", "overlayContainer", "updateImageOverlay", "props", "prevProps", "setBounds", "setUrl"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/ImageOverlay.js"], "sourcesContent": ["import { createElementObject, createLayerComponent, extendContext, updateMediaOverlay } from '@react-leaflet/core';\nimport { LatLngBounds, ImageOverlay as LeafletImageOverlay } from 'leaflet';\nexport const ImageOverlay = createLayerComponent(function createImageOverlay({ bounds, url, ...options }, ctx) {\n    const overlay = new LeafletImageOverlay(url, bounds, options);\n    return createElementObject(overlay, extendContext(ctx, {\n        overlayContainer: overlay\n    }));\n}, function updateImageOverlay(overlay, props, prevProps) {\n    updateMediaOverlay(overlay, props, prevProps);\n    if (props.bounds !== prevProps.bounds) {\n        const bounds = props.bounds instanceof LatLngBounds ? props.bounds : new LatLngBounds(props.bounds);\n        overlay.setBounds(bounds);\n    }\n    if (props.url !== prevProps.url) {\n        overlay.setUrl(props.url);\n    }\n});\n"], "mappings": ";;AAAA,SAASA,mBAAmB,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,kBAAkB,QAAQ,qBAAqB;AAClH,SAASC,YAAY,EAAEC,YAAY,IAAIC,mBAAmB,QAAQ,SAAS;AAC3E,OAAO,MAAMD,YAAY,GAAGJ,oBAAoB,CAAC,SAASM,kBAAkBA,CAAAC,IAAA,EAA8BC,GAAG,EAAE;EAAA,IAAlC;MAAEC,MAAM;MAAEC;IAAgB,CAAC,GAAAH,IAAA;IAATI,OAAO,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EAClG,MAAMC,OAAO,GAAG,IAAIT,mBAAmB,CAACK,GAAG,EAAED,MAAM,EAAEE,OAAO,CAAC;EAC7D,OAAOZ,mBAAmB,CAACe,OAAO,EAAEb,aAAa,CAACO,GAAG,EAAE;IACnDO,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASE,kBAAkBA,CAACF,OAAO,EAAEG,KAAK,EAAEC,SAAS,EAAE;EACtDhB,kBAAkB,CAACY,OAAO,EAAEG,KAAK,EAAEC,SAAS,CAAC;EAC7C,IAAID,KAAK,CAACR,MAAM,KAAKS,SAAS,CAACT,MAAM,EAAE;IACnC,MAAMA,MAAM,GAAGQ,KAAK,CAACR,MAAM,YAAYN,YAAY,GAAGc,KAAK,CAACR,MAAM,GAAG,IAAIN,YAAY,CAACc,KAAK,CAACR,MAAM,CAAC;IACnGK,OAAO,CAACK,SAAS,CAACV,MAAM,CAAC;EAC7B;EACA,IAAIQ,KAAK,CAACP,GAAG,KAAKQ,SAAS,CAACR,GAAG,EAAE;IAC7BI,OAAO,CAACM,MAAM,CAACH,KAAK,CAACP,GAAG,CAAC;EAC7B;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}