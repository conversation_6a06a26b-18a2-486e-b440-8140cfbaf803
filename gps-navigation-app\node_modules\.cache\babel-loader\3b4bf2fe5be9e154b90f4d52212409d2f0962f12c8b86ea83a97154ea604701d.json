{"ast": null, "code": "import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';\nimport { createPortal } from 'react-dom';\nimport { LeafletContext } from './context.js';\nexport function createContainerComponent(useElement) {\n  function ContainerComponent(props, forwardedRef) {\n    const {\n      instance,\n      context\n    } = useElement(props).current;\n    useImperativeHandle(forwardedRef, () => instance);\n    const {\n      children\n    } = props;\n    return children == null ? null : /*#__PURE__*/React.createElement(LeafletContext, {\n      value: context\n    }, children);\n  }\n  return /*#__PURE__*/forwardRef(ContainerComponent);\n}\nexport function createDivOverlayComponent(useElement) {\n  function OverlayComponent(props, forwardedRef) {\n    const [isOpen, setOpen] = useState(false);\n    const {\n      instance\n    } = useElement(props, setOpen).current;\n    useImperativeHandle(forwardedRef, () => instance);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: update overlay when children change\n    useEffect(function updateOverlay() {\n      if (isOpen) {\n        instance.update();\n      }\n    }, [instance, isOpen, props.children]);\n    // @ts-ignore _contentNode missing in type definition\n    const contentNode = instance._contentNode;\n    return contentNode ? /*#__PURE__*/createPortal(props.children, contentNode) : null;\n  }\n  return /*#__PURE__*/forwardRef(OverlayComponent);\n}\nexport function createLeafComponent(useElement) {\n  function LeafComponent(props, forwardedRef) {\n    const {\n      instance\n    } = useElement(props).current;\n    useImperativeHandle(forwardedRef, () => instance);\n    return null;\n  }\n  return /*#__PURE__*/forwardRef(LeafComponent);\n}", "map": {"version": 3, "names": ["React", "forwardRef", "useEffect", "useImperativeHandle", "useState", "createPortal", "LeafletContext", "createContainerComponent", "useElement", "ContainerComponent", "props", "forwardedRef", "instance", "context", "current", "children", "createElement", "value", "createDivOverlayComponent", "OverlayComponent", "isOpen", "<PERSON><PERSON><PERSON>", "updateOverlay", "update", "contentNode", "_contentNode", "createLeafComponent", "LeafComponent"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@react-leaflet/core/lib/component.js"], "sourcesContent": ["import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';\nimport { createPortal } from 'react-dom';\nimport { LeafletContext } from './context.js';\nexport function createContainerComponent(useElement) {\n    function ContainerComponent(props, forwardedRef) {\n        const { instance, context } = useElement(props).current;\n        useImperativeHandle(forwardedRef, ()=>instance);\n        const { children } = props;\n        return children == null ? null : /*#__PURE__*/ React.createElement(LeafletContext, {\n            value: context\n        }, children);\n    }\n    return /*#__PURE__*/ forwardRef(ContainerComponent);\n}\nexport function createDivOverlayComponent(useElement) {\n    function OverlayComponent(props, forwardedRef) {\n        const [isOpen, setOpen] = useState(false);\n        const { instance } = useElement(props, setOpen).current;\n        useImperativeHandle(forwardedRef, ()=>instance);\n        // biome-ignore lint/correctness/useExhaustiveDependencies: update overlay when children change\n        useEffect(function updateOverlay() {\n            if (isOpen) {\n                instance.update();\n            }\n        }, [\n            instance,\n            isOpen,\n            props.children\n        ]);\n        // @ts-ignore _contentNode missing in type definition\n        const contentNode = instance._contentNode;\n        return contentNode ? /*#__PURE__*/ createPortal(props.children, contentNode) : null;\n    }\n    return /*#__PURE__*/ forwardRef(OverlayComponent);\n}\nexport function createLeafComponent(useElement) {\n    function LeafComponent(props, forwardedRef) {\n        const { instance } = useElement(props).current;\n        useImperativeHandle(forwardedRef, ()=>instance);\n        return null;\n    }\n    return /*#__PURE__*/ forwardRef(LeafComponent);\n}\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,QAAQ,QAAQ,OAAO;AACnF,SAASC,YAAY,QAAQ,WAAW;AACxC,SAASC,cAAc,QAAQ,cAAc;AAC7C,OAAO,SAASC,wBAAwBA,CAACC,UAAU,EAAE;EACjD,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,YAAY,EAAE;IAC7C,MAAM;MAAEC,QAAQ;MAAEC;IAAQ,CAAC,GAAGL,UAAU,CAACE,KAAK,CAAC,CAACI,OAAO;IACvDX,mBAAmB,CAACQ,YAAY,EAAE,MAAIC,QAAQ,CAAC;IAC/C,MAAM;MAAEG;IAAS,CAAC,GAAGL,KAAK;IAC1B,OAAOK,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,aAAcf,KAAK,CAACgB,aAAa,CAACV,cAAc,EAAE;MAC/EW,KAAK,EAAEJ;IACX,CAAC,EAAEE,QAAQ,CAAC;EAChB;EACA,OAAO,aAAcd,UAAU,CAACQ,kBAAkB,CAAC;AACvD;AACA,OAAO,SAASS,yBAAyBA,CAACV,UAAU,EAAE;EAClD,SAASW,gBAAgBA,CAACT,KAAK,EAAEC,YAAY,EAAE;IAC3C,MAAM,CAACS,MAAM,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;IACzC,MAAM;MAAEQ;IAAS,CAAC,GAAGJ,UAAU,CAACE,KAAK,EAAEW,OAAO,CAAC,CAACP,OAAO;IACvDX,mBAAmB,CAACQ,YAAY,EAAE,MAAIC,QAAQ,CAAC;IAC/C;IACAV,SAAS,CAAC,SAASoB,aAAaA,CAAA,EAAG;MAC/B,IAAIF,MAAM,EAAE;QACRR,QAAQ,CAACW,MAAM,CAAC,CAAC;MACrB;IACJ,CAAC,EAAE,CACCX,QAAQ,EACRQ,MAAM,EACNV,KAAK,CAACK,QAAQ,CACjB,CAAC;IACF;IACA,MAAMS,WAAW,GAAGZ,QAAQ,CAACa,YAAY;IACzC,OAAOD,WAAW,GAAG,aAAcnB,YAAY,CAACK,KAAK,CAACK,QAAQ,EAAES,WAAW,CAAC,GAAG,IAAI;EACvF;EACA,OAAO,aAAcvB,UAAU,CAACkB,gBAAgB,CAAC;AACrD;AACA,OAAO,SAASO,mBAAmBA,CAAClB,UAAU,EAAE;EAC5C,SAASmB,aAAaA,CAACjB,KAAK,EAAEC,YAAY,EAAE;IACxC,MAAM;MAAEC;IAAS,CAAC,GAAGJ,UAAU,CAACE,KAAK,CAAC,CAACI,OAAO;IAC9CX,mBAAmB,CAACQ,YAAY,EAAE,MAAIC,QAAQ,CAAC;IAC/C,OAAO,IAAI;EACf;EACA,OAAO,aAAcX,UAAU,CAAC0B,aAAa,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}