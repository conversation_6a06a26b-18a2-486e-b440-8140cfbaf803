{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getBounds = _interopRequireDefault(require(\"./getBounds\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar getCenterOfBounds = function getCenterOfBounds(coords) {\n  var bounds = (0, _getBounds.default)(coords);\n  var latitude = bounds.minLat + (bounds.maxLat - bounds.minLat) / 2;\n  var longitude = bounds.minLng + (bounds.maxLng - bounds.minLng) / 2;\n  return {\n    latitude: parseFloat(latitude.toFixed(6)),\n    longitude: parseFloat(longitude.toFixed(6))\n  };\n};\nvar _default = getCenterOfBounds;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getBounds", "_interopRequireDefault", "require", "obj", "__esModule", "getCenterOfBounds", "coords", "bounds", "latitude", "minLat", "maxLat", "longitude", "minLng", "maxLng", "parseFloat", "toFixed", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getCenterOfBounds.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getBounds=_interopRequireDefault(require(\"./getBounds\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var getCenterOfBounds=function getCenterOfBounds(coords){var bounds=(0,_getBounds.default)(coords);var latitude=bounds.minLat+(bounds.maxLat-bounds.minLat)/2;var longitude=bounds.minLng+(bounds.maxLng-bounds.minLng)/2;return{latitude:parseFloat(latitude.toFixed(6)),longitude:parseFloat(longitude.toFixed(6))}};var _default=getCenterOfBounds;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,UAAU,GAACC,sBAAsB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACE,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACJ,OAAO,EAACI;EAAG,CAAC;AAAA;AAAC,IAAIE,iBAAiB,GAAC,SAASA,iBAAiBA,CAACC,MAAM,EAAC;EAAC,IAAIC,MAAM,GAAC,CAAC,CAAC,EAACP,UAAU,CAACD,OAAO,EAAEO,MAAM,CAAC;EAAC,IAAIE,QAAQ,GAACD,MAAM,CAACE,MAAM,GAAC,CAACF,MAAM,CAACG,MAAM,GAACH,MAAM,CAACE,MAAM,IAAE,CAAC;EAAC,IAAIE,SAAS,GAACJ,MAAM,CAACK,MAAM,GAAC,CAACL,MAAM,CAACM,MAAM,GAACN,MAAM,CAACK,MAAM,IAAE,CAAC;EAAC,OAAM;IAACJ,QAAQ,EAACM,UAAU,CAACN,QAAQ,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC;IAACJ,SAAS,EAACG,UAAU,CAACH,SAAS,CAACI,OAAO,CAAC,CAAC,CAAC;EAAC,CAAC;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACX,iBAAiB;AAACR,OAAO,CAACE,OAAO,GAACiB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}