{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getCoordinateKeys2 = _interopRequireDefault(require(\"./getCoordinateKeys\"));\nvar _isValidLatitude = _interopRequireDefault(require(\"./isValidLatitude\"));\nvar _isValidLongitude = _interopRequireDefault(require(\"./isValidLongitude\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar isValidCoordinate = function isValidCoordinate(point) {\n  var _getCoordinateKeys = (0, _getCoordinateKeys2.default)(point),\n    latitude = _getCoordinateKeys.latitude,\n    longitude = _getCoordinateKeys.longitude;\n  if (Array.isArray(point) && point.length >= 2) {\n    return (0, _isValidLongitude.default)(point[0]) && (0, _isValidLatitude.default)(point[1]);\n  }\n  if (typeof latitude === \"undefined\" || typeof longitude === \"undefined\") {\n    return false;\n  }\n  var lon = point[longitude];\n  var lat = point[latitude];\n  if (typeof lat === \"undefined\" || typeof lon === \"undefined\") {\n    return false;\n  }\n  if ((0, _isValidLatitude.default)(lat) === false || (0, _isValidLongitude.default)(lon) === false) {\n    return false;\n  }\n  return true;\n};\nvar _default = isValidCoordinate;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getCoordinateKeys2", "_interopRequireDefault", "require", "_isValidLatitude", "_isValidLongitude", "obj", "__esModule", "isValidCoordinate", "point", "_getCoordinateKeys", "latitude", "longitude", "Array", "isArray", "length", "lon", "lat", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/isValidCoordinate.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getCoordinateKeys2=_interopRequireDefault(require(\"./getCoordinateKeys\"));var _isValidLatitude=_interopRequireDefault(require(\"./isValidLatitude\"));var _isValidLongitude=_interopRequireDefault(require(\"./isValidLongitude\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var isValidCoordinate=function isValidCoordinate(point){var _getCoordinateKeys=(0,_getCoordinateKeys2.default)(point),latitude=_getCoordinateKeys.latitude,longitude=_getCoordinateKeys.longitude;if(Array.isArray(point)&&point.length>=2){return(0,_isValidLongitude.default)(point[0])&&(0,_isValidLatitude.default)(point[1])}if(typeof latitude===\"undefined\"||typeof longitude===\"undefined\"){return false}var lon=point[longitude];var lat=point[latitude];if(typeof lat===\"undefined\"||typeof lon===\"undefined\"){return false}if((0,_isValidLatitude.default)(lat)===false||(0,_isValidLongitude.default)(lon)===false){return false}return true};var _default=isValidCoordinate;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,mBAAmB,GAACC,sBAAsB,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAAC,IAAIC,gBAAgB,GAACF,sBAAsB,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAAC,IAAIE,iBAAiB,GAACH,sBAAsB,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACI,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACN,OAAO,EAACM;EAAG,CAAC;AAAA;AAAC,IAAIE,iBAAiB,GAAC,SAASA,iBAAiBA,CAACC,KAAK,EAAC;EAAC,IAAIC,kBAAkB,GAAC,CAAC,CAAC,EAACT,mBAAmB,CAACD,OAAO,EAAES,KAAK,CAAC;IAACE,QAAQ,GAACD,kBAAkB,CAACC,QAAQ;IAACC,SAAS,GAACF,kBAAkB,CAACE,SAAS;EAAC,IAAGC,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,IAAEA,KAAK,CAACM,MAAM,IAAE,CAAC,EAAC;IAAC,OAAM,CAAC,CAAC,EAACV,iBAAiB,CAACL,OAAO,EAAES,KAAK,CAAC,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC,EAACL,gBAAgB,CAACJ,OAAO,EAAES,KAAK,CAAC,CAAC,CAAC,CAAC;EAAA;EAAC,IAAG,OAAOE,QAAQ,KAAG,WAAW,IAAE,OAAOC,SAAS,KAAG,WAAW,EAAC;IAAC,OAAO,KAAK;EAAA;EAAC,IAAII,GAAG,GAACP,KAAK,CAACG,SAAS,CAAC;EAAC,IAAIK,GAAG,GAACR,KAAK,CAACE,QAAQ,CAAC;EAAC,IAAG,OAAOM,GAAG,KAAG,WAAW,IAAE,OAAOD,GAAG,KAAG,WAAW,EAAC;IAAC,OAAO,KAAK;EAAA;EAAC,IAAG,CAAC,CAAC,EAACZ,gBAAgB,CAACJ,OAAO,EAAEiB,GAAG,CAAC,KAAG,KAAK,IAAE,CAAC,CAAC,EAACZ,iBAAiB,CAACL,OAAO,EAAEgB,GAAG,CAAC,KAAG,KAAK,EAAC;IAAC,OAAO,KAAK;EAAA;EAAC,OAAO,IAAI;AAAA,CAAC;AAAC,IAAIE,QAAQ,GAACV,iBAAiB;AAACV,OAAO,CAACE,OAAO,GAACkB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}