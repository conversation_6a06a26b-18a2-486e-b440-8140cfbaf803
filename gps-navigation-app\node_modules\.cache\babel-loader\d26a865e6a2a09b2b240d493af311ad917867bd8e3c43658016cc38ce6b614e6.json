{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"eventHandlers\", \"params\", \"url\"];\nimport { createElementObject, createTileLayerComponent, updateGridLayer, withPane } from '@react-leaflet/core';\nimport { TileLayer } from 'leaflet';\nexport const WMSTileLayer = createTileLayerComponent(function createWMSTileLayer(_ref, context) {\n  let {\n      eventHandlers: _eh,\n      params = {},\n      url\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const layer = new TileLayer.WMS(url, _objectSpread(_objectSpread({}, params), withPane(options, context)));\n  return createElementObject(layer, context);\n}, function updateWMSTileLayer(layer, props, prevProps) {\n  updateGridLayer(layer, props, prevProps);\n  if (props.params != null && props.params !== prevProps.params) {\n    layer.setParams(props.params);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createTileLayerComponent", "updateGridLayer", "with<PERSON>ane", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WMSTileLayer", "createWMSTileLayer", "_ref", "context", "eventHandlers", "_eh", "params", "url", "options", "_objectWithoutProperties", "_excluded", "layer", "WMS", "_objectSpread", "updateWMSTileLayer", "props", "prevProps", "setParams"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/WMSTileLayer.js"], "sourcesContent": ["import { createElementObject, createTileLayerComponent, updateGridLayer, withPane } from '@react-leaflet/core';\nimport { TileLayer } from 'leaflet';\nexport const WMSTileLayer = createTileLayerComponent(function createWMSTileLayer({ eventHandlers: _eh, params = {}, url, ...options }, context) {\n    const layer = new TileLayer.WMS(url, {\n        ...params,\n        ...withPane(options, context)\n    });\n    return createElementObject(layer, context);\n}, function updateWMSTileLayer(layer, props, prevProps) {\n    updateGridLayer(layer, props, prevProps);\n    if (props.params != null && props.params !== prevProps.params) {\n        layer.setParams(props.params);\n    }\n});\n"], "mappings": ";;;AAAA,SAASA,mBAAmB,EAAEC,wBAAwB,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,qBAAqB;AAC9G,SAASC,SAAS,QAAQ,SAAS;AACnC,OAAO,MAAMC,YAAY,GAAGJ,wBAAwB,CAAC,SAASK,kBAAkBA,CAAAC,IAAA,EAAuDC,OAAO,EAAE;EAAA,IAA/D;MAAEC,aAAa,EAAEC,GAAG;MAAEC,MAAM,GAAG,CAAC,CAAC;MAAEC;IAAgB,CAAC,GAAAL,IAAA;IAATM,OAAO,GAAAC,wBAAA,CAAAP,IAAA,EAAAQ,SAAA;EAC/H,MAAMC,KAAK,GAAG,IAAIZ,SAAS,CAACa,GAAG,CAACL,GAAG,EAAAM,aAAA,CAAAA,aAAA,KAC5BP,MAAM,GACNR,QAAQ,CAACU,OAAO,EAAEL,OAAO,CAAC,CAChC,CAAC;EACF,OAAOR,mBAAmB,CAACgB,KAAK,EAAER,OAAO,CAAC;AAC9C,CAAC,EAAE,SAASW,kBAAkBA,CAACH,KAAK,EAAEI,KAAK,EAAEC,SAAS,EAAE;EACpDnB,eAAe,CAACc,KAAK,EAAEI,KAAK,EAAEC,SAAS,CAAC;EACxC,IAAID,KAAK,CAACT,MAAM,IAAI,IAAI,IAAIS,KAAK,CAACT,MAAM,KAAKU,SAAS,CAACV,MAAM,EAAE;IAC3DK,KAAK,CAACM,SAAS,CAACF,KAAK,CAACT,MAAM,CAAC;EACjC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}