{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _constants = require(\"./constants\");\nvar sexagesimalToDecimal = function sexagesimalToDecimal(sexagesimal) {\n  var data = new RegExp(_constants.sexagesimalPattern).exec(sexagesimal.toString().trim());\n  if (typeof data === \"undefined\" || data === null) {\n    throw new Error(\"Given value is not in sexagesimal format\");\n  }\n  var min = Number(data[2]) / 60 || 0;\n  var sec = Number(data[4]) / 3600 || 0;\n  var decimal = parseFloat(data[1]) + min + sec;\n  return [\"S\", \"W\"].includes(data[7]) ? -decimal : decimal;\n};\nvar _default = sexagesimalToDecimal;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_constants", "require", "sexagesimalToDecimal", "sexagesimal", "data", "RegExp", "sexagesimalPattern", "exec", "toString", "trim", "Error", "min", "Number", "sec", "decimal", "parseFloat", "includes", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/sexagesimalToDecimal.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _constants=require(\"./constants\");var sexagesimalToDecimal=function sexagesimalToDecimal(sexagesimal){var data=new RegExp(_constants.sexagesimalPattern).exec(sexagesimal.toString().trim());if(typeof data===\"undefined\"||data===null){throw new Error(\"Given value is not in sexagesimal format\")}var min=Number(data[2])/60||0;var sec=Number(data[4])/3600||0;var decimal=parseFloat(data[1])+min+sec;return[\"S\",\"W\"].includes(data[7])?-decimal:decimal};var _default=sexagesimalToDecimal;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,UAAU,GAACC,OAAO,CAAC,aAAa,CAAC;AAAC,IAAIC,oBAAoB,GAAC,SAASA,oBAAoBA,CAACC,WAAW,EAAC;EAAC,IAAIC,IAAI,GAAC,IAAIC,MAAM,CAACL,UAAU,CAACM,kBAAkB,CAAC,CAACC,IAAI,CAACJ,WAAW,CAACK,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;EAAC,IAAG,OAAOL,IAAI,KAAG,WAAW,IAAEA,IAAI,KAAG,IAAI,EAAC;IAAC,MAAM,IAAIM,KAAK,CAAC,0CAA0C,CAAC;EAAA;EAAC,IAAIC,GAAG,GAACC,MAAM,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC,GAAC,EAAE,IAAE,CAAC;EAAC,IAAIS,GAAG,GAACD,MAAM,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC,GAAC,IAAI,IAAE,CAAC;EAAC,IAAIU,OAAO,GAACC,UAAU,CAACX,IAAI,CAAC,CAAC,CAAC,CAAC,GAACO,GAAG,GAACE,GAAG;EAAC,OAAM,CAAC,GAAG,EAAC,GAAG,CAAC,CAACG,QAAQ,CAACZ,IAAI,CAAC,CAAC,CAAC,CAAC,GAAC,CAACU,OAAO,GAACA,OAAO;AAAA,CAAC;AAAC,IAAIG,QAAQ,GAACf,oBAAoB;AAACL,OAAO,CAACE,OAAO,GAACkB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}