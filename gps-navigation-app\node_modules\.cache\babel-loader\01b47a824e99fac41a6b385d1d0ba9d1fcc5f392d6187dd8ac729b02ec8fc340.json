{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nvar wktToPolygon = function wktToPolygon(wkt) {\n  if (!wkt.startsWith(\"POLYGON\")) {\n    throw new Error(\"Invalid wkt.\");\n  }\n  var coordsText = wkt.slice(wkt.indexOf(\"(\") + 2, wkt.indexOf(\")\")).split(\", \");\n  var polygon = coordsText.map(function (coordText) {\n    var _coordText$split = coordText.split(\" \"),\n      _coordText$split2 = _slicedToArray(_coordText$split, 2),\n      longitude = _coordText$split2[0],\n      latitude = _coordText$split2[1];\n    return {\n      longitude: parseFloat(longitude),\n      latitude: parseFloat(latitude)\n    };\n  });\n  return polygon;\n};\nvar _default = wktToPolygon;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_slicedToArray", "arr", "i", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "prototype", "toString", "call", "slice", "constructor", "name", "Array", "from", "test", "len", "length", "arr2", "Symbol", "iterator", "_arr", "_n", "_d", "_e", "undefined", "_i", "_s", "next", "done", "push", "err", "isArray", "wktToPolygon", "wkt", "startsWith", "Error", "coordsText", "indexOf", "split", "polygon", "map", "coordText", "_coordText$split", "_coordText$split2", "longitude", "latitude", "parseFloat", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/wktToPolygon.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;function _slicedToArray(arr,i){return _arrayWithHoles(arr)||_iterableToArrayLimit(arr,i)||_unsupportedIterableToArray(arr,i)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}function _unsupportedIterableToArray(o,minLen){if(!o)return;if(typeof o===\"string\")return _arrayLikeToArray(o,minLen);var n=Object.prototype.toString.call(o).slice(8,-1);if(n===\"Object\"&&o.constructor)n=o.constructor.name;if(n===\"Map\"||n===\"Set\")return Array.from(o);if(n===\"Arguments\"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(o,minLen)}function _arrayLikeToArray(arr,len){if(len==null||len>arr.length)len=arr.length;for(var i=0,arr2=new Array(len);i<len;i++){arr2[i]=arr[i]}return arr2}function _iterableToArrayLimit(arr,i){if(typeof Symbol===\"undefined\"||!(Symbol.iterator in Object(arr)))return;var _arr=[];var _n=true;var _d=false;var _e=undefined;try{for(var _i=arr[Symbol.iterator](),_s;!(_n=(_s=_i.next()).done);_n=true){_arr.push(_s.value);if(i&&_arr.length===i)break}}catch(err){_d=true;_e=err}finally{try{if(!_n&&_i[\"return\"]!=null)_i[\"return\"]()}finally{if(_d)throw _e}}return _arr}function _arrayWithHoles(arr){if(Array.isArray(arr))return arr}var wktToPolygon=function wktToPolygon(wkt){if(!wkt.startsWith(\"POLYGON\")){throw new Error(\"Invalid wkt.\")}var coordsText=wkt.slice(wkt.indexOf(\"(\")+2,wkt.indexOf(\")\")).split(\", \");var polygon=coordsText.map(function(coordText){var _coordText$split=coordText.split(\" \"),_coordText$split2=_slicedToArray(_coordText$split,2),longitude=_coordText$split2[0],latitude=_coordText$split2[1];return{longitude:parseFloat(longitude),latitude:parseFloat(latitude)}});return polygon};var _default=wktToPolygon;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,SAASC,cAAcA,CAACC,GAAG,EAACC,CAAC,EAAC;EAAC,OAAOC,eAAe,CAACF,GAAG,CAAC,IAAEG,qBAAqB,CAACH,GAAG,EAACC,CAAC,CAAC,IAAEG,2BAA2B,CAACJ,GAAG,EAACC,CAAC,CAAC,IAAEI,gBAAgB,CAAC,CAAC;AAAA;AAAC,SAASA,gBAAgBA,CAAA,EAAE;EAAC,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAA;AAAC,SAASF,2BAA2BA,CAACG,CAAC,EAACC,MAAM,EAAC;EAAC,IAAG,CAACD,CAAC,EAAC;EAAO,IAAG,OAAOA,CAAC,KAAG,QAAQ,EAAC,OAAOE,iBAAiB,CAACF,CAAC,EAACC,MAAM,CAAC;EAAC,IAAIE,CAAC,GAAChB,MAAM,CAACiB,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACN,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;EAAC,IAAGJ,CAAC,KAAG,QAAQ,IAAEH,CAAC,CAACQ,WAAW,EAACL,CAAC,GAACH,CAAC,CAACQ,WAAW,CAACC,IAAI;EAAC,IAAGN,CAAC,KAAG,KAAK,IAAEA,CAAC,KAAG,KAAK,EAAC,OAAOO,KAAK,CAACC,IAAI,CAACX,CAAC,CAAC;EAAC,IAAGG,CAAC,KAAG,WAAW,IAAE,0CAA0C,CAACS,IAAI,CAACT,CAAC,CAAC,EAAC,OAAOD,iBAAiB,CAACF,CAAC,EAACC,MAAM,CAAC;AAAA;AAAC,SAASC,iBAAiBA,CAACT,GAAG,EAACoB,GAAG,EAAC;EAAC,IAAGA,GAAG,IAAE,IAAI,IAAEA,GAAG,GAACpB,GAAG,CAACqB,MAAM,EAACD,GAAG,GAACpB,GAAG,CAACqB,MAAM;EAAC,KAAI,IAAIpB,CAAC,GAAC,CAAC,EAACqB,IAAI,GAAC,IAAIL,KAAK,CAACG,GAAG,CAAC,EAACnB,CAAC,GAACmB,GAAG,EAACnB,CAAC,EAAE,EAAC;IAACqB,IAAI,CAACrB,CAAC,CAAC,GAACD,GAAG,CAACC,CAAC,CAAC;EAAA;EAAC,OAAOqB,IAAI;AAAA;AAAC,SAASnB,qBAAqBA,CAACH,GAAG,EAACC,CAAC,EAAC;EAAC,IAAG,OAAOsB,MAAM,KAAG,WAAW,IAAE,EAAEA,MAAM,CAACC,QAAQ,IAAI9B,MAAM,CAACM,GAAG,CAAC,CAAC,EAAC;EAAO,IAAIyB,IAAI,GAAC,EAAE;EAAC,IAAIC,EAAE,GAAC,IAAI;EAAC,IAAIC,EAAE,GAAC,KAAK;EAAC,IAAIC,EAAE,GAACC,SAAS;EAAC,IAAG;IAAC,KAAI,IAAIC,EAAE,GAAC9B,GAAG,CAACuB,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC,EAACO,EAAE,EAAC,EAAEL,EAAE,GAAC,CAACK,EAAE,GAACD,EAAE,CAACE,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAACP,EAAE,GAAC,IAAI,EAAC;MAACD,IAAI,CAACS,IAAI,CAACH,EAAE,CAAClC,KAAK,CAAC;MAAC,IAAGI,CAAC,IAAEwB,IAAI,CAACJ,MAAM,KAAGpB,CAAC,EAAC;IAAK;EAAC,CAAC,QAAMkC,GAAG,EAAC;IAACR,EAAE,GAAC,IAAI;IAACC,EAAE,GAACO,GAAG;EAAA,CAAC,SAAO;IAAC,IAAG;MAAC,IAAG,CAACT,EAAE,IAAEI,EAAE,CAAC,QAAQ,CAAC,IAAE,IAAI,EAACA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAA,CAAC,SAAO;MAAC,IAAGH,EAAE,EAAC,MAAMC,EAAE;IAAA;EAAC;EAAC,OAAOH,IAAI;AAAA;AAAC,SAASvB,eAAeA,CAACF,GAAG,EAAC;EAAC,IAAGiB,KAAK,CAACmB,OAAO,CAACpC,GAAG,CAAC,EAAC,OAAOA,GAAG;AAAA;AAAC,IAAIqC,YAAY,GAAC,SAASA,YAAYA,CAACC,GAAG,EAAC;EAAC,IAAG,CAACA,GAAG,CAACC,UAAU,CAAC,SAAS,CAAC,EAAC;IAAC,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;EAAA;EAAC,IAAIC,UAAU,GAACH,GAAG,CAACxB,KAAK,CAACwB,GAAG,CAACI,OAAO,CAAC,GAAG,CAAC,GAAC,CAAC,EAACJ,GAAG,CAACI,OAAO,CAAC,GAAG,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC;EAAC,IAAIC,OAAO,GAACH,UAAU,CAACI,GAAG,CAAC,UAASC,SAAS,EAAC;IAAC,IAAIC,gBAAgB,GAACD,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;MAACK,iBAAiB,GAACjD,cAAc,CAACgD,gBAAgB,EAAC,CAAC,CAAC;MAACE,SAAS,GAACD,iBAAiB,CAAC,CAAC,CAAC;MAACE,QAAQ,GAACF,iBAAiB,CAAC,CAAC,CAAC;IAAC,OAAM;MAACC,SAAS,EAACE,UAAU,CAACF,SAAS,CAAC;MAACC,QAAQ,EAACC,UAAU,CAACD,QAAQ;IAAC,CAAC;EAAA,CAAC,CAAC;EAAC,OAAON,OAAO;AAAA,CAAC;AAAC,IAAIQ,QAAQ,GAACf,YAAY;AAACzC,OAAO,CAACE,OAAO,GAACsD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}