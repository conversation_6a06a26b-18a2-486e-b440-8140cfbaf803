import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import MapComponent from './MapComponent';
import NavigationPanel from './NavigationPanel';
import SearchPanel from './SearchPanel';
import { LocationData, RouteData } from '../types/gps.types';

const AppContainer = styled.div`
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: #1a1a1a;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
`;

const MapContainer = styled.div`
  flex: 1;
  position: relative;
  height: 100%;
`;

const SidePanel = styled.div<{ isOpen: boolean }>`
  width: ${props => props.isOpen ? '400px' : '0px'};
  transition: width 0.3s ease;
  background-color: #2d2d2d;
  border-left: 1px solid #444;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  @media (max-width: 768px) {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    z-index: 1000;
    width: ${props => props.isOpen ? '100%' : '0px'};
  }
`;

const ToggleButton = styled.button`
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1001;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #0056b3;
    transform: scale(1.1);
  }
  
  &:active {
    transform: scale(0.95);
  }
`;

const GPSNavigationApp: React.FC = () => {
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);
  const [destination, setDestination] = useState<LocationData | null>(null);
  const [route, setRoute] = useState<RouteData | null>(null);
  const [isNavigating, setIsNavigating] = useState(false);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [searchMode, setSearchMode] = useState<'search' | 'navigation'>('search');

  // Get user's current location
  useEffect(() => {
    if (navigator.geolocation) {
      const watchId = navigator.geolocation.watchPosition(
        (position) => {
          const newLocation: LocationData = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: Date.now()
          };
          setCurrentLocation(newLocation);
        },
        (error) => {
          console.error('Error getting location:', error);
          // Fallback to a default location (Tehran, Iran)
          setCurrentLocation({
            lat: 35.6892,
            lng: 51.3890,
            accuracy: 100,
            timestamp: Date.now()
          });
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );

      return () => navigator.geolocation.clearWatch(watchId);
    }
  }, []);

  const handleDestinationSelect = (location: LocationData) => {
    setDestination(location);
    setSearchMode('navigation');
    setIsPanelOpen(true);
  };

  const handleStartNavigation = (routeData: RouteData) => {
    setRoute(routeData);
    setIsNavigating(true);
    setIsPanelOpen(false);
  };

  const handleStopNavigation = () => {
    setIsNavigating(false);
    setRoute(null);
    setDestination(null);
    setSearchMode('search');
  };

  const togglePanel = () => {
    setIsPanelOpen(!isPanelOpen);
  };

  return (
    <AppContainer>
      <MapContainer>
        <MapComponent
          currentLocation={currentLocation}
          destination={destination}
          route={route}
          isNavigating={isNavigating}
          onLocationSelect={handleDestinationSelect}
        />
        <ToggleButton onClick={togglePanel}>
          {isPanelOpen ? '×' : '☰'}
        </ToggleButton>
      </MapContainer>
      
      <SidePanel isOpen={isPanelOpen}>
        {searchMode === 'search' ? (
          <SearchPanel
            currentLocation={currentLocation}
            onDestinationSelect={handleDestinationSelect}
          />
        ) : (
          <NavigationPanel
            currentLocation={currentLocation}
            destination={destination}
            route={route}
            isNavigating={isNavigating}
            onStartNavigation={handleStartNavigation}
            onStopNavigation={handleStopNavigation}
            onBackToSearch={() => setSearchMode('search')}
          />
        )}
      </SidePanel>
    </AppContainer>
  );
};

export default GPSNavigationApp;
