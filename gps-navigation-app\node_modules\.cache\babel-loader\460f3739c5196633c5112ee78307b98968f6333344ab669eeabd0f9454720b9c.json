{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _constants = require(\"./constants\");\nvar _getCoordinateKey = _interopRequireDefault(require(\"./getCoordinateKey\"));\nvar _toDecimal = _interopRequireDefault(require(\"./toDecimal\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar getLatitude = function getLatitude(point, raw) {\n  var latKey = (0, _getCoordinateKey.default)(point, _constants.latitudeKeys);\n  if (typeof latKey === \"undefined\" || latKey === null) {\n    return;\n  }\n  var value = point[latKey];\n  return raw === true ? value : (0, _toDecimal.default)(value);\n};\nvar _default = getLatitude;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_constants", "require", "_getCoordinateKey", "_interopRequireDefault", "_toDecimal", "obj", "__esModule", "getLatitude", "point", "raw", "latKey", "latitudeKeys", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getLatitude.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _constants=require(\"./constants\");var _getCoordinateKey=_interopRequireDefault(require(\"./getCoordinateKey\"));var _toDecimal=_interopRequireDefault(require(\"./toDecimal\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var getLatitude=function getLatitude(point,raw){var latKey=(0,_getCoordinateKey.default)(point,_constants.latitudeKeys);if(typeof latKey===\"undefined\"||latKey===null){return}var value=point[latKey];return raw===true?value:(0,_toDecimal.default)(value)};var _default=getLatitude;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,UAAU,GAACC,OAAO,CAAC,aAAa,CAAC;AAAC,IAAIC,iBAAiB,GAACC,sBAAsB,CAACF,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAAC,IAAIG,UAAU,GAACD,sBAAsB,CAACF,OAAO,CAAC,aAAa,CAAC,CAAC;AAAC,SAASE,sBAAsBA,CAACE,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACN,OAAO,EAACM;EAAG,CAAC;AAAA;AAAC,IAAIE,WAAW,GAAC,SAASA,WAAWA,CAACC,KAAK,EAACC,GAAG,EAAC;EAAC,IAAIC,MAAM,GAAC,CAAC,CAAC,EAACR,iBAAiB,CAACH,OAAO,EAAES,KAAK,EAACR,UAAU,CAACW,YAAY,CAAC;EAAC,IAAG,OAAOD,MAAM,KAAG,WAAW,IAAEA,MAAM,KAAG,IAAI,EAAC;IAAC;EAAM;EAAC,IAAIZ,KAAK,GAACU,KAAK,CAACE,MAAM,CAAC;EAAC,OAAOD,GAAG,KAAG,IAAI,GAACX,KAAK,GAAC,CAAC,CAAC,EAACM,UAAU,CAACL,OAAO,EAAED,KAAK,CAAC;AAAA,CAAC;AAAC,IAAIc,QAAQ,GAACL,WAAW;AAACV,OAAO,CAACE,OAAO,GAACa,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}