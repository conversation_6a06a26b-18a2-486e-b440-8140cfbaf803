[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\components\\GPSNavigationApp.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\components\\MapComponent.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\components\\SearchPanel.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\components\\NavigationPanel.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\components\\TestComponent.tsx": "8"}, {"size": 554, "mtime": 1756848383477, "results": "9", "hashOfConfig": "10"}, {"size": 425, "mtime": 1756848383291, "results": "11", "hashOfConfig": "10"}, {"size": 395, "mtime": 1756850614510, "results": "12", "hashOfConfig": "10"}, {"size": 4527, "mtime": 1756848529763, "results": "13", "hashOfConfig": "10"}, {"size": 8133, "mtime": 1756848583655, "results": "14", "hashOfConfig": "10"}, {"size": 11809, "mtime": 1756850530987, "results": "15", "hashOfConfig": "10"}, {"size": 11790, "mtime": 1756850417906, "results": "16", "hashOfConfig": "10"}, {"size": 845, "mtime": 1756850605000, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13azs01", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\components\\GPSNavigationApp.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\components\\MapComponent.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\components\\SearchPanel.tsx", ["42"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\components\\NavigationPanel.tsx", ["43"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\GPS\\gps-navigation-app\\src\\components\\TestComponent.tsx", [], [], {"ruleId": "44", "severity": 1, "message": "45", "line": 254, "column": 6, "nodeType": "46", "endLine": 254, "endColumn": 36, "suggestions": "47"}, {"ruleId": "48", "severity": 1, "message": "49", "line": 1, "column": 27, "nodeType": "50", "messageId": "51", "endLine": 1, "endColumn": 36}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'performSearch'. Either include it or remove the dependency array.", "ArrayExpression", ["52"], "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", {"desc": "53", "fix": "54"}, "Update the dependencies array to be: [searchQuery, currentLocation, performSearch]", {"range": "55", "text": "56"}, [5528, 5558], "[searchQuery, currentLocation, performSearch]"]