{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar getRoughCompassDirection = function getRoughCompassDirection(exact) {\n  if (/^(NNE|NE|NNW|N)$/.test(exact)) {\n    return \"N\";\n  }\n  if (/^(ENE|E|ESE|SE)$/.test(exact)) {\n    return \"E\";\n  }\n  if (/^(SSE|S|SSW|SW)$/.test(exact)) {\n    return \"S\";\n  }\n  if (/^(WSW|W|WNW|NW)$/.test(exact)) {\n    return \"W\";\n  }\n};\nvar _default = getRoughCompassDirection;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "getRoughCompassDirection", "exact", "test", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getRoughCompassDirection.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var getRoughCompassDirection=function getRoughCompassDirection(exact){if(/^(NNE|NE|NNW|N)$/.test(exact)){return\"N\"}if(/^(ENE|E|ESE|SE)$/.test(exact)){return\"E\"}if(/^(SSE|S|SSW|SW)$/.test(exact)){return\"S\"}if(/^(WSW|W|WNW|NW)$/.test(exact)){return\"W\"}};var _default=getRoughCompassDirection;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,wBAAwB,GAAC,SAASA,wBAAwBA,CAACC,KAAK,EAAC;EAAC,IAAG,kBAAkB,CAACC,IAAI,CAACD,KAAK,CAAC,EAAC;IAAC,OAAM,GAAG;EAAA;EAAC,IAAG,kBAAkB,CAACC,IAAI,CAACD,KAAK,CAAC,EAAC;IAAC,OAAM,GAAG;EAAA;EAAC,IAAG,kBAAkB,CAACC,IAAI,CAACD,KAAK,CAAC,EAAC;IAAC,OAAM,GAAG;EAAA;EAAC,IAAG,kBAAkB,CAACC,IAAI,CAACD,KAAK,CAAC,EAAC;IAAC,OAAM,GAAG;EAAA;AAAC,CAAC;AAAC,IAAIE,QAAQ,GAACH,wBAAwB;AAACH,OAAO,CAACE,OAAO,GAACI,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}