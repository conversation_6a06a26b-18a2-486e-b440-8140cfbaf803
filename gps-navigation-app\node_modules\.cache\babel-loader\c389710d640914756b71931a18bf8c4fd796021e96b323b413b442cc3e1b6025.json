{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"positions\"];\nimport { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Polyline as LeafletPolyline } from 'leaflet';\nexport const Polyline = createPathComponent(function createPolyline(_ref, ctx) {\n  let {\n      positions\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const polyline = new LeafletPolyline(positions, options);\n  return createElementObject(polyline, extendContext(ctx, {\n    overlayContainer: polyline\n  }));\n}, function updatePolyline(layer, props, prevProps) {\n  if (props.positions !== prevProps.positions) {\n    layer.setLatLngs(props.positions);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "Polyline", "LeafletPolyline", "createPolyline", "_ref", "ctx", "positions", "options", "_objectWithoutProperties", "_excluded", "polyline", "overlayContainer", "updatePolyline", "layer", "props", "prevProps", "setLatLngs"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/Polyline.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Polyline as LeafletPolyline } from 'leaflet';\nexport const Polyline = createPathComponent(function createPolyline({ positions, ...options }, ctx) {\n    const polyline = new LeafletPolyline(positions, options);\n    return createElementObject(polyline, extendContext(ctx, {\n        overlayContainer: polyline\n    }));\n}, function updatePolyline(layer, props, prevProps) {\n    if (props.positions !== prevProps.positions) {\n        layer.setLatLngs(props.positions);\n    }\n});\n"], "mappings": ";;AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,qBAAqB;AAC7F,SAASC,QAAQ,IAAIC,eAAe,QAAQ,SAAS;AACrD,OAAO,MAAMD,QAAQ,GAAGF,mBAAmB,CAAC,SAASI,cAAcA,CAAAC,IAAA,EAA4BC,GAAG,EAAE;EAAA,IAAhC;MAAEC;IAAsB,CAAC,GAAAF,IAAA;IAATG,OAAO,GAAAC,wBAAA,CAAAJ,IAAA,EAAAK,SAAA;EACvF,MAAMC,QAAQ,GAAG,IAAIR,eAAe,CAACI,SAAS,EAAEC,OAAO,CAAC;EACxD,OAAOT,mBAAmB,CAACY,QAAQ,EAAEV,aAAa,CAACK,GAAG,EAAE;IACpDM,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASE,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAChD,IAAID,KAAK,CAACR,SAAS,KAAKS,SAAS,CAACT,SAAS,EAAE;IACzCO,KAAK,CAACG,UAAU,CAACF,KAAK,CAACR,SAAS,CAAC;EACrC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}