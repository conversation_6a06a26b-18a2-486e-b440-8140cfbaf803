/* GPS Navigation App Styles */
.App {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #1a1a1a;
  color: white;
}

/* Reset default styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  background-color: #1a1a1a;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  /* Larger touch targets for tablets */
  button {
    min-height: 48px;
    min-width: 48px;
  }

  input {
    min-height: 48px;
  }

  /* Optimize for landscape orientation */
  @media (orientation: landscape) {
    .App {
      flex-direction: row;
    }
  }
}

/* Mobile optimizations */
@media (max-width: 767px) {
  button {
    min-height: 44px;
    min-width: 44px;
  }

  input {
    min-height: 44px;
  }
}

/* Scrollbar styling for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* Focus styles for accessibility */
button:focus,
input:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Animation for smooth transitions */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading spinner */
.loading-spinner {
  border: 3px solid #444;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Persian/RTL text support */
.rtl {
  direction: rtl;
  text-align: right;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .App {
    background-color: #000;
    color: #fff;
  }

  button {
    border: 2px solid #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
