{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\GPS\\\\gps-navigation-app\\\\src\\\\components\\\\NavigationPanel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\n// Using Unicode symbols instead of react-icons for React 19 compatibility\n\nimport { getDistance } from 'geolib';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PanelContainer = styled.div`\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background-color: #2d2d2d;\n  color: white;\n`;\n_c = PanelContainer;\nconst Header = styled.div`\n  padding: 20px;\n  border-bottom: 1px solid #444;\n  background-color: #1a1a1a;\n`;\n_c2 = Header;\nconst BackButton = styled.button`\n  background: none;\n  border: none;\n  color: #007bff;\n  font-size: 16px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 16px;\n  padding: 8px 0;\n  \n  &:hover {\n    color: #0056b3;\n  }\n`;\n_c3 = BackButton;\nconst DestinationInfo = styled.div`\n  margin-bottom: 20px;\n`;\n_c4 = DestinationInfo;\nconst DestinationName = styled.h2`\n  margin: 0 0 8px 0;\n  font-size: 20px;\n  font-weight: 600;\n  color: white;\n`;\n_c5 = DestinationName;\nconst DestinationAddress = styled.div`\n  font-size: 14px;\n  color: #aaa;\n  margin-bottom: 12px;\n`;\n_c6 = DestinationAddress;\nconst RouteOptions = styled.div`\n  display: flex;\n  gap: 12px;\n  margin-bottom: 20px;\n`;\n_c7 = RouteOptions;\nconst RouteOption = styled.button`\n  flex: 1;\n  padding: 12px;\n  border: 1px solid #444;\n  border-radius: 8px;\n  background-color: ${props => props.active ? '#007bff' : '#3d3d3d'};\n  color: white;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background-color: #007bff;\n    border-color: #007bff;\n  }\n`;\n_c8 = RouteOption;\nconst NavigationControls = styled.div`\n  display: flex;\n  gap: 12px;\n  margin-bottom: 20px;\n`;\n_c9 = NavigationControls;\nconst ControlButton = styled.button`\n  flex: 1;\n  padding: 16px;\n  border: none;\n  border-radius: 12px;\n  background-color: ${props => props.primary ? '#28a745' : '#dc3545'};\n  color: white;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  \n  &:hover {\n    opacity: 0.9;\n    transform: translateY(-2px);\n  }\n  \n  &:active {\n    transform: translateY(0);\n  }\n`;\n_c0 = ControlButton;\nconst RouteInfo = styled.div`\n  background-color: #3d3d3d;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 20px;\n`;\n_c1 = RouteInfo;\nconst RouteStats = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 16px;\n  margin-bottom: 16px;\n`;\n_c10 = RouteStats;\nconst StatItem = styled.div`\n  text-align: center;\n`;\n_c11 = StatItem;\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 600;\n  color: #007bff;\n  margin-bottom: 4px;\n`;\n_c12 = StatValue;\nconst StatLabel = styled.div`\n  font-size: 12px;\n  color: #aaa;\n`;\n_c13 = StatLabel;\nconst Content = styled.div`\n  flex: 1;\n  overflow-y: auto;\n  padding: 0 20px 20px;\n`;\n_c14 = Content;\nconst NavigationStatus = styled.div`\n  background-color: ${props => props.isNavigating ? '#28a745' : '#6c757d'};\n  color: white;\n  padding: 16px;\n  border-radius: 12px;\n  margin-bottom: 20px;\n  text-align: center;\n  font-weight: 600;\n`;\n_c15 = NavigationStatus;\nconst CurrentInstruction = styled.div`\n  background-color: #007bff;\n  color: white;\n  padding: 20px;\n  border-radius: 12px;\n  margin-bottom: 20px;\n  text-align: center;\n`;\n_c16 = CurrentInstruction;\nconst InstructionText = styled.div`\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 8px;\n`;\n_c17 = InstructionText;\nconst InstructionDistance = styled.div`\n  font-size: 14px;\n  opacity: 0.9;\n`;\n_c18 = InstructionDistance;\nconst InstructionsList = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n_c19 = InstructionsList;\nconst InstructionItem = styled.div`\n  padding: 12px;\n  border: 1px solid #444;\n  border-radius: 8px;\n  background-color: ${props => props.isCurrent ? '#007bff' : '#3d3d3d'};\n  font-size: 14px;\n`;\n_c20 = InstructionItem;\nconst VoiceControls = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px;\n  background-color: #3d3d3d;\n  border-radius: 12px;\n  margin-bottom: 20px;\n`;\n_c21 = VoiceControls;\nconst VoiceButton = styled.button`\n  background: none;\n  border: none;\n  color: ${props => props.active ? '#007bff' : '#aaa'};\n  font-size: 24px;\n  cursor: pointer;\n  transition: color 0.3s ease;\n  \n  &:hover {\n    color: #007bff;\n  }\n`;\n_c22 = VoiceButton;\nconst NavigationPanel = ({\n  currentLocation,\n  destination,\n  route,\n  isNavigating,\n  onStartNavigation,\n  onStopNavigation,\n  onBackToSearch\n}) => {\n  _s();\n  const [selectedRouteType, setSelectedRouteType] = useState('fastest');\n  const [voiceEnabled, setVoiceEnabled] = useState(true);\n  const [navigationState, setNavigationState] = useState({\n    isNavigating: false,\n    remainingDistance: 0,\n    remainingTime: 0\n  });\n\n  // Generate mock route data\n  const generateRoute = () => {\n    if (!currentLocation || !destination) {\n      throw new Error('Current location and destination are required');\n    }\n    const distance = getDistance({\n      latitude: currentLocation.lat,\n      longitude: currentLocation.lng\n    }, {\n      latitude: destination.lat,\n      longitude: destination.lng\n    });\n\n    // Generate simple route coordinates (in real app, this would come from routing API)\n    const coordinates = [[currentLocation.lng, currentLocation.lat], [destination.lng, destination.lat]];\n    const mockInstructions = [{\n      text: 'از موقعیت فعلی شروع کنید',\n      distance: 0,\n      duration: 0,\n      maneuver: 'start',\n      location: [currentLocation.lng, currentLocation.lat]\n    }, {\n      text: `به سمت ${destination.name || 'مقصد'} حرکت کنید`,\n      distance: distance * 0.8,\n      duration: distance * 0.8 / 50 * 3.6,\n      // Assuming 50 km/h average speed\n      maneuver: 'straight',\n      location: [destination.lng, destination.lat]\n    }, {\n      text: 'به مقصد رسیده‌اید',\n      distance: distance,\n      duration: distance / 50 * 3.6,\n      maneuver: 'arrive',\n      location: [destination.lng, destination.lat]\n    }];\n    return {\n      coordinates,\n      distance,\n      duration: distance / 50 * 3.6,\n      // Assuming 50 km/h average speed\n      instructions: mockInstructions,\n      bounds: {\n        north: Math.max(currentLocation.lat, destination.lat),\n        south: Math.min(currentLocation.lat, destination.lat),\n        east: Math.max(currentLocation.lng, destination.lng),\n        west: Math.min(currentLocation.lng, destination.lng)\n      }\n    };\n  };\n  const handleStartNavigation = () => {\n    if (!currentLocation || !destination) return;\n    const routeData = generateRoute();\n    setNavigationState({\n      isNavigating: true,\n      remainingDistance: routeData.distance,\n      remainingTime: routeData.duration,\n      currentInstruction: routeData.instructions[0],\n      nextInstruction: routeData.instructions[1]\n    });\n    onStartNavigation(routeData);\n  };\n  const handleStopNavigation = () => {\n    setNavigationState({\n      isNavigating: false,\n      remainingDistance: 0,\n      remainingTime: 0\n    });\n    onStopNavigation();\n  };\n  const formatDistance = meters => {\n    if (meters < 1000) {\n      return `${Math.round(meters)} متر`;\n    }\n    return `${(meters / 1000).toFixed(1)} کیلومتر`;\n  };\n  const formatDuration = seconds => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    if (hours > 0) {\n      return `${hours} ساعت ${minutes} دقیقه`;\n    }\n    return `${minutes} دقیقه`;\n  };\n  const currentRoute = route || (currentLocation && destination ? generateRoute() : null);\n  return /*#__PURE__*/_jsxDEV(PanelContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(BackButton, {\n        onClick: onBackToSearch,\n        children: \"\\u2190 \\u0628\\u0627\\u0632\\u06AF\\u0634\\u062A \\u0628\\u0647 \\u062C\\u0633\\u062A\\u062C\\u0648\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this), destination && /*#__PURE__*/_jsxDEV(DestinationInfo, {\n        children: [/*#__PURE__*/_jsxDEV(DestinationName, {\n          children: destination.name || 'مقصد انتخاب شده'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this), destination.address && /*#__PURE__*/_jsxDEV(DestinationAddress, {\n          children: destination.address\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(RouteOptions, {\n        children: [/*#__PURE__*/_jsxDEV(RouteOption, {\n          active: selectedRouteType === 'fastest',\n          onClick: () => setSelectedRouteType('fastest'),\n          children: \"\\u0633\\u0631\\u06CC\\u0639\\u200C\\u062A\\u0631\\u06CC\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RouteOption, {\n          active: selectedRouteType === 'shortest',\n          onClick: () => setSelectedRouteType('shortest'),\n          children: \"\\u06A9\\u0648\\u062A\\u0627\\u0647\\u200C\\u062A\\u0631\\u06CC\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RouteOption, {\n          active: selectedRouteType === 'eco',\n          onClick: () => setSelectedRouteType('eco'),\n          children: \"\\u0627\\u0642\\u062A\\u0635\\u0627\\u062F\\u06CC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(NavigationControls, {\n        children: !isNavigating ? /*#__PURE__*/_jsxDEV(ControlButton, {\n          primary: true,\n          onClick: handleStartNavigation,\n          children: \"\\u25B6 \\u0634\\u0631\\u0648\\u0639 \\u0645\\u0633\\u06CC\\u0631\\u06CC\\u0627\\u0628\\u06CC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ControlButton, {\n          onClick: handleStopNavigation,\n          children: \"\\u23F9 \\u062A\\u0648\\u0642\\u0641 \\u0645\\u0633\\u06CC\\u0631\\u06CC\\u0627\\u0628\\u06CC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Content, {\n      children: [/*#__PURE__*/_jsxDEV(NavigationStatus, {\n        isNavigating: isNavigating,\n        children: isNavigating ? 'در حال مسیریابی...' : 'آماده برای شروع'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this), currentRoute && /*#__PURE__*/_jsxDEV(RouteInfo, {\n        children: /*#__PURE__*/_jsxDEV(RouteStats, {\n          children: [/*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatValue, {\n              children: formatDistance(currentRoute.distance)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"\\u0645\\u0633\\u0627\\u0641\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatValue, {\n              children: formatDuration(currentRoute.duration)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"\\u0632\\u0645\\u0627\\u0646 \\u062A\\u0642\\u0631\\u06CC\\u0628\\u06CC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 11\n      }, this), isNavigating && navigationState.currentInstruction && /*#__PURE__*/_jsxDEV(CurrentInstruction, {\n        children: [/*#__PURE__*/_jsxDEV(InstructionText, {\n          children: navigationState.currentInstruction.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(InstructionDistance, {\n          children: formatDistance(navigationState.currentInstruction.distance)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(VoiceControls, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u0631\\u0627\\u0647\\u0646\\u0645\\u0627\\u06CC\\u06CC \\u0635\\u0648\\u062A\\u06CC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(VoiceButton, {\n          active: voiceEnabled,\n          onClick: () => setVoiceEnabled(!voiceEnabled),\n          children: voiceEnabled ? '🔊' : '🔇'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this), currentRoute && currentRoute.instructions && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '20px 0 12px 0',\n            fontSize: '16px',\n            color: '#ccc'\n          },\n          children: \"\\u062F\\u0633\\u062A\\u0648\\u0631\\u0627\\u0644\\u0639\\u0645\\u0644\\u200C\\u0647\\u0627\\u06CC \\u0645\\u0633\\u06CC\\u0631\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(InstructionsList, {\n          children: currentRoute.instructions.map((instruction, index) => /*#__PURE__*/_jsxDEV(InstructionItem, {\n            isCurrent: isNavigating && index === 0,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '500',\n                marginBottom: '4px'\n              },\n              children: instruction.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '12px',\n                color: '#aaa'\n              },\n              children: formatDistance(instruction.distance)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 339,\n    columnNumber: 5\n  }, this);\n};\n_s(NavigationPanel, \"MW8DDzehnRqPeEG61AUwJ87A2QY=\");\n_c23 = NavigationPanel;\nexport default NavigationPanel;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23;\n$RefreshReg$(_c, \"PanelContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"BackButton\");\n$RefreshReg$(_c4, \"DestinationInfo\");\n$RefreshReg$(_c5, \"DestinationName\");\n$RefreshReg$(_c6, \"DestinationAddress\");\n$RefreshReg$(_c7, \"RouteOptions\");\n$RefreshReg$(_c8, \"RouteOption\");\n$RefreshReg$(_c9, \"NavigationControls\");\n$RefreshReg$(_c0, \"ControlButton\");\n$RefreshReg$(_c1, \"RouteInfo\");\n$RefreshReg$(_c10, \"RouteStats\");\n$RefreshReg$(_c11, \"StatItem\");\n$RefreshReg$(_c12, \"StatValue\");\n$RefreshReg$(_c13, \"StatLabel\");\n$RefreshReg$(_c14, \"Content\");\n$RefreshReg$(_c15, \"NavigationStatus\");\n$RefreshReg$(_c16, \"CurrentInstruction\");\n$RefreshReg$(_c17, \"InstructionText\");\n$RefreshReg$(_c18, \"InstructionDistance\");\n$RefreshReg$(_c19, \"InstructionsList\");\n$RefreshReg$(_c20, \"InstructionItem\");\n$RefreshReg$(_c21, \"VoiceControls\");\n$RefreshReg$(_c22, \"VoiceButton\");\n$RefreshReg$(_c23, \"NavigationPanel\");", "map": {"version": 3, "names": ["React", "useState", "styled", "getDistance", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PanelContainer", "div", "_c", "Header", "_c2", "BackButton", "button", "_c3", "DestinationInfo", "_c4", "DestinationName", "h2", "_c5", "DestinationAddress", "_c6", "RouteOptions", "_c7", "RouteOption", "props", "active", "_c8", "NavigationControls", "_c9", "ControlButton", "primary", "_c0", "RouteInfo", "_c1", "RouteStats", "_c10", "StatItem", "_c11", "StatValue", "_c12", "StatLabel", "_c13", "Content", "_c14", "NavigationStatus", "isNavigating", "_c15", "CurrentInstruction", "_c16", "InstructionText", "_c17", "InstructionDistance", "_c18", "InstructionsList", "_c19", "InstructionItem", "isCurrent", "_c20", "VoiceControls", "_c21", "VoiceButton", "_c22", "NavigationPanel", "currentLocation", "destination", "route", "onStartNavigation", "onStopNavigation", "onBackToSearch", "_s", "selectedRouteType", "setSelectedRouteType", "voiceEnabled", "setVoiceEnabled", "navigationState", "setNavigationState", "remainingDistance", "remainingTime", "generateRoute", "Error", "distance", "latitude", "lat", "longitude", "lng", "coordinates", "mockInstructions", "text", "duration", "maneuver", "location", "name", "instructions", "bounds", "north", "Math", "max", "south", "min", "east", "west", "handleStartNavigation", "routeData", "currentInstruction", "nextInstruction", "handleStopNavigation", "formatDistance", "meters", "round", "toFixed", "formatDuration", "seconds", "hours", "floor", "minutes", "currentRoute", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "address", "style", "margin", "fontSize", "color", "map", "instruction", "index", "fontWeight", "marginBottom", "_c23", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/src/components/NavigationPanel.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\n// Using Unicode symbols instead of react-icons for React 19 compatibility\nimport { LocationData, RouteData, RouteInstruction, NavigationState } from '../types/gps.types';\nimport { getDistance } from 'geolib';\n\nconst PanelContainer = styled.div`\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background-color: #2d2d2d;\n  color: white;\n`;\n\nconst Header = styled.div`\n  padding: 20px;\n  border-bottom: 1px solid #444;\n  background-color: #1a1a1a;\n`;\n\nconst BackButton = styled.button`\n  background: none;\n  border: none;\n  color: #007bff;\n  font-size: 16px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 16px;\n  padding: 8px 0;\n  \n  &:hover {\n    color: #0056b3;\n  }\n`;\n\nconst DestinationInfo = styled.div`\n  margin-bottom: 20px;\n`;\n\nconst DestinationName = styled.h2`\n  margin: 0 0 8px 0;\n  font-size: 20px;\n  font-weight: 600;\n  color: white;\n`;\n\nconst DestinationAddress = styled.div`\n  font-size: 14px;\n  color: #aaa;\n  margin-bottom: 12px;\n`;\n\nconst RouteOptions = styled.div`\n  display: flex;\n  gap: 12px;\n  margin-bottom: 20px;\n`;\n\nconst RouteOption = styled.button<{ active?: boolean }>`\n  flex: 1;\n  padding: 12px;\n  border: 1px solid #444;\n  border-radius: 8px;\n  background-color: ${props => props.active ? '#007bff' : '#3d3d3d'};\n  color: white;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background-color: #007bff;\n    border-color: #007bff;\n  }\n`;\n\nconst NavigationControls = styled.div`\n  display: flex;\n  gap: 12px;\n  margin-bottom: 20px;\n`;\n\nconst ControlButton = styled.button<{ primary?: boolean }>`\n  flex: 1;\n  padding: 16px;\n  border: none;\n  border-radius: 12px;\n  background-color: ${props => props.primary ? '#28a745' : '#dc3545'};\n  color: white;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  \n  &:hover {\n    opacity: 0.9;\n    transform: translateY(-2px);\n  }\n  \n  &:active {\n    transform: translateY(0);\n  }\n`;\n\nconst RouteInfo = styled.div`\n  background-color: #3d3d3d;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 20px;\n`;\n\nconst RouteStats = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 16px;\n  margin-bottom: 16px;\n`;\n\nconst StatItem = styled.div`\n  text-align: center;\n`;\n\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 600;\n  color: #007bff;\n  margin-bottom: 4px;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 12px;\n  color: #aaa;\n`;\n\nconst Content = styled.div`\n  flex: 1;\n  overflow-y: auto;\n  padding: 0 20px 20px;\n`;\n\nconst NavigationStatus = styled.div<{ isNavigating: boolean }>`\n  background-color: ${props => props.isNavigating ? '#28a745' : '#6c757d'};\n  color: white;\n  padding: 16px;\n  border-radius: 12px;\n  margin-bottom: 20px;\n  text-align: center;\n  font-weight: 600;\n`;\n\nconst CurrentInstruction = styled.div`\n  background-color: #007bff;\n  color: white;\n  padding: 20px;\n  border-radius: 12px;\n  margin-bottom: 20px;\n  text-align: center;\n`;\n\nconst InstructionText = styled.div`\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 8px;\n`;\n\nconst InstructionDistance = styled.div`\n  font-size: 14px;\n  opacity: 0.9;\n`;\n\nconst InstructionsList = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n\nconst InstructionItem = styled.div<{ isCurrent?: boolean }>`\n  padding: 12px;\n  border: 1px solid #444;\n  border-radius: 8px;\n  background-color: ${props => props.isCurrent ? '#007bff' : '#3d3d3d'};\n  font-size: 14px;\n`;\n\nconst VoiceControls = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px;\n  background-color: #3d3d3d;\n  border-radius: 12px;\n  margin-bottom: 20px;\n`;\n\nconst VoiceButton = styled.button<{ active?: boolean }>`\n  background: none;\n  border: none;\n  color: ${props => props.active ? '#007bff' : '#aaa'};\n  font-size: 24px;\n  cursor: pointer;\n  transition: color 0.3s ease;\n  \n  &:hover {\n    color: #007bff;\n  }\n`;\n\ninterface NavigationPanelProps {\n  currentLocation: LocationData | null;\n  destination: LocationData | null;\n  route: RouteData | null;\n  isNavigating: boolean;\n  onStartNavigation: (route: RouteData) => void;\n  onStopNavigation: () => void;\n  onBackToSearch: () => void;\n}\n\nconst NavigationPanel: React.FC<NavigationPanelProps> = ({\n  currentLocation,\n  destination,\n  route,\n  isNavigating,\n  onStartNavigation,\n  onStopNavigation,\n  onBackToSearch\n}) => {\n  const [selectedRouteType, setSelectedRouteType] = useState<'fastest' | 'shortest' | 'eco'>('fastest');\n  const [voiceEnabled, setVoiceEnabled] = useState(true);\n  const [navigationState, setNavigationState] = useState<NavigationState>({\n    isNavigating: false,\n    remainingDistance: 0,\n    remainingTime: 0\n  });\n\n  // Generate mock route data\n  const generateRoute = (): RouteData => {\n    if (!currentLocation || !destination) {\n      throw new Error('Current location and destination are required');\n    }\n\n    const distance = getDistance(\n      { latitude: currentLocation.lat, longitude: currentLocation.lng },\n      { latitude: destination.lat, longitude: destination.lng }\n    );\n\n    // Generate simple route coordinates (in real app, this would come from routing API)\n    const coordinates: [number, number][] = [\n      [currentLocation.lng, currentLocation.lat],\n      [destination.lng, destination.lat]\n    ];\n\n    const mockInstructions: RouteInstruction[] = [\n      {\n        text: 'از موقعیت فعلی شروع کنید',\n        distance: 0,\n        duration: 0,\n        maneuver: 'start',\n        location: [currentLocation.lng, currentLocation.lat]\n      },\n      {\n        text: `به سمت ${destination.name || 'مقصد'} حرکت کنید`,\n        distance: distance * 0.8,\n        duration: (distance * 0.8) / 50 * 3.6, // Assuming 50 km/h average speed\n        maneuver: 'straight',\n        location: [destination.lng, destination.lat]\n      },\n      {\n        text: 'به مقصد رسیده‌اید',\n        distance: distance,\n        duration: distance / 50 * 3.6,\n        maneuver: 'arrive',\n        location: [destination.lng, destination.lat]\n      }\n    ];\n\n    return {\n      coordinates,\n      distance,\n      duration: distance / 50 * 3.6, // Assuming 50 km/h average speed\n      instructions: mockInstructions,\n      bounds: {\n        north: Math.max(currentLocation.lat, destination.lat),\n        south: Math.min(currentLocation.lat, destination.lat),\n        east: Math.max(currentLocation.lng, destination.lng),\n        west: Math.min(currentLocation.lng, destination.lng)\n      }\n    };\n  };\n\n  const handleStartNavigation = () => {\n    if (!currentLocation || !destination) return;\n    \n    const routeData = generateRoute();\n    setNavigationState({\n      isNavigating: true,\n      remainingDistance: routeData.distance,\n      remainingTime: routeData.duration,\n      currentInstruction: routeData.instructions[0],\n      nextInstruction: routeData.instructions[1]\n    });\n    \n    onStartNavigation(routeData);\n  };\n\n  const handleStopNavigation = () => {\n    setNavigationState({\n      isNavigating: false,\n      remainingDistance: 0,\n      remainingTime: 0\n    });\n    onStopNavigation();\n  };\n\n  const formatDistance = (meters: number): string => {\n    if (meters < 1000) {\n      return `${Math.round(meters)} متر`;\n    }\n    return `${(meters / 1000).toFixed(1)} کیلومتر`;\n  };\n\n  const formatDuration = (seconds: number): string => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    \n    if (hours > 0) {\n      return `${hours} ساعت ${minutes} دقیقه`;\n    }\n    return `${minutes} دقیقه`;\n  };\n\n  const currentRoute = route || (currentLocation && destination ? generateRoute() : null);\n\n  return (\n    <PanelContainer>\n      <Header>\n        <BackButton onClick={onBackToSearch}>\n          ← بازگشت به جستجو\n        </BackButton>\n        \n        {destination && (\n          <DestinationInfo>\n            <DestinationName>{destination.name || 'مقصد انتخاب شده'}</DestinationName>\n            {destination.address && (\n              <DestinationAddress>{destination.address}</DestinationAddress>\n            )}\n          </DestinationInfo>\n        )}\n\n        <RouteOptions>\n          <RouteOption \n            active={selectedRouteType === 'fastest'}\n            onClick={() => setSelectedRouteType('fastest')}\n          >\n            سریع‌ترین\n          </RouteOption>\n          <RouteOption \n            active={selectedRouteType === 'shortest'}\n            onClick={() => setSelectedRouteType('shortest')}\n          >\n            کوتاه‌ترین\n          </RouteOption>\n          <RouteOption \n            active={selectedRouteType === 'eco'}\n            onClick={() => setSelectedRouteType('eco')}\n          >\n            اقتصادی\n          </RouteOption>\n        </RouteOptions>\n\n        <NavigationControls>\n          {!isNavigating ? (\n            <ControlButton primary onClick={handleStartNavigation}>\n              ▶ شروع مسیریابی\n            </ControlButton>\n          ) : (\n            <ControlButton onClick={handleStopNavigation}>\n              ⏹ توقف مسیریابی\n            </ControlButton>\n          )}\n        </NavigationControls>\n      </Header>\n\n      <Content>\n        <NavigationStatus isNavigating={isNavigating}>\n          {isNavigating ? 'در حال مسیریابی...' : 'آماده برای شروع'}\n        </NavigationStatus>\n\n        {currentRoute && (\n          <RouteInfo>\n            <RouteStats>\n              <StatItem>\n                <StatValue>{formatDistance(currentRoute.distance)}</StatValue>\n                <StatLabel>مسافت</StatLabel>\n              </StatItem>\n              <StatItem>\n                <StatValue>{formatDuration(currentRoute.duration)}</StatValue>\n                <StatLabel>زمان تقریبی</StatLabel>\n              </StatItem>\n            </RouteStats>\n          </RouteInfo>\n        )}\n\n        {isNavigating && navigationState.currentInstruction && (\n          <CurrentInstruction>\n            <InstructionText>{navigationState.currentInstruction.text}</InstructionText>\n            <InstructionDistance>\n              {formatDistance(navigationState.currentInstruction.distance)}\n            </InstructionDistance>\n          </CurrentInstruction>\n        )}\n\n        <VoiceControls>\n          <span>راهنمایی صوتی</span>\n          <VoiceButton\n            active={voiceEnabled}\n            onClick={() => setVoiceEnabled(!voiceEnabled)}\n          >\n            {voiceEnabled ? '🔊' : '🔇'}\n          </VoiceButton>\n        </VoiceControls>\n\n        {currentRoute && currentRoute.instructions && (\n          <>\n            <h3 style={{ margin: '20px 0 12px 0', fontSize: '16px', color: '#ccc' }}>\n              دستورالعمل‌های مسیر\n            </h3>\n            <InstructionsList>\n              {currentRoute.instructions.map((instruction, index) => (\n                <InstructionItem \n                  key={index}\n                  isCurrent={isNavigating && index === 0}\n                >\n                  <div style={{ fontWeight: '500', marginBottom: '4px' }}>\n                    {instruction.text}\n                  </div>\n                  <div style={{ fontSize: '12px', color: '#aaa' }}>\n                    {formatDistance(instruction.distance)}\n                  </div>\n                </InstructionItem>\n              ))}\n            </InstructionsList>\n          </>\n        )}\n      </Content>\n    </PanelContainer>\n  );\n};\n\nexport default NavigationPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC;;AAEA,SAASC,WAAW,QAAQ,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErC,MAAMC,cAAc,GAAGN,MAAM,CAACO,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,cAAc;AAQpB,MAAMG,MAAM,GAAGT,MAAM,CAACO,GAAG;AACzB;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAJID,MAAM;AAMZ,MAAME,UAAU,GAAGX,MAAM,CAACY,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,UAAU;AAiBhB,MAAMG,eAAe,GAAGd,MAAM,CAACO,GAAG;AAClC;AACA,CAAC;AAACQ,GAAA,GAFID,eAAe;AAIrB,MAAME,eAAe,GAAGhB,MAAM,CAACiB,EAAE;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,eAAe;AAOrB,MAAMG,kBAAkB,GAAGnB,MAAM,CAACO,GAAG;AACrC;AACA;AACA;AACA,CAAC;AAACa,GAAA,GAJID,kBAAkB;AAMxB,MAAME,YAAY,GAAGrB,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAJID,YAAY;AAMlB,MAAME,WAAW,GAAGvB,MAAM,CAACY,MAA4B;AACvD;AACA;AACA;AACA;AACA,sBAAsBY,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,SAAS;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIH,WAAW;AAiBjB,MAAMI,kBAAkB,GAAG3B,MAAM,CAACO,GAAG;AACrC;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GAJID,kBAAkB;AAMxB,MAAME,aAAa,GAAG7B,MAAM,CAACY,MAA6B;AAC1D;AACA;AACA;AACA;AACA,sBAAsBY,KAAK,IAAIA,KAAK,CAACM,OAAO,GAAG,SAAS,GAAG,SAAS;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAxBIF,aAAa;AA0BnB,MAAMG,SAAS,GAAGhC,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GALID,SAAS;AAOf,MAAME,UAAU,GAAGlC,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GALID,UAAU;AAOhB,MAAME,QAAQ,GAAGpC,MAAM,CAACO,GAAG;AAC3B;AACA,CAAC;AAAC8B,IAAA,GAFID,QAAQ;AAId,MAAME,SAAS,GAAGtC,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GALID,SAAS;AAOf,MAAME,SAAS,GAAGxC,MAAM,CAACO,GAAG;AAC5B;AACA;AACA,CAAC;AAACkC,IAAA,GAHID,SAAS;AAKf,MAAME,OAAO,GAAG1C,MAAM,CAACO,GAAG;AAC1B;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GAJID,OAAO;AAMb,MAAME,gBAAgB,GAAG5C,MAAM,CAACO,GAA8B;AAC9D,sBAAsBiB,KAAK,IAAIA,KAAK,CAACqB,YAAY,GAAG,SAAS,GAAG,SAAS;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GARIF,gBAAgB;AAUtB,MAAMG,kBAAkB,GAAG/C,MAAM,CAACO,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyC,IAAA,GAPID,kBAAkB;AASxB,MAAME,eAAe,GAAGjD,MAAM,CAACO,GAAG;AAClC;AACA;AACA;AACA,CAAC;AAAC2C,IAAA,GAJID,eAAe;AAMrB,MAAME,mBAAmB,GAAGnD,MAAM,CAACO,GAAG;AACtC;AACA;AACA,CAAC;AAAC6C,IAAA,GAHID,mBAAmB;AAKzB,MAAME,gBAAgB,GAAGrD,MAAM,CAACO,GAAG;AACnC;AACA;AACA;AACA,CAAC;AAAC+C,IAAA,GAJID,gBAAgB;AAMtB,MAAME,eAAe,GAAGvD,MAAM,CAACO,GAA4B;AAC3D;AACA;AACA;AACA,sBAAsBiB,KAAK,IAAIA,KAAK,CAACgC,SAAS,GAAG,SAAS,GAAG,SAAS;AACtE;AACA,CAAC;AAACC,IAAA,GANIF,eAAe;AAQrB,MAAMG,aAAa,GAAG1D,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoD,IAAA,GARID,aAAa;AAUnB,MAAME,WAAW,GAAG5D,MAAM,CAACY,MAA4B;AACvD;AACA;AACA,WAAWY,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,MAAM;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GAXID,WAAW;AAuBjB,MAAME,eAA+C,GAAGA,CAAC;EACvDC,eAAe;EACfC,WAAW;EACXC,KAAK;EACLpB,YAAY;EACZqB,iBAAiB;EACjBC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxE,QAAQ,CAAiC,SAAS,CAAC;EACrG,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2E,eAAe,EAAEC,kBAAkB,CAAC,GAAG5E,QAAQ,CAAkB;IACtE8C,YAAY,EAAE,KAAK;IACnB+B,iBAAiB,EAAE,CAAC;IACpBC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAiB;IACrC,IAAI,CAACf,eAAe,IAAI,CAACC,WAAW,EAAE;MACpC,MAAM,IAAIe,KAAK,CAAC,+CAA+C,CAAC;IAClE;IAEA,MAAMC,QAAQ,GAAG/E,WAAW,CAC1B;MAAEgF,QAAQ,EAAElB,eAAe,CAACmB,GAAG;MAAEC,SAAS,EAAEpB,eAAe,CAACqB;IAAI,CAAC,EACjE;MAAEH,QAAQ,EAAEjB,WAAW,CAACkB,GAAG;MAAEC,SAAS,EAAEnB,WAAW,CAACoB;IAAI,CAC1D,CAAC;;IAED;IACA,MAAMC,WAA+B,GAAG,CACtC,CAACtB,eAAe,CAACqB,GAAG,EAAErB,eAAe,CAACmB,GAAG,CAAC,EAC1C,CAAClB,WAAW,CAACoB,GAAG,EAAEpB,WAAW,CAACkB,GAAG,CAAC,CACnC;IAED,MAAMI,gBAAoC,GAAG,CAC3C;MACEC,IAAI,EAAE,0BAA0B;MAChCP,QAAQ,EAAE,CAAC;MACXQ,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,CAAC3B,eAAe,CAACqB,GAAG,EAAErB,eAAe,CAACmB,GAAG;IACrD,CAAC,EACD;MACEK,IAAI,EAAE,UAAUvB,WAAW,CAAC2B,IAAI,IAAI,MAAM,YAAY;MACtDX,QAAQ,EAAEA,QAAQ,GAAG,GAAG;MACxBQ,QAAQ,EAAGR,QAAQ,GAAG,GAAG,GAAI,EAAE,GAAG,GAAG;MAAE;MACvCS,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,CAAC1B,WAAW,CAACoB,GAAG,EAAEpB,WAAW,CAACkB,GAAG;IAC7C,CAAC,EACD;MACEK,IAAI,EAAE,mBAAmB;MACzBP,QAAQ,EAAEA,QAAQ;MAClBQ,QAAQ,EAAER,QAAQ,GAAG,EAAE,GAAG,GAAG;MAC7BS,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,CAAC1B,WAAW,CAACoB,GAAG,EAAEpB,WAAW,CAACkB,GAAG;IAC7C,CAAC,CACF;IAED,OAAO;MACLG,WAAW;MACXL,QAAQ;MACRQ,QAAQ,EAAER,QAAQ,GAAG,EAAE,GAAG,GAAG;MAAE;MAC/BY,YAAY,EAAEN,gBAAgB;MAC9BO,MAAM,EAAE;QACNC,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACjC,eAAe,CAACmB,GAAG,EAAElB,WAAW,CAACkB,GAAG,CAAC;QACrDe,KAAK,EAAEF,IAAI,CAACG,GAAG,CAACnC,eAAe,CAACmB,GAAG,EAAElB,WAAW,CAACkB,GAAG,CAAC;QACrDiB,IAAI,EAAEJ,IAAI,CAACC,GAAG,CAACjC,eAAe,CAACqB,GAAG,EAAEpB,WAAW,CAACoB,GAAG,CAAC;QACpDgB,IAAI,EAAEL,IAAI,CAACG,GAAG,CAACnC,eAAe,CAACqB,GAAG,EAAEpB,WAAW,CAACoB,GAAG;MACrD;IACF,CAAC;EACH,CAAC;EAED,MAAMiB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACtC,eAAe,IAAI,CAACC,WAAW,EAAE;IAEtC,MAAMsC,SAAS,GAAGxB,aAAa,CAAC,CAAC;IACjCH,kBAAkB,CAAC;MACjB9B,YAAY,EAAE,IAAI;MAClB+B,iBAAiB,EAAE0B,SAAS,CAACtB,QAAQ;MACrCH,aAAa,EAAEyB,SAAS,CAACd,QAAQ;MACjCe,kBAAkB,EAAED,SAAS,CAACV,YAAY,CAAC,CAAC,CAAC;MAC7CY,eAAe,EAAEF,SAAS,CAACV,YAAY,CAAC,CAAC;IAC3C,CAAC,CAAC;IAEF1B,iBAAiB,CAACoC,SAAS,CAAC;EAC9B,CAAC;EAED,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;IACjC9B,kBAAkB,CAAC;MACjB9B,YAAY,EAAE,KAAK;MACnB+B,iBAAiB,EAAE,CAAC;MACpBC,aAAa,EAAE;IACjB,CAAC,CAAC;IACFV,gBAAgB,CAAC,CAAC;EACpB,CAAC;EAED,MAAMuC,cAAc,GAAIC,MAAc,IAAa;IACjD,IAAIA,MAAM,GAAG,IAAI,EAAE;MACjB,OAAO,GAAGZ,IAAI,CAACa,KAAK,CAACD,MAAM,CAAC,MAAM;IACpC;IACA,OAAO,GAAG,CAACA,MAAM,GAAG,IAAI,EAAEE,OAAO,CAAC,CAAC,CAAC,UAAU;EAChD,CAAC;EAED,MAAMC,cAAc,GAAIC,OAAe,IAAa;IAClD,MAAMC,KAAK,GAAGjB,IAAI,CAACkB,KAAK,CAACF,OAAO,GAAG,IAAI,CAAC;IACxC,MAAMG,OAAO,GAAGnB,IAAI,CAACkB,KAAK,CAAEF,OAAO,GAAG,IAAI,GAAI,EAAE,CAAC;IAEjD,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,SAASE,OAAO,QAAQ;IACzC;IACA,OAAO,GAAGA,OAAO,QAAQ;EAC3B,CAAC;EAED,MAAMC,YAAY,GAAGlD,KAAK,KAAKF,eAAe,IAAIC,WAAW,GAAGc,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC;EAEvF,oBACE3E,OAAA,CAACG,cAAc;IAAA8G,QAAA,gBACbjH,OAAA,CAACM,MAAM;MAAA2G,QAAA,gBACLjH,OAAA,CAACQ,UAAU;QAAC0G,OAAO,EAAEjD,cAAe;QAAAgD,QAAA,EAAC;MAErC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZzD,WAAW,iBACV7D,OAAA,CAACW,eAAe;QAAAsG,QAAA,gBACdjH,OAAA,CAACa,eAAe;UAAAoG,QAAA,EAAEpD,WAAW,CAAC2B,IAAI,IAAI;QAAiB;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC,EACzEzD,WAAW,CAAC0D,OAAO,iBAClBvH,OAAA,CAACgB,kBAAkB;UAAAiG,QAAA,EAAEpD,WAAW,CAAC0D;QAAO;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAqB,CAC9D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAClB,eAEDtH,OAAA,CAACkB,YAAY;QAAA+F,QAAA,gBACXjH,OAAA,CAACoB,WAAW;UACVE,MAAM,EAAE6C,iBAAiB,KAAK,SAAU;UACxC+C,OAAO,EAAEA,CAAA,KAAM9C,oBAAoB,CAAC,SAAS,CAAE;UAAA6C,QAAA,EAChD;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACdtH,OAAA,CAACoB,WAAW;UACVE,MAAM,EAAE6C,iBAAiB,KAAK,UAAW;UACzC+C,OAAO,EAAEA,CAAA,KAAM9C,oBAAoB,CAAC,UAAU,CAAE;UAAA6C,QAAA,EACjD;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACdtH,OAAA,CAACoB,WAAW;UACVE,MAAM,EAAE6C,iBAAiB,KAAK,KAAM;UACpC+C,OAAO,EAAEA,CAAA,KAAM9C,oBAAoB,CAAC,KAAK,CAAE;UAAA6C,QAAA,EAC5C;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEftH,OAAA,CAACwB,kBAAkB;QAAAyF,QAAA,EAChB,CAACvE,YAAY,gBACZ1C,OAAA,CAAC0B,aAAa;UAACC,OAAO;UAACuF,OAAO,EAAEhB,qBAAsB;UAAAe,QAAA,EAAC;QAEvD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,gBAEhBtH,OAAA,CAAC0B,aAAa;UAACwF,OAAO,EAAEZ,oBAAqB;UAAAW,QAAA,EAAC;QAE9C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe;MAChB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACiB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAETtH,OAAA,CAACuC,OAAO;MAAA0E,QAAA,gBACNjH,OAAA,CAACyC,gBAAgB;QAACC,YAAY,EAAEA,YAAa;QAAAuE,QAAA,EAC1CvE,YAAY,GAAG,oBAAoB,GAAG;MAAiB;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,EAElBN,YAAY,iBACXhH,OAAA,CAAC6B,SAAS;QAAAoF,QAAA,eACRjH,OAAA,CAAC+B,UAAU;UAAAkF,QAAA,gBACTjH,OAAA,CAACiC,QAAQ;YAAAgF,QAAA,gBACPjH,OAAA,CAACmC,SAAS;cAAA8E,QAAA,EAAEV,cAAc,CAACS,YAAY,CAACnC,QAAQ;YAAC;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9DtH,OAAA,CAACqC,SAAS;cAAA4E,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACXtH,OAAA,CAACiC,QAAQ;YAAAgF,QAAA,gBACPjH,OAAA,CAACmC,SAAS;cAAA8E,QAAA,EAAEN,cAAc,CAACK,YAAY,CAAC3B,QAAQ;YAAC;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9DtH,OAAA,CAACqC,SAAS;cAAA4E,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACZ,EAEA5E,YAAY,IAAI6B,eAAe,CAAC6B,kBAAkB,iBACjDpG,OAAA,CAAC4C,kBAAkB;QAAAqE,QAAA,gBACjBjH,OAAA,CAAC8C,eAAe;UAAAmE,QAAA,EAAE1C,eAAe,CAAC6B,kBAAkB,CAAChB;QAAI;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC,eAC5EtH,OAAA,CAACgD,mBAAmB;UAAAiE,QAAA,EACjBV,cAAc,CAAChC,eAAe,CAAC6B,kBAAkB,CAACvB,QAAQ;QAAC;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACrB,eAEDtH,OAAA,CAACuD,aAAa;QAAA0D,QAAA,gBACZjH,OAAA;UAAAiH,QAAA,EAAM;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1BtH,OAAA,CAACyD,WAAW;UACVnC,MAAM,EAAE+C,YAAa;UACrB6C,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,CAACD,YAAY,CAAE;UAAA4C,QAAA,EAE7C5C,YAAY,GAAG,IAAI,GAAG;QAAI;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAEfN,YAAY,IAAIA,YAAY,CAACvB,YAAY,iBACxCzF,OAAA,CAAAE,SAAA;QAAA+G,QAAA,gBACEjH,OAAA;UAAIwH,KAAK,EAAE;YAAEC,MAAM,EAAE,eAAe;YAAEC,QAAQ,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAEzE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtH,OAAA,CAACkD,gBAAgB;UAAA+D,QAAA,EACdD,YAAY,CAACvB,YAAY,CAACmC,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBAChD9H,OAAA,CAACoD,eAAe;YAEdC,SAAS,EAAEX,YAAY,IAAIoF,KAAK,KAAK,CAAE;YAAAb,QAAA,gBAEvCjH,OAAA;cAAKwH,KAAK,EAAE;gBAAEO,UAAU,EAAE,KAAK;gBAAEC,YAAY,EAAE;cAAM,CAAE;cAAAf,QAAA,EACpDY,WAAW,CAACzC;YAAI;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACNtH,OAAA;cAAKwH,KAAK,EAAE;gBAAEE,QAAQ,EAAE,MAAM;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAV,QAAA,EAC7CV,cAAc,CAACsB,WAAW,CAAChD,QAAQ;YAAC;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA,GARDQ,KAAK;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASK,CAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA,eACnB,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAErB,CAAC;AAACpD,EAAA,CArOIP,eAA+C;AAAAsE,IAAA,GAA/CtE,eAA+C;AAuOrD,eAAeA,eAAe;AAAC,IAAAtD,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAuE,IAAA;AAAAC,YAAA,CAAA7H,EAAA;AAAA6H,YAAA,CAAA3H,GAAA;AAAA2H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAAnH,GAAA;AAAAmH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAzG,GAAA;AAAAyG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAlG,IAAA;AAAAkG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}