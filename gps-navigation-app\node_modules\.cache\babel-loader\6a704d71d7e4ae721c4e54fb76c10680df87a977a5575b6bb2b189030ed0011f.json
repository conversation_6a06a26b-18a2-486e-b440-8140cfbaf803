{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _constants = require(\"./constants\");\nvar convertDistance = function convertDistance(meters) {\n  var targetUnit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"m\";\n  var factor = _constants.distanceConversion[targetUnit];\n  if (factor) {\n    return meters * factor;\n  }\n  throw new Error(\"Invalid unit used for distance conversion.\");\n};\nvar _default = convertDistance;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_constants", "require", "convertDistance", "meters", "targetUnit", "arguments", "length", "undefined", "factor", "distanceConversion", "Error", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/convertDistance.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _constants=require(\"./constants\");var convertDistance=function convertDistance(meters){var targetUnit=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"m\";var factor=_constants.distanceConversion[targetUnit];if(factor){return meters*factor}throw new Error(\"Invalid unit used for distance conversion.\")};var _default=convertDistance;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,UAAU,GAACC,OAAO,CAAC,aAAa,CAAC;AAAC,IAAIC,eAAe,GAAC,SAASA,eAAeA,CAACC,MAAM,EAAC;EAAC,IAAIC,UAAU,GAACC,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAGE,SAAS,GAACF,SAAS,CAAC,CAAC,CAAC,GAAC,GAAG;EAAC,IAAIG,MAAM,GAACR,UAAU,CAACS,kBAAkB,CAACL,UAAU,CAAC;EAAC,IAAGI,MAAM,EAAC;IAAC,OAAOL,MAAM,GAACK,MAAM;EAAA;EAAC,MAAM,IAAIE,KAAK,CAAC,4CAA4C,CAAC;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACT,eAAe;AAACL,OAAO,CAACE,OAAO,GAACY,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}