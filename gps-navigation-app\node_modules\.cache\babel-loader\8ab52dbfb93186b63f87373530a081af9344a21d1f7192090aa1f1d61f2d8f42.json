{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\GPS\\\\gps-navigation-app\\\\src\\\\components\\\\GPSNavigationApp.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport MapComponent from './MapComponent';\nimport NavigationPanel from './NavigationPanel';\nimport SearchPanel from './SearchPanel';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  display: flex;\n  height: 100vh;\n  width: 100vw;\n  background-color: #1a1a1a;\n  color: white;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  overflow: hidden;\n`;\n_c = AppContainer;\nconst MapContainer = styled.div`\n  flex: 1;\n  position: relative;\n  height: 100%;\n`;\n_c2 = MapContainer;\nconst SidePanel = styled.div`\n  width: ${props => props.isOpen ? '400px' : '0px'};\n  transition: width 0.3s ease;\n  background-color: #2d2d2d;\n  border-left: 1px solid #444;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  \n  @media (max-width: 768px) {\n    position: absolute;\n    right: 0;\n    top: 0;\n    height: 100%;\n    z-index: 1000;\n    width: ${props => props.isOpen ? '100%' : '0px'};\n  }\n`;\n_c3 = SidePanel;\nconst ToggleButton = styled.button`\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  z-index: 1001;\n  background-color: #007bff;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 60px;\n  height: 60px;\n  font-size: 24px;\n  cursor: pointer;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background-color: #0056b3;\n    transform: scale(1.1);\n  }\n  \n  &:active {\n    transform: scale(0.95);\n  }\n`;\n_c4 = ToggleButton;\nconst GPSNavigationApp = () => {\n  _s();\n  const [currentLocation, setCurrentLocation] = useState(null);\n  const [destination, setDestination] = useState(null);\n  const [route, setRoute] = useState(null);\n  const [isNavigating, setIsNavigating] = useState(false);\n  const [isPanelOpen, setIsPanelOpen] = useState(false);\n  const [searchMode, setSearchMode] = useState('search');\n\n  // Get user's current location\n  useEffect(() => {\n    if (navigator.geolocation) {\n      const watchId = navigator.geolocation.watchPosition(position => {\n        const newLocation = {\n          lat: position.coords.latitude,\n          lng: position.coords.longitude,\n          accuracy: position.coords.accuracy,\n          timestamp: Date.now()\n        };\n        setCurrentLocation(newLocation);\n      }, error => {\n        console.error('Error getting location:', error);\n        // Fallback to a default location (Tehran, Iran)\n        setCurrentLocation({\n          lat: 35.6892,\n          lng: 51.3890,\n          accuracy: 100,\n          timestamp: Date.now()\n        });\n      }, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 60000\n      });\n      return () => navigator.geolocation.clearWatch(watchId);\n    }\n  }, []);\n  const handleDestinationSelect = location => {\n    setDestination(location);\n    setSearchMode('navigation');\n    setIsPanelOpen(true);\n  };\n  const handleStartNavigation = routeData => {\n    setRoute(routeData);\n    setIsNavigating(true);\n    setIsPanelOpen(false);\n  };\n  const handleStopNavigation = () => {\n    setIsNavigating(false);\n    setRoute(null);\n    setDestination(null);\n    setSearchMode('search');\n  };\n  const togglePanel = () => {\n    setIsPanelOpen(!isPanelOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(AppContainer, {\n    children: [/*#__PURE__*/_jsxDEV(MapContainer, {\n      children: [/*#__PURE__*/_jsxDEV(MapComponent, {\n        currentLocation: currentLocation,\n        destination: destination,\n        route: route,\n        isNavigating: isNavigating,\n        onLocationSelect: handleDestinationSelect\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ToggleButton, {\n        onClick: togglePanel,\n        children: isPanelOpen ? '×' : '☰'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SidePanel, {\n      isOpen: isPanelOpen,\n      children: searchMode === 'search' ? /*#__PURE__*/_jsxDEV(SearchPanel, {\n        currentLocation: currentLocation,\n        onDestinationSelect: handleDestinationSelect\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(NavigationPanel, {\n        currentLocation: currentLocation,\n        destination: destination,\n        route: route,\n        isNavigating: isNavigating,\n        onStartNavigation: handleStartNavigation,\n        onStopNavigation: handleStopNavigation,\n        onBackToSearch: () => setSearchMode('search')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(GPSNavigationApp, \"XJAX9vehu0YQ35xVLm3CEFNQblM=\");\n_c5 = GPSNavigationApp;\nexport default GPSNavigationApp;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"MapContainer\");\n$RefreshReg$(_c3, \"SidePanel\");\n$RefreshReg$(_c4, \"ToggleButton\");\n$RefreshReg$(_c5, \"GPSNavigationApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "MapComponent", "NavigationPanel", "SearchPanel", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "MapContainer", "_c2", "SidePanel", "props", "isOpen", "_c3", "ToggleButton", "button", "_c4", "GPSNavigationApp", "_s", "currentLocation", "setCurrentLocation", "destination", "setDestination", "route", "setRoute", "isNavigating", "setIsNavigating", "isPanelOpen", "setIsPanelOpen", "searchMode", "setSearchMode", "navigator", "geolocation", "watchId", "watchPosition", "position", "newLocation", "lat", "coords", "latitude", "lng", "longitude", "accuracy", "timestamp", "Date", "now", "error", "console", "enableHighAccuracy", "timeout", "maximumAge", "clearWatch", "handleDestinationSelect", "location", "handleStartNavigation", "routeData", "handleStopNavigation", "togglePanel", "children", "onLocationSelect", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onDestinationSelect", "onStartNavigation", "onStopNavigation", "onBackToSearch", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/src/components/GPSNavigationApp.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport MapComponent from './MapComponent';\nimport NavigationPanel from './NavigationPanel';\nimport SearchPanel from './SearchPanel';\nimport { LocationData, RouteData } from '../types/gps.types';\n\nconst AppContainer = styled.div`\n  display: flex;\n  height: 100vh;\n  width: 100vw;\n  background-color: #1a1a1a;\n  color: white;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  overflow: hidden;\n`;\n\nconst MapContainer = styled.div`\n  flex: 1;\n  position: relative;\n  height: 100%;\n`;\n\nconst SidePanel = styled.div<{ isOpen: boolean }>`\n  width: ${props => props.isOpen ? '400px' : '0px'};\n  transition: width 0.3s ease;\n  background-color: #2d2d2d;\n  border-left: 1px solid #444;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  \n  @media (max-width: 768px) {\n    position: absolute;\n    right: 0;\n    top: 0;\n    height: 100%;\n    z-index: 1000;\n    width: ${props => props.isOpen ? '100%' : '0px'};\n  }\n`;\n\nconst ToggleButton = styled.button`\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  z-index: 1001;\n  background-color: #007bff;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 60px;\n  height: 60px;\n  font-size: 24px;\n  cursor: pointer;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background-color: #0056b3;\n    transform: scale(1.1);\n  }\n  \n  &:active {\n    transform: scale(0.95);\n  }\n`;\n\nconst GPSNavigationApp: React.FC = () => {\n  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);\n  const [destination, setDestination] = useState<LocationData | null>(null);\n  const [route, setRoute] = useState<RouteData | null>(null);\n  const [isNavigating, setIsNavigating] = useState(false);\n  const [isPanelOpen, setIsPanelOpen] = useState(false);\n  const [searchMode, setSearchMode] = useState<'search' | 'navigation'>('search');\n\n  // Get user's current location\n  useEffect(() => {\n    if (navigator.geolocation) {\n      const watchId = navigator.geolocation.watchPosition(\n        (position) => {\n          const newLocation: LocationData = {\n            lat: position.coords.latitude,\n            lng: position.coords.longitude,\n            accuracy: position.coords.accuracy,\n            timestamp: Date.now()\n          };\n          setCurrentLocation(newLocation);\n        },\n        (error) => {\n          console.error('Error getting location:', error);\n          // Fallback to a default location (Tehran, Iran)\n          setCurrentLocation({\n            lat: 35.6892,\n            lng: 51.3890,\n            accuracy: 100,\n            timestamp: Date.now()\n          });\n        },\n        {\n          enableHighAccuracy: true,\n          timeout: 10000,\n          maximumAge: 60000\n        }\n      );\n\n      return () => navigator.geolocation.clearWatch(watchId);\n    }\n  }, []);\n\n  const handleDestinationSelect = (location: LocationData) => {\n    setDestination(location);\n    setSearchMode('navigation');\n    setIsPanelOpen(true);\n  };\n\n  const handleStartNavigation = (routeData: RouteData) => {\n    setRoute(routeData);\n    setIsNavigating(true);\n    setIsPanelOpen(false);\n  };\n\n  const handleStopNavigation = () => {\n    setIsNavigating(false);\n    setRoute(null);\n    setDestination(null);\n    setSearchMode('search');\n  };\n\n  const togglePanel = () => {\n    setIsPanelOpen(!isPanelOpen);\n  };\n\n  return (\n    <AppContainer>\n      <MapContainer>\n        <MapComponent\n          currentLocation={currentLocation}\n          destination={destination}\n          route={route}\n          isNavigating={isNavigating}\n          onLocationSelect={handleDestinationSelect}\n        />\n        <ToggleButton onClick={togglePanel}>\n          {isPanelOpen ? '×' : '☰'}\n        </ToggleButton>\n      </MapContainer>\n      \n      <SidePanel isOpen={isPanelOpen}>\n        {searchMode === 'search' ? (\n          <SearchPanel\n            currentLocation={currentLocation}\n            onDestinationSelect={handleDestinationSelect}\n          />\n        ) : (\n          <NavigationPanel\n            currentLocation={currentLocation}\n            destination={destination}\n            route={route}\n            isNavigating={isNavigating}\n            onStartNavigation={handleStartNavigation}\n            onStopNavigation={handleStopNavigation}\n            onBackToSearch={() => setSearchMode('search')}\n          />\n        )}\n      </SidePanel>\n    </AppContainer>\n  );\n};\n\nexport default GPSNavigationApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGxC,MAAMC,YAAY,GAAGN,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,YAAY;AAUlB,MAAMG,YAAY,GAAGT,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAJID,YAAY;AAMlB,MAAME,SAAS,GAAGX,MAAM,CAACO,GAAwB;AACjD,WAAWK,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,OAAO,GAAG,KAAK;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,MAAM,GAAG,KAAK;AACnD;AACA,CAAC;AAACC,GAAA,GAjBIH,SAAS;AAmBf,MAAMI,YAAY,GAAGf,MAAM,CAACgB,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAxBIF,YAAY;AA0BlB,MAAMG,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAsB,IAAI,CAAC;EACjF,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAsB,IAAI,CAAC;EACzE,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAmB,IAAI,CAAC;EAC1D,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAA0B,QAAQ,CAAC;;EAE/E;EACAC,SAAS,CAAC,MAAM;IACd,IAAIiC,SAAS,CAACC,WAAW,EAAE;MACzB,MAAMC,OAAO,GAAGF,SAAS,CAACC,WAAW,CAACE,aAAa,CAChDC,QAAQ,IAAK;QACZ,MAAMC,WAAyB,GAAG;UAChCC,GAAG,EAAEF,QAAQ,CAACG,MAAM,CAACC,QAAQ;UAC7BC,GAAG,EAAEL,QAAQ,CAACG,MAAM,CAACG,SAAS;UAC9BC,QAAQ,EAAEP,QAAQ,CAACG,MAAM,CAACI,QAAQ;UAClCC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;QACtB,CAAC;QACDzB,kBAAkB,CAACgB,WAAW,CAAC;MACjC,CAAC,EACAU,KAAK,IAAK;QACTC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;QACA1B,kBAAkB,CAAC;UACjBiB,GAAG,EAAE,OAAO;UACZG,GAAG,EAAE,OAAO;UACZE,QAAQ,EAAE,GAAG;UACbC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,EACD;QACEG,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE;MACd,CACF,CAAC;MAED,OAAO,MAAMnB,SAAS,CAACC,WAAW,CAACmB,UAAU,CAAClB,OAAO,CAAC;IACxD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmB,uBAAuB,GAAIC,QAAsB,IAAK;IAC1D/B,cAAc,CAAC+B,QAAQ,CAAC;IACxBvB,aAAa,CAAC,YAAY,CAAC;IAC3BF,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM0B,qBAAqB,GAAIC,SAAoB,IAAK;IACtD/B,QAAQ,CAAC+B,SAAS,CAAC;IACnB7B,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAM4B,oBAAoB,GAAGA,CAAA,KAAM;IACjC9B,eAAe,CAAC,KAAK,CAAC;IACtBF,QAAQ,CAAC,IAAI,CAAC;IACdF,cAAc,CAAC,IAAI,CAAC;IACpBQ,aAAa,CAAC,QAAQ,CAAC;EACzB,CAAC;EAED,MAAM2B,WAAW,GAAGA,CAAA,KAAM;IACxB7B,cAAc,CAAC,CAACD,WAAW,CAAC;EAC9B,CAAC;EAED,oBACEvB,OAAA,CAACC,YAAY;IAAAqD,QAAA,gBACXtD,OAAA,CAACI,YAAY;MAAAkD,QAAA,gBACXtD,OAAA,CAACJ,YAAY;QACXmB,eAAe,EAAEA,eAAgB;QACjCE,WAAW,EAAEA,WAAY;QACzBE,KAAK,EAAEA,KAAM;QACbE,YAAY,EAAEA,YAAa;QAC3BkC,gBAAgB,EAAEP;MAAwB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACF3D,OAAA,CAACU,YAAY;QAACkD,OAAO,EAAEP,WAAY;QAAAC,QAAA,EAChC/B,WAAW,GAAG,GAAG,GAAG;MAAG;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEf3D,OAAA,CAACM,SAAS;MAACE,MAAM,EAAEe,WAAY;MAAA+B,QAAA,EAC5B7B,UAAU,KAAK,QAAQ,gBACtBzB,OAAA,CAACF,WAAW;QACViB,eAAe,EAAEA,eAAgB;QACjC8C,mBAAmB,EAAEb;MAAwB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,gBAEF3D,OAAA,CAACH,eAAe;QACdkB,eAAe,EAAEA,eAAgB;QACjCE,WAAW,EAAEA,WAAY;QACzBE,KAAK,EAAEA,KAAM;QACbE,YAAY,EAAEA,YAAa;QAC3ByC,iBAAiB,EAAEZ,qBAAsB;QACzCa,gBAAgB,EAAEX,oBAAqB;QACvCY,cAAc,EAAEA,CAAA,KAAMtC,aAAa,CAAC,QAAQ;MAAE;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEnB,CAAC;AAAC7C,EAAA,CApGID,gBAA0B;AAAAoD,GAAA,GAA1BpD,gBAA0B;AAsGhC,eAAeA,gBAAgB;AAAC,IAAAV,EAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAqD,GAAA;AAAAC,YAAA,CAAA/D,EAAA;AAAA+D,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAAzD,GAAA;AAAAyD,YAAA,CAAAtD,GAAA;AAAAsD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}