{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"bounds\"];\nimport { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Rectangle as LeafletRectangle } from 'leaflet';\nexport const Rectangle = createPathComponent(function createRectangle(_ref, ctx) {\n  let {\n      bounds\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const rectangle = new LeafletRectangle(bounds, options);\n  return createElementObject(rectangle, extendContext(ctx, {\n    overlayContainer: rectangle\n  }));\n}, function updateRectangle(layer, props, prevProps) {\n  if (props.bounds !== prevProps.bounds) {\n    layer.setBounds(props.bounds);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "Rectangle", "LeafletRectangle", "createRectangle", "_ref", "ctx", "bounds", "options", "_objectWithoutProperties", "_excluded", "rectangle", "overlayContainer", "updateRectangle", "layer", "props", "prevProps", "setBounds"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/Rectangle.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Rectangle as LeafletRectangle } from 'leaflet';\nexport const Rectangle = createPathComponent(function createRectangle({ bounds, ...options }, ctx) {\n    const rectangle = new LeafletRectangle(bounds, options);\n    return createElementObject(rectangle, extendContext(ctx, {\n        overlayContainer: rectangle\n    }));\n}, function updateRectangle(layer, props, prevProps) {\n    if (props.bounds !== prevProps.bounds) {\n        layer.setBounds(props.bounds);\n    }\n});\n"], "mappings": ";;AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,qBAAqB;AAC7F,SAASC,SAAS,IAAIC,gBAAgB,QAAQ,SAAS;AACvD,OAAO,MAAMD,SAAS,GAAGF,mBAAmB,CAAC,SAASI,eAAeA,CAAAC,IAAA,EAAyBC,GAAG,EAAE;EAAA,IAA7B;MAAEC;IAAmB,CAAC,GAAAF,IAAA;IAATG,OAAO,GAAAC,wBAAA,CAAAJ,IAAA,EAAAK,SAAA;EACtF,MAAMC,SAAS,GAAG,IAAIR,gBAAgB,CAACI,MAAM,EAAEC,OAAO,CAAC;EACvD,OAAOT,mBAAmB,CAACY,SAAS,EAAEV,aAAa,CAACK,GAAG,EAAE;IACrDM,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASE,eAAeA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EACjD,IAAID,KAAK,CAACR,MAAM,KAAKS,SAAS,CAACT,MAAM,EAAE;IACnCO,KAAK,CAACG,SAAS,CAACF,KAAK,CAACR,MAAM,CAAC;EACjC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}