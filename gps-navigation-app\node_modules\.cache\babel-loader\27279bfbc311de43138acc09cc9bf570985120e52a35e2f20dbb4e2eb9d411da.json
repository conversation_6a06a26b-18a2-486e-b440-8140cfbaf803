{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getLatitude = _interopRequireDefault(require(\"./getLatitude\"));\nvar _getLongitude = _interopRequireDefault(require(\"./getLongitude\"));\nvar _toRad = _interopRequireDefault(require(\"./toRad\"));\nvar _constants = require(\"./constants\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar getDistance = function getDistance(start, end) {\n  var accuracy = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  accuracy = typeof accuracy !== \"undefined\" && !isNaN(accuracy) ? accuracy : 1;\n  var startLat = (0, _getLatitude.default)(start);\n  var startLon = (0, _getLongitude.default)(start);\n  var endLat = (0, _getLatitude.default)(end);\n  var endLon = (0, _getLongitude.default)(end);\n  var b = 6356752.314245;\n  var ellipsoidParams = 1 / 298.257223563;\n  var L = (0, _toRad.default)(endLon - startLon);\n  var cosSigma;\n  var sigma;\n  var sinAlpha;\n  var cosSqAlpha;\n  var cos2SigmaM;\n  var sinSigma;\n  var U1 = Math.atan((1 - ellipsoidParams) * Math.tan((0, _toRad.default)(parseFloat(startLat))));\n  var U2 = Math.atan((1 - ellipsoidParams) * Math.tan((0, _toRad.default)(parseFloat(endLat))));\n  var sinU1 = Math.sin(U1);\n  var cosU1 = Math.cos(U1);\n  var sinU2 = Math.sin(U2);\n  var cosU2 = Math.cos(U2);\n  var lambda = L;\n  var lambdaP;\n  var iterLimit = 100;\n  do {\n    var sinLambda = Math.sin(lambda);\n    var cosLambda = Math.cos(lambda);\n    sinSigma = Math.sqrt(cosU2 * sinLambda * (cosU2 * sinLambda) + (cosU1 * sinU2 - sinU1 * cosU2 * cosLambda) * (cosU1 * sinU2 - sinU1 * cosU2 * cosLambda));\n    if (sinSigma === 0) {\n      return 0;\n    }\n    cosSigma = sinU1 * sinU2 + cosU1 * cosU2 * cosLambda;\n    sigma = Math.atan2(sinSigma, cosSigma);\n    sinAlpha = cosU1 * cosU2 * sinLambda / sinSigma;\n    cosSqAlpha = 1 - sinAlpha * sinAlpha;\n    cos2SigmaM = cosSigma - 2 * sinU1 * sinU2 / cosSqAlpha;\n    if (isNaN(cos2SigmaM)) {\n      cos2SigmaM = 0;\n    }\n    var C = ellipsoidParams / 16 * cosSqAlpha * (4 + ellipsoidParams * (4 - 3 * cosSqAlpha));\n    lambdaP = lambda;\n    lambda = L + (1 - C) * ellipsoidParams * sinAlpha * (sigma + C * sinSigma * (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));\n  } while (Math.abs(lambda - lambdaP) > 1e-12 && --iterLimit > 0);\n  if (iterLimit === 0) {\n    return NaN;\n  }\n  var uSq = cosSqAlpha * (_constants.earthRadius * _constants.earthRadius - b * b) / (b * b);\n  var A = 1 + uSq / 16384 * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));\n  var B = uSq / 1024 * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));\n  var deltaSigma = B * sinSigma * (cos2SigmaM + B / 4 * (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM) - B / 6 * cos2SigmaM * (-3 + 4 * sinSigma * sinSigma) * (-3 + 4 * cos2SigmaM * cos2SigmaM)));\n  var distance = b * A * (sigma - deltaSigma);\n  return Math.round(distance / accuracy) * accuracy;\n};\nvar _default = getDistance;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getLatitude", "_interopRequireDefault", "require", "_getLongitude", "_toRad", "_constants", "obj", "__esModule", "getDistance", "start", "end", "accuracy", "arguments", "length", "undefined", "isNaN", "startLat", "startLon", "endLat", "endLon", "b", "ellipsoidParams", "L", "cosSigma", "sigma", "sinAlpha", "cosSqAlpha", "cos2SigmaM", "sinSigma", "U1", "Math", "atan", "tan", "parseFloat", "U2", "sinU1", "sin", "cosU1", "cos", "sinU2", "cosU2", "lambda", "lambdaP", "iterLimit", "sinLambda", "cosLambda", "sqrt", "atan2", "C", "abs", "NaN", "uSq", "earthRadius", "A", "B", "deltaSigma", "distance", "round", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getPreciseDistance.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getLatitude=_interopRequireDefault(require(\"./getLatitude\"));var _getLongitude=_interopRequireDefault(require(\"./getLongitude\"));var _toRad=_interopRequireDefault(require(\"./toRad\"));var _constants=require(\"./constants\");function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var getDistance=function getDistance(start,end){var accuracy=arguments.length>2&&arguments[2]!==undefined?arguments[2]:1;accuracy=typeof accuracy!==\"undefined\"&&!isNaN(accuracy)?accuracy:1;var startLat=(0,_getLatitude.default)(start);var startLon=(0,_getLongitude.default)(start);var endLat=(0,_getLatitude.default)(end);var endLon=(0,_getLongitude.default)(end);var b=6356752.314245;var ellipsoidParams=1/298.257223563;var L=(0,_toRad.default)(endLon-startLon);var cosSigma;var sigma;var sinAlpha;var cosSqAlpha;var cos2SigmaM;var sinSigma;var U1=Math.atan((1-ellipsoidParams)*Math.tan((0,_toRad.default)(parseFloat(startLat))));var U2=Math.atan((1-ellipsoidParams)*Math.tan((0,_toRad.default)(parseFloat(endLat))));var sinU1=Math.sin(U1);var cosU1=Math.cos(U1);var sinU2=Math.sin(U2);var cosU2=Math.cos(U2);var lambda=L;var lambdaP;var iterLimit=100;do{var sinLambda=Math.sin(lambda);var cosLambda=Math.cos(lambda);sinSigma=Math.sqrt(cosU2*sinLambda*(cosU2*sinLambda)+(cosU1*sinU2-sinU1*cosU2*cosLambda)*(cosU1*sinU2-sinU1*cosU2*cosLambda));if(sinSigma===0){return 0}cosSigma=sinU1*sinU2+cosU1*cosU2*cosLambda;sigma=Math.atan2(sinSigma,cosSigma);sinAlpha=cosU1*cosU2*sinLambda/sinSigma;cosSqAlpha=1-sinAlpha*sinAlpha;cos2SigmaM=cosSigma-2*sinU1*sinU2/cosSqAlpha;if(isNaN(cos2SigmaM)){cos2SigmaM=0}var C=ellipsoidParams/16*cosSqAlpha*(4+ellipsoidParams*(4-3*cosSqAlpha));lambdaP=lambda;lambda=L+(1-C)*ellipsoidParams*sinAlpha*(sigma+C*sinSigma*(cos2SigmaM+C*cosSigma*(-1+2*cos2SigmaM*cos2SigmaM)))}while(Math.abs(lambda-lambdaP)>1e-12&&--iterLimit>0);if(iterLimit===0){return NaN}var uSq=cosSqAlpha*(_constants.earthRadius*_constants.earthRadius-b*b)/(b*b);var A=1+uSq/16384*(4096+uSq*(-768+uSq*(320-175*uSq)));var B=uSq/1024*(256+uSq*(-128+uSq*(74-47*uSq)));var deltaSigma=B*sinSigma*(cos2SigmaM+B/4*(cosSigma*(-1+2*cos2SigmaM*cos2SigmaM)-B/6*cos2SigmaM*(-3+4*sinSigma*sinSigma)*(-3+4*cos2SigmaM*cos2SigmaM)));var distance=b*A*(sigma-deltaSigma);return Math.round(distance/accuracy)*accuracy};var _default=getDistance;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,IAAIC,aAAa,GAACF,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAAC,IAAIE,MAAM,GAACH,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAAC,IAAIG,UAAU,GAACH,OAAO,CAAC,aAAa,CAAC;AAAC,SAASD,sBAAsBA,CAACK,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACP,OAAO,EAACO;EAAG,CAAC;AAAA;AAAC,IAAIE,WAAW,GAAC,SAASA,WAAWA,CAACC,KAAK,EAACC,GAAG,EAAC;EAAC,IAAIC,QAAQ,GAACC,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAGE,SAAS,GAACF,SAAS,CAAC,CAAC,CAAC,GAAC,CAAC;EAACD,QAAQ,GAAC,OAAOA,QAAQ,KAAG,WAAW,IAAE,CAACI,KAAK,CAACJ,QAAQ,CAAC,GAACA,QAAQ,GAAC,CAAC;EAAC,IAAIK,QAAQ,GAAC,CAAC,CAAC,EAAChB,YAAY,CAACD,OAAO,EAAEU,KAAK,CAAC;EAAC,IAAIQ,QAAQ,GAAC,CAAC,CAAC,EAACd,aAAa,CAACJ,OAAO,EAAEU,KAAK,CAAC;EAAC,IAAIS,MAAM,GAAC,CAAC,CAAC,EAAClB,YAAY,CAACD,OAAO,EAAEW,GAAG,CAAC;EAAC,IAAIS,MAAM,GAAC,CAAC,CAAC,EAAChB,aAAa,CAACJ,OAAO,EAAEW,GAAG,CAAC;EAAC,IAAIU,CAAC,GAAC,cAAc;EAAC,IAAIC,eAAe,GAAC,CAAC,GAAC,aAAa;EAAC,IAAIC,CAAC,GAAC,CAAC,CAAC,EAAClB,MAAM,CAACL,OAAO,EAAEoB,MAAM,GAACF,QAAQ,CAAC;EAAC,IAAIM,QAAQ;EAAC,IAAIC,KAAK;EAAC,IAAIC,QAAQ;EAAC,IAAIC,UAAU;EAAC,IAAIC,UAAU;EAAC,IAAIC,QAAQ;EAAC,IAAIC,EAAE,GAACC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAACV,eAAe,IAAES,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,EAAC5B,MAAM,CAACL,OAAO,EAAEkC,UAAU,CAACjB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAAC,IAAIkB,EAAE,GAACJ,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAACV,eAAe,IAAES,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,EAAC5B,MAAM,CAACL,OAAO,EAAEkC,UAAU,CAACf,MAAM,CAAC,CAAC,CAAC,CAAC;EAAC,IAAIiB,KAAK,GAACL,IAAI,CAACM,GAAG,CAACP,EAAE,CAAC;EAAC,IAAIQ,KAAK,GAACP,IAAI,CAACQ,GAAG,CAACT,EAAE,CAAC;EAAC,IAAIU,KAAK,GAACT,IAAI,CAACM,GAAG,CAACF,EAAE,CAAC;EAAC,IAAIM,KAAK,GAACV,IAAI,CAACQ,GAAG,CAACJ,EAAE,CAAC;EAAC,IAAIO,MAAM,GAACnB,CAAC;EAAC,IAAIoB,OAAO;EAAC,IAAIC,SAAS,GAAC,GAAG;EAAC,GAAE;IAAC,IAAIC,SAAS,GAACd,IAAI,CAACM,GAAG,CAACK,MAAM,CAAC;IAAC,IAAII,SAAS,GAACf,IAAI,CAACQ,GAAG,CAACG,MAAM,CAAC;IAACb,QAAQ,GAACE,IAAI,CAACgB,IAAI,CAACN,KAAK,GAACI,SAAS,IAAEJ,KAAK,GAACI,SAAS,CAAC,GAAC,CAACP,KAAK,GAACE,KAAK,GAACJ,KAAK,GAACK,KAAK,GAACK,SAAS,KAAGR,KAAK,GAACE,KAAK,GAACJ,KAAK,GAACK,KAAK,GAACK,SAAS,CAAC,CAAC;IAAC,IAAGjB,QAAQ,KAAG,CAAC,EAAC;MAAC,OAAO,CAAC;IAAA;IAACL,QAAQ,GAACY,KAAK,GAACI,KAAK,GAACF,KAAK,GAACG,KAAK,GAACK,SAAS;IAACrB,KAAK,GAACM,IAAI,CAACiB,KAAK,CAACnB,QAAQ,EAACL,QAAQ,CAAC;IAACE,QAAQ,GAACY,KAAK,GAACG,KAAK,GAACI,SAAS,GAAChB,QAAQ;IAACF,UAAU,GAAC,CAAC,GAACD,QAAQ,GAACA,QAAQ;IAACE,UAAU,GAACJ,QAAQ,GAAC,CAAC,GAACY,KAAK,GAACI,KAAK,GAACb,UAAU;IAAC,IAAGX,KAAK,CAACY,UAAU,CAAC,EAAC;MAACA,UAAU,GAAC,CAAC;IAAA;IAAC,IAAIqB,CAAC,GAAC3B,eAAe,GAAC,EAAE,GAACK,UAAU,IAAE,CAAC,GAACL,eAAe,IAAE,CAAC,GAAC,CAAC,GAACK,UAAU,CAAC,CAAC;IAACgB,OAAO,GAACD,MAAM;IAACA,MAAM,GAACnB,CAAC,GAAC,CAAC,CAAC,GAAC0B,CAAC,IAAE3B,eAAe,GAACI,QAAQ,IAAED,KAAK,GAACwB,CAAC,GAACpB,QAAQ,IAAED,UAAU,GAACqB,CAAC,GAACzB,QAAQ,IAAE,CAAC,CAAC,GAAC,CAAC,GAACI,UAAU,GAACA,UAAU,CAAC,CAAC,CAAC;EAAA,CAAC,QAAMG,IAAI,CAACmB,GAAG,CAACR,MAAM,GAACC,OAAO,CAAC,GAAC,KAAK,IAAE,EAAEC,SAAS,GAAC,CAAC;EAAE,IAAGA,SAAS,KAAG,CAAC,EAAC;IAAC,OAAOO,GAAG;EAAA;EAAC,IAAIC,GAAG,GAACzB,UAAU,IAAErB,UAAU,CAAC+C,WAAW,GAAC/C,UAAU,CAAC+C,WAAW,GAAChC,CAAC,GAACA,CAAC,CAAC,IAAEA,CAAC,GAACA,CAAC,CAAC;EAAC,IAAIiC,CAAC,GAAC,CAAC,GAACF,GAAG,GAAC,KAAK,IAAE,IAAI,GAACA,GAAG,IAAE,CAAC,GAAG,GAACA,GAAG,IAAE,GAAG,GAAC,GAAG,GAACA,GAAG,CAAC,CAAC,CAAC;EAAC,IAAIG,CAAC,GAACH,GAAG,GAAC,IAAI,IAAE,GAAG,GAACA,GAAG,IAAE,CAAC,GAAG,GAACA,GAAG,IAAE,EAAE,GAAC,EAAE,GAACA,GAAG,CAAC,CAAC,CAAC;EAAC,IAAII,UAAU,GAACD,CAAC,GAAC1B,QAAQ,IAAED,UAAU,GAAC2B,CAAC,GAAC,CAAC,IAAE/B,QAAQ,IAAE,CAAC,CAAC,GAAC,CAAC,GAACI,UAAU,GAACA,UAAU,CAAC,GAAC2B,CAAC,GAAC,CAAC,GAAC3B,UAAU,IAAE,CAAC,CAAC,GAAC,CAAC,GAACC,QAAQ,GAACA,QAAQ,CAAC,IAAE,CAAC,CAAC,GAAC,CAAC,GAACD,UAAU,GAACA,UAAU,CAAC,CAAC,CAAC;EAAC,IAAI6B,QAAQ,GAACpC,CAAC,GAACiC,CAAC,IAAE7B,KAAK,GAAC+B,UAAU,CAAC;EAAC,OAAOzB,IAAI,CAAC2B,KAAK,CAACD,QAAQ,GAAC7C,QAAQ,CAAC,GAACA,QAAQ;AAAA,CAAC;AAAC,IAAI+C,QAAQ,GAAClD,WAAW;AAACX,OAAO,CAACE,OAAO,GAAC2D,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}