import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMap, useMapEvents } from 'react-leaflet';
import L from 'leaflet';
import styled from 'styled-components';
import { LocationData, RouteData } from '../types/gps.types';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),
  iconUrl: require('leaflet/dist/images/marker-icon.png'),
  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),
});

const MapWrapper = styled.div`
  height: 100%;
  width: 100%;
  position: relative;
  
  .leaflet-container {
    height: 100%;
    width: 100%;
    background-color: #1a1a1a;
  }
  
  .leaflet-control-zoom {
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  
  .leaflet-control-zoom a {
    background-color: #2d2d2d;
    color: white;
    border: 1px solid #444;
    font-size: 18px;
    line-height: 26px;
    
    &:hover {
      background-color: #007bff;
      color: white;
    }
  }
`;

const MapControls = styled.div`
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const ControlButton = styled.button`
  background-color: #2d2d2d;
  color: white;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  font-size: 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #007bff;
  }
  
  &:active {
    transform: scale(0.95);
  }
`;

const LocationInfo = styled.div`
  position: absolute;
  bottom: 20px;
  left: 20px;
  background-color: rgba(45, 45, 45, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  max-width: 300px;
`;

// Custom icons
const currentLocationIcon = new L.Icon({
  iconUrl: 'data:image/svg+xml;base64,' + btoa(`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="8" fill="#007bff" stroke="white" stroke-width="2"/>
      <circle cx="12" cy="12" r="3" fill="white"/>
    </svg>
  `),
  iconSize: [24, 24],
  iconAnchor: [12, 12],
  popupAnchor: [0, -12],
});

const destinationIcon = new L.Icon({
  iconUrl: 'data:image/svg+xml;base64,' + btoa(`
    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="#dc3545" stroke="white" stroke-width="1"/>
      <circle cx="12" cy="9" r="2.5" fill="white"/>
    </svg>
  `),
  iconSize: [32, 32],
  iconAnchor: [16, 32],
  popupAnchor: [0, -32],
});

interface MapComponentProps {
  currentLocation: LocationData | null;
  destination: LocationData | null;
  route: RouteData | null;
  isNavigating: boolean;
  onLocationSelect: (location: LocationData) => void;
}

// Component to handle map events
const MapEventHandler: React.FC<{ onLocationSelect: (location: LocationData) => void }> = ({ onLocationSelect }) => {
  useMapEvents({
    click: (e) => {
      const { lat, lng } = e.latlng;
      onLocationSelect({
        lat,
        lng,
        timestamp: Date.now()
      });
    }
  });
  return null;
};

// Component to handle map centering
const MapCenter: React.FC<{ center: [number, number]; zoom?: number }> = ({ center, zoom = 15 }) => {
  const map = useMap();
  
  useEffect(() => {
    map.setView(center, zoom);
  }, [map, center, zoom]);
  
  return null;
};

const MapComponent: React.FC<MapComponentProps> = ({
  currentLocation,
  destination,
  route,
  isNavigating,
  onLocationSelect
}) => {
  const [mapTheme, setMapTheme] = useState<'light' | 'dark' | 'satellite'>('dark');
  const [showTraffic, setShowTraffic] = useState(false);
  const mapRef = useRef<L.Map | null>(null);

  const defaultCenter: [number, number] = [35.6892, 51.3890]; // Tehran, Iran
  const mapCenter: [number, number] = currentLocation 
    ? [currentLocation.lat, currentLocation.lng] 
    : defaultCenter;

  const getTileLayerUrl = () => {
    switch (mapTheme) {
      case 'light':
        return 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';
      case 'satellite':
        return 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}';
      case 'dark':
      default:
        return 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png';
    }
  };

  const handleCenterOnLocation = () => {
    if (currentLocation && mapRef.current) {
      mapRef.current.setView([currentLocation.lat, currentLocation.lng], 16);
    }
  };

  const toggleMapTheme = () => {
    const themes: ('light' | 'dark' | 'satellite')[] = ['light', 'dark', 'satellite'];
    const currentIndex = themes.indexOf(mapTheme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setMapTheme(themes[nextIndex]);
  };

  return (
    <MapWrapper>
      <MapContainer
        center={mapCenter}
        zoom={15}
        style={{ height: '100%', width: '100%' }}
        ref={mapRef}
        zoomControl={true}
        attributionControl={false}
      >
        <TileLayer
          url={getTileLayerUrl()}
          attribution='&copy; OpenStreetMap contributors'
        />
        
        <MapEventHandler onLocationSelect={onLocationSelect} />
        
        {currentLocation && (
          <MapCenter center={[currentLocation.lat, currentLocation.lng]} />
        )}
        
        {currentLocation && (
          <Marker 
            position={[currentLocation.lat, currentLocation.lng]} 
            icon={currentLocationIcon}
          >
            <Popup>
              <div>
                <strong>موقعیت فعلی شما</strong><br />
                عرض جغرافیایی: {currentLocation.lat.toFixed(6)}<br />
                طول جغرافیایی: {currentLocation.lng.toFixed(6)}<br />
                {currentLocation.accuracy && (
                  <>دقت: {currentLocation.accuracy.toFixed(0)} متر</>
                )}
              </div>
            </Popup>
          </Marker>
        )}
        
        {destination && (
          <Marker 
            position={[destination.lat, destination.lng]} 
            icon={destinationIcon}
          >
            <Popup>
              <div>
                <strong>مقصد</strong><br />
                {destination.name || 'موقعیت انتخاب شده'}<br />
                {destination.address && <>{destination.address}<br /></>}
                عرض جغرافیایی: {destination.lat.toFixed(6)}<br />
                طول جغرافیایی: {destination.lng.toFixed(6)}
              </div>
            </Popup>
          </Marker>
        )}
        
        {route && route.coordinates && (
          <Polyline
            positions={route.coordinates.map(coord => [coord[1], coord[0]])}
            color="#007bff"
            weight={6}
            opacity={0.8}
          />
        )}
      </MapContainer>
      
      <MapControls>
        <ControlButton onClick={handleCenterOnLocation} title="مرکز کردن روی موقعیت فعلی">
          📍
        </ControlButton>
        <ControlButton onClick={toggleMapTheme} title="تغییر تم نقشه">
          🌓
        </ControlButton>
        <ControlButton onClick={() => setShowTraffic(!showTraffic)} title="نمایش ترافیک">
          🚦
        </ControlButton>
      </MapControls>
      
      {currentLocation && (
        <LocationInfo>
          <div><strong>موقعیت فعلی:</strong></div>
          <div>عرض: {currentLocation.lat.toFixed(6)}</div>
          <div>طول: {currentLocation.lng.toFixed(6)}</div>
          {currentLocation.accuracy && (
            <div>دقت: {currentLocation.accuracy.toFixed(0)}م</div>
          )}
        </LocationInfo>
      )}
    </MapWrapper>
  );
};

export default MapComponent;
