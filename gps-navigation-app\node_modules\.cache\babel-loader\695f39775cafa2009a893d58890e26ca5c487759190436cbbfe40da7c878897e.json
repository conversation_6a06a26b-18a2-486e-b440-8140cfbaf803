{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _constants = require(\"./constants\");\nvar _getCoordinateKey = _interopRequireDefault(require(\"./getCoordinateKey\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nvar getCoordinateKeys = function getCoordinateKeys(point) {\n  var keysToLookup = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    longitude: _constants.longitudeKeys,\n    latitude: _constants.latitudeKeys,\n    altitude: _constants.altitudeKeys\n  };\n  var longitude = (0, _getCoordinateKey.default)(point, keysToLookup.longitude);\n  var latitude = (0, _getCoordinateKey.default)(point, keysToLookup.latitude);\n  var altitude = (0, _getCoordinateKey.default)(point, keysToLookup.altitude);\n  return _objectSpread({\n    latitude: latitude,\n    longitude: longitude\n  }, altitude ? {\n    altitude: altitude\n  } : {});\n};\nvar _default = getCoordinateKeys;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_constants", "require", "_getCoordinateKey", "_interopRequireDefault", "obj", "__esModule", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "configurable", "writable", "getCoordinateKeys", "point", "keysToLookup", "undefined", "longitude", "longitudeKeys", "latitude", "latitudeKeys", "altitude", "altitudeKeys", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getCoordinateKeys.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _constants=require(\"./constants\");var _getCoordinateKey=_interopRequireDefault(require(\"./getCoordinateKey\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function ownKeys(object,enumerableOnly){var keys=Object.keys(object);if(Object.getOwnPropertySymbols){var symbols=Object.getOwnPropertySymbols(object);if(enumerableOnly)symbols=symbols.filter(function(sym){return Object.getOwnPropertyDescriptor(object,sym).enumerable});keys.push.apply(keys,symbols)}return keys}function _objectSpread(target){for(var i=1;i<arguments.length;i++){var source=arguments[i]!=null?arguments[i]:{};if(i%2){ownKeys(Object(source),true).forEach(function(key){_defineProperty(target,key,source[key])})}else if(Object.getOwnPropertyDescriptors){Object.defineProperties(target,Object.getOwnPropertyDescriptors(source))}else{ownKeys(Object(source)).forEach(function(key){Object.defineProperty(target,key,Object.getOwnPropertyDescriptor(source,key))})}}return target}function _defineProperty(obj,key,value){if(key in obj){Object.defineProperty(obj,key,{value:value,enumerable:true,configurable:true,writable:true})}else{obj[key]=value}return obj}var getCoordinateKeys=function getCoordinateKeys(point){var keysToLookup=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{longitude:_constants.longitudeKeys,latitude:_constants.latitudeKeys,altitude:_constants.altitudeKeys};var longitude=(0,_getCoordinateKey.default)(point,keysToLookup.longitude);var latitude=(0,_getCoordinateKey.default)(point,keysToLookup.latitude);var altitude=(0,_getCoordinateKey.default)(point,keysToLookup.altitude);return _objectSpread({latitude:latitude,longitude:longitude},altitude?{altitude:altitude}:{})};var _default=getCoordinateKeys;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,UAAU,GAACC,OAAO,CAAC,aAAa,CAAC;AAAC,IAAIC,iBAAiB,GAACC,sBAAsB,CAACF,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAAC,SAASE,sBAAsBA,CAACC,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACL,OAAO,EAACK;EAAG,CAAC;AAAA;AAAC,SAASE,OAAOA,CAACC,MAAM,EAACC,cAAc,EAAC;EAAC,IAAIC,IAAI,GAACd,MAAM,CAACc,IAAI,CAACF,MAAM,CAAC;EAAC,IAAGZ,MAAM,CAACe,qBAAqB,EAAC;IAAC,IAAIC,OAAO,GAAChB,MAAM,CAACe,qBAAqB,CAACH,MAAM,CAAC;IAAC,IAAGC,cAAc,EAACG,OAAO,GAACA,OAAO,CAACC,MAAM,CAAC,UAASC,GAAG,EAAC;MAAC,OAAOlB,MAAM,CAACmB,wBAAwB,CAACP,MAAM,EAACM,GAAG,CAAC,CAACE,UAAU;IAAA,CAAC,CAAC;IAACN,IAAI,CAACO,IAAI,CAACC,KAAK,CAACR,IAAI,EAACE,OAAO,CAAC;EAAA;EAAC,OAAOF,IAAI;AAAA;AAAC,SAASS,aAAaA,CAACC,MAAM,EAAC;EAAC,KAAI,IAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACC,SAAS,CAACC,MAAM,EAACF,CAAC,EAAE,EAAC;IAAC,IAAIG,MAAM,GAACF,SAAS,CAACD,CAAC,CAAC,IAAE,IAAI,GAACC,SAAS,CAACD,CAAC,CAAC,GAAC,CAAC,CAAC;IAAC,IAAGA,CAAC,GAAC,CAAC,EAAC;MAACd,OAAO,CAACX,MAAM,CAAC4B,MAAM,CAAC,EAAC,IAAI,CAAC,CAACC,OAAO,CAAC,UAASC,GAAG,EAAC;QAACC,eAAe,CAACP,MAAM,EAACM,GAAG,EAACF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAA,CAAC,CAAC;IAAA,CAAC,MAAK,IAAG9B,MAAM,CAACgC,yBAAyB,EAAC;MAAChC,MAAM,CAACiC,gBAAgB,CAACT,MAAM,EAACxB,MAAM,CAACgC,yBAAyB,CAACJ,MAAM,CAAC,CAAC;IAAA,CAAC,MAAI;MAACjB,OAAO,CAACX,MAAM,CAAC4B,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAASC,GAAG,EAAC;QAAC9B,MAAM,CAACC,cAAc,CAACuB,MAAM,EAACM,GAAG,EAAC9B,MAAM,CAACmB,wBAAwB,CAACS,MAAM,EAACE,GAAG,CAAC,CAAC;MAAA,CAAC,CAAC;IAAA;EAAC;EAAC,OAAON,MAAM;AAAA;AAAC,SAASO,eAAeA,CAACtB,GAAG,EAACqB,GAAG,EAAC3B,KAAK,EAAC;EAAC,IAAG2B,GAAG,IAAIrB,GAAG,EAAC;IAACT,MAAM,CAACC,cAAc,CAACQ,GAAG,EAACqB,GAAG,EAAC;MAAC3B,KAAK,EAACA,KAAK;MAACiB,UAAU,EAAC,IAAI;MAACc,YAAY,EAAC,IAAI;MAACC,QAAQ,EAAC;IAAI,CAAC,CAAC;EAAA,CAAC,MAAI;IAAC1B,GAAG,CAACqB,GAAG,CAAC,GAAC3B,KAAK;EAAA;EAAC,OAAOM,GAAG;AAAA;AAAC,IAAI2B,iBAAiB,GAAC,SAASA,iBAAiBA,CAACC,KAAK,EAAC;EAAC,IAAIC,YAAY,GAACZ,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAGa,SAAS,GAACb,SAAS,CAAC,CAAC,CAAC,GAAC;IAACc,SAAS,EAACnC,UAAU,CAACoC,aAAa;IAACC,QAAQ,EAACrC,UAAU,CAACsC,YAAY;IAACC,QAAQ,EAACvC,UAAU,CAACwC;EAAY,CAAC;EAAC,IAAIL,SAAS,GAAC,CAAC,CAAC,EAACjC,iBAAiB,CAACH,OAAO,EAAEiC,KAAK,EAACC,YAAY,CAACE,SAAS,CAAC;EAAC,IAAIE,QAAQ,GAAC,CAAC,CAAC,EAACnC,iBAAiB,CAACH,OAAO,EAAEiC,KAAK,EAACC,YAAY,CAACI,QAAQ,CAAC;EAAC,IAAIE,QAAQ,GAAC,CAAC,CAAC,EAACrC,iBAAiB,CAACH,OAAO,EAAEiC,KAAK,EAACC,YAAY,CAACM,QAAQ,CAAC;EAAC,OAAOrB,aAAa,CAAC;IAACmB,QAAQ,EAACA,QAAQ;IAACF,SAAS,EAACA;EAAS,CAAC,EAACI,QAAQ,GAAC;IAACA,QAAQ,EAACA;EAAQ,CAAC,GAAC,CAAC,CAAC,CAAC;AAAA,CAAC;AAAC,IAAIE,QAAQ,GAACV,iBAAiB;AAAClC,OAAO,CAACE,OAAO,GAAC0C,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}