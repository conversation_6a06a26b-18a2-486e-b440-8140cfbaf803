{"ast": null, "code": "import { createElementObject, createTileLayerComponent, updateGridLayer, withPane } from '@react-leaflet/core';\nimport { TileLayer } from 'leaflet';\nexport const WMSTileLayer = createTileLayerComponent(function createWMSTileLayer({\n  eventHandlers: _eh,\n  params = {},\n  url,\n  ...options\n}, context) {\n  const layer = new TileLayer.WMS(url, {\n    ...params,\n    ...withPane(options, context)\n  });\n  return createElementObject(layer, context);\n}, function updateWMSTileLayer(layer, props, prevProps) {\n  updateGridLayer(layer, props, prevProps);\n  if (props.params != null && props.params !== prevProps.params) {\n    layer.setParams(props.params);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createTileLayerComponent", "updateGridLayer", "with<PERSON>ane", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WMSTileLayer", "createWMSTileLayer", "eventHandlers", "_eh", "params", "url", "options", "context", "layer", "WMS", "updateWMSTileLayer", "props", "prevProps", "setParams"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/WMSTileLayer.js"], "sourcesContent": ["import { createElementObject, createTileLayerComponent, updateGridLayer, withPane } from '@react-leaflet/core';\nimport { TileLayer } from 'leaflet';\nexport const WMSTileLayer = createTileLayerComponent(function createWMSTileLayer({ eventHandlers: _eh, params = {}, url, ...options }, context) {\n    const layer = new TileLayer.WMS(url, {\n        ...params,\n        ...withPane(options, context)\n    });\n    return createElementObject(layer, context);\n}, function updateWMSTileLayer(layer, props, prevProps) {\n    updateGridLayer(layer, props, prevProps);\n    if (props.params != null && props.params !== prevProps.params) {\n        layer.setParams(props.params);\n    }\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,wBAAwB,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,qBAAqB;AAC9G,SAASC,SAAS,QAAQ,SAAS;AACnC,OAAO,MAAMC,YAAY,GAAGJ,wBAAwB,CAAC,SAASK,kBAAkBA,CAAC;EAAEC,aAAa,EAAEC,GAAG;EAAEC,MAAM,GAAG,CAAC,CAAC;EAAEC,GAAG;EAAE,GAAGC;AAAQ,CAAC,EAAEC,OAAO,EAAE;EAC5I,MAAMC,KAAK,GAAG,IAAIT,SAAS,CAACU,GAAG,CAACJ,GAAG,EAAE;IACjC,GAAGD,MAAM;IACT,GAAGN,QAAQ,CAACQ,OAAO,EAAEC,OAAO;EAChC,CAAC,CAAC;EACF,OAAOZ,mBAAmB,CAACa,KAAK,EAAED,OAAO,CAAC;AAC9C,CAAC,EAAE,SAASG,kBAAkBA,CAACF,KAAK,EAAEG,KAAK,EAAEC,SAAS,EAAE;EACpDf,eAAe,CAACW,KAAK,EAAEG,KAAK,EAAEC,SAAS,CAAC;EACxC,IAAID,KAAK,CAACP,MAAM,IAAI,IAAI,IAAIO,KAAK,CAACP,MAAM,KAAKQ,SAAS,CAACR,MAAM,EAAE;IAC3DI,KAAK,CAACK,SAAS,CAACF,KAAK,CAACP,MAAM,CAAC;EACjC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}