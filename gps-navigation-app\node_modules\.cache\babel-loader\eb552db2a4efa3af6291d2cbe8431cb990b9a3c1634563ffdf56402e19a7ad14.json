{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"positions\"];\nimport { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Polygon as LeafletPolygon } from 'leaflet';\nexport const Polygon = createPathComponent(function createPolygon(_ref, ctx) {\n  let {\n      positions\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const polygon = new LeafletPolygon(positions, options);\n  return createElementObject(polygon, extendContext(ctx, {\n    overlayContainer: polygon\n  }));\n}, function updatePolygon(layer, props, prevProps) {\n  if (props.positions !== prevProps.positions) {\n    layer.setLatLngs(props.positions);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "Polygon", "LeafletPolygon", "createPolygon", "_ref", "ctx", "positions", "options", "_objectWithoutProperties", "_excluded", "polygon", "overlayContainer", "updatePolygon", "layer", "props", "prevProps", "setLatLngs"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/Polygon.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Polygon as LeafletPolygon } from 'leaflet';\nexport const Polygon = createPathComponent(function createPolygon({ positions, ...options }, ctx) {\n    const polygon = new LeafletPolygon(positions, options);\n    return createElementObject(polygon, extendContext(ctx, {\n        overlayContainer: polygon\n    }));\n}, function updatePolygon(layer, props, prevProps) {\n    if (props.positions !== prevProps.positions) {\n        layer.setLatLngs(props.positions);\n    }\n});\n"], "mappings": ";;AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,qBAAqB;AAC7F,SAASC,OAAO,IAAIC,cAAc,QAAQ,SAAS;AACnD,OAAO,MAAMD,OAAO,GAAGF,mBAAmB,CAAC,SAASI,aAAaA,CAAAC,IAAA,EAA4BC,GAAG,EAAE;EAAA,IAAhC;MAAEC;IAAsB,CAAC,GAAAF,IAAA;IAATG,OAAO,GAAAC,wBAAA,CAAAJ,IAAA,EAAAK,SAAA;EACrF,MAAMC,OAAO,GAAG,IAAIR,cAAc,CAACI,SAAS,EAAEC,OAAO,CAAC;EACtD,OAAOT,mBAAmB,CAACY,OAAO,EAAEV,aAAa,CAACK,GAAG,EAAE;IACnDM,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASE,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC/C,IAAID,KAAK,CAACR,SAAS,KAAKS,SAAS,CAACT,SAAS,EAAE;IACzCO,KAAK,CAACG,UAAU,CAACF,KAAK,CAACR,SAAS,CAAC;EACrC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}