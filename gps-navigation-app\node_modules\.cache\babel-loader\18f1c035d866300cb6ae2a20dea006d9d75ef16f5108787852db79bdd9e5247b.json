{"ast": null, "code": "import { useAttribution } from './attribution.js';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { withPane } from './pane.js';\nexport function createDivOverlayHook(useElement, useLifecycle) {\n  return function useDivOverlay(props, setOpen) {\n    const context = useLeafletContext();\n    const elementRef = useElement(withPane(props, context), context);\n    useAttribution(context.map, props.attribution);\n    useEventHandlers(elementRef.current, props.eventHandlers);\n    useLifecycle(elementRef.current, context, props, setOpen);\n    return elementRef;\n  };\n}", "map": {"version": 3, "names": ["useAttribution", "useLeafletContext", "useEventHandlers", "with<PERSON>ane", "createDivOverlayHook", "useElement", "useLifecycle", "useDivOverlay", "props", "<PERSON><PERSON><PERSON>", "context", "elementRef", "map", "attribution", "current", "eventHandlers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@react-leaflet/core/lib/div-overlay.js"], "sourcesContent": ["import { useAttribution } from './attribution.js';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { withPane } from './pane.js';\nexport function createDivOverlayHook(useElement, useLifecycle) {\n    return function useDivOverlay(props, setOpen) {\n        const context = useLeafletContext();\n        const elementRef = useElement(withPane(props, context), context);\n        useAttribution(context.map, props.attribution);\n        useEventHandlers(elementRef.current, props.eventHandlers);\n        useLifecycle(elementRef.current, context, props, setOpen);\n        return elementRef;\n    };\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,cAAc;AAChD,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,QAAQ,QAAQ,WAAW;AACpC,OAAO,SAASC,oBAAoBA,CAACC,UAAU,EAAEC,YAAY,EAAE;EAC3D,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC1C,MAAMC,OAAO,GAAGT,iBAAiB,CAAC,CAAC;IACnC,MAAMU,UAAU,GAAGN,UAAU,CAACF,QAAQ,CAACK,KAAK,EAAEE,OAAO,CAAC,EAAEA,OAAO,CAAC;IAChEV,cAAc,CAACU,OAAO,CAACE,GAAG,EAAEJ,KAAK,CAACK,WAAW,CAAC;IAC9CX,gBAAgB,CAACS,UAAU,CAACG,OAAO,EAAEN,KAAK,CAACO,aAAa,CAAC;IACzDT,YAAY,CAACK,UAAU,CAACG,OAAO,EAAEJ,OAAO,EAAEF,KAAK,EAAEC,OAAO,CAAC;IACzD,OAAOE,UAAU;EACrB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}