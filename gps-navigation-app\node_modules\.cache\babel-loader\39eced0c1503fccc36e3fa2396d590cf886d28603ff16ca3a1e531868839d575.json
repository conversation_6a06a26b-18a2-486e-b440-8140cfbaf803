{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getDistance = _interopRequireDefault(require(\"./getDistance\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar isPointInLine = function isPointInLine(point, lineStart, lineEnd) {\n  return (0, _getDistance.default)(lineStart, point) + (0, _getDistance.default)(point, lineEnd) === (0, _getDistance.default)(lineStart, lineEnd);\n};\nvar _default = isPointInLine;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getDistance", "_interopRequireDefault", "require", "obj", "__esModule", "isPointInLine", "point", "lineStart", "lineEnd", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/isPointInLine.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getDistance=_interopRequireDefault(require(\"./getDistance\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var isPointInLine=function isPointInLine(point,lineStart,lineEnd){return(0,_getDistance.default)(lineStart,point)+(0,_getDistance.default)(point,lineEnd)===(0,_getDistance.default)(lineStart,lineEnd)};var _default=isPointInLine;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACE,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACJ,OAAO,EAACI;EAAG,CAAC;AAAA;AAAC,IAAIE,aAAa,GAAC,SAASA,aAAaA,CAACC,KAAK,EAACC,SAAS,EAACC,OAAO,EAAC;EAAC,OAAM,CAAC,CAAC,EAACR,YAAY,CAACD,OAAO,EAAEQ,SAAS,EAACD,KAAK,CAAC,GAAC,CAAC,CAAC,EAACN,YAAY,CAACD,OAAO,EAAEO,KAAK,EAACE,OAAO,CAAC,KAAG,CAAC,CAAC,EAACR,YAAY,CAACD,OAAO,EAAEQ,SAAS,EAACC,OAAO,CAAC;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACJ,aAAa;AAACR,OAAO,CAACE,OAAO,GAACU,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}