{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"bounds\", \"url\"];\nimport { createElementObject, createLayerComponent, extendContext, updateMediaOverlay } from '@react-leaflet/core';\nimport { VideoOverlay as LeafletVideoOverlay } from 'leaflet';\nexport const VideoOverlay = createLayerComponent(function createVideoOverlay(_ref, ctx) {\n  let {\n      bounds,\n      url\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const overlay = new LeafletVideoOverlay(url, bounds, options);\n  if (options.play === true) {\n    var _overlay$getElement;\n    (_overlay$getElement = overlay.getElement()) === null || _overlay$getElement === void 0 || _overlay$getElement.play();\n  }\n  return createElementObject(overlay, extendContext(ctx, {\n    overlayContainer: overlay\n  }));\n}, function updateVideoOverlay(overlay, props, prevProps) {\n  updateMediaOverlay(overlay, props, prevProps);\n  if (typeof props.url === 'string' && props.url !== prevProps.url) {\n    overlay.setUrl(props.url);\n  }\n  const video = overlay.getElement();\n  if (video != null) {\n    if (props.play === true && !prevProps.play) {\n      video.play();\n    } else if (!props.play && prevProps.play === true) {\n      video.pause();\n    }\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createLayerComponent", "extendContext", "updateMediaOverlay", "VideoOverlay", "LeafletVideoOverlay", "createVideoOverlay", "_ref", "ctx", "bounds", "url", "options", "_objectWithoutProperties", "_excluded", "overlay", "play", "_overlay$getElement", "getElement", "overlayContainer", "updateVideoOverlay", "props", "prevProps", "setUrl", "video", "pause"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/VideoOverlay.js"], "sourcesContent": ["import { createElementObject, createLayerComponent, extendContext, updateMediaOverlay } from '@react-leaflet/core';\nimport { VideoOverlay as LeafletVideoOverlay } from 'leaflet';\nexport const VideoOverlay = createLayerComponent(function createVideoOverlay({ bounds, url, ...options }, ctx) {\n    const overlay = new LeafletVideoOverlay(url, bounds, options);\n    if (options.play === true) {\n        overlay.getElement()?.play();\n    }\n    return createElementObject(overlay, extendContext(ctx, {\n        overlayContainer: overlay\n    }));\n}, function updateVideoOverlay(overlay, props, prevProps) {\n    updateMediaOverlay(overlay, props, prevProps);\n    if (typeof props.url === 'string' && props.url !== prevProps.url) {\n        overlay.setUrl(props.url);\n    }\n    const video = overlay.getElement();\n    if (video != null) {\n        if (props.play === true && !prevProps.play) {\n            video.play();\n        } else if (!props.play && prevProps.play === true) {\n            video.pause();\n        }\n    }\n});\n"], "mappings": ";;AAAA,SAASA,mBAAmB,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,kBAAkB,QAAQ,qBAAqB;AAClH,SAASC,YAAY,IAAIC,mBAAmB,QAAQ,SAAS;AAC7D,OAAO,MAAMD,YAAY,GAAGH,oBAAoB,CAAC,SAASK,kBAAkBA,CAAAC,IAAA,EAA8BC,GAAG,EAAE;EAAA,IAAlC;MAAEC,MAAM;MAAEC;IAAgB,CAAC,GAAAH,IAAA;IAATI,OAAO,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EAClG,MAAMC,OAAO,GAAG,IAAIT,mBAAmB,CAACK,GAAG,EAAED,MAAM,EAAEE,OAAO,CAAC;EAC7D,IAAIA,OAAO,CAACI,IAAI,KAAK,IAAI,EAAE;IAAA,IAAAC,mBAAA;IACvB,CAAAA,mBAAA,GAAAF,OAAO,CAACG,UAAU,CAAC,CAAC,cAAAD,mBAAA,eAApBA,mBAAA,CAAsBD,IAAI,CAAC,CAAC;EAChC;EACA,OAAOf,mBAAmB,CAACc,OAAO,EAAEZ,aAAa,CAACM,GAAG,EAAE;IACnDU,gBAAgB,EAAEJ;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASK,kBAAkBA,CAACL,OAAO,EAAEM,KAAK,EAAEC,SAAS,EAAE;EACtDlB,kBAAkB,CAACW,OAAO,EAAEM,KAAK,EAAEC,SAAS,CAAC;EAC7C,IAAI,OAAOD,KAAK,CAACV,GAAG,KAAK,QAAQ,IAAIU,KAAK,CAACV,GAAG,KAAKW,SAAS,CAACX,GAAG,EAAE;IAC9DI,OAAO,CAACQ,MAAM,CAACF,KAAK,CAACV,GAAG,CAAC;EAC7B;EACA,MAAMa,KAAK,GAAGT,OAAO,CAACG,UAAU,CAAC,CAAC;EAClC,IAAIM,KAAK,IAAI,IAAI,EAAE;IACf,IAAIH,KAAK,CAACL,IAAI,KAAK,IAAI,IAAI,CAACM,SAAS,CAACN,IAAI,EAAE;MACxCQ,KAAK,CAACR,IAAI,CAAC,CAAC;IAChB,CAAC,MAAM,IAAI,CAACK,KAAK,CAACL,IAAI,IAAIM,SAAS,CAACN,IAAI,KAAK,IAAI,EAAE;MAC/CQ,KAAK,CAACC,KAAK,CAAC,CAAC;IACjB;EACJ;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}