{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"center\", \"children\"];\nimport { createElementObject, createPathComponent, extendContext, updateCircle } from '@react-leaflet/core';\nimport { Circle as LeafletCircle } from 'leaflet';\nexport const Circle = createPathComponent(function createCircle(_ref, ctx) {\n  let {\n      center,\n      children: _c\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const circle = new LeafletCircle(center, options);\n  return createElementObject(circle, extendContext(ctx, {\n    overlayContainer: circle\n  }));\n}, updateCircle);", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "updateCircle", "Circle", "LeafletCircle", "createCircle", "_ref", "ctx", "center", "children", "_c", "options", "_objectWithoutProperties", "_excluded", "circle", "overlayContainer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/Circle.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext, updateCircle } from '@react-leaflet/core';\nimport { Circle as LeafletCircle } from 'leaflet';\nexport const Circle = createPathComponent(function createCircle({ center, children: _c, ...options }, ctx) {\n    const circle = new LeafletCircle(center, options);\n    return createElementObject(circle, extendContext(ctx, {\n        overlayContainer: circle\n    }));\n}, updateCircle);\n"], "mappings": ";;AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,qBAAqB;AAC3G,SAASC,MAAM,IAAIC,aAAa,QAAQ,SAAS;AACjD,OAAO,MAAMD,MAAM,GAAGH,mBAAmB,CAAC,SAASK,YAAYA,CAAAC,IAAA,EAAuCC,GAAG,EAAE;EAAA,IAA3C;MAAEC,MAAM;MAAEC,QAAQ,EAAEC;IAAe,CAAC,GAAAJ,IAAA;IAATK,OAAO,GAAAC,wBAAA,CAAAN,IAAA,EAAAO,SAAA;EAC9F,MAAMC,MAAM,GAAG,IAAIV,aAAa,CAACI,MAAM,EAAEG,OAAO,CAAC;EACjD,OAAOZ,mBAAmB,CAACe,MAAM,EAAEb,aAAa,CAACM,GAAG,EAAE;IAClDQ,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAEZ,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}