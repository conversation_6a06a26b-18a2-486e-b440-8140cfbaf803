{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"children\"];\nimport { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { FeatureGroup as LeafletFeatureGroup } from 'leaflet';\nexport const FeatureGroup = createPathComponent(function createFeatureGroup(_ref, ctx) {\n  let {\n      children: _c\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const group = new LeafletFeatureGroup([], options);\n  return createElementObject(group, extendContext(ctx, {\n    layerContainer: group,\n    overlayContainer: group\n  }));\n});", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "FeatureGroup", "LeafletFeatureGroup", "createFeatureGroup", "_ref", "ctx", "children", "_c", "options", "_objectWithoutProperties", "_excluded", "group", "layerContainer", "overlayContainer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/FeatureGroup.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { FeatureGroup as LeafletFeatureGroup } from 'leaflet';\nexport const FeatureGroup = createPathComponent(function createFeatureGroup({ children: _c, ...options }, ctx) {\n    const group = new LeafletFeatureGroup([], options);\n    return createElementObject(group, extendContext(ctx, {\n        layerContainer: group,\n        overlayContainer: group\n    }));\n});\n"], "mappings": ";;AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,qBAAqB;AAC7F,SAASC,YAAY,IAAIC,mBAAmB,QAAQ,SAAS;AAC7D,OAAO,MAAMD,YAAY,GAAGF,mBAAmB,CAAC,SAASI,kBAAkBA,CAAAC,IAAA,EAA+BC,GAAG,EAAE;EAAA,IAAnC;MAAEC,QAAQ,EAAEC;IAAe,CAAC,GAAAH,IAAA;IAATI,OAAO,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EAClG,MAAMC,KAAK,GAAG,IAAIT,mBAAmB,CAAC,EAAE,EAAEM,OAAO,CAAC;EAClD,OAAOV,mBAAmB,CAACa,KAAK,EAAEX,aAAa,CAACK,GAAG,EAAE;IACjDO,cAAc,EAAED,KAAK;IACrBE,gBAAgB,EAAEF;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}