{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getLatitude = _interopRequireDefault(require(\"./getLatitude\"));\nvar _getLongitude = _interopRequireDefault(require(\"./getLongitude\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar isPointInPolygon = function isPointInPolygon(point, polygon) {\n  var isInside = false;\n  var totalPolys = polygon.length;\n  for (var i = -1, j = totalPolys - 1; ++i < totalPolys; j = i) {\n    if (((0, _getLongitude.default)(polygon[i]) <= (0, _getLongitude.default)(point) && (0, _getLongitude.default)(point) < (0, _getLongitude.default)(polygon[j]) || (0, _getLongitude.default)(polygon[j]) <= (0, _getLongitude.default)(point) && (0, _getLongitude.default)(point) < (0, _getLongitude.default)(polygon[i])) && (0, _getLatitude.default)(point) < ((0, _getLatitude.default)(polygon[j]) - (0, _getLatitude.default)(polygon[i])) * ((0, _getLongitude.default)(point) - (0, _getLongitude.default)(polygon[i])) / ((0, _getLongitude.default)(polygon[j]) - (0, _getLongitude.default)(polygon[i])) + (0, _getLatitude.default)(polygon[i])) {\n      isInside = !isInside;\n    }\n  }\n  return isInside;\n};\nvar _default = isPointInPolygon;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getLatitude", "_interopRequireDefault", "require", "_getLongitude", "obj", "__esModule", "isPointInPolygon", "point", "polygon", "isInside", "totalPolys", "length", "i", "j", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/isPointInPolygon.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getLatitude=_interopRequireDefault(require(\"./getLatitude\"));var _getLongitude=_interopRequireDefault(require(\"./getLongitude\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var isPointInPolygon=function isPointInPolygon(point,polygon){var isInside=false;var totalPolys=polygon.length;for(var i=-1,j=totalPolys-1;++i<totalPolys;j=i){if(((0,_getLongitude.default)(polygon[i])<=(0,_getLongitude.default)(point)&&(0,_getLongitude.default)(point)<(0,_getLongitude.default)(polygon[j])||(0,_getLongitude.default)(polygon[j])<=(0,_getLongitude.default)(point)&&(0,_getLongitude.default)(point)<(0,_getLongitude.default)(polygon[i]))&&(0,_getLatitude.default)(point)<((0,_getLatitude.default)(polygon[j])-(0,_getLatitude.default)(polygon[i]))*((0,_getLongitude.default)(point)-(0,_getLongitude.default)(polygon[i]))/((0,_getLongitude.default)(polygon[j])-(0,_getLongitude.default)(polygon[i]))+(0,_getLatitude.default)(polygon[i])){isInside=!isInside}}return isInside};var _default=isPointInPolygon;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,IAAIC,aAAa,GAACF,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACG,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACL,OAAO,EAACK;EAAG,CAAC;AAAA;AAAC,IAAIE,gBAAgB,GAAC,SAASA,gBAAgBA,CAACC,KAAK,EAACC,OAAO,EAAC;EAAC,IAAIC,QAAQ,GAAC,KAAK;EAAC,IAAIC,UAAU,GAACF,OAAO,CAACG,MAAM;EAAC,KAAI,IAAIC,CAAC,GAAC,CAAC,CAAC,EAACC,CAAC,GAACH,UAAU,GAAC,CAAC,EAAC,EAAEE,CAAC,GAACF,UAAU,EAACG,CAAC,GAACD,CAAC,EAAC;IAAC,IAAG,CAAC,CAAC,CAAC,EAACT,aAAa,CAACJ,OAAO,EAAES,OAAO,CAACI,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC,EAACT,aAAa,CAACJ,OAAO,EAAEQ,KAAK,CAAC,IAAE,CAAC,CAAC,EAACJ,aAAa,CAACJ,OAAO,EAAEQ,KAAK,CAAC,GAAC,CAAC,CAAC,EAACJ,aAAa,CAACJ,OAAO,EAAES,OAAO,CAACK,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC,EAACV,aAAa,CAACJ,OAAO,EAAES,OAAO,CAACK,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC,EAACV,aAAa,CAACJ,OAAO,EAAEQ,KAAK,CAAC,IAAE,CAAC,CAAC,EAACJ,aAAa,CAACJ,OAAO,EAAEQ,KAAK,CAAC,GAAC,CAAC,CAAC,EAACJ,aAAa,CAACJ,OAAO,EAAES,OAAO,CAACI,CAAC,CAAC,CAAC,KAAG,CAAC,CAAC,EAACZ,YAAY,CAACD,OAAO,EAAEQ,KAAK,CAAC,GAAC,CAAC,CAAC,CAAC,EAACP,YAAY,CAACD,OAAO,EAAES,OAAO,CAACK,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,EAACb,YAAY,CAACD,OAAO,EAAES,OAAO,CAACI,CAAC,CAAC,CAAC,KAAG,CAAC,CAAC,EAACT,aAAa,CAACJ,OAAO,EAAEQ,KAAK,CAAC,GAAC,CAAC,CAAC,EAACJ,aAAa,CAACJ,OAAO,EAAES,OAAO,CAACI,CAAC,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC,EAACT,aAAa,CAACJ,OAAO,EAAES,OAAO,CAACK,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,EAACV,aAAa,CAACJ,OAAO,EAAES,OAAO,CAACI,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,EAACZ,YAAY,CAACD,OAAO,EAAES,OAAO,CAACI,CAAC,CAAC,CAAC,EAAC;MAACH,QAAQ,GAAC,CAACA,QAAQ;IAAA;EAAC;EAAC,OAAOA,QAAQ;AAAA,CAAC;AAAC,IAAIK,QAAQ,GAACR,gBAAgB;AAACT,OAAO,CAACE,OAAO,GAACe,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}