{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _constants = require(\"./constants\");\nvar convertSpeed = function convertSpeed(metersPerSecond) {\n  var targetUnit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"kmh\";\n  switch (targetUnit) {\n    case \"kmh\":\n      return metersPerSecond * _constants.timeConversion.h * _constants.distanceConversion.km;\n    case \"mph\":\n      return metersPerSecond * _constants.timeConversion.h * _constants.distanceConversion.mi;\n    default:\n      return metersPerSecond;\n  }\n};\nvar _default = convertSpeed;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_constants", "require", "convertSpeed", "metersPerSecond", "targetUnit", "arguments", "length", "undefined", "timeConversion", "h", "distanceConversion", "km", "mi", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/convertSpeed.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _constants=require(\"./constants\");var convertSpeed=function convertSpeed(metersPerSecond){var targetUnit=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"kmh\";switch(targetUnit){case\"kmh\":return metersPerSecond*_constants.timeConversion.h*_constants.distanceConversion.km;case\"mph\":return metersPerSecond*_constants.timeConversion.h*_constants.distanceConversion.mi;default:return metersPerSecond;}};var _default=convertSpeed;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,UAAU,GAACC,OAAO,CAAC,aAAa,CAAC;AAAC,IAAIC,YAAY,GAAC,SAASA,YAAYA,CAACC,eAAe,EAAC;EAAC,IAAIC,UAAU,GAACC,SAAS,CAACC,MAAM,GAAC,CAAC,IAAED,SAAS,CAAC,CAAC,CAAC,KAAGE,SAAS,GAACF,SAAS,CAAC,CAAC,CAAC,GAAC,KAAK;EAAC,QAAOD,UAAU;IAAE,KAAI,KAAK;MAAC,OAAOD,eAAe,GAACH,UAAU,CAACQ,cAAc,CAACC,CAAC,GAACT,UAAU,CAACU,kBAAkB,CAACC,EAAE;IAAC,KAAI,KAAK;MAAC,OAAOR,eAAe,GAACH,UAAU,CAACQ,cAAc,CAACC,CAAC,GAACT,UAAU,CAACU,kBAAkB,CAACE,EAAE;IAAC;MAAQ,OAAOT,eAAe;EAAC;AAAC,CAAC;AAAC,IAAIU,QAAQ,GAACX,YAAY;AAACL,OAAO,CAACE,OAAO,GAACc,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}