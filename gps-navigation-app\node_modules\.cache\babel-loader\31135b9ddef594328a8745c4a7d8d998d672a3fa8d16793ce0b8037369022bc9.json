{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route}from'react-router-dom';import GPSNavigationApp from'./components/GPSNavigationApp';import'./App.css';import{jsx as _jsx}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsx(Routes,{children:/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(GPSNavigationApp,{})})})})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "GPSNavigationApp", "jsx", "_jsx", "App", "children", "className", "path", "element"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport GPSNavigationApp from './components/GPSNavigationApp';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"App\">\n        <Routes>\n          <Route path=\"/\" element={<GPSNavigationApp />} />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CACzE,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAC5D,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACED,IAAA,CAACL,MAAM,EAAAO,QAAA,cACLF,IAAA,QAAKG,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBF,IAAA,CAACJ,MAAM,EAAAM,QAAA,cACLF,IAAA,CAACH,KAAK,EAACO,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEL,IAAA,CAACF,gBAAgB,GAAE,CAAE,CAAE,CAAC,CAC3C,CAAC,CACN,CAAC,CACA,CAAC,CAEb,CAEA,cAAe,CAAAG,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}