{"ast": null, "code": "import { useEffect } from 'react';\nimport { useAttribution } from './attribution.js';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { withPane } from './pane.js';\nexport function useLayerLifecycle(element, context) {\n  useEffect(function addLayer() {\n    const container = context.layerContainer ?? context.map;\n    container.addLayer(element.instance);\n    return function removeLayer() {\n      context.layerContainer?.removeLayer(element.instance);\n      context.map.removeLayer(element.instance);\n    };\n  }, [context, element]);\n}\nexport function createLayerHook(useElement) {\n  return function useLayer(props) {\n    const context = useLeafletContext();\n    const elementRef = useElement(withPane(props, context), context);\n    useAttribution(context.map, props.attribution);\n    useEventHandlers(elementRef.current, props.eventHandlers);\n    useLayerLifecycle(elementRef.current, context);\n    return elementRef;\n  };\n}", "map": {"version": 3, "names": ["useEffect", "useAttribution", "useLeafletContext", "useEventHandlers", "with<PERSON>ane", "useLayerLifecycle", "element", "context", "add<PERSON><PERSON>er", "container", "layerContainer", "map", "instance", "<PERSON><PERSON><PERSON>er", "createLayerHook", "useElement", "useLayer", "props", "elementRef", "attribution", "current", "eventHandlers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@react-leaflet/core/lib/layer.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useAttribution } from './attribution.js';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { withPane } from './pane.js';\nexport function useLayerLifecycle(element, context) {\n    useEffect(function addLayer() {\n        const container = context.layerContainer ?? context.map;\n        container.addLayer(element.instance);\n        return function removeLayer() {\n            context.layerContainer?.removeLayer(element.instance);\n            context.map.removeLayer(element.instance);\n        };\n    }, [\n        context,\n        element\n    ]);\n}\nexport function createLayerHook(useElement) {\n    return function useLayer(props) {\n        const context = useLeafletContext();\n        const elementRef = useElement(withPane(props, context), context);\n        useAttribution(context.map, props.attribution);\n        useEventHandlers(elementRef.current, props.eventHandlers);\n        useLayerLifecycle(elementRef.current, context);\n        return elementRef;\n    };\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,cAAc;AAChD,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,QAAQ,QAAQ,WAAW;AACpC,OAAO,SAASC,iBAAiBA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAChDP,SAAS,CAAC,SAASQ,QAAQA,CAAA,EAAG;IAC1B,MAAMC,SAAS,GAAGF,OAAO,CAACG,cAAc,IAAIH,OAAO,CAACI,GAAG;IACvDF,SAAS,CAACD,QAAQ,CAACF,OAAO,CAACM,QAAQ,CAAC;IACpC,OAAO,SAASC,WAAWA,CAAA,EAAG;MAC1BN,OAAO,CAACG,cAAc,EAAEG,WAAW,CAACP,OAAO,CAACM,QAAQ,CAAC;MACrDL,OAAO,CAACI,GAAG,CAACE,WAAW,CAACP,OAAO,CAACM,QAAQ,CAAC;IAC7C,CAAC;EACL,CAAC,EAAE,CACCL,OAAO,EACPD,OAAO,CACV,CAAC;AACN;AACA,OAAO,SAASQ,eAAeA,CAACC,UAAU,EAAE;EACxC,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;IAC5B,MAAMV,OAAO,GAAGL,iBAAiB,CAAC,CAAC;IACnC,MAAMgB,UAAU,GAAGH,UAAU,CAACX,QAAQ,CAACa,KAAK,EAAEV,OAAO,CAAC,EAAEA,OAAO,CAAC;IAChEN,cAAc,CAACM,OAAO,CAACI,GAAG,EAAEM,KAAK,CAACE,WAAW,CAAC;IAC9ChB,gBAAgB,CAACe,UAAU,CAACE,OAAO,EAAEH,KAAK,CAACI,aAAa,CAAC;IACzDhB,iBAAiB,CAACa,UAAU,CAACE,OAAO,EAAEb,OAAO,CAAC;IAC9C,OAAOW,UAAU;EACrB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}