{"ast": null, "code": "import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Rectangle as LeafletRectangle } from 'leaflet';\nexport const Rectangle = createPathComponent(function createRectangle({\n  bounds,\n  ...options\n}, ctx) {\n  const rectangle = new LeafletRectangle(bounds, options);\n  return createElementObject(rectangle, extendContext(ctx, {\n    overlayContainer: rectangle\n  }));\n}, function updateRectangle(layer, props, prevProps) {\n  if (props.bounds !== prevProps.bounds) {\n    layer.setBounds(props.bounds);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "Rectangle", "LeafletRectangle", "createRectangle", "bounds", "options", "ctx", "rectangle", "overlayContainer", "updateRectangle", "layer", "props", "prevProps", "setBounds"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-leaflet/lib/Rectangle.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Rectangle as LeafletRectangle } from 'leaflet';\nexport const Rectangle = createPathComponent(function createRectangle({ bounds, ...options }, ctx) {\n    const rectangle = new LeafletRectangle(bounds, options);\n    return createElementObject(rectangle, extendContext(ctx, {\n        overlayContainer: rectangle\n    }));\n}, function updateRectangle(layer, props, prevProps) {\n    if (props.bounds !== prevProps.bounds) {\n        layer.setBounds(props.bounds);\n    }\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,qBAAqB;AAC7F,SAASC,SAAS,IAAIC,gBAAgB,QAAQ,SAAS;AACvD,OAAO,MAAMD,SAAS,GAAGF,mBAAmB,CAAC,SAASI,eAAeA,CAAC;EAAEC,MAAM;EAAE,GAAGC;AAAQ,CAAC,EAAEC,GAAG,EAAE;EAC/F,MAAMC,SAAS,GAAG,IAAIL,gBAAgB,CAACE,MAAM,EAAEC,OAAO,CAAC;EACvD,OAAOP,mBAAmB,CAACS,SAAS,EAAEP,aAAa,CAACM,GAAG,EAAE;IACrDE,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASE,eAAeA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EACjD,IAAID,KAAK,CAACP,MAAM,KAAKQ,SAAS,CAACR,MAAM,EAAE;IACnCM,KAAK,CAACG,SAAS,CAACF,KAAK,CAACP,MAAM,CAAC;EACjC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}