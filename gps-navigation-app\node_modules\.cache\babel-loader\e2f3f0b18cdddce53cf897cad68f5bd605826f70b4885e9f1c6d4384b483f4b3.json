{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getLatitude = _interopRequireDefault(require(\"./getLatitude\"));\nvar _getLongitude = _interopRequireDefault(require(\"./getLongitude\"));\nvar _toRad = _interopRequireDefault(require(\"./toRad\"));\nvar _toDeg = _interopRequireDefault(require(\"./toDeg\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar getRhumbLineBearing = function getRhumbLineBearing(origin, dest) {\n  var diffLon = (0, _toRad.default)((0, _getLongitude.default)(dest)) - (0, _toRad.default)((0, _getLongitude.default)(origin));\n  var diffPhi = Math.log(Math.tan((0, _toRad.default)((0, _getLatitude.default)(dest)) / 2 + Math.PI / 4) / Math.tan((0, _toRad.default)((0, _getLatitude.default)(origin)) / 2 + Math.PI / 4));\n  if (Math.abs(diffLon) > Math.PI) {\n    if (diffLon > 0) {\n      diffLon = (Math.PI * 2 - diffLon) * -1;\n    } else {\n      diffLon = Math.PI * 2 + diffLon;\n    }\n  }\n  return ((0, _toDeg.default)(Math.atan2(diffLon, diffPhi)) + 360) % 360;\n};\nvar _default = getRhumbLineBearing;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getLatitude", "_interopRequireDefault", "require", "_getLongitude", "_toRad", "_toDeg", "obj", "__esModule", "getRhumbLineBearing", "origin", "dest", "diffLon", "diffPhi", "Math", "log", "tan", "PI", "abs", "atan2", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getRhumbLineBearing.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getLatitude=_interopRequireDefault(require(\"./getLatitude\"));var _getLongitude=_interopRequireDefault(require(\"./getLongitude\"));var _toRad=_interopRequireDefault(require(\"./toRad\"));var _toDeg=_interopRequireDefault(require(\"./toDeg\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var getRhumbLineBearing=function getRhumbLineBearing(origin,dest){var diffLon=(0,_toRad.default)((0,_getLongitude.default)(dest))-(0,_toRad.default)((0,_getLongitude.default)(origin));var diffPhi=Math.log(Math.tan((0,_toRad.default)((0,_getLatitude.default)(dest))/2+Math.PI/4)/Math.tan((0,_toRad.default)((0,_getLatitude.default)(origin))/2+Math.PI/4));if(Math.abs(diffLon)>Math.PI){if(diffLon>0){diffLon=(Math.PI*2-diffLon)*-1}else{diffLon=Math.PI*2+diffLon}}return((0,_toDeg.default)(Math.atan2(diffLon,diffPhi))+360)%360};var _default=getRhumbLineBearing;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,IAAIC,aAAa,GAACF,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAAC,IAAIE,MAAM,GAACH,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAAC,IAAIG,MAAM,GAACJ,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACK,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACP,OAAO,EAACO;EAAG,CAAC;AAAA;AAAC,IAAIE,mBAAmB,GAAC,SAASA,mBAAmBA,CAACC,MAAM,EAACC,IAAI,EAAC;EAAC,IAAIC,OAAO,GAAC,CAAC,CAAC,EAACP,MAAM,CAACL,OAAO,EAAE,CAAC,CAAC,EAACI,aAAa,CAACJ,OAAO,EAAEW,IAAI,CAAC,CAAC,GAAC,CAAC,CAAC,EAACN,MAAM,CAACL,OAAO,EAAE,CAAC,CAAC,EAACI,aAAa,CAACJ,OAAO,EAAEU,MAAM,CAAC,CAAC;EAAC,IAAIG,OAAO,GAACC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,EAACX,MAAM,CAACL,OAAO,EAAE,CAAC,CAAC,EAACC,YAAY,CAACD,OAAO,EAAEW,IAAI,CAAC,CAAC,GAAC,CAAC,GAACG,IAAI,CAACG,EAAE,GAAC,CAAC,CAAC,GAACH,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,EAACX,MAAM,CAACL,OAAO,EAAE,CAAC,CAAC,EAACC,YAAY,CAACD,OAAO,EAAEU,MAAM,CAAC,CAAC,GAAC,CAAC,GAACI,IAAI,CAACG,EAAE,GAAC,CAAC,CAAC,CAAC;EAAC,IAAGH,IAAI,CAACI,GAAG,CAACN,OAAO,CAAC,GAACE,IAAI,CAACG,EAAE,EAAC;IAAC,IAAGL,OAAO,GAAC,CAAC,EAAC;MAACA,OAAO,GAAC,CAACE,IAAI,CAACG,EAAE,GAAC,CAAC,GAACL,OAAO,IAAE,CAAC,CAAC;IAAA,CAAC,MAAI;MAACA,OAAO,GAACE,IAAI,CAACG,EAAE,GAAC,CAAC,GAACL,OAAO;IAAA;EAAC;EAAC,OAAM,CAAC,CAAC,CAAC,EAACN,MAAM,CAACN,OAAO,EAAEc,IAAI,CAACK,KAAK,CAACP,OAAO,EAACC,OAAO,CAAC,CAAC,GAAC,GAAG,IAAE,GAAG;AAAA,CAAC;AAAC,IAAIO,QAAQ,GAACX,mBAAmB;AAACX,OAAO,CAACE,OAAO,GAACoB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}