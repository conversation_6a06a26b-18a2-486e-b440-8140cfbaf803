{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nimport _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nconst _excluded = [\"secrets\"];\n/**\n * react-router v7.8.2\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { ENABLE_DEV_WARNINGS, ErrorResponseImpl, FrameworkContext, NO_BODY_STATUS_CODES, Outlet, RSCRouterContext, RemixErrorBoundary, RouterProvider, SINGLE_FETCH_REDIRECT_STATUS, SingleFetchRedirectSymbol, StaticRouterProvider, StreamTransfer, convertRoutesToDataRoutes, createBrowserHistory, createMemoryRout<PERSON>, createRequestInit, createRouter, createServerRout<PERSON>, createStaticHandler, createStaticRouter, decodeViaTurboStream, encode, getManifestPath, getSingleFetchDataStrategyImpl, getStaticContextFromError, invariant, isDataWithResponseInit, isMutationMethod, isRedirectResponse, isRedirectStatusCode, isResponse, isRouteErrorResponse, matchRoutes, noActionDefinedError, redirect, redirectDocument, replace, setIsHydrated, shouldHydrateRouteLoader, singleFetchUrl, stripBasename, stripIndexParam, unstable_RouterContextProvider, unstable_createContext, useRouteError, warnOnce, withComponentProps, withErrorBoundaryProps, withHydrateFallbackProps } from \"./chunk-PVWAREVJ.mjs\";\n\n// lib/dom/ssr/server.tsx\nimport * as React from \"react\";\nfunction ServerRouter(_ref) {\n  let {\n    context,\n    url,\n    nonce\n  } = _ref;\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let {\n    manifest,\n    routeModules,\n    criticalCss,\n    serverHandoffString\n  } = context;\n  let routes = createServerRoutes(manifest.routes, routeModules, context.future, context.isSpaMode);\n  context.staticHandlerContext.loaderData = _objectSpread({}, context.staticHandlerContext.loaderData);\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    if (route && manifestRoute && shouldHydrateRouteLoader(routeId, route.clientLoader, manifestRoute.hasLoader, context.isSpaMode) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      delete context.staticHandlerContext.loaderData[routeId];\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext);\n  return /* @__PURE__ */React.createElement(React.Fragment, null, /* @__PURE__ */React.createElement(FrameworkContext.Provider, {\n    value: {\n      manifest,\n      routeModules,\n      criticalCss,\n      serverHandoffString,\n      future: context.future,\n      ssr: context.ssr,\n      isSpaMode: context.isSpaMode,\n      routeDiscovery: context.routeDiscovery,\n      serializeError: context.serializeError,\n      renderMeta: context.renderMeta\n    }\n  }, /* @__PURE__ */React.createElement(RemixErrorBoundary, {\n    location: router.state.location\n  }, /* @__PURE__ */React.createElement(StaticRouterProvider, {\n    router,\n    context: context.staticHandlerContext,\n    hydrate: false\n  }))), context.serverHandoffStream ? /* @__PURE__ */React.createElement(React.Suspense, null, /* @__PURE__ */React.createElement(StreamTransfer, {\n    context,\n    identifier: 0,\n    reader: context.serverHandoffStream.getReader(),\n    textDecoder: new TextDecoder(),\n    nonce\n  })) : null);\n}\n\n// lib/dom/ssr/routes-test-stub.tsx\nimport * as React2 from \"react\";\nfunction createRoutesStub(routes, _context) {\n  return function RoutesTestStub(_ref2) {\n    let {\n      initialEntries,\n      initialIndex,\n      hydrationData,\n      future\n    } = _ref2;\n    let routerRef = React2.useRef();\n    let frameworkContextRef = React2.useRef();\n    if (routerRef.current == null) {\n      frameworkContextRef.current = {\n        future: {\n          unstable_subResourceIntegrity: (future === null || future === void 0 ? void 0 : future.unstable_subResourceIntegrity) === true,\n          unstable_middleware: (future === null || future === void 0 ? void 0 : future.unstable_middleware) === true\n        },\n        manifest: {\n          routes: {},\n          entry: {\n            imports: [],\n            module: \"\"\n          },\n          url: \"\",\n          version: \"\"\n        },\n        routeModules: {},\n        ssr: false,\n        isSpaMode: false,\n        routeDiscovery: {\n          mode: \"lazy\",\n          manifestPath: \"/__manifest\"\n        }\n      };\n      let patched = processRoutes(\n      // @ts-expect-error `StubRouteObject` is stricter about `loader`/`action`\n      // types compared to `AgnosticRouteObject`\n      convertRoutesToDataRoutes(routes, r => r), _context !== void 0 ? _context : future !== null && future !== void 0 && future.unstable_middleware ? new unstable_RouterContextProvider() : {}, frameworkContextRef.current.manifest, frameworkContextRef.current.routeModules);\n      routerRef.current = createMemoryRouter(patched, {\n        initialEntries,\n        initialIndex,\n        hydrationData\n      });\n    }\n    return /* @__PURE__ */React2.createElement(FrameworkContext.Provider, {\n      value: frameworkContextRef.current\n    }, /* @__PURE__ */React2.createElement(RouterProvider, {\n      router: routerRef.current\n    }));\n  };\n}\nfunction processRoutes(routes, context, manifest, routeModules, parentId) {\n  return routes.map(route => {\n    if (!route.id) {\n      throw new Error(\"Expected a route.id in react-router processRoutes() function\");\n    }\n    let newRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      Component: route.Component ? withComponentProps(route.Component) : void 0,\n      HydrateFallback: route.HydrateFallback ? withHydrateFallbackProps(route.HydrateFallback) : void 0,\n      ErrorBoundary: route.ErrorBoundary ? withErrorBoundaryProps(route.ErrorBoundary) : void 0,\n      action: route.action ? args => route.action(_objectSpread(_objectSpread({}, args), {}, {\n        context\n      })) : void 0,\n      loader: route.loader ? args => route.loader(_objectSpread(_objectSpread({}, args), {}, {\n        context\n      })) : void 0,\n      handle: route.handle,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    let entryRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      parentId,\n      hasAction: route.action != null,\n      hasLoader: route.loader != null,\n      // When testing routes, you should be stubbing loader/action/middleware,\n      // not trying to re-implement the full loader/clientLoader/SSR/hydration\n      // flow. That is better tested via E2E tests.\n      hasClientAction: false,\n      hasClientLoader: false,\n      hasClientMiddleware: false,\n      hasErrorBoundary: route.ErrorBoundary != null,\n      // any need for these?\n      module: \"build/stub-path-to-module.js\",\n      clientActionModule: void 0,\n      clientLoaderModule: void 0,\n      clientMiddlewareModule: void 0,\n      hydrateFallbackModule: void 0\n    };\n    manifest.routes[newRoute.id] = entryRoute;\n    routeModules[route.id] = {\n      default: newRoute.Component || Outlet,\n      ErrorBoundary: newRoute.ErrorBoundary || void 0,\n      handle: route.handle,\n      links: route.links,\n      meta: route.meta,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    if (route.children) {\n      newRoute.children = processRoutes(route.children, context, manifest, routeModules, newRoute.id);\n    }\n    return newRoute;\n  });\n}\n\n// lib/server-runtime/cookies.ts\nimport { parse, serialize } from \"cookie\";\n\n// lib/server-runtime/crypto.ts\nvar encoder = /* @__PURE__ */new TextEncoder();\nvar sign = async (value, secret) => {\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"sign\"]);\n  let signature = await crypto.subtle.sign(\"HMAC\", key, data2);\n  let hash = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(/=+$/, \"\");\n  return value + \".\" + hash;\n};\nvar unsign = async (cookie, secret) => {\n  let index = cookie.lastIndexOf(\".\");\n  let value = cookie.slice(0, index);\n  let hash = cookie.slice(index + 1);\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"verify\"]);\n  try {\n    let signature = byteStringToUint8Array(atob(hash));\n    let valid = await crypto.subtle.verify(\"HMAC\", key, signature, data2);\n    return valid ? value : false;\n  } catch (error) {\n    return false;\n  }\n};\nvar createKey = async (secret, usages) => crypto.subtle.importKey(\"raw\", encoder.encode(secret), {\n  name: \"HMAC\",\n  hash: \"SHA-256\"\n}, false, usages);\nfunction byteStringToUint8Array(byteString) {\n  let array = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    array[i] = byteString.charCodeAt(i);\n  }\n  return array;\n}\n\n// lib/server-runtime/cookies.ts\nvar createCookie = function (name) {\n  let cookieOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let _path$sameSite$cookie = _objectSpread({\n      path: \"/\",\n      sameSite: \"lax\"\n    }, cookieOptions),\n    {\n      secrets = []\n    } = _path$sameSite$cookie,\n    options = _objectWithoutProperties(_path$sameSite$cookie, _excluded);\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1e3) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, _objectSpread(_objectSpread({}, options), parseOptions));\n      if (name in cookies) {\n        let value = cookies[name];\n        if (typeof value === \"string\" && value !== \"\") {\n          let decoded = await decodeCookieValue(value, secrets);\n          return decoded;\n        } else {\n          return \"\";\n        }\n      } else {\n        return null;\n      }\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(name, value === \"\" ? \"\" : await encodeCookieValue(value, secrets), _objectSpread(_objectSpread({}, options), serializeOptions));\n    }\n  };\n};\nvar isCookie = object => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(!expires, \"The \\\"\".concat(name, \"\\\" cookie has an \\\"expires\\\" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use `commitSession(session, { expires })` if using a session storage object, or `cookie.serialize(\\\"value\\\", { expires })` if you're using the cookie directly.\"));\n}\n\n// lib/server-runtime/entry.ts\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo, routeId) => {\n    let route = manifest[routeId];\n    if (route) {\n      memo[routeId] = route.module;\n    }\n    return memo;\n  }, {});\n}\n\n// lib/server-runtime/mode.ts\nvar ServerMode = /* @__PURE__ */(ServerMode2 => {\n  ServerMode2[\"Development\"] = \"development\";\n  ServerMode2[\"Production\"] = \"production\";\n  ServerMode2[\"Test\"] = \"test\";\n  return ServerMode2;\n})(ServerMode || {});\nfunction isServerMode(value) {\n  return value === \"development\" /* Development */ || value === \"production\" /* Production */ || value === \"test\" /* Test */;\n}\n\n// lib/server-runtime/errors.ts\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== \"development\" /* Development */) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = void 0;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, _ref3) => {\n    let [routeId, error] = _ref3;\n    return Object.assign(acc, {\n      [routeId]: sanitizeError(error, serverMode)\n    });\n  }, {});\n}\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = _objectSpread(_objectSpread({}, val), {}, {\n        __type: \"RouteErrorResponse\"\n      });\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = _objectSpread({\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\"\n      }, sanitized.name !== \"Error\" ? {\n        __subType: sanitized.name\n      } : {});\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/server-runtime/routeMatching.ts\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(routes, pathname, basename);\n  if (!matches) return null;\n  return matches.map(match => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\n// lib/server-runtime/data.ts\nasync function callRouteHandler(handler, args) {\n  let result = await handler({\n    request: stripRoutesParam(stripIndexParam2(args.request)),\n    params: args.params,\n    context: args.context\n  });\n  if (isDataWithResponseInit(result) && result.init && result.init.status && isRedirectStatusCode(result.init.status)) {\n    throw new Response(null, result.init);\n  }\n  return result;\n}\nfunction stripIndexParam2(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\n// lib/server-runtime/invariant.ts\nfunction invariant2(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\"The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose\");\n    throw new Error(message);\n  }\n}\n\n// lib/server-runtime/dev.ts\nvar globalDevServerHooksKey = \"__reactRouterDevServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  return globalThis[globalDevServerHooksKey];\n}\nfunction getBuildTimeHeader(request, headerName) {\n  if (typeof process !== \"undefined\") {\n    try {\n      var _process$env;\n      if (((_process$env = process.env) === null || _process$env === void 0 ? void 0 : _process$env.IS_RR_BUILD_REQUEST) === \"yes\") {\n        return request.headers.get(headerName);\n      }\n    } catch (e) {}\n  }\n  return null;\n}\n\n// lib/server-runtime/routes.ts\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach(route => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction createRoutes(manifest) {\n  let parentId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"\";\n  let routesByParentId = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : groupRoutesByParentId(manifest);\n  return (routesByParentId[parentId] || []).map(route => _objectSpread(_objectSpread({}, route), {}, {\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\nfunction createStaticHandlerDataRoutes(manifest, future) {\n  let parentId = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : \"\";\n  let routesByParentId = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : groupRoutesByParentId(manifest);\n  return (routesByParentId[parentId] || []).map(route => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      unstable_middleware: route.module.unstable_middleware,\n      // Need to use RR's version in the param typed here to permit the optional\n      // context even though we know it'll always be provided in remix\n      loader: route.module.loader ? async args => {\n        let preRenderedData = getBuildTimeHeader(args.request, \"X-React-Router-Prerender-Data\");\n        if (preRenderedData != null) {\n          let encoded = preRenderedData ? decodeURI(preRenderedData) : preRenderedData;\n          invariant2(encoded, \"Missing prerendered data for route\");\n          let uint8array = new TextEncoder().encode(encoded);\n          let stream = new ReadableStream({\n            start(controller) {\n              controller.enqueue(uint8array);\n              controller.close();\n            }\n          });\n          let decoded = await decodeViaTurboStream(stream, global);\n          let data2 = decoded.value;\n          if (data2 && SingleFetchRedirectSymbol in data2) {\n            let result = data2[SingleFetchRedirectSymbol];\n            let init = {\n              status: result.status\n            };\n            if (result.reload) {\n              throw redirectDocument(result.redirect, init);\n            } else if (result.replace) {\n              throw replace(result.redirect, init);\n            } else {\n              throw redirect(result.redirect, init);\n            }\n          } else {\n            invariant2(data2 && route.id in data2, \"Unable to decode prerendered data\");\n            let result = data2[route.id];\n            invariant2(\"data\" in result, \"Unable to process prerendered data\");\n            return result.data;\n          }\n        }\n        let val = await callRouteHandler(route.module.loader, args);\n        return val;\n      } : void 0,\n      action: route.module.action ? args => callRouteHandler(route.module.action, args) : void 0,\n      handle: route.module.handle\n    };\n    return route.index ? _objectSpread({\n      index: true\n    }, commonRoute) : _objectSpread({\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(manifest, future, route.id, routesByParentId)\n    }, commonRoute);\n  });\n}\n\n// lib/server-runtime/markup.ts\nvar ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nvar ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction escapeHtml(html) {\n  return html.replace(ESCAPE_REGEX, match => ESCAPE_LOOKUP[match]);\n}\n\n// lib/server-runtime/serverHandoff.ts\nfunction createServerHandoffString(serverHandoff) {\n  return escapeHtml(JSON.stringify(serverHandoff));\n}\n\n// lib/server-runtime/headers.ts\nimport { splitCookiesString } from \"set-cookie-parser\";\nfunction getDocumentHeaders(context, build) {\n  return getDocumentHeadersImpl(context, m => {\n    let route = build.routes[m.route.id];\n    invariant2(route, \"Route with id \\\"\".concat(m.route.id, \"\\\" not found in build\"));\n    return route.module.headers;\n  });\n}\nfunction getDocumentHeadersImpl(context, getRouteHeadersFn, _defaultHeaders) {\n  let boundaryIdx = context.errors ? context.matches.findIndex(m => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    let {\n      actionHeaders,\n      actionData,\n      loaderHeaders,\n      loaderData\n    } = context;\n    context.matches.slice(boundaryIdx).some(match => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || !actionData.hasOwnProperty(id))) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && !loaderData.hasOwnProperty(id)) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  const defaultHeaders = new Headers(_defaultHeaders);\n  return matches.reduce((parentHeaders, match, idx) => {\n    let {\n      id\n    } = match.route;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n    let includeErrorHeaders = errorHeaders != null && idx === matches.length - 1;\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n    let headersFn = getRouteHeadersFn(match);\n    if (headersFn == null) {\n      let headers2 = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers2);\n      }\n      prependCookies(actionHeaders, headers2);\n      prependCookies(loaderHeaders, headers2);\n      return headers2;\n    }\n    let headers = new Headers(typeof headersFn === \"function\" ? headersFn({\n      loaderHeaders,\n      parentHeaders,\n      actionHeaders,\n      errorHeaders: includeErrorHeaders ? errorHeaders : void 0\n    }) : headersFn);\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers(defaultHeaders));\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    let cookies = splitCookiesString(parentSetCookieString);\n    let childCookies = new Set(childHeaders.getSetCookie());\n    cookies.forEach(cookie => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\n// lib/server-runtime/single-fetch.ts\nvar SERVER_NO_BODY_STATUS_CODES = /* @__PURE__ */new Set([...NO_BODY_STATUS_CODES, 304]);\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let handlerRequest = new Request(handlerUrl, _objectSpread({\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal\n    }, request.body ? {\n      duplex: \"half\"\n    } : void 0));\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      skipRevalidation: true,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async query => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: {\n        error\n      },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let singleFetchResult;\n    if (context.errors) {\n      singleFetchResult = {\n        error: Object.values(context.errors)[0]\n      };\n    } else {\n      singleFetchResult = {\n        data: Object.values(context.actionData || {})[0]\n      };\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: singleFetchResult,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let routesParam = new URL(request.url).searchParams.get(\"_routes\");\n  let loadRouteIds = routesParam ? new Set(routesParam.split(\",\")) : null;\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      filterMatchesToLoad: m => !loadRouteIds || loadRouteIds.has(m.route.id),\n      skipLoaderErrorBubbling: true,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async query => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: {\n        error\n      },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let results = {};\n    let loadedMatches = new Set(context.matches.filter(m => loadRouteIds ? loadRouteIds.has(m.route.id) : m.route.loader != null).map(m => m.route.id));\n    if (context.errors) {\n      for (let [id, error] of Object.entries(context.errors)) {\n        results[id] = {\n          error\n        };\n      }\n    }\n    for (let [id, data2] of Object.entries(context.loaderData)) {\n      if (!(id in results) && loadedMatches.has(id)) {\n        results[id] = {\n          data: data2\n        };\n      }\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: results,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nfunction generateSingleFetchResponse(request, build, serverMode, _ref4) {\n  let {\n    result,\n    headers,\n    status\n  } = _ref4;\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n  if (SERVER_NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, {\n      status,\n      headers: resultHeaders\n    });\n  }\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n  resultHeaders.delete(\"Content-Length\");\n  return new Response(encodeViaTurboStream(result, request.signal, build.entry.module.streamTimeout, serverMode), {\n    status: status || 200,\n    headers: resultHeaders\n  });\n}\nfunction generateSingleFetchRedirectResponse(redirectResponse, request, build, serverMode) {\n  let redirect2 = getSingleFetchRedirect(redirectResponse.status, redirectResponse.headers, build.basename);\n  let headers = new Headers(redirectResponse.headers);\n  headers.delete(\"Location\");\n  headers.set(\"Content-Type\", \"text/x-script\");\n  return generateSingleFetchResponse(request, build, serverMode, {\n    result: request.method === \"GET\" ? {\n      [SingleFetchRedirectSymbol]: redirect2\n    } : redirect2,\n    headers,\n    status: SINGLE_FETCH_REDIRECT_STATUS\n  });\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect2 = headers.get(\"Location\");\n  if (basename) {\n    redirect2 = stripBasename(redirect2, basename) || redirect2;\n  }\n  return {\n    redirect: redirect2,\n    status,\n    revalidate:\n    // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n    // detail of ?_data requests as our way to tell the front end to revalidate when\n    // we didn't have a response body to include that information in.\n    // With single fetch, we tell the front end via this revalidate boolean field.\n    // However, we're respecting it for now because it may be something folks have\n    // used in their own responses\n    // TODO(v3): Consider removing or making this official public API\n    headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\"),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\nfunction encodeViaTurboStream(data2, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  let timeoutId = setTimeout(() => controller.abort(new Error(\"Server Timeout\")), typeof streamTimeout === \"number\" ? streamTimeout : 4950);\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data2, {\n    signal: controller.signal,\n    plugins: [value => {\n      if (value instanceof Error) {\n        let {\n          name,\n          message,\n          stack\n        } = serverMode === \"production\" /* Production */ ? sanitizeError(value, serverMode) : value;\n        return [\"SanitizedError\", name, message, stack];\n      }\n      if (value instanceof ErrorResponseImpl) {\n        let {\n          data: data3,\n          status,\n          statusText\n        } = value;\n        return [\"ErrorResponse\", data3, status, statusText];\n      }\n      if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n        return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n      }\n    }],\n    postPlugins: [value => {\n      if (!value) return;\n      if (typeof value !== \"object\") return;\n      return [\"SingleFetchClassInstance\", Object.fromEntries(Object.entries(value))];\n    }, () => [\"SingleFetchFallback\"]]\n  });\n}\n\n// lib/server-runtime/server.ts\nfunction derive(build, mode) {\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : \"production\" /* Production */;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename\n  });\n  let errorHandler = build.entry.module.handleError || ((error, _ref5) => {\n    let {\n      request\n    } = _ref5;\n    if (serverMode !== \"test\" /* Test */ && !request.signal.aborted) {\n      console.error(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      isRouteErrorResponse(error) && error.error ? error.error : error);\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nvar createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, initialContext) {\n    _build = typeof build === \"function\" ? await build() : build;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let params = {};\n    let loadContext;\n    let handleError = error => {\n      if (mode === \"development\" /* Development */) {\n        var _getDevServerHooks, _getDevServerHooks$pr;\n        (_getDevServerHooks = getDevServerHooks()) === null || _getDevServerHooks === void 0 || (_getDevServerHooks$pr = _getDevServerHooks.processRequestError) === null || _getDevServerHooks$pr === void 0 || _getDevServerHooks$pr.call(_getDevServerHooks, error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n    if (_build.future.unstable_middleware) {\n      if (initialContext && !(initialContext instanceof unstable_RouterContextProvider)) {\n        let error = new Error(\"Invalid `context` value provided to `handleRequest`. When middleware is enabled you must return an instance of `unstable_RouterContextProvider` from your `getLoadContext` function.\");\n        handleError(error);\n        return returnLastResortErrorResponse(error, serverMode);\n      }\n      loadContext = initialContext || new unstable_RouterContextProvider();\n    } else {\n      loadContext = initialContext || {};\n    }\n    let url = new URL(request.url);\n    let normalizedBasename = _build.basename || \"/\";\n    let normalizedPath = url.pathname;\n    if (stripBasename(normalizedPath, normalizedBasename) === \"/_root.data\") {\n      normalizedPath = normalizedBasename;\n    } else if (normalizedPath.endsWith(\".data\")) {\n      normalizedPath = normalizedPath.replace(/\\.data$/, \"\");\n    }\n    if (stripBasename(normalizedPath, normalizedBasename) !== \"/\" && normalizedPath.endsWith(\"/\")) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n    let isSpaMode = getBuildTimeHeader(request, \"X-React-Router-SPA-Mode\") === \"yes\";\n    if (!_build.ssr) {\n      let decodedPath = decodeURI(normalizedPath);\n      if (normalizedBasename !== \"/\") {\n        let strippedPath = stripBasename(decodedPath, normalizedBasename);\n        if (strippedPath == null) {\n          errorHandler(new ErrorResponseImpl(404, \"Not Found\", \"Refusing to prerender the `\".concat(decodedPath, \"` path because it does not start with the basename `\").concat(normalizedBasename, \"`\")), {\n            context: loadContext,\n            params,\n            request\n          });\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        }\n        decodedPath = strippedPath;\n      }\n      if (_build.prerender.length === 0) {\n        isSpaMode = true;\n      } else if (!_build.prerender.includes(decodedPath) && !_build.prerender.includes(decodedPath + \"/\")) {\n        if (url.pathname.endsWith(\".data\")) {\n          errorHandler(new ErrorResponseImpl(404, \"Not Found\", \"Refusing to SSR the path `\".concat(decodedPath, \"` because `ssr:false` is set and the path is not included in the `prerender` config, so in production the path will be a 404.\")), {\n            context: loadContext,\n            params,\n            request\n          });\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        } else {\n          isSpaMode = true;\n        }\n      }\n    }\n    let manifestUrl = getManifestPath(_build.routeDiscovery.manifestPath, normalizedBasename);\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", {\n          status: 500\n        });\n      }\n    }\n    let matches = matchServerRoutes(routes, normalizedPath, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = normalizedPath;\n      let singleFetchMatches = matchServerRoutes(routes, handlerUrl.pathname, _build.basename);\n      response = await handleSingleFetchRequest(serverMode, _build, staticHandler, request, handlerUrl, loadContext, handleError);\n      if (isRedirectResponse(response)) {\n        response = generateSingleFetchRedirectResponse(response, request, _build, serverMode);\n      }\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          response = generateSingleFetchRedirectResponse(response, request, _build, serverMode);\n        }\n      }\n    } else if (!isSpaMode && matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(serverMode, _build, staticHandler, matches.slice(-1)[0].route.id, request, loadContext, handleError);\n    } else {\n      var _getDevServerHooks2;\n      let {\n        pathname\n      } = url;\n      let criticalCss = void 0;\n      if (_build.unstable_getCriticalCss) {\n        criticalCss = await _build.unstable_getCriticalCss({\n          pathname\n        });\n      } else if (mode === \"development\" /* Development */ && (_getDevServerHooks2 = getDevServerHooks()) !== null && _getDevServerHooks2 !== void 0 && _getDevServerHooks2.getCriticalCss) {\n        var _getDevServerHooks3, _getDevServerHooks3$g;\n        criticalCss = await ((_getDevServerHooks3 = getDevServerHooks()) === null || _getDevServerHooks3 === void 0 || (_getDevServerHooks3$g = _getDevServerHooks3.getCriticalCss) === null || _getDevServerHooks3$g === void 0 ? void 0 : _getDevServerHooks3$g.call(_getDevServerHooks3, pathname));\n      }\n      response = await handleDocumentRequest(serverMode, _build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss);\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"p\")) {\n    let paths = /* @__PURE__ */new Set();\n    url.searchParams.getAll(\"p\").forEach(path => {\n      if (!path.startsWith(\"/\")) {\n        path = \"/\".concat(path);\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(\"/\".concat(partialPath));\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          let route = build.assets.routes[routeId];\n          if (route) {\n            patches[routeId] = route;\n          }\n        }\n      }\n    }\n    return Response.json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    });\n  }\n  return new Response(\"Invalid Request\", {\n    status: 400\n  });\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let response = request.method !== \"GET\" ? await singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) : await singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError);\n  return response;\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss) {\n  try {\n    let result = await staticHandler.query(request, {\n      requestContext: loadContext,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async query => {\n        try {\n          let innerResult = await query(request);\n          if (!isResponse(innerResult)) {\n            innerResult = await renderHtml(innerResult, isSpaMode);\n          }\n          return innerResult;\n        } catch (error) {\n          handleError(error);\n          return new Response(null, {\n            status: 500\n          });\n        }\n      } : void 0\n    });\n    if (!isResponse(result)) {\n      result = await renderHtml(result, isSpaMode);\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return new Response(null, {\n      status: 500\n    });\n  }\n  async function renderHtml(context, isSpaMode2) {\n    let headers = getDocumentHeaders(context, build);\n    if (SERVER_NO_BODY_STATUS_CODES.has(context.statusCode)) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors, serverMode)\n    };\n    let baseServerHandoff = {\n      basename: build.basename,\n      future: build.future,\n      routeDiscovery: build.routeDiscovery,\n      ssr: build.ssr,\n      isSpaMode: isSpaMode2\n    };\n    let entryContext = {\n      manifest: build.assets,\n      routeModules: createEntryRouteModules(build.routes),\n      staticHandlerContext: context,\n      criticalCss,\n      serverHandoffString: createServerHandoffString(_objectSpread(_objectSpread({}, baseServerHandoff), {}, {\n        criticalCss\n      })),\n      serverHandoffStream: encodeViaTurboStream(state, request.signal, build.entry.module.streamTimeout, serverMode),\n      renderMeta: {},\n      future: build.future,\n      ssr: build.ssr,\n      routeDiscovery: build.routeDiscovery,\n      isSpaMode: isSpaMode2,\n      serializeError: err => serializeError(err, serverMode)\n    };\n    let handleDocumentRequestFunction = build.entry.module.default;\n    try {\n      return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n    } catch (error) {\n      handleError(error);\n      let errorForSecondRender = error;\n      if (isResponse(error)) {\n        try {\n          let data2 = await unwrapResponse(error);\n          errorForSecondRender = new ErrorResponseImpl(error.status, error.statusText, data2);\n        } catch (e) {}\n      }\n      context = getStaticContextFromError(staticHandler.dataRoutes, context, errorForSecondRender);\n      if (context.errors) {\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let state2 = {\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: serializeErrors(context.errors, serverMode)\n      };\n      entryContext = _objectSpread(_objectSpread({}, entryContext), {}, {\n        staticHandlerContext: context,\n        serverHandoffString: createServerHandoffString(baseServerHandoff),\n        serverHandoffStream: encodeViaTurboStream(state2, request.signal, build.entry.module.streamTimeout, serverMode),\n        renderMeta: {}\n      });\n      try {\n        return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n      } catch (error2) {\n        handleError(error2);\n        return returnLastResortErrorResponse(error2, serverMode);\n      }\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let result = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async queryRoute => {\n        try {\n          let innerResult = await queryRoute(request);\n          return handleQueryRouteResult(innerResult);\n        } catch (error) {\n          return handleQueryRouteError(error);\n        }\n      } : void 0\n    });\n    return handleQueryRouteResult(result);\n  } catch (error) {\n    return handleQueryRouteError(error);\n  }\n  function handleQueryRouteResult(result) {\n    if (isResponse(result)) {\n      return result;\n    }\n    if (typeof result === \"string\") {\n      return new Response(result);\n    }\n    return Response.json(result);\n  }\n  function handleQueryRouteError(error) {\n    if (isResponse(error)) {\n      error.headers.set(\"X-Remix-Catch\", \"yes\");\n      return error;\n    }\n    if (isRouteErrorResponse(error)) {\n      handleError(error);\n      return errorResponseToJson(error, serverMode);\n    }\n    if (error instanceof Error && error.message === \"Expected a response from queryRoute\") {\n      let newError = new Error(\"Expected a Response to be returned from resource route handler\");\n      handleError(newError);\n      return returnLastResortErrorResponse(newError, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return Response.json(serializeError(\n  // @ts-expect-error This is \"private\" from users but intended for internal use\n  errorResponse.error || new Error(\"Unexpected Server Error\"), serverMode), {\n    status: errorResponse.status,\n    statusText: errorResponse.statusText,\n    headers: {\n      \"X-Remix-Error\": \"yes\"\n    }\n  });\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== \"production\" /* Production */) {\n    message += \"\\n\\n\".concat(String(error));\n  }\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\n\n// lib/server-runtime/sessions.ts\nfunction flash(name) {\n  return \"__flash_\".concat(name, \"__\");\n}\nvar createSession = function () {\n  let initialData = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let id = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"\";\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return void 0;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\nvar isSession = object => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\nfunction createSessionStorage(_ref6) {\n  let {\n    cookie: cookieArg,\n    createData,\n    readData,\n    updateData,\n    deleteData\n  } = _ref6;\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie((cookieArg === null || cookieArg === void 0 ? void 0 : cookieArg.name) || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && (await cookie.parse(cookieHeader, options));\n      let data2 = id && (await readData(id));\n      return createSession(data2 || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let {\n        id,\n        data: data2\n      } = session;\n      let expires = (options === null || options === void 0 ? void 0 : options.maxAge) != null ? new Date(Date.now() + options.maxAge * 1e3) : (options === null || options === void 0 ? void 0 : options.expires) != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data2, expires);\n      } else {\n        id = await createData(data2, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", _objectSpread(_objectSpread({}, options), {}, {\n        maxAge: void 0,\n        expires: /* @__PURE__ */new Date(0)\n      }));\n    }\n  };\n}\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(cookie.isSigned, \"The \\\"\".concat(cookie.name, \"\\\" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.\"));\n}\n\n// lib/server-runtime/sessions/cookieStorage.ts\nfunction createCookieSessionStorage() {\n  let {\n    cookie: cookieArg\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie((cookieArg === null || cookieArg === void 0 ? void 0 : cookieArg.name) || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(cookieHeader && (await cookie.parse(cookieHeader, options)) || {});\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length);\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", _objectSpread(_objectSpread({}, options), {}, {\n        maxAge: void 0,\n        expires: /* @__PURE__ */new Date(0)\n      }));\n    }\n  };\n}\n\n// lib/server-runtime/sessions/memoryStorage.ts\nfunction createMemorySessionStorage() {\n  let {\n    cookie\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let map = /* @__PURE__ */new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data2, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, {\n        data: data2,\n        expires\n      });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let {\n          data: data2,\n          expires\n        } = map.get(id);\n        if (!expires || expires > /* @__PURE__ */new Date()) {\n          return data2;\n        }\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data2, expires) {\n      map.set(id, {\n        data: data2,\n        expires\n      });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n}\n\n// lib/href.ts\nfunction href(path) {\n  let params = arguments.length <= 1 ? undefined : arguments[1];\n  return path.split(\"/\").map(segment => {\n    if (segment === \"*\") {\n      return params ? params[\"*\"] : void 0;\n    }\n    const match = segment.match(/^:([\\w-]+)(\\?)?/);\n    if (!match) return segment;\n    const param = match[1];\n    const value = params ? params[param] : void 0;\n    const isRequired = match[2] === void 0;\n    if (isRequired && value === void 0) {\n      throw Error(\"Path '\".concat(path, \"' requires param '\").concat(param, \"' but it was not provided\"));\n    }\n    return value;\n  }).filter(segment => segment !== void 0).join(\"/\");\n}\n\n// lib/rsc/browser.tsx\nimport * as React4 from \"react\";\nimport * as ReactDOM from \"react-dom\";\n\n// lib/dom/ssr/hydration.tsx\nfunction getHydrationData(state, routes, getRouteInfo, location2, basename, isSpaMode) {\n  let hydrationData = _objectSpread(_objectSpread({}, state), {}, {\n    loaderData: _objectSpread({}, state.loaderData)\n  });\n  let initialMatches = matchRoutes(routes, location2, basename);\n  if (initialMatches) {\n    for (let match of initialMatches) {\n      let routeId = match.route.id;\n      let routeInfo = getRouteInfo(routeId);\n      if (shouldHydrateRouteLoader(routeId, routeInfo.clientLoader, routeInfo.hasLoader, isSpaMode) && (routeInfo.hasHydrateFallback || !routeInfo.hasLoader)) {\n        delete hydrationData.loaderData[routeId];\n      } else if (!routeInfo.hasLoader) {\n        hydrationData.loaderData[routeId] = null;\n      }\n    }\n  }\n  return hydrationData;\n}\n\n// lib/rsc/errorBoundaries.tsx\nimport React3 from \"react\";\nvar RSCRouterGlobalErrorBoundary = class extends React3.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: null,\n      location: props.location\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location) {\n      return {\n        error: null,\n        location: props.location\n      };\n    }\n    return {\n      error: state.error,\n      location: state.location\n    };\n  }\n  render() {\n    if (this.state.error) {\n      return /* @__PURE__ */React3.createElement(RSCDefaultRootErrorBoundaryImpl, {\n        error: this.state.error,\n        renderAppShell: true\n      });\n    } else {\n      return this.props.children;\n    }\n  }\n};\nfunction ErrorWrapper(_ref7) {\n  let {\n    renderAppShell,\n    title,\n    children\n  } = _ref7;\n  if (!renderAppShell) {\n    return children;\n  }\n  return /* @__PURE__ */React3.createElement(\"html\", {\n    lang: \"en\"\n  }, /* @__PURE__ */React3.createElement(\"head\", null, /* @__PURE__ */React3.createElement(\"meta\", {\n    charSet: \"utf-8\"\n  }), /* @__PURE__ */React3.createElement(\"meta\", {\n    name: \"viewport\",\n    content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n  }), /* @__PURE__ */React3.createElement(\"title\", null, title)), /* @__PURE__ */React3.createElement(\"body\", null, /* @__PURE__ */React3.createElement(\"main\", {\n    style: {\n      fontFamily: \"system-ui, sans-serif\",\n      padding: \"2rem\"\n    }\n  }, children)));\n}\nfunction RSCDefaultRootErrorBoundaryImpl(_ref8) {\n  let {\n    error,\n    renderAppShell\n  } = _ref8;\n  console.error(error);\n  let heyDeveloper = /* @__PURE__ */React3.createElement(\"script\", {\n    dangerouslySetInnerHTML: {\n      __html: \"\\n        console.log(\\n          \\\"\\uD83D\\uDCBF Hey developer \\uD83D\\uDC4B. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information.\\\"\\n        );\\n      \"\n    }\n  });\n  if (isRouteErrorResponse(error)) {\n    return /* @__PURE__ */React3.createElement(ErrorWrapper, {\n      renderAppShell,\n      title: \"Unhandled Thrown Response!\"\n    }, /* @__PURE__ */React3.createElement(\"h1\", {\n      style: {\n        fontSize: \"24px\"\n      }\n    }, error.status, \" \", error.statusText), ENABLE_DEV_WARNINGS ? heyDeveloper : null);\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /* @__PURE__ */React3.createElement(ErrorWrapper, {\n    renderAppShell,\n    title: \"Application Error!\"\n  }, /* @__PURE__ */React3.createElement(\"h1\", {\n    style: {\n      fontSize: \"24px\"\n    }\n  }, \"Application Error\"), /* @__PURE__ */React3.createElement(\"pre\", {\n    style: {\n      padding: \"2rem\",\n      background: \"hsla(10, 50%, 50%, 0.1)\",\n      color: \"red\",\n      overflow: \"auto\"\n    }\n  }, errorInstance.stack), heyDeveloper);\n}\nfunction RSCDefaultRootErrorBoundary(_ref9) {\n  let {\n    hasRootLayout\n  } = _ref9;\n  let error = useRouteError();\n  if (hasRootLayout === void 0) {\n    throw new Error(\"Missing 'hasRootLayout' prop\");\n  }\n  return /* @__PURE__ */React3.createElement(RSCDefaultRootErrorBoundaryImpl, {\n    renderAppShell: !hasRootLayout,\n    error\n  });\n}\n\n// lib/rsc/route-modules.ts\nfunction createRSCRouteModules(payload) {\n  const routeModules = {};\n  for (const match of payload.matches) {\n    populateRSCRouteModules(routeModules, match);\n  }\n  return routeModules;\n}\nfunction populateRSCRouteModules(routeModules, matches) {\n  matches = Array.isArray(matches) ? matches : [matches];\n  for (const match of matches) {\n    routeModules[match.id] = {\n      links: match.links,\n      meta: match.meta,\n      default: noopComponent\n    };\n  }\n}\nvar noopComponent = () => null;\n\n// lib/rsc/browser.tsx\nfunction createCallServer(_ref0) {\n  let {\n    createFromReadableStream,\n    createTemporaryReferenceSet,\n    encodeReply,\n    fetch: fetchImplementation = fetch\n  } = _ref0;\n  const globalVar = window;\n  let landedActionId = 0;\n  return async (id, args) => {\n    var _globalVar$__routerAc;\n    let actionId = globalVar.__routerActionID = ((_globalVar$__routerAc = globalVar.__routerActionID) !== null && _globalVar$__routerAc !== void 0 ? _globalVar$__routerAc : globalVar.__routerActionID = 0) + 1;\n    const temporaryReferences = createTemporaryReferenceSet();\n    const response = await fetchImplementation(new Request(location.href, {\n      body: await encodeReply(args, {\n        temporaryReferences\n      }),\n      method: \"POST\",\n      headers: {\n        Accept: \"text/x-component\",\n        \"rsc-action-id\": id\n      }\n    }));\n    if (!response.body) {\n      throw new Error(\"No response body\");\n    }\n    const payload = await createFromReadableStream(response.body, {\n      temporaryReferences\n    });\n    if (payload.type === \"redirect\") {\n      if (payload.reload) {\n        window.location.href = payload.location;\n        return;\n      }\n      globalVar.__reactRouterDataRouter.navigate(payload.location, {\n        replace: payload.replace\n      });\n      return payload.actionResult;\n    }\n    if (payload.type !== \"action\") {\n      throw new Error(\"Unexpected payload type\");\n    }\n    if (payload.rerender) {\n      React4.startTransition(\n      // @ts-expect-error - We have old react types that don't know this can be async\n      async () => {\n        const rerender = await payload.rerender;\n        if (!rerender) return;\n        if (landedActionId < actionId && globalVar.__routerActionID <= actionId) {\n          landedActionId = actionId;\n          if (rerender.type === \"redirect\") {\n            if (rerender.reload) {\n              window.location.href = rerender.location;\n              return;\n            }\n            globalVar.__reactRouterDataRouter.navigate(rerender.location, {\n              replace: rerender.replace\n            });\n            return;\n          }\n          let lastMatch;\n          for (const match of rerender.matches) {\n            var _lastMatch$id, _lastMatch;\n            globalVar.__reactRouterDataRouter.patchRoutes((_lastMatch$id = (_lastMatch = lastMatch) === null || _lastMatch === void 0 ? void 0 : _lastMatch.id) !== null && _lastMatch$id !== void 0 ? _lastMatch$id : null, [createRouteFromServerManifest(match)], true);\n            lastMatch = match;\n          }\n          window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp({});\n          React4.startTransition(() => {\n            window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp({\n              loaderData: Object.assign({}, globalVar.__reactRouterDataRouter.state.loaderData, rerender.loaderData),\n              errors: rerender.errors ? Object.assign({}, globalVar.__reactRouterDataRouter.state.errors, rerender.errors) : null\n            });\n          });\n        }\n      });\n    }\n    return payload.actionResult;\n  };\n}\nfunction createRouterFromPayload(_ref1) {\n  var _globalVar$__reactRou, _payload$patches;\n  let {\n    fetchImplementation,\n    createFromReadableStream,\n    unstable_getContext,\n    payload\n  } = _ref1;\n  const globalVar = window;\n  if (globalVar.__reactRouterDataRouter && globalVar.__reactRouterRouteModules) return {\n    router: globalVar.__reactRouterDataRouter,\n    routeModules: globalVar.__reactRouterRouteModules\n  };\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  globalVar.__reactRouterRouteModules = (_globalVar$__reactRou = globalVar.__reactRouterRouteModules) !== null && _globalVar$__reactRou !== void 0 ? _globalVar$__reactRou : {};\n  populateRSCRouteModules(globalVar.__reactRouterRouteModules, payload.matches);\n  let patches = /* @__PURE__ */new Map();\n  (_payload$patches = payload.patches) === null || _payload$patches === void 0 || _payload$patches.forEach(patch => {\n    var _patches$get;\n    invariant(patch.parentId, \"Invalid patch parentId\");\n    if (!patches.has(patch.parentId)) {\n      patches.set(patch.parentId, []);\n    }\n    (_patches$get = patches.get(patch.parentId)) === null || _patches$get === void 0 || _patches$get.push(patch);\n  });\n  let routes = payload.matches.reduceRight((previous, match) => {\n    const route = createRouteFromServerManifest(match, payload);\n    if (previous.length > 0) {\n      route.children = previous;\n      let childrenToPatch = patches.get(match.id);\n      if (childrenToPatch) {\n        route.children.push(...childrenToPatch.map(r => createRouteFromServerManifest(r)));\n      }\n    }\n    return [route];\n  }, []);\n  globalVar.__reactRouterDataRouter = createRouter({\n    routes,\n    unstable_getContext,\n    basename: payload.basename,\n    history: createBrowserHistory(),\n    hydrationData: getHydrationData({\n      loaderData: payload.loaderData,\n      actionData: payload.actionData,\n      errors: payload.errors\n    }, routes, routeId => {\n      let match = payload.matches.find(m => m.id === routeId);\n      invariant(match, \"Route not found in payload\");\n      return {\n        clientLoader: match.clientLoader,\n        hasLoader: match.hasLoader,\n        hasHydrateFallback: match.hydrateFallbackElement != null\n      };\n    }, payload.location, void 0, false),\n    async patchRoutesOnNavigation(_ref10) {\n      let {\n        path,\n        signal\n      } = _ref10;\n      if (discoveredPaths.has(path)) {\n        return;\n      }\n      await fetchAndApplyManifestPatches([path], createFromReadableStream, fetchImplementation, signal);\n    },\n    // FIXME: Pass `build.ssr` into this function\n    dataStrategy: getRSCSingleFetchDataStrategy(() => globalVar.__reactRouterDataRouter, true, payload.basename, createFromReadableStream, fetchImplementation)\n  });\n  if (globalVar.__reactRouterDataRouter.state.initialized) {\n    globalVar.__routerInitialized = true;\n    globalVar.__reactRouterDataRouter.initialize();\n  } else {\n    globalVar.__routerInitialized = false;\n  }\n  let lastLoaderData = void 0;\n  globalVar.__reactRouterDataRouter.subscribe(_ref11 => {\n    let {\n      loaderData,\n      actionData\n    } = _ref11;\n    if (lastLoaderData !== loaderData) {\n      var _globalVar$__routerAc2;\n      globalVar.__routerActionID = ((_globalVar$__routerAc2 = globalVar.__routerActionID) !== null && _globalVar$__routerAc2 !== void 0 ? _globalVar$__routerAc2 : globalVar.__routerActionID = 0) + 1;\n    }\n  });\n  globalVar.__reactRouterDataRouter._updateRoutesForHMR = routeUpdateByRouteId => {\n    const oldRoutes = window.__reactRouterDataRouter.routes;\n    const newRoutes = [];\n    function walkRoutes(routes2, parentId) {\n      return routes2.map(route => {\n        const routeUpdate = routeUpdateByRouteId.get(route.id);\n        if (routeUpdate) {\n          const {\n            routeModule,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader\n          } = routeUpdate;\n          const newRoute = createRouteFromServerManifest({\n            clientAction: routeModule.clientAction,\n            clientLoader: routeModule.clientLoader,\n            element: route.element,\n            errorElement: route.errorElement,\n            handle: route.handle,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader,\n            hydrateFallbackElement: route.hydrateFallbackElement,\n            id: route.id,\n            index: route.index,\n            links: routeModule.links,\n            meta: routeModule.meta,\n            parentId,\n            path: route.path,\n            shouldRevalidate: routeModule.shouldRevalidate\n          });\n          if (route.children) {\n            newRoute.children = walkRoutes(route.children, route.id);\n          }\n          return newRoute;\n        }\n        const updatedRoute = _objectSpread({}, route);\n        if (route.children) {\n          updatedRoute.children = walkRoutes(route.children, route.id);\n        }\n        return updatedRoute;\n      });\n    }\n    newRoutes.push(...walkRoutes(oldRoutes, void 0));\n    window.__reactRouterDataRouter._internalSetRoutes(newRoutes);\n  };\n  return {\n    router: globalVar.__reactRouterDataRouter,\n    routeModules: globalVar.__reactRouterRouteModules\n  };\n}\nvar renderedRoutesContext = unstable_createContext();\nfunction getRSCSingleFetchDataStrategy(getRouter, ssr, basename, createFromReadableStream, fetchImplementation) {\n  let dataStrategy = getSingleFetchDataStrategyImpl(getRouter, match => {\n    let M = match;\n    return {\n      hasLoader: M.route.hasLoader,\n      hasClientLoader: M.route.hasClientLoader,\n      hasComponent: M.route.hasComponent,\n      hasAction: M.route.hasAction,\n      hasClientAction: M.route.hasClientAction,\n      hasShouldRevalidate: M.route.hasShouldRevalidate\n    };\n  },\n  // pass map into fetchAndDecode so it can add payloads\n  getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation), ssr, basename,\n  // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match => {\n    let M = match;\n    return M.route.hasComponent && !M.route.element;\n  });\n  return async args => args.unstable_runClientMiddleware(async () => {\n    let context = args.context;\n    context.set(renderedRoutesContext, []);\n    let results = await dataStrategy(args);\n    const renderedRoutesById = /* @__PURE__ */new Map();\n    for (const route of context.get(renderedRoutesContext)) {\n      if (!renderedRoutesById.has(route.id)) {\n        renderedRoutesById.set(route.id, []);\n      }\n      renderedRoutesById.get(route.id).push(route);\n    }\n    for (const match of args.matches) {\n      const renderedRoutes = renderedRoutesById.get(match.route.id);\n      if (renderedRoutes) {\n        for (const rendered of renderedRoutes) {\n          var _rendered$parentId;\n          window.__reactRouterDataRouter.patchRoutes((_rendered$parentId = rendered.parentId) !== null && _rendered$parentId !== void 0 ? _rendered$parentId : null, [createRouteFromServerManifest(rendered)], true);\n        }\n      }\n    }\n    return results;\n  });\n}\nfunction getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation) {\n  return async (args, basename, targetRoutes) => {\n    let {\n      request,\n      context\n    } = args;\n    let url = singleFetchUrl(request.url, basename, \"rsc\");\n    if (request.method === \"GET\") {\n      url = stripIndexParam(url);\n      if (targetRoutes) {\n        url.searchParams.set(\"_routes\", targetRoutes.join(\",\"));\n      }\n    }\n    let res = await fetchImplementation(new Request(url, await createRequestInit(request)));\n    if (res.status === 404 && !res.headers.has(\"X-Remix-Response\")) {\n      throw new ErrorResponseImpl(404, \"Not Found\", true);\n    }\n    invariant(res.body, \"No response body to decode\");\n    try {\n      const payload = await createFromReadableStream(res.body, {\n        temporaryReferences: void 0\n      });\n      if (payload.type === \"redirect\") {\n        return {\n          status: res.status,\n          data: {\n            redirect: {\n              redirect: payload.location,\n              reload: payload.reload,\n              replace: payload.replace,\n              revalidate: false,\n              status: payload.status\n            }\n          }\n        };\n      }\n      if (payload.type !== \"render\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      context.get(renderedRoutesContext).push(...payload.matches);\n      let results = {\n        routes: {}\n      };\n      const dataKey = isMutationMethod(request.method) ? \"actionData\" : \"loaderData\";\n      for (let [routeId, data2] of Object.entries(payload[dataKey] || {})) {\n        results.routes[routeId] = {\n          data: data2\n        };\n      }\n      if (payload.errors) {\n        for (let [routeId, error] of Object.entries(payload.errors)) {\n          results.routes[routeId] = {\n            error\n          };\n        }\n      }\n      return {\n        status: res.status,\n        data: results\n      };\n    } catch (e) {\n      throw new Error(\"Unable to decode RSC response\");\n    }\n  };\n}\nfunction RSCHydratedRouter(_ref12) {\n  let {\n    createFromReadableStream,\n    fetch: fetchImplementation = fetch,\n    payload,\n    routeDiscovery = \"eager\",\n    unstable_getContext\n  } = _ref12;\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let {\n    router,\n    routeModules\n  } = React4.useMemo(() => createRouterFromPayload({\n    payload,\n    fetchImplementation,\n    unstable_getContext,\n    createFromReadableStream\n  }), [createFromReadableStream, payload, fetchImplementation, unstable_getContext]);\n  React4.useEffect(() => {\n    setIsHydrated();\n  }, []);\n  React4.useLayoutEffect(() => {\n    const globalVar = window;\n    if (!globalVar.__routerInitialized) {\n      globalVar.__routerInitialized = true;\n      globalVar.__reactRouterDataRouter.initialize();\n    }\n  }, []);\n  let [location2, setLocation] = React4.useState(router.state.location);\n  React4.useLayoutEffect(() => router.subscribe(newState => {\n    if (newState.location !== location2) {\n      setLocation(newState.location);\n    }\n  }), [router, location2]);\n  React4.useEffect(() => {\n    var _window$navigator;\n    if (routeDiscovery === \"lazy\" ||\n    // @ts-expect-error - TS doesn't know about this yet\n    ((_window$navigator = window.navigator) === null || _window$navigator === void 0 || (_window$navigator = _window$navigator.connection) === null || _window$navigator === void 0 ? void 0 : _window$navigator.saveData) === true) {\n      return;\n    }\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let pathname = el.tagName === \"A\" ? el.pathname : new URL(path, window.location.origin).pathname;\n      if (!discoveredPaths.has(pathname)) {\n        nextPaths.add(pathname);\n      }\n    }\n    async function fetchPatches() {\n      document.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(registerElement);\n      let paths = Array.from(nextPaths.keys()).filter(path => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (paths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation);\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    fetchPatches();\n    let observer = new MutationObserver(() => debouncedFetchPatches());\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n  }, [routeDiscovery, createFromReadableStream, fetchImplementation]);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: {\n      mode: \"lazy\",\n      manifestPath: \"/__manifest\"\n    },\n    routeModules\n  };\n  return /* @__PURE__ */React4.createElement(RSCRouterContext.Provider, {\n    value: true\n  }, /* @__PURE__ */React4.createElement(RSCRouterGlobalErrorBoundary, {\n    location: location2\n  }, /* @__PURE__ */React4.createElement(FrameworkContext.Provider, {\n    value: frameworkContext\n  }, /* @__PURE__ */React4.createElement(RouterProvider, {\n    router,\n    flushSync: ReactDOM.flushSync\n  }))));\n}\nfunction createRouteFromServerManifest(match, payload) {\n  var _payload$errors, _match$clientLoader;\n  let hasInitialData = payload && match.id in payload.loaderData;\n  let initialData = payload === null || payload === void 0 ? void 0 : payload.loaderData[match.id];\n  let hasInitialError = (payload === null || payload === void 0 ? void 0 : payload.errors) && match.id in payload.errors;\n  let initialError = payload === null || payload === void 0 || (_payload$errors = payload.errors) === null || _payload$errors === void 0 ? void 0 : _payload$errors[match.id];\n  let isHydrationRequest = ((_match$clientLoader = match.clientLoader) === null || _match$clientLoader === void 0 ? void 0 : _match$clientLoader.hydrate) === true || !match.hasLoader ||\n  // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match.hasComponent && !match.element;\n  invariant(window.__reactRouterRouteModules);\n  populateRSCRouteModules(window.__reactRouterRouteModules, match);\n  let dataRoute = {\n    id: match.id,\n    element: match.element,\n    errorElement: match.errorElement,\n    handle: match.handle,\n    hasErrorBoundary: match.hasErrorBoundary,\n    hydrateFallbackElement: match.hydrateFallbackElement,\n    index: match.index,\n    loader: match.clientLoader ? async (args, singleFetch) => {\n      try {\n        let result = await match.clientLoader(_objectSpread(_objectSpread({}, args), {}, {\n          serverLoader: () => {\n            preventInvalidServerHandlerCall(\"loader\", match.id, match.hasLoader);\n            if (isHydrationRequest) {\n              if (hasInitialData) {\n                return initialData;\n              }\n              if (hasInitialError) {\n                throw initialError;\n              }\n            }\n            return callSingleFetch(singleFetch);\n          }\n        }));\n        return result;\n      } finally {\n        isHydrationRequest = false;\n      }\n    } :\n    // We always make the call in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    (_, singleFetch) => callSingleFetch(singleFetch),\n    action: match.clientAction ? (args, singleFetch) => match.clientAction(_objectSpread(_objectSpread({}, args), {}, {\n      serverAction: async () => {\n        preventInvalidServerHandlerCall(\"action\", match.id, match.hasLoader);\n        return await callSingleFetch(singleFetch);\n      }\n    })) : match.hasAction ? (_, singleFetch) => callSingleFetch(singleFetch) : () => {\n      throw noActionDefinedError(\"action\", match.id);\n    },\n    path: match.path,\n    shouldRevalidate: match.shouldRevalidate,\n    // We always have a \"loader\" in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    hasLoader: true,\n    hasClientLoader: match.clientLoader != null,\n    hasAction: match.hasAction,\n    hasClientAction: match.clientAction != null,\n    hasShouldRevalidate: match.shouldRevalidate != null\n  };\n  if (typeof dataRoute.loader === \"function\") {\n    dataRoute.loader.hydrate = shouldHydrateRouteLoader(match.id, match.clientLoader, match.hasLoader, false);\n  }\n  return dataRoute;\n}\nfunction callSingleFetch(singleFetch) {\n  invariant(typeof singleFetch === \"function\", \"Invalid singleFetch parameter\");\n  return singleFetch();\n}\nfunction preventInvalidServerHandlerCall(type, routeId, hasHandler) {\n  if (!hasHandler) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = \"You are trying to call \".concat(fn, \" on a route that does not have a server \").concat(type, \" (routeId: \\\"\").concat(routeId, \"\\\")\");\n    console.error(msg);\n    throw new ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nvar nextPaths = /* @__PURE__ */new Set();\nvar discoveredPathsMaxSize = 1e3;\nvar discoveredPaths = /* @__PURE__ */new Set();\nvar URL_LIMIT = 7680;\nfunction getManifestUrl(paths) {\n  var _globalVar$__reactRou2;\n  if (paths.length === 0) {\n    return null;\n  }\n  if (paths.length === 1) {\n    return new URL(\"\".concat(paths[0], \".manifest\"), window.location.origin);\n  }\n  const globalVar = window;\n  let basename = ((_globalVar$__reactRou2 = globalVar.__reactRouterDataRouter.basename) !== null && _globalVar$__reactRou2 !== void 0 ? _globalVar$__reactRou2 : \"\").replace(/^\\/|\\/$/g, \"\");\n  let url = new URL(\"\".concat(basename, \"/.manifest\"), window.location.origin);\n  paths.sort().forEach(path => url.searchParams.append(\"p\", path));\n  return url;\n}\nasync function fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation, signal) {\n  let url = getManifestUrl(paths);\n  if (url == null) {\n    return;\n  }\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let response = await fetchImplementation(new Request(url, {\n    signal\n  }));\n  if (!response.body || response.status < 200 || response.status >= 300) {\n    throw new Error(\"Unable to fetch new route matches from the server\");\n  }\n  let payload = await createFromReadableStream(response.body, {\n    temporaryReferences: void 0\n  });\n  if (payload.type !== \"manifest\") {\n    throw new Error(\"Failed to patch routes\");\n  }\n  paths.forEach(p => addToFifoQueue(p, discoveredPaths));\n  payload.patches.forEach(p => {\n    var _p$parentId;\n    window.__reactRouterDataRouter.patchRoutes((_p$parentId = p.parentId) !== null && _p$parentId !== void 0 ? _p$parentId : null, [createRouteFromServerManifest(p)]);\n  });\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    queue.delete(first);\n  }\n  queue.add(path);\n}\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\n// lib/rsc/server.ssr.tsx\nimport * as React5 from \"react\";\n\n// lib/rsc/html-stream/server.ts\nvar encoder2 = new TextEncoder();\nvar trailer = \"</body></html>\";\nfunction injectRSCPayload(rscStream) {\n  let decoder = new TextDecoder();\n  let resolveFlightDataPromise;\n  let flightDataPromise = new Promise(resolve => resolveFlightDataPromise = resolve);\n  let startedRSC = false;\n  let buffered = [];\n  let timeout = null;\n  function flushBufferedChunks(controller) {\n    for (let chunk of buffered) {\n      let buf = decoder.decode(chunk, {\n        stream: true\n      });\n      if (buf.endsWith(trailer)) {\n        buf = buf.slice(0, -trailer.length);\n      }\n      controller.enqueue(encoder2.encode(buf));\n    }\n    buffered.length = 0;\n    timeout = null;\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      buffered.push(chunk);\n      if (timeout) {\n        return;\n      }\n      timeout = setTimeout(async () => {\n        flushBufferedChunks(controller);\n        if (!startedRSC) {\n          startedRSC = true;\n          writeRSCStream(rscStream, controller).catch(err => controller.error(err)).then(resolveFlightDataPromise);\n        }\n      }, 0);\n    },\n    async flush(controller) {\n      await flightDataPromise;\n      if (timeout) {\n        clearTimeout(timeout);\n        flushBufferedChunks(controller);\n      }\n      controller.enqueue(encoder2.encode(\"</body></html>\"));\n    }\n  });\n}\nasync function writeRSCStream(rscStream, controller) {\n  let decoder = new TextDecoder(\"utf-8\", {\n    fatal: true\n  });\n  const reader = rscStream.getReader();\n  try {\n    let read;\n    while ((read = await reader.read()) && !read.done) {\n      const chunk = read.value;\n      try {\n        writeChunk(JSON.stringify(decoder.decode(chunk, {\n          stream: true\n        })), controller);\n      } catch (err) {\n        let base64 = JSON.stringify(btoa(String.fromCodePoint(...chunk)));\n        writeChunk(\"Uint8Array.from(atob(\".concat(base64, \"), m => m.codePointAt(0))\"), controller);\n      }\n    }\n  } finally {\n    reader.releaseLock();\n  }\n  let remaining = decoder.decode();\n  if (remaining.length) {\n    writeChunk(JSON.stringify(remaining), controller);\n  }\n}\nfunction writeChunk(chunk, controller) {\n  controller.enqueue(encoder2.encode(\"<script>\".concat(escapeScript(\"(self.__FLIGHT_DATA||=[]).push(\".concat(chunk, \")\")), \"</script>\")));\n}\nfunction escapeScript(script) {\n  return script.replace(/<!--/g, \"<\\\\!--\").replace(/<\\/(script)/gi, \"</\\\\$1\");\n}\n\n// lib/rsc/server.ssr.tsx\nvar REACT_USE = \"use\";\nvar useImpl = React5[REACT_USE];\nfunction useSafe(promise) {\n  if (useImpl) {\n    return useImpl(promise);\n  }\n  throw new Error(\"React Router v7 requires React 19+ for RSC features.\");\n}\nasync function routeRSCServerRequest(_ref13) {\n  let {\n    request,\n    fetchServer,\n    createFromReadableStream,\n    renderHTML,\n    hydrate = true\n  } = _ref13;\n  const url = new URL(request.url);\n  const isDataRequest = isReactServerRequest(url);\n  const respondWithRSCPayload = isDataRequest || isManifestRequest(url) || request.headers.has(\"rsc-action-id\");\n  const serverResponse = await fetchServer(request);\n  if (respondWithRSCPayload || serverResponse.headers.get(\"React-Router-Resource\") === \"true\") {\n    return serverResponse;\n  }\n  if (!serverResponse.body) {\n    throw new Error(\"Missing body in server response\");\n  }\n  let serverResponseB = null;\n  if (hydrate) {\n    serverResponseB = serverResponse.clone();\n  }\n  const body = serverResponse.body;\n  let payloadPromise;\n  const getPayload = async () => {\n    if (payloadPromise) return payloadPromise;\n    payloadPromise = createFromReadableStream(body);\n    return payloadPromise;\n  };\n  try {\n    var _serverResponseB2;\n    const payload = await getPayload();\n    if (serverResponse.status === SINGLE_FETCH_REDIRECT_STATUS && payload.type === \"redirect\") {\n      var _serverResponseB;\n      const headers2 = new Headers(serverResponse.headers);\n      headers2.delete(\"Content-Encoding\");\n      headers2.delete(\"Content-Length\");\n      headers2.delete(\"Content-Type\");\n      headers2.delete(\"x-remix-response\");\n      headers2.set(\"Location\", payload.location);\n      return new Response(((_serverResponseB = serverResponseB) === null || _serverResponseB === void 0 ? void 0 : _serverResponseB.body) || \"\", {\n        headers: headers2,\n        status: payload.status,\n        statusText: serverResponse.statusText\n      });\n    }\n    const html = await renderHTML(getPayload);\n    const headers = new Headers(serverResponse.headers);\n    headers.set(\"Content-Type\", \"text/html\");\n    if (!hydrate) {\n      return new Response(html, {\n        status: serverResponse.status,\n        headers\n      });\n    }\n    if (!((_serverResponseB2 = serverResponseB) !== null && _serverResponseB2 !== void 0 && _serverResponseB2.body)) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n    return new Response(body2, {\n      status: serverResponse.status,\n      headers\n    });\n  } catch (reason) {\n    if (reason instanceof Response) {\n      return reason;\n    }\n    throw reason;\n  }\n}\nfunction RSCStaticRouter(_ref14) {\n  let {\n    getPayload\n  } = _ref14;\n  const payload = useSafe(getPayload());\n  if (payload.type === \"redirect\") {\n    throw new Response(null, {\n      status: payload.status,\n      headers: {\n        Location: payload.location\n      }\n    });\n  }\n  if (payload.type !== \"render\") return null;\n  let patchedLoaderData = _objectSpread({}, payload.loaderData);\n  for (const match of payload.matches) {\n    if (shouldHydrateRouteLoader(match.id, match.clientLoader, match.hasLoader, false) && (match.hydrateFallbackElement || !match.hasLoader)) {\n      delete patchedLoaderData[match.id];\n    }\n  }\n  const context = {\n    actionData: payload.actionData,\n    actionHeaders: {},\n    basename: payload.basename,\n    errors: payload.errors,\n    loaderData: patchedLoaderData,\n    loaderHeaders: {},\n    location: payload.location,\n    statusCode: 200,\n    matches: payload.matches.map(match => ({\n      params: match.params,\n      pathname: match.pathname,\n      pathnameBase: match.pathnameBase,\n      route: {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        handle: match.handle,\n        hasErrorBoundary: match.hasErrorBoundary,\n        loader: match.hasLoader || !!match.clientLoader,\n        index: match.index,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      }\n    }))\n  };\n  const router = createStaticRouter(payload.matches.reduceRight((previous, match) => {\n    const route = {\n      id: match.id,\n      action: match.hasAction || !!match.clientAction,\n      element: match.element,\n      errorElement: match.errorElement,\n      handle: match.handle,\n      hasErrorBoundary: !!match.errorElement,\n      hydrateFallbackElement: match.hydrateFallbackElement,\n      index: match.index,\n      loader: match.hasLoader || !!match.clientLoader,\n      path: match.path,\n      shouldRevalidate: match.shouldRevalidate\n    };\n    if (previous.length > 0) {\n      route.children = previous;\n    }\n    return [route];\n  }, []), context);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: {\n      mode: \"lazy\",\n      manifestPath: \"/__manifest\"\n    },\n    routeModules: createRSCRouteModules(payload)\n  };\n  return /* @__PURE__ */React5.createElement(RSCRouterContext.Provider, {\n    value: true\n  }, /* @__PURE__ */React5.createElement(RSCRouterGlobalErrorBoundary, {\n    location: payload.location\n  }, /* @__PURE__ */React5.createElement(FrameworkContext.Provider, {\n    value: frameworkContext\n  }, /* @__PURE__ */React5.createElement(StaticRouterProvider, {\n    context,\n    router,\n    hydrate: false,\n    nonce: payload.nonce\n  }))));\n}\nfunction isReactServerRequest(url) {\n  return url.pathname.endsWith(\".rsc\");\n}\nfunction isManifestRequest(url) {\n  return url.pathname.endsWith(\".manifest\");\n}\n\n// lib/rsc/html-stream/browser.ts\nfunction getRSCStream() {\n  let encoder3 = new TextEncoder();\n  let streamController = null;\n  let rscStream = new ReadableStream({\n    start(controller) {\n      if (typeof window === \"undefined\") {\n        return;\n      }\n      let handleChunk = chunk => {\n        if (typeof chunk === \"string\") {\n          controller.enqueue(encoder3.encode(chunk));\n        } else {\n          controller.enqueue(chunk);\n        }\n      };\n      window.__FLIGHT_DATA || (window.__FLIGHT_DATA = []);\n      window.__FLIGHT_DATA.forEach(handleChunk);\n      window.__FLIGHT_DATA.push = chunk => {\n        handleChunk(chunk);\n        return 0;\n      };\n      streamController = controller;\n    }\n  });\n  if (typeof document !== \"undefined\" && document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      var _streamController;\n      (_streamController = streamController) === null || _streamController === void 0 || _streamController.close();\n    });\n  } else {\n    var _streamController2;\n    (_streamController2 = streamController) === null || _streamController2 === void 0 || _streamController2.close();\n  }\n  return rscStream;\n}\n\n// lib/dom/ssr/errors.ts\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {}\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\nexport { ServerRouter, createRoutesStub, createCookie, isCookie, ServerMode, setDevServerHooks, createRequestHandler, createSession, isSession, createSessionStorage, createCookieSessionStorage, createMemorySessionStorage, href, getHydrationData, RSCDefaultRootErrorBoundary, createCallServer, RSCHydratedRouter, routeRSCServerRequest, RSCStaticRouter, getRSCStream, deserializeErrors };", "map": {"version": 3, "names": ["ENABLE_DEV_WARNINGS", "ErrorResponseImpl", "FrameworkContext", "NO_BODY_STATUS_CODES", "Outlet", "RSCRouterContext", "RemixErrorBoundary", "RouterProvider", "SINGLE_FETCH_REDIRECT_STATUS", "SingleFetchRedirectSymbol", "StaticRouterProvider", "StreamTransfer", "convertRoutesToDataRoutes", "createBrowserHistory", "createMemoryRouter", "createRequestInit", "createRouter", "createServerRoutes", "createStaticHandler", "createStaticRouter", "decodeViaTurboStream", "encode", "getManifestPath", "getSingleFetchDataStrategyImpl", "getStaticContextFromError", "invariant", "isDataWithResponseInit", "isMutationMethod", "isRedirectResponse", "isRedirectStatusCode", "isResponse", "isRouteErrorResponse", "matchRoutes", "noActionDefinedError", "redirect", "redirectDocument", "replace", "setIsHydrated", "shouldHydrateRouteLoader", "singleFetchUrl", "stripBasename", "stripIndexParam", "unstable_RouterContextProvider", "unstable_createContext", "useRouteError", "warnOnce", "withComponentProps", "withErrorBoundaryProps", "withHydrateFallbackProps", "React", "ServerRouter", "_ref", "context", "url", "nonce", "URL", "manifest", "routeModules", "criticalCss", "serverHandoffString", "routes", "future", "isSpaMode", "staticHandlerContext", "loaderData", "_objectSpread", "match", "matches", "routeId", "route", "id", "manifestRoute", "clientLoader", "<PERSON><PERSON><PERSON><PERSON>", "HydrateFallback", "router", "createElement", "Fragment", "Provider", "value", "ssr", "routeDiscovery", "serializeError", "renderMeta", "location", "state", "hydrate", "serverHandoffStream", "Suspense", "identifier", "reader", "<PERSON><PERSON><PERSON><PERSON>", "textDecoder", "TextDecoder", "React2", "createRoutesStub", "_context", "RoutesTestStub", "_ref2", "initialEntries", "initialIndex", "hydrationData", "routerRef", "useRef", "frameworkContextRef", "current", "unstable_subResourceIntegrity", "unstable_middleware", "entry", "imports", "module", "version", "mode", "manifestPath", "patched", "processRoutes", "r", "parentId", "map", "Error", "newRoute", "path", "index", "Component", "Error<PERSON>ou<PERSON><PERSON>", "action", "args", "loader", "handle", "shouldRevalidate", "entryRoute", "hasAction", "hasClientAction", "hasClientLoader", "hasClientMiddleware", "hasErrorBou<PERSON>ry", "clientActionModule", "clientLoaderModule", "clientMiddlewareModule", "hydrateFallbackModule", "default", "links", "meta", "children", "parse", "serialize", "encoder", "TextEncoder", "sign", "secret", "data2", "key", "create<PERSON><PERSON>", "signature", "crypto", "subtle", "hash", "btoa", "String", "fromCharCode", "Uint8Array", "unsign", "cookie", "lastIndexOf", "slice", "byteStringToUint8Array", "atob", "valid", "verify", "error", "usages", "importKey", "name", "byteString", "array", "length", "i", "charCodeAt", "createCookie", "cookieOptions", "arguments", "undefined", "_path$sameSite$cookie", "sameSite", "secrets", "options", "_objectWithoutProperties", "_excluded", "warnOnceAboutExpiresCookie", "expires", "isSigned", "maxAge", "Date", "now", "cookieHeader", "parseOptions", "cookies", "decoded", "decodeCookieValue", "serializeOptions", "encodeCookieValue", "<PERSON><PERSON><PERSON><PERSON>", "object", "encoded", "encodeData", "unsignedValue", "decodeData", "myUnescape", "encodeURIComponent", "JSON", "stringify", "decodeURIComponent", "myEscape", "str", "toString", "result", "chr", "code", "char<PERSON>t", "exec", "hex", "toUpperCase", "part", "parseInt", "concat", "createEntryRouteModules", "Object", "keys", "reduce", "memo", "ServerMode", "ServerMode2", "isServerMode", "sanitizeError", "serverMode", "sanitized", "stack", "sanitizeErrors", "errors", "entries", "acc", "_ref3", "assign", "message", "serializeErrors", "serialized", "val", "__type", "__subType", "matchServerRoutes", "pathname", "basename", "params", "callRouteHandler", "handler", "request", "stripRoutesParam", "stripIndexParam2", "init", "status", "Response", "indexValues", "searchParams", "getAll", "delete", "indexValuesToKeep", "indexValue", "push", "<PERSON><PERSON><PERSON>", "append", "method", "body", "headers", "signal", "duplex", "Request", "href", "invariant2", "console", "globalDevServerHooksKey", "setDevServerHooks", "devServerHooks", "globalThis", "getDevServerHooks", "getBuildTimeHeader", "headerName", "process", "_process$env", "env", "IS_RR_BUILD_REQUEST", "get", "e", "groupRoutesByParentId", "values", "for<PERSON>ach", "createRoutes", "routesByParentId", "createStaticHandlerDataRoutes", "commonRoute", "preRenderedData", "decodeURI", "uint8array", "stream", "ReadableStream", "start", "controller", "enqueue", "close", "global", "reload", "data", "caseSensitive", "ESCAPE_LOOKUP", "ESCAPE_REGEX", "escapeHtml", "html", "createServerHandoffString", "serverHandoff", "splitCookiesString", "getDocumentHeaders", "build", "getDocumentHeadersImpl", "m", "getRouteHeadersFn", "_defaultHeaders", "boundaryIdx", "findIndex", "errorHeaders", "actionHeaders", "actionData", "loaderHeaders", "some", "hasOwnProperty", "defaultHeaders", "Headers", "parentHeaders", "idx", "includeErrorHeaders", "includeErrorCookies", "headersFn", "headers2", "prependCookies", "childHeaders", "parentSetCookieString", "childCookies", "Set", "getSetCookie", "has", "SERVER_NO_BODY_STATUS_CODES", "singleFetchAction", "static<PERSON><PERSON><PERSON>", "handlerUrl", "loadContext", "handleError", "handlerRequest", "query", "requestContext", "skipL<PERSON>derError<PERSON><PERSON>bling", "skipRevalidation", "unstable_generateMiddlewareResponse", "innerResult", "handleQueryResult", "handleQueryError", "staticContextToResponse", "generateSingleFetchResponse", "statusCode", "err", "singleFetchResult", "singleFetchLoaders", "routesParam", "loadRouteIds", "split", "filterMatchesToLoad", "results", "loadedMatches", "filter", "_ref4", "resultHeaders", "set", "encodeViaTurboStream", "streamTimeout", "generateSingleFetchRedirectResponse", "redirectResponse", "redirect2", "getSingleFetchRedirect", "revalidate", "requestSignal", "AbortController", "timeoutId", "setTimeout", "abort", "addEventListener", "clearTimeout", "plugins", "data3", "statusText", "postPlugins", "fromEntries", "derive", "dataRoutes", "<PERSON><PERSON><PERSON><PERSON>", "_ref5", "aborted", "createRequestHandler", "_build", "requestHandler", "initialContext", "derived", "_getDevServerHooks", "_getDevServerHooks$pr", "processRequestError", "call", "returnLastResortErrorResponse", "normalizedBasename", "normalizedPath", "endsWith", "decodedPath", "<PERSON><PERSON><PERSON>", "prerender", "includes", "manifestUrl", "res", "handleManifestRequest", "response", "singleFetchMatches", "handleSingleFetchRequest", "handleDataRequest", "handleResourceRequest", "_getDevServerHooks2", "unstable_getCriticalCss", "getCriticalCss", "_getDevServerHooks3", "_getDevServerHooks3$g", "handleDocumentRequest", "assets", "patches", "paths", "startsWith", "segments", "_", "partialPath", "join", "add", "json", "renderHtml", "isSpaMode2", "baseServerHandoff", "entryContext", "handleDocumentRequestFunction", "errorForSecondRender", "unwrapResponse", "state2", "error2", "queryRoute", "handleQueryRouteResult", "handleQueryRouteError", "errorResponseToJson", "newError", "errorResponse", "contentType", "test", "text", "flash", "createSession", "initialData", "Map", "flashName", "unset", "isSession", "createSessionStorage", "_ref6", "cookieArg", "createData", "readData", "updateData", "deleteData", "warnOnceAboutSigningSessionCookie", "getSession", "commitSession", "session", "destroySession", "createCookieSessionStorage", "serializedCookie", "_session", "createMemorySessionStorage", "Math", "random", "substring", "segment", "param", "isRequired", "React4", "ReactDOM", "getHydrationData", "getRouteInfo", "location2", "initialMatches", "routeInfo", "hasHydrateFallback", "React3", "RSCRouterGlobalErrorBoundary", "constructor", "props", "getDerivedStateFromError", "getDerivedStateFromProps", "render", "RSCDefaultRootErrorBoundaryImpl", "renderAppShell", "ErrorWrapper", "_ref7", "title", "lang", "charSet", "content", "style", "fontFamily", "padding", "_ref8", "heyDeveloper", "dangerouslySetInnerHTML", "__html", "fontSize", "errorInstance", "errorString", "background", "color", "overflow", "RSCDefaultRootErrorBoundary", "_ref9", "hasRootLayout", "createRSCRouteModules", "payload", "populateRSCRouteModules", "Array", "isArray", "noopComponent", "createCallServer", "_ref0", "createFromReadableStream", "createTemporaryReferenceSet", "encodeReply", "fetch", "fetchImplementation", "globalVar", "window", "landedActionId", "_globalVar$__routerAc", "actionId", "__routerActionID", "temporaryReferences", "Accept", "type", "__reactRouterDataRouter", "navigate", "actionResult", "rerender", "startTransition", "lastMatch", "_lastMatch$id", "_lastMatch", "patchRoutes", "createRouteFromServerManifest", "_internalSetStateDoNotUseOrYouWillBreakYourApp", "createRouterFromPayload", "_ref1", "_globalVar$__reactRou", "_payload$patches", "unstable_getContext", "__reactRouterRouteModules", "patch", "_patches$get", "reduceRight", "previous", "childrenToPatch", "history", "find", "hydrateFallbackElement", "patchRoutesOnNavigation", "_ref10", "discoveredPaths", "fetchAndApplyManifestPatches", "dataStrategy", "getRSCSingleFetchDataStrategy", "initialized", "__routerInitialized", "initialize", "lastLoaderData", "subscribe", "_ref11", "_globalVar$__routerAc2", "_updateRoutesForHMR", "routeUpdateByRouteId", "oldRoutes", "newRoutes", "walkRoutes", "routes2", "routeUpdate", "routeModule", "hasComponent", "clientAction", "element", "errorElement", "updatedRoute", "_internalSetRoutes", "renderedRoutesContext", "getRouter", "M", "hasShouldRevalidate", "getFetchAndDecodeViaRSC", "unstable_runClientMiddleware", "renderedRoutesById", "renderedRoutes", "rendered", "_rendered$parentId", "targetRoutes", "dataKey", "RSCHydratedRouter", "_ref12", "useMemo", "useEffect", "useLayoutEffect", "setLocation", "useState", "newState", "_window$navigator", "navigator", "connection", "saveData", "registerElement", "el", "tagName", "getAttribute", "origin", "nextPaths", "fetchPatches", "document", "querySelectorAll", "from", "debouncedFetchPatches", "debounce", "observer", "MutationObserver", "observe", "documentElement", "subtree", "childList", "attributes", "attributeFilter", "frameworkContext", "flushSync", "_payload$errors", "_match$clientLoader", "hasInitialData", "hasInitialError", "initialError", "isHydrationRequest", "dataRoute", "singleFetch", "serverLoader", "preventInvalidServerHandlerCall", "callSingleFetch", "serverAction", "<PERSON><PERSON><PERSON><PERSON>", "fn", "msg", "discoveredPathsMaxSize", "URL_LIMIT", "getManifestUrl", "_globalVar$__reactRou2", "sort", "clear", "p", "addToFifoQueue", "_p$parentId", "queue", "size", "first", "next", "callback", "wait", "_len", "_key", "React5", "encoder2", "trailer", "injectRSCPayload", "rscStream", "decoder", "resolveFlightDataPromise", "flightDataPromise", "Promise", "resolve", "startedRSC", "buffered", "timeout", "flushBufferedChunks", "chunk", "buf", "decode", "TransformStream", "transform", "writeRSCStream", "catch", "then", "flush", "fatal", "read", "done", "writeChunk", "base64", "fromCodePoint", "releaseLock", "remaining", "escapeScript", "script", "REACT_USE", "useImpl", "useSafe", "promise", "routeRSCServerRequest", "_ref13", "fetchServer", "renderHTML", "isDataRequest", "isReactServerRequest", "respondWithRSCPayload", "isManifestRequest", "serverResponse", "serverResponseB", "clone", "payloadPromise", "getPayload", "_serverResponseB2", "_serverResponseB", "body2", "pipeThrough", "reason", "RSCStatic<PERSON><PERSON><PERSON>", "_ref14", "Location", "patchedLoaderData", "pathnameBase", "getRSCStream", "encoder3", "streamController", "handleChunk", "__FLIGHT_DATA", "readyState", "_streamController", "_streamController2", "deserializeErrors", "internal", "ErrorConstructor"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/react-router/dist/development/chunk-5UALIXAM.mjs"], "sourcesContent": ["/**\n * react-router v7.8.2\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport {\n  ENABLE_DEV_WARNINGS,\n  ErrorResponseImpl,\n  FrameworkContext,\n  NO_BODY_STATUS_CODES,\n  Outlet,\n  RSCRouterContext,\n  RemixErrorBoundary,\n  RouterProvider,\n  SINGLE_FETCH_REDIRECT_STATUS,\n  SingleFetchRedirectSymbol,\n  StaticRouterProvider,\n  StreamTransfer,\n  convertRoutesToDataRoutes,\n  createBrowserHistory,\n  createMemoryRouter,\n  createRequestInit,\n  createRouter,\n  createServerRoutes,\n  createStaticHandler,\n  createStaticRouter,\n  decodeViaTurboStream,\n  encode,\n  getManifestPath,\n  getSingleFetchDataStrategyImpl,\n  getStaticContextFromError,\n  invariant,\n  isDataWithResponseInit,\n  isMutationMethod,\n  isRedirectResponse,\n  isRedirectStatusCode,\n  isResponse,\n  isRouteErrorResponse,\n  matchRoutes,\n  noActionDefinedError,\n  redirect,\n  redirectDocument,\n  replace,\n  setIsHydrated,\n  shouldHydrateRouteLoader,\n  singleFetchUrl,\n  stripBasename,\n  stripIndexParam,\n  unstable_RouterContextProvider,\n  unstable_createContext,\n  useRouteError,\n  warnOnce,\n  withComponentProps,\n  withErrorBoundaryProps,\n  withHydrateFallbackProps\n} from \"./chunk-PVWAREVJ.mjs\";\n\n// lib/dom/ssr/server.tsx\nimport * as React from \"react\";\nfunction ServerRouter({\n  context,\n  url,\n  nonce\n}) {\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let { manifest, routeModules, criticalCss, serverHandoffString } = context;\n  let routes = createServerRoutes(\n    manifest.routes,\n    routeModules,\n    context.future,\n    context.isSpaMode\n  );\n  context.staticHandlerContext.loaderData = {\n    ...context.staticHandlerContext.loaderData\n  };\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    if (route && manifestRoute && shouldHydrateRouteLoader(\n      routeId,\n      route.clientLoader,\n      manifestRoute.hasLoader,\n      context.isSpaMode\n    ) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      delete context.staticHandlerContext.loaderData[routeId];\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext);\n  return /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement(\n    FrameworkContext.Provider,\n    {\n      value: {\n        manifest,\n        routeModules,\n        criticalCss,\n        serverHandoffString,\n        future: context.future,\n        ssr: context.ssr,\n        isSpaMode: context.isSpaMode,\n        routeDiscovery: context.routeDiscovery,\n        serializeError: context.serializeError,\n        renderMeta: context.renderMeta\n      }\n    },\n    /* @__PURE__ */ React.createElement(RemixErrorBoundary, { location: router.state.location }, /* @__PURE__ */ React.createElement(\n      StaticRouterProvider,\n      {\n        router,\n        context: context.staticHandlerContext,\n        hydrate: false\n      }\n    ))\n  ), context.serverHandoffStream ? /* @__PURE__ */ React.createElement(React.Suspense, null, /* @__PURE__ */ React.createElement(\n    StreamTransfer,\n    {\n      context,\n      identifier: 0,\n      reader: context.serverHandoffStream.getReader(),\n      textDecoder: new TextDecoder(),\n      nonce\n    }\n  )) : null);\n}\n\n// lib/dom/ssr/routes-test-stub.tsx\nimport * as React2 from \"react\";\nfunction createRoutesStub(routes, _context) {\n  return function RoutesTestStub({\n    initialEntries,\n    initialIndex,\n    hydrationData,\n    future\n  }) {\n    let routerRef = React2.useRef();\n    let frameworkContextRef = React2.useRef();\n    if (routerRef.current == null) {\n      frameworkContextRef.current = {\n        future: {\n          unstable_subResourceIntegrity: future?.unstable_subResourceIntegrity === true,\n          unstable_middleware: future?.unstable_middleware === true\n        },\n        manifest: {\n          routes: {},\n          entry: { imports: [], module: \"\" },\n          url: \"\",\n          version: \"\"\n        },\n        routeModules: {},\n        ssr: false,\n        isSpaMode: false,\n        routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" }\n      };\n      let patched = processRoutes(\n        // @ts-expect-error `StubRouteObject` is stricter about `loader`/`action`\n        // types compared to `AgnosticRouteObject`\n        convertRoutesToDataRoutes(routes, (r) => r),\n        _context !== void 0 ? _context : future?.unstable_middleware ? new unstable_RouterContextProvider() : {},\n        frameworkContextRef.current.manifest,\n        frameworkContextRef.current.routeModules\n      );\n      routerRef.current = createMemoryRouter(patched, {\n        initialEntries,\n        initialIndex,\n        hydrationData\n      });\n    }\n    return /* @__PURE__ */ React2.createElement(FrameworkContext.Provider, { value: frameworkContextRef.current }, /* @__PURE__ */ React2.createElement(RouterProvider, { router: routerRef.current }));\n  };\n}\nfunction processRoutes(routes, context, manifest, routeModules, parentId) {\n  return routes.map((route) => {\n    if (!route.id) {\n      throw new Error(\n        \"Expected a route.id in react-router processRoutes() function\"\n      );\n    }\n    let newRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      Component: route.Component ? withComponentProps(route.Component) : void 0,\n      HydrateFallback: route.HydrateFallback ? withHydrateFallbackProps(route.HydrateFallback) : void 0,\n      ErrorBoundary: route.ErrorBoundary ? withErrorBoundaryProps(route.ErrorBoundary) : void 0,\n      action: route.action ? (args) => route.action({ ...args, context }) : void 0,\n      loader: route.loader ? (args) => route.loader({ ...args, context }) : void 0,\n      handle: route.handle,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    let entryRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      parentId,\n      hasAction: route.action != null,\n      hasLoader: route.loader != null,\n      // When testing routes, you should be stubbing loader/action/middleware,\n      // not trying to re-implement the full loader/clientLoader/SSR/hydration\n      // flow. That is better tested via E2E tests.\n      hasClientAction: false,\n      hasClientLoader: false,\n      hasClientMiddleware: false,\n      hasErrorBoundary: route.ErrorBoundary != null,\n      // any need for these?\n      module: \"build/stub-path-to-module.js\",\n      clientActionModule: void 0,\n      clientLoaderModule: void 0,\n      clientMiddlewareModule: void 0,\n      hydrateFallbackModule: void 0\n    };\n    manifest.routes[newRoute.id] = entryRoute;\n    routeModules[route.id] = {\n      default: newRoute.Component || Outlet,\n      ErrorBoundary: newRoute.ErrorBoundary || void 0,\n      handle: route.handle,\n      links: route.links,\n      meta: route.meta,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    if (route.children) {\n      newRoute.children = processRoutes(\n        route.children,\n        context,\n        manifest,\n        routeModules,\n        newRoute.id\n      );\n    }\n    return newRoute;\n  });\n}\n\n// lib/server-runtime/cookies.ts\nimport { parse, serialize } from \"cookie\";\n\n// lib/server-runtime/crypto.ts\nvar encoder = /* @__PURE__ */ new TextEncoder();\nvar sign = async (value, secret) => {\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"sign\"]);\n  let signature = await crypto.subtle.sign(\"HMAC\", key, data2);\n  let hash = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(\n    /=+$/,\n    \"\"\n  );\n  return value + \".\" + hash;\n};\nvar unsign = async (cookie, secret) => {\n  let index = cookie.lastIndexOf(\".\");\n  let value = cookie.slice(0, index);\n  let hash = cookie.slice(index + 1);\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"verify\"]);\n  try {\n    let signature = byteStringToUint8Array(atob(hash));\n    let valid = await crypto.subtle.verify(\"HMAC\", key, signature, data2);\n    return valid ? value : false;\n  } catch (error) {\n    return false;\n  }\n};\nvar createKey = async (secret, usages) => crypto.subtle.importKey(\n  \"raw\",\n  encoder.encode(secret),\n  { name: \"HMAC\", hash: \"SHA-256\" },\n  false,\n  usages\n);\nfunction byteStringToUint8Array(byteString) {\n  let array = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    array[i] = byteString.charCodeAt(i);\n  }\n  return array;\n}\n\n// lib/server-runtime/cookies.ts\nvar createCookie = (name, cookieOptions = {}) => {\n  let { secrets = [], ...options } = {\n    path: \"/\",\n    sameSite: \"lax\",\n    ...cookieOptions\n  };\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1e3) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, { ...options, ...parseOptions });\n      if (name in cookies) {\n        let value = cookies[name];\n        if (typeof value === \"string\" && value !== \"\") {\n          let decoded = await decodeCookieValue(value, secrets);\n          return decoded;\n        } else {\n          return \"\";\n        }\n      } else {\n        return null;\n      }\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(\n        name,\n        value === \"\" ? \"\" : await encodeCookieValue(value, secrets),\n        {\n          ...options,\n          ...serializeOptions\n        }\n      );\n    }\n  };\n};\nvar isCookie = (object) => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(\n    !expires,\n    `The \"${name}\" cookie has an \"expires\" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use \\`commitSession(session, { expires })\\` if using a session storage object, or \\`cookie.serialize(\"value\", { expires })\\` if you're using the cookie directly.`\n  );\n}\n\n// lib/server-runtime/entry.ts\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo, routeId) => {\n    let route = manifest[routeId];\n    if (route) {\n      memo[routeId] = route.module;\n    }\n    return memo;\n  }, {});\n}\n\n// lib/server-runtime/mode.ts\nvar ServerMode = /* @__PURE__ */ ((ServerMode2) => {\n  ServerMode2[\"Development\"] = \"development\";\n  ServerMode2[\"Production\"] = \"production\";\n  ServerMode2[\"Test\"] = \"test\";\n  return ServerMode2;\n})(ServerMode || {});\nfunction isServerMode(value) {\n  return value === \"development\" /* Development */ || value === \"production\" /* Production */ || value === \"test\" /* Test */;\n}\n\n// lib/server-runtime/errors.ts\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== \"development\" /* Development */) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = void 0;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, [routeId, error]) => {\n    return Object.assign(acc, { [routeId]: sanitizeError(error, serverMode) });\n  }, {});\n}\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = { ...val, __type: \"RouteErrorResponse\" };\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = {\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.  This will only apply\n        // in dev mode since all production errors are sanitized to normal\n        // Error instances\n        ...sanitized.name !== \"Error\" ? {\n          __subType: sanitized.name\n        } : {}\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/server-runtime/routeMatching.ts\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(\n    routes,\n    pathname,\n    basename\n  );\n  if (!matches) return null;\n  return matches.map((match) => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\n// lib/server-runtime/data.ts\nasync function callRouteHandler(handler, args) {\n  let result = await handler({\n    request: stripRoutesParam(stripIndexParam2(args.request)),\n    params: args.params,\n    context: args.context\n  });\n  if (isDataWithResponseInit(result) && result.init && result.init.status && isRedirectStatusCode(result.init.status)) {\n    throw new Response(null, result.init);\n  }\n  return result;\n}\nfunction stripIndexParam2(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\n// lib/server-runtime/invariant.ts\nfunction invariant2(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\n      \"The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose\"\n    );\n    throw new Error(message);\n  }\n}\n\n// lib/server-runtime/dev.ts\nvar globalDevServerHooksKey = \"__reactRouterDevServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  return globalThis[globalDevServerHooksKey];\n}\nfunction getBuildTimeHeader(request, headerName) {\n  if (typeof process !== \"undefined\") {\n    try {\n      if (process.env?.IS_RR_BUILD_REQUEST === \"yes\") {\n        return request.headers.get(headerName);\n      }\n    } catch (e) {\n    }\n  }\n  return null;\n}\n\n// lib/server-runtime/routes.ts\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach((route) => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction createRoutes(manifest, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => ({\n    ...route,\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\nfunction createStaticHandlerDataRoutes(manifest, future, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      unstable_middleware: route.module.unstable_middleware,\n      // Need to use RR's version in the param typed here to permit the optional\n      // context even though we know it'll always be provided in remix\n      loader: route.module.loader ? async (args) => {\n        let preRenderedData = getBuildTimeHeader(\n          args.request,\n          \"X-React-Router-Prerender-Data\"\n        );\n        if (preRenderedData != null) {\n          let encoded = preRenderedData ? decodeURI(preRenderedData) : preRenderedData;\n          invariant2(encoded, \"Missing prerendered data for route\");\n          let uint8array = new TextEncoder().encode(encoded);\n          let stream = new ReadableStream({\n            start(controller) {\n              controller.enqueue(uint8array);\n              controller.close();\n            }\n          });\n          let decoded = await decodeViaTurboStream(stream, global);\n          let data2 = decoded.value;\n          if (data2 && SingleFetchRedirectSymbol in data2) {\n            let result = data2[SingleFetchRedirectSymbol];\n            let init = { status: result.status };\n            if (result.reload) {\n              throw redirectDocument(result.redirect, init);\n            } else if (result.replace) {\n              throw replace(result.redirect, init);\n            } else {\n              throw redirect(result.redirect, init);\n            }\n          } else {\n            invariant2(\n              data2 && route.id in data2,\n              \"Unable to decode prerendered data\"\n            );\n            let result = data2[route.id];\n            invariant2(\n              \"data\" in result,\n              \"Unable to process prerendered data\"\n            );\n            return result.data;\n          }\n        }\n        let val = await callRouteHandler(route.module.loader, args);\n        return val;\n      } : void 0,\n      action: route.module.action ? (args) => callRouteHandler(route.module.action, args) : void 0,\n      handle: route.module.handle\n    };\n    return route.index ? {\n      index: true,\n      ...commonRoute\n    } : {\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(\n        manifest,\n        future,\n        route.id,\n        routesByParentId\n      ),\n      ...commonRoute\n    };\n  });\n}\n\n// lib/server-runtime/markup.ts\nvar ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nvar ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction escapeHtml(html) {\n  return html.replace(ESCAPE_REGEX, (match) => ESCAPE_LOOKUP[match]);\n}\n\n// lib/server-runtime/serverHandoff.ts\nfunction createServerHandoffString(serverHandoff) {\n  return escapeHtml(JSON.stringify(serverHandoff));\n}\n\n// lib/server-runtime/headers.ts\nimport { splitCookiesString } from \"set-cookie-parser\";\nfunction getDocumentHeaders(context, build) {\n  return getDocumentHeadersImpl(context, (m) => {\n    let route = build.routes[m.route.id];\n    invariant2(route, `Route with id \"${m.route.id}\" not found in build`);\n    return route.module.headers;\n  });\n}\nfunction getDocumentHeadersImpl(context, getRouteHeadersFn, _defaultHeaders) {\n  let boundaryIdx = context.errors ? context.matches.findIndex((m) => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    let { actionHeaders, actionData, loaderHeaders, loaderData } = context;\n    context.matches.slice(boundaryIdx).some((match) => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || !actionData.hasOwnProperty(id))) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && !loaderData.hasOwnProperty(id)) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  const defaultHeaders = new Headers(_defaultHeaders);\n  return matches.reduce((parentHeaders, match, idx) => {\n    let { id } = match.route;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n    let includeErrorHeaders = errorHeaders != null && idx === matches.length - 1;\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n    let headersFn = getRouteHeadersFn(match);\n    if (headersFn == null) {\n      let headers2 = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers2);\n      }\n      prependCookies(actionHeaders, headers2);\n      prependCookies(loaderHeaders, headers2);\n      return headers2;\n    }\n    let headers = new Headers(\n      typeof headersFn === \"function\" ? headersFn({\n        loaderHeaders,\n        parentHeaders,\n        actionHeaders,\n        errorHeaders: includeErrorHeaders ? errorHeaders : void 0\n      }) : headersFn\n    );\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers(defaultHeaders));\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    let cookies = splitCookiesString(parentSetCookieString);\n    let childCookies = new Set(childHeaders.getSetCookie());\n    cookies.forEach((cookie) => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\n// lib/server-runtime/single-fetch.ts\nvar SERVER_NO_BODY_STATUS_CODES = /* @__PURE__ */ new Set([\n  ...NO_BODY_STATUS_CODES,\n  304\n]);\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...request.body ? { duplex: \"half\" } : void 0\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      skipRevalidation: true,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async (query) => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { error },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let singleFetchResult;\n    if (context.errors) {\n      singleFetchResult = { error: Object.values(context.errors)[0] };\n    } else {\n      singleFetchResult = {\n        data: Object.values(context.actionData || {})[0]\n      };\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: singleFetchResult,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let routesParam = new URL(request.url).searchParams.get(\"_routes\");\n  let loadRouteIds = routesParam ? new Set(routesParam.split(\",\")) : null;\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      filterMatchesToLoad: (m) => !loadRouteIds || loadRouteIds.has(m.route.id),\n      skipLoaderErrorBubbling: true,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async (query) => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { error },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let results = {};\n    let loadedMatches = new Set(\n      context.matches.filter(\n        (m) => loadRouteIds ? loadRouteIds.has(m.route.id) : m.route.loader != null\n      ).map((m) => m.route.id)\n    );\n    if (context.errors) {\n      for (let [id, error] of Object.entries(context.errors)) {\n        results[id] = { error };\n      }\n    }\n    for (let [id, data2] of Object.entries(context.loaderData)) {\n      if (!(id in results) && loadedMatches.has(id)) {\n        results[id] = { data: data2 };\n      }\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: results,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nfunction generateSingleFetchResponse(request, build, serverMode, {\n  result,\n  headers,\n  status\n}) {\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n  if (SERVER_NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, { status, headers: resultHeaders });\n  }\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n  resultHeaders.delete(\"Content-Length\");\n  return new Response(\n    encodeViaTurboStream(\n      result,\n      request.signal,\n      build.entry.module.streamTimeout,\n      serverMode\n    ),\n    {\n      status: status || 200,\n      headers: resultHeaders\n    }\n  );\n}\nfunction generateSingleFetchRedirectResponse(redirectResponse, request, build, serverMode) {\n  let redirect2 = getSingleFetchRedirect(\n    redirectResponse.status,\n    redirectResponse.headers,\n    build.basename\n  );\n  let headers = new Headers(redirectResponse.headers);\n  headers.delete(\"Location\");\n  headers.set(\"Content-Type\", \"text/x-script\");\n  return generateSingleFetchResponse(request, build, serverMode, {\n    result: request.method === \"GET\" ? { [SingleFetchRedirectSymbol]: redirect2 } : redirect2,\n    headers,\n    status: SINGLE_FETCH_REDIRECT_STATUS\n  });\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect2 = headers.get(\"Location\");\n  if (basename) {\n    redirect2 = stripBasename(redirect2, basename) || redirect2;\n  }\n  return {\n    redirect: redirect2,\n    status,\n    revalidate: (\n      // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n      // detail of ?_data requests as our way to tell the front end to revalidate when\n      // we didn't have a response body to include that information in.\n      // With single fetch, we tell the front end via this revalidate boolean field.\n      // However, we're respecting it for now because it may be something folks have\n      // used in their own responses\n      // TODO(v3): Consider removing or making this official public API\n      headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\")\n    ),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\nfunction encodeViaTurboStream(data2, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  let timeoutId = setTimeout(\n    () => controller.abort(new Error(\"Server Timeout\")),\n    typeof streamTimeout === \"number\" ? streamTimeout : 4950\n  );\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data2, {\n    signal: controller.signal,\n    plugins: [\n      (value) => {\n        if (value instanceof Error) {\n          let { name, message, stack } = serverMode === \"production\" /* Production */ ? sanitizeError(value, serverMode) : value;\n          return [\"SanitizedError\", name, message, stack];\n        }\n        if (value instanceof ErrorResponseImpl) {\n          let { data: data3, status, statusText } = value;\n          return [\"ErrorResponse\", data3, status, statusText];\n        }\n        if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n          return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n        }\n      }\n    ],\n    postPlugins: [\n      (value) => {\n        if (!value) return;\n        if (typeof value !== \"object\") return;\n        return [\n          \"SingleFetchClassInstance\",\n          Object.fromEntries(Object.entries(value))\n        ];\n      },\n      () => [\"SingleFetchFallback\"]\n    ]\n  });\n}\n\n// lib/server-runtime/server.ts\nfunction derive(build, mode) {\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : \"production\" /* Production */;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename\n  });\n  let errorHandler = build.entry.module.handleError || ((error, { request }) => {\n    if (serverMode !== \"test\" /* Test */ && !request.signal.aborted) {\n      console.error(\n        // @ts-expect-error This is \"private\" from users but intended for internal use\n        isRouteErrorResponse(error) && error.error ? error.error : error\n      );\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nvar createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, initialContext) {\n    _build = typeof build === \"function\" ? await build() : build;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let params = {};\n    let loadContext;\n    let handleError = (error) => {\n      if (mode === \"development\" /* Development */) {\n        getDevServerHooks()?.processRequestError?.(error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n    if (_build.future.unstable_middleware) {\n      if (initialContext && !(initialContext instanceof unstable_RouterContextProvider)) {\n        let error = new Error(\n          \"Invalid `context` value provided to `handleRequest`. When middleware is enabled you must return an instance of `unstable_RouterContextProvider` from your `getLoadContext` function.\"\n        );\n        handleError(error);\n        return returnLastResortErrorResponse(error, serverMode);\n      }\n      loadContext = initialContext || new unstable_RouterContextProvider();\n    } else {\n      loadContext = initialContext || {};\n    }\n    let url = new URL(request.url);\n    let normalizedBasename = _build.basename || \"/\";\n    let normalizedPath = url.pathname;\n    if (stripBasename(normalizedPath, normalizedBasename) === \"/_root.data\") {\n      normalizedPath = normalizedBasename;\n    } else if (normalizedPath.endsWith(\".data\")) {\n      normalizedPath = normalizedPath.replace(/\\.data$/, \"\");\n    }\n    if (stripBasename(normalizedPath, normalizedBasename) !== \"/\" && normalizedPath.endsWith(\"/\")) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n    let isSpaMode = getBuildTimeHeader(request, \"X-React-Router-SPA-Mode\") === \"yes\";\n    if (!_build.ssr) {\n      let decodedPath = decodeURI(normalizedPath);\n      if (normalizedBasename !== \"/\") {\n        let strippedPath = stripBasename(decodedPath, normalizedBasename);\n        if (strippedPath == null) {\n          errorHandler(\n            new ErrorResponseImpl(\n              404,\n              \"Not Found\",\n              `Refusing to prerender the \\`${decodedPath}\\` path because it does not start with the basename \\`${normalizedBasename}\\``\n            ),\n            {\n              context: loadContext,\n              params,\n              request\n            }\n          );\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        }\n        decodedPath = strippedPath;\n      }\n      if (_build.prerender.length === 0) {\n        isSpaMode = true;\n      } else if (!_build.prerender.includes(decodedPath) && !_build.prerender.includes(decodedPath + \"/\")) {\n        if (url.pathname.endsWith(\".data\")) {\n          errorHandler(\n            new ErrorResponseImpl(\n              404,\n              \"Not Found\",\n              `Refusing to SSR the path \\`${decodedPath}\\` because \\`ssr:false\\` is set and the path is not included in the \\`prerender\\` config, so in production the path will be a 404.`\n            ),\n            {\n              context: loadContext,\n              params,\n              request\n            }\n          );\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        } else {\n          isSpaMode = true;\n        }\n      }\n    }\n    let manifestUrl = getManifestPath(\n      _build.routeDiscovery.manifestPath,\n      normalizedBasename\n    );\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", { status: 500 });\n      }\n    }\n    let matches = matchServerRoutes(routes, normalizedPath, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = normalizedPath;\n      let singleFetchMatches = matchServerRoutes(\n        routes,\n        handlerUrl.pathname,\n        _build.basename\n      );\n      response = await handleSingleFetchRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        handlerUrl,\n        loadContext,\n        handleError\n      );\n      if (isRedirectResponse(response)) {\n        response = generateSingleFetchRedirectResponse(\n          response,\n          request,\n          _build,\n          serverMode\n        );\n      }\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          response = generateSingleFetchRedirectResponse(\n            response,\n            request,\n            _build,\n            serverMode\n          );\n        }\n      }\n    } else if (!isSpaMode && matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        matches.slice(-1)[0].route.id,\n        request,\n        loadContext,\n        handleError\n      );\n    } else {\n      let { pathname } = url;\n      let criticalCss = void 0;\n      if (_build.unstable_getCriticalCss) {\n        criticalCss = await _build.unstable_getCriticalCss({ pathname });\n      } else if (mode === \"development\" /* Development */ && getDevServerHooks()?.getCriticalCss) {\n        criticalCss = await getDevServerHooks()?.getCriticalCss?.(pathname);\n      }\n      response = await handleDocumentRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        loadContext,\n        handleError,\n        isSpaMode,\n        criticalCss\n      );\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"p\")) {\n    let paths = /* @__PURE__ */ new Set();\n    url.searchParams.getAll(\"p\").forEach((path) => {\n      if (!path.startsWith(\"/\")) {\n        path = `/${path}`;\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(`/${partialPath}`);\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          let route = build.assets.routes[routeId];\n          if (route) {\n            patches[routeId] = route;\n          }\n        }\n      }\n    }\n    return Response.json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    });\n  }\n  return new Response(\"Invalid Request\", { status: 400 });\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let response = request.method !== \"GET\" ? await singleFetchAction(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  ) : await singleFetchLoaders(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  );\n  return response;\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss) {\n  try {\n    let result = await staticHandler.query(request, {\n      requestContext: loadContext,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async (query) => {\n        try {\n          let innerResult = await query(request);\n          if (!isResponse(innerResult)) {\n            innerResult = await renderHtml(innerResult, isSpaMode);\n          }\n          return innerResult;\n        } catch (error) {\n          handleError(error);\n          return new Response(null, { status: 500 });\n        }\n      } : void 0\n    });\n    if (!isResponse(result)) {\n      result = await renderHtml(result, isSpaMode);\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return new Response(null, { status: 500 });\n  }\n  async function renderHtml(context, isSpaMode2) {\n    let headers = getDocumentHeaders(context, build);\n    if (SERVER_NO_BODY_STATUS_CODES.has(context.statusCode)) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors, serverMode)\n    };\n    let baseServerHandoff = {\n      basename: build.basename,\n      future: build.future,\n      routeDiscovery: build.routeDiscovery,\n      ssr: build.ssr,\n      isSpaMode: isSpaMode2\n    };\n    let entryContext = {\n      manifest: build.assets,\n      routeModules: createEntryRouteModules(build.routes),\n      staticHandlerContext: context,\n      criticalCss,\n      serverHandoffString: createServerHandoffString({\n        ...baseServerHandoff,\n        criticalCss\n      }),\n      serverHandoffStream: encodeViaTurboStream(\n        state,\n        request.signal,\n        build.entry.module.streamTimeout,\n        serverMode\n      ),\n      renderMeta: {},\n      future: build.future,\n      ssr: build.ssr,\n      routeDiscovery: build.routeDiscovery,\n      isSpaMode: isSpaMode2,\n      serializeError: (err) => serializeError(err, serverMode)\n    };\n    let handleDocumentRequestFunction = build.entry.module.default;\n    try {\n      return await handleDocumentRequestFunction(\n        request,\n        context.statusCode,\n        headers,\n        entryContext,\n        loadContext\n      );\n    } catch (error) {\n      handleError(error);\n      let errorForSecondRender = error;\n      if (isResponse(error)) {\n        try {\n          let data2 = await unwrapResponse(error);\n          errorForSecondRender = new ErrorResponseImpl(\n            error.status,\n            error.statusText,\n            data2\n          );\n        } catch (e) {\n        }\n      }\n      context = getStaticContextFromError(\n        staticHandler.dataRoutes,\n        context,\n        errorForSecondRender\n      );\n      if (context.errors) {\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let state2 = {\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: serializeErrors(context.errors, serverMode)\n      };\n      entryContext = {\n        ...entryContext,\n        staticHandlerContext: context,\n        serverHandoffString: createServerHandoffString(baseServerHandoff),\n        serverHandoffStream: encodeViaTurboStream(\n          state2,\n          request.signal,\n          build.entry.module.streamTimeout,\n          serverMode\n        ),\n        renderMeta: {}\n      };\n      try {\n        return await handleDocumentRequestFunction(\n          request,\n          context.statusCode,\n          headers,\n          entryContext,\n          loadContext\n        );\n      } catch (error2) {\n        handleError(error2);\n        return returnLastResortErrorResponse(error2, serverMode);\n      }\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let result = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext,\n      unstable_generateMiddlewareResponse: build.future.unstable_middleware ? async (queryRoute) => {\n        try {\n          let innerResult = await queryRoute(request);\n          return handleQueryRouteResult(innerResult);\n        } catch (error) {\n          return handleQueryRouteError(error);\n        }\n      } : void 0\n    });\n    return handleQueryRouteResult(result);\n  } catch (error) {\n    return handleQueryRouteError(error);\n  }\n  function handleQueryRouteResult(result) {\n    if (isResponse(result)) {\n      return result;\n    }\n    if (typeof result === \"string\") {\n      return new Response(result);\n    }\n    return Response.json(result);\n  }\n  function handleQueryRouteError(error) {\n    if (isResponse(error)) {\n      error.headers.set(\"X-Remix-Catch\", \"yes\");\n      return error;\n    }\n    if (isRouteErrorResponse(error)) {\n      handleError(error);\n      return errorResponseToJson(error, serverMode);\n    }\n    if (error instanceof Error && error.message === \"Expected a response from queryRoute\") {\n      let newError = new Error(\n        \"Expected a Response to be returned from resource route handler\"\n      );\n      handleError(newError);\n      return returnLastResortErrorResponse(newError, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return Response.json(\n    serializeError(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      errorResponse.error || new Error(\"Unexpected Server Error\"),\n      serverMode\n    ),\n    {\n      status: errorResponse.status,\n      statusText: errorResponse.statusText,\n      headers: {\n        \"X-Remix-Error\": \"yes\"\n      }\n    }\n  );\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== \"production\" /* Production */) {\n    message += `\n\n${String(error)}`;\n  }\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\n\n// lib/server-runtime/sessions.ts\nfunction flash(name) {\n  return `__flash_${name}__`;\n}\nvar createSession = (initialData = {}, id = \"\") => {\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return void 0;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\nvar isSession = (object) => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\nfunction createSessionStorage({\n  cookie: cookieArg,\n  createData,\n  readData,\n  updateData,\n  deleteData\n}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && await cookie.parse(cookieHeader, options);\n      let data2 = id && await readData(id);\n      return createSession(data2 || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let { id, data: data2 } = session;\n      let expires = options?.maxAge != null ? new Date(Date.now() + options.maxAge * 1e3) : options?.expires != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data2, expires);\n      } else {\n        id = await createData(data2, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(\n    cookie.isSigned,\n    `The \"${cookie.name}\" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.`\n  );\n}\n\n// lib/server-runtime/sessions/cookieStorage.ts\nfunction createCookieSessionStorage({ cookie: cookieArg } = {}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(\n        cookieHeader && await cookie.parse(cookieHeader, options) || {}\n      );\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\n          \"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length\n        );\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\n\n// lib/server-runtime/sessions/memoryStorage.ts\nfunction createMemorySessionStorage({ cookie } = {}) {\n  let map = /* @__PURE__ */ new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data2, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, { data: data2, expires });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let { data: data2, expires } = map.get(id);\n        if (!expires || expires > /* @__PURE__ */ new Date()) {\n          return data2;\n        }\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data2, expires) {\n      map.set(id, { data: data2, expires });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n}\n\n// lib/href.ts\nfunction href(path, ...args) {\n  let params = args[0];\n  return path.split(\"/\").map((segment) => {\n    if (segment === \"*\") {\n      return params ? params[\"*\"] : void 0;\n    }\n    const match = segment.match(/^:([\\w-]+)(\\?)?/);\n    if (!match) return segment;\n    const param = match[1];\n    const value = params ? params[param] : void 0;\n    const isRequired = match[2] === void 0;\n    if (isRequired && value === void 0) {\n      throw Error(\n        `Path '${path}' requires param '${param}' but it was not provided`\n      );\n    }\n    return value;\n  }).filter((segment) => segment !== void 0).join(\"/\");\n}\n\n// lib/rsc/browser.tsx\nimport * as React4 from \"react\";\nimport * as ReactDOM from \"react-dom\";\n\n// lib/dom/ssr/hydration.tsx\nfunction getHydrationData(state, routes, getRouteInfo, location2, basename, isSpaMode) {\n  let hydrationData = {\n    ...state,\n    loaderData: { ...state.loaderData }\n  };\n  let initialMatches = matchRoutes(routes, location2, basename);\n  if (initialMatches) {\n    for (let match of initialMatches) {\n      let routeId = match.route.id;\n      let routeInfo = getRouteInfo(routeId);\n      if (shouldHydrateRouteLoader(\n        routeId,\n        routeInfo.clientLoader,\n        routeInfo.hasLoader,\n        isSpaMode\n      ) && (routeInfo.hasHydrateFallback || !routeInfo.hasLoader)) {\n        delete hydrationData.loaderData[routeId];\n      } else if (!routeInfo.hasLoader) {\n        hydrationData.loaderData[routeId] = null;\n      }\n    }\n  }\n  return hydrationData;\n}\n\n// lib/rsc/errorBoundaries.tsx\nimport React3 from \"react\";\nvar RSCRouterGlobalErrorBoundary = class extends React3.Component {\n  constructor(props) {\n    super(props);\n    this.state = { error: null, location: props.location };\n  }\n  static getDerivedStateFromError(error) {\n    return { error };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location) {\n      return { error: null, location: props.location };\n    }\n    return { error: state.error, location: state.location };\n  }\n  render() {\n    if (this.state.error) {\n      return /* @__PURE__ */ React3.createElement(\n        RSCDefaultRootErrorBoundaryImpl,\n        {\n          error: this.state.error,\n          renderAppShell: true\n        }\n      );\n    } else {\n      return this.props.children;\n    }\n  }\n};\nfunction ErrorWrapper({\n  renderAppShell,\n  title,\n  children\n}) {\n  if (!renderAppShell) {\n    return children;\n  }\n  return /* @__PURE__ */ React3.createElement(\"html\", { lang: \"en\" }, /* @__PURE__ */ React3.createElement(\"head\", null, /* @__PURE__ */ React3.createElement(\"meta\", { charSet: \"utf-8\" }), /* @__PURE__ */ React3.createElement(\n    \"meta\",\n    {\n      name: \"viewport\",\n      content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n    }\n  ), /* @__PURE__ */ React3.createElement(\"title\", null, title)), /* @__PURE__ */ React3.createElement(\"body\", null, /* @__PURE__ */ React3.createElement(\"main\", { style: { fontFamily: \"system-ui, sans-serif\", padding: \"2rem\" } }, children)));\n}\nfunction RSCDefaultRootErrorBoundaryImpl({\n  error,\n  renderAppShell\n}) {\n  console.error(error);\n  let heyDeveloper = /* @__PURE__ */ React3.createElement(\n    \"script\",\n    {\n      dangerouslySetInnerHTML: {\n        __html: `\n        console.log(\n          \"\\u{1F4BF} Hey developer \\u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information.\"\n        );\n      `\n      }\n    }\n  );\n  if (isRouteErrorResponse(error)) {\n    return /* @__PURE__ */ React3.createElement(\n      ErrorWrapper,\n      {\n        renderAppShell,\n        title: \"Unhandled Thrown Response!\"\n      },\n      /* @__PURE__ */ React3.createElement(\"h1\", { style: { fontSize: \"24px\" } }, error.status, \" \", error.statusText),\n      ENABLE_DEV_WARNINGS ? heyDeveloper : null\n    );\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /* @__PURE__ */ React3.createElement(ErrorWrapper, { renderAppShell, title: \"Application Error!\" }, /* @__PURE__ */ React3.createElement(\"h1\", { style: { fontSize: \"24px\" } }, \"Application Error\"), /* @__PURE__ */ React3.createElement(\n    \"pre\",\n    {\n      style: {\n        padding: \"2rem\",\n        background: \"hsla(10, 50%, 50%, 0.1)\",\n        color: \"red\",\n        overflow: \"auto\"\n      }\n    },\n    errorInstance.stack\n  ), heyDeveloper);\n}\nfunction RSCDefaultRootErrorBoundary({\n  hasRootLayout\n}) {\n  let error = useRouteError();\n  if (hasRootLayout === void 0) {\n    throw new Error(\"Missing 'hasRootLayout' prop\");\n  }\n  return /* @__PURE__ */ React3.createElement(\n    RSCDefaultRootErrorBoundaryImpl,\n    {\n      renderAppShell: !hasRootLayout,\n      error\n    }\n  );\n}\n\n// lib/rsc/route-modules.ts\nfunction createRSCRouteModules(payload) {\n  const routeModules = {};\n  for (const match of payload.matches) {\n    populateRSCRouteModules(routeModules, match);\n  }\n  return routeModules;\n}\nfunction populateRSCRouteModules(routeModules, matches) {\n  matches = Array.isArray(matches) ? matches : [matches];\n  for (const match of matches) {\n    routeModules[match.id] = {\n      links: match.links,\n      meta: match.meta,\n      default: noopComponent\n    };\n  }\n}\nvar noopComponent = () => null;\n\n// lib/rsc/browser.tsx\nfunction createCallServer({\n  createFromReadableStream,\n  createTemporaryReferenceSet,\n  encodeReply,\n  fetch: fetchImplementation = fetch\n}) {\n  const globalVar = window;\n  let landedActionId = 0;\n  return async (id, args) => {\n    let actionId = globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    const temporaryReferences = createTemporaryReferenceSet();\n    const response = await fetchImplementation(\n      new Request(location.href, {\n        body: await encodeReply(args, { temporaryReferences }),\n        method: \"POST\",\n        headers: {\n          Accept: \"text/x-component\",\n          \"rsc-action-id\": id\n        }\n      })\n    );\n    if (!response.body) {\n      throw new Error(\"No response body\");\n    }\n    const payload = await createFromReadableStream(response.body, {\n      temporaryReferences\n    });\n    if (payload.type === \"redirect\") {\n      if (payload.reload) {\n        window.location.href = payload.location;\n        return;\n      }\n      globalVar.__reactRouterDataRouter.navigate(payload.location, {\n        replace: payload.replace\n      });\n      return payload.actionResult;\n    }\n    if (payload.type !== \"action\") {\n      throw new Error(\"Unexpected payload type\");\n    }\n    if (payload.rerender) {\n      React4.startTransition(\n        // @ts-expect-error - We have old react types that don't know this can be async\n        async () => {\n          const rerender = await payload.rerender;\n          if (!rerender) return;\n          if (landedActionId < actionId && globalVar.__routerActionID <= actionId) {\n            landedActionId = actionId;\n            if (rerender.type === \"redirect\") {\n              if (rerender.reload) {\n                window.location.href = rerender.location;\n                return;\n              }\n              globalVar.__reactRouterDataRouter.navigate(rerender.location, {\n                replace: rerender.replace\n              });\n              return;\n            }\n            let lastMatch;\n            for (const match of rerender.matches) {\n              globalVar.__reactRouterDataRouter.patchRoutes(\n                lastMatch?.id ?? null,\n                [createRouteFromServerManifest(match)],\n                true\n              );\n              lastMatch = match;\n            }\n            window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp(\n              {}\n            );\n            React4.startTransition(() => {\n              window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp(\n                {\n                  loaderData: Object.assign(\n                    {},\n                    globalVar.__reactRouterDataRouter.state.loaderData,\n                    rerender.loaderData\n                  ),\n                  errors: rerender.errors ? Object.assign(\n                    {},\n                    globalVar.__reactRouterDataRouter.state.errors,\n                    rerender.errors\n                  ) : null\n                }\n              );\n            });\n          }\n        }\n      );\n    }\n    return payload.actionResult;\n  };\n}\nfunction createRouterFromPayload({\n  fetchImplementation,\n  createFromReadableStream,\n  unstable_getContext,\n  payload\n}) {\n  const globalVar = window;\n  if (globalVar.__reactRouterDataRouter && globalVar.__reactRouterRouteModules)\n    return {\n      router: globalVar.__reactRouterDataRouter,\n      routeModules: globalVar.__reactRouterRouteModules\n    };\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  globalVar.__reactRouterRouteModules = globalVar.__reactRouterRouteModules ?? {};\n  populateRSCRouteModules(globalVar.__reactRouterRouteModules, payload.matches);\n  let patches = /* @__PURE__ */ new Map();\n  payload.patches?.forEach((patch) => {\n    invariant(patch.parentId, \"Invalid patch parentId\");\n    if (!patches.has(patch.parentId)) {\n      patches.set(patch.parentId, []);\n    }\n    patches.get(patch.parentId)?.push(patch);\n  });\n  let routes = payload.matches.reduceRight((previous, match) => {\n    const route = createRouteFromServerManifest(\n      match,\n      payload\n    );\n    if (previous.length > 0) {\n      route.children = previous;\n      let childrenToPatch = patches.get(match.id);\n      if (childrenToPatch) {\n        route.children.push(\n          ...childrenToPatch.map((r) => createRouteFromServerManifest(r))\n        );\n      }\n    }\n    return [route];\n  }, []);\n  globalVar.__reactRouterDataRouter = createRouter({\n    routes,\n    unstable_getContext,\n    basename: payload.basename,\n    history: createBrowserHistory(),\n    hydrationData: getHydrationData(\n      {\n        loaderData: payload.loaderData,\n        actionData: payload.actionData,\n        errors: payload.errors\n      },\n      routes,\n      (routeId) => {\n        let match = payload.matches.find((m) => m.id === routeId);\n        invariant(match, \"Route not found in payload\");\n        return {\n          clientLoader: match.clientLoader,\n          hasLoader: match.hasLoader,\n          hasHydrateFallback: match.hydrateFallbackElement != null\n        };\n      },\n      payload.location,\n      void 0,\n      false\n    ),\n    async patchRoutesOnNavigation({ path, signal }) {\n      if (discoveredPaths.has(path)) {\n        return;\n      }\n      await fetchAndApplyManifestPatches(\n        [path],\n        createFromReadableStream,\n        fetchImplementation,\n        signal\n      );\n    },\n    // FIXME: Pass `build.ssr` into this function\n    dataStrategy: getRSCSingleFetchDataStrategy(\n      () => globalVar.__reactRouterDataRouter,\n      true,\n      payload.basename,\n      createFromReadableStream,\n      fetchImplementation\n    )\n  });\n  if (globalVar.__reactRouterDataRouter.state.initialized) {\n    globalVar.__routerInitialized = true;\n    globalVar.__reactRouterDataRouter.initialize();\n  } else {\n    globalVar.__routerInitialized = false;\n  }\n  let lastLoaderData = void 0;\n  globalVar.__reactRouterDataRouter.subscribe(({ loaderData, actionData }) => {\n    if (lastLoaderData !== loaderData) {\n      globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    }\n  });\n  globalVar.__reactRouterDataRouter._updateRoutesForHMR = (routeUpdateByRouteId) => {\n    const oldRoutes = window.__reactRouterDataRouter.routes;\n    const newRoutes = [];\n    function walkRoutes(routes2, parentId) {\n      return routes2.map((route) => {\n        const routeUpdate = routeUpdateByRouteId.get(route.id);\n        if (routeUpdate) {\n          const {\n            routeModule,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader\n          } = routeUpdate;\n          const newRoute = createRouteFromServerManifest({\n            clientAction: routeModule.clientAction,\n            clientLoader: routeModule.clientLoader,\n            element: route.element,\n            errorElement: route.errorElement,\n            handle: route.handle,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader,\n            hydrateFallbackElement: route.hydrateFallbackElement,\n            id: route.id,\n            index: route.index,\n            links: routeModule.links,\n            meta: routeModule.meta,\n            parentId,\n            path: route.path,\n            shouldRevalidate: routeModule.shouldRevalidate\n          });\n          if (route.children) {\n            newRoute.children = walkRoutes(route.children, route.id);\n          }\n          return newRoute;\n        }\n        const updatedRoute = { ...route };\n        if (route.children) {\n          updatedRoute.children = walkRoutes(route.children, route.id);\n        }\n        return updatedRoute;\n      });\n    }\n    newRoutes.push(\n      ...walkRoutes(oldRoutes, void 0)\n    );\n    window.__reactRouterDataRouter._internalSetRoutes(newRoutes);\n  };\n  return {\n    router: globalVar.__reactRouterDataRouter,\n    routeModules: globalVar.__reactRouterRouteModules\n  };\n}\nvar renderedRoutesContext = unstable_createContext();\nfunction getRSCSingleFetchDataStrategy(getRouter, ssr, basename, createFromReadableStream, fetchImplementation) {\n  let dataStrategy = getSingleFetchDataStrategyImpl(\n    getRouter,\n    (match) => {\n      let M = match;\n      return {\n        hasLoader: M.route.hasLoader,\n        hasClientLoader: M.route.hasClientLoader,\n        hasComponent: M.route.hasComponent,\n        hasAction: M.route.hasAction,\n        hasClientAction: M.route.hasClientAction,\n        hasShouldRevalidate: M.route.hasShouldRevalidate\n      };\n    },\n    // pass map into fetchAndDecode so it can add payloads\n    getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation),\n    ssr,\n    basename,\n    // If the route has a component but we don't have an element, we need to hit\n    // the server loader flow regardless of whether the client loader calls\n    // `serverLoader` or not, otherwise we'll have nothing to render.\n    (match) => {\n      let M = match;\n      return M.route.hasComponent && !M.route.element;\n    }\n  );\n  return async (args) => args.unstable_runClientMiddleware(async () => {\n    let context = args.context;\n    context.set(renderedRoutesContext, []);\n    let results = await dataStrategy(args);\n    const renderedRoutesById = /* @__PURE__ */ new Map();\n    for (const route of context.get(renderedRoutesContext)) {\n      if (!renderedRoutesById.has(route.id)) {\n        renderedRoutesById.set(route.id, []);\n      }\n      renderedRoutesById.get(route.id).push(route);\n    }\n    for (const match of args.matches) {\n      const renderedRoutes = renderedRoutesById.get(match.route.id);\n      if (renderedRoutes) {\n        for (const rendered of renderedRoutes) {\n          window.__reactRouterDataRouter.patchRoutes(\n            rendered.parentId ?? null,\n            [createRouteFromServerManifest(rendered)],\n            true\n          );\n        }\n      }\n    }\n    return results;\n  });\n}\nfunction getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation) {\n  return async (args, basename, targetRoutes) => {\n    let { request, context } = args;\n    let url = singleFetchUrl(request.url, basename, \"rsc\");\n    if (request.method === \"GET\") {\n      url = stripIndexParam(url);\n      if (targetRoutes) {\n        url.searchParams.set(\"_routes\", targetRoutes.join(\",\"));\n      }\n    }\n    let res = await fetchImplementation(\n      new Request(url, await createRequestInit(request))\n    );\n    if (res.status === 404 && !res.headers.has(\"X-Remix-Response\")) {\n      throw new ErrorResponseImpl(404, \"Not Found\", true);\n    }\n    invariant(res.body, \"No response body to decode\");\n    try {\n      const payload = await createFromReadableStream(res.body, {\n        temporaryReferences: void 0\n      });\n      if (payload.type === \"redirect\") {\n        return {\n          status: res.status,\n          data: {\n            redirect: {\n              redirect: payload.location,\n              reload: payload.reload,\n              replace: payload.replace,\n              revalidate: false,\n              status: payload.status\n            }\n          }\n        };\n      }\n      if (payload.type !== \"render\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      context.get(renderedRoutesContext).push(...payload.matches);\n      let results = { routes: {} };\n      const dataKey = isMutationMethod(request.method) ? \"actionData\" : \"loaderData\";\n      for (let [routeId, data2] of Object.entries(payload[dataKey] || {})) {\n        results.routes[routeId] = { data: data2 };\n      }\n      if (payload.errors) {\n        for (let [routeId, error] of Object.entries(payload.errors)) {\n          results.routes[routeId] = { error };\n        }\n      }\n      return { status: res.status, data: results };\n    } catch (e) {\n      throw new Error(\"Unable to decode RSC response\");\n    }\n  };\n}\nfunction RSCHydratedRouter({\n  createFromReadableStream,\n  fetch: fetchImplementation = fetch,\n  payload,\n  routeDiscovery = \"eager\",\n  unstable_getContext\n}) {\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let { router, routeModules } = React4.useMemo(\n    () => createRouterFromPayload({\n      payload,\n      fetchImplementation,\n      unstable_getContext,\n      createFromReadableStream\n    }),\n    [\n      createFromReadableStream,\n      payload,\n      fetchImplementation,\n      unstable_getContext\n    ]\n  );\n  React4.useEffect(() => {\n    setIsHydrated();\n  }, []);\n  React4.useLayoutEffect(() => {\n    const globalVar = window;\n    if (!globalVar.__routerInitialized) {\n      globalVar.__routerInitialized = true;\n      globalVar.__reactRouterDataRouter.initialize();\n    }\n  }, []);\n  let [location2, setLocation] = React4.useState(router.state.location);\n  React4.useLayoutEffect(\n    () => router.subscribe((newState) => {\n      if (newState.location !== location2) {\n        setLocation(newState.location);\n      }\n    }),\n    [router, location2]\n  );\n  React4.useEffect(() => {\n    if (routeDiscovery === \"lazy\" || // @ts-expect-error - TS doesn't know about this yet\n    window.navigator?.connection?.saveData === true) {\n      return;\n    }\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let pathname = el.tagName === \"A\" ? el.pathname : new URL(path, window.location.origin).pathname;\n      if (!discoveredPaths.has(pathname)) {\n        nextPaths.add(pathname);\n      }\n    }\n    async function fetchPatches() {\n      document.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(registerElement);\n      let paths = Array.from(nextPaths.keys()).filter((path) => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (paths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(\n          paths,\n          createFromReadableStream,\n          fetchImplementation\n        );\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    fetchPatches();\n    let observer = new MutationObserver(() => debouncedFetchPatches());\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n  }, [routeDiscovery, createFromReadableStream, fetchImplementation]);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" },\n    routeModules\n  };\n  return /* @__PURE__ */ React4.createElement(RSCRouterContext.Provider, { value: true }, /* @__PURE__ */ React4.createElement(RSCRouterGlobalErrorBoundary, { location: location2 }, /* @__PURE__ */ React4.createElement(FrameworkContext.Provider, { value: frameworkContext }, /* @__PURE__ */ React4.createElement(RouterProvider, { router, flushSync: ReactDOM.flushSync }))));\n}\nfunction createRouteFromServerManifest(match, payload) {\n  let hasInitialData = payload && match.id in payload.loaderData;\n  let initialData = payload?.loaderData[match.id];\n  let hasInitialError = payload?.errors && match.id in payload.errors;\n  let initialError = payload?.errors?.[match.id];\n  let isHydrationRequest = match.clientLoader?.hydrate === true || !match.hasLoader || // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match.hasComponent && !match.element;\n  invariant(window.__reactRouterRouteModules);\n  populateRSCRouteModules(window.__reactRouterRouteModules, match);\n  let dataRoute = {\n    id: match.id,\n    element: match.element,\n    errorElement: match.errorElement,\n    handle: match.handle,\n    hasErrorBoundary: match.hasErrorBoundary,\n    hydrateFallbackElement: match.hydrateFallbackElement,\n    index: match.index,\n    loader: match.clientLoader ? async (args, singleFetch) => {\n      try {\n        let result = await match.clientLoader({\n          ...args,\n          serverLoader: () => {\n            preventInvalidServerHandlerCall(\n              \"loader\",\n              match.id,\n              match.hasLoader\n            );\n            if (isHydrationRequest) {\n              if (hasInitialData) {\n                return initialData;\n              }\n              if (hasInitialError) {\n                throw initialError;\n              }\n            }\n            return callSingleFetch(singleFetch);\n          }\n        });\n        return result;\n      } finally {\n        isHydrationRequest = false;\n      }\n    } : (\n      // We always make the call in this RSC world since even if we don't\n      // have a `loader` we may need to get the `element` implementation\n      (_, singleFetch) => callSingleFetch(singleFetch)\n    ),\n    action: match.clientAction ? (args, singleFetch) => match.clientAction({\n      ...args,\n      serverAction: async () => {\n        preventInvalidServerHandlerCall(\n          \"action\",\n          match.id,\n          match.hasLoader\n        );\n        return await callSingleFetch(singleFetch);\n      }\n    }) : match.hasAction ? (_, singleFetch) => callSingleFetch(singleFetch) : () => {\n      throw noActionDefinedError(\"action\", match.id);\n    },\n    path: match.path,\n    shouldRevalidate: match.shouldRevalidate,\n    // We always have a \"loader\" in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    hasLoader: true,\n    hasClientLoader: match.clientLoader != null,\n    hasAction: match.hasAction,\n    hasClientAction: match.clientAction != null,\n    hasShouldRevalidate: match.shouldRevalidate != null\n  };\n  if (typeof dataRoute.loader === \"function\") {\n    dataRoute.loader.hydrate = shouldHydrateRouteLoader(\n      match.id,\n      match.clientLoader,\n      match.hasLoader,\n      false\n    );\n  }\n  return dataRoute;\n}\nfunction callSingleFetch(singleFetch) {\n  invariant(typeof singleFetch === \"function\", \"Invalid singleFetch parameter\");\n  return singleFetch();\n}\nfunction preventInvalidServerHandlerCall(type, routeId, hasHandler) {\n  if (!hasHandler) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You are trying to call ${fn} on a route that does not have a server ${type} (routeId: \"${routeId}\")`;\n    console.error(msg);\n    throw new ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nvar nextPaths = /* @__PURE__ */ new Set();\nvar discoveredPathsMaxSize = 1e3;\nvar discoveredPaths = /* @__PURE__ */ new Set();\nvar URL_LIMIT = 7680;\nfunction getManifestUrl(paths) {\n  if (paths.length === 0) {\n    return null;\n  }\n  if (paths.length === 1) {\n    return new URL(`${paths[0]}.manifest`, window.location.origin);\n  }\n  const globalVar = window;\n  let basename = (globalVar.__reactRouterDataRouter.basename ?? \"\").replace(\n    /^\\/|\\/$/g,\n    \"\"\n  );\n  let url = new URL(`${basename}/.manifest`, window.location.origin);\n  paths.sort().forEach((path) => url.searchParams.append(\"p\", path));\n  return url;\n}\nasync function fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation, signal) {\n  let url = getManifestUrl(paths);\n  if (url == null) {\n    return;\n  }\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let response = await fetchImplementation(new Request(url, { signal }));\n  if (!response.body || response.status < 200 || response.status >= 300) {\n    throw new Error(\"Unable to fetch new route matches from the server\");\n  }\n  let payload = await createFromReadableStream(response.body, {\n    temporaryReferences: void 0\n  });\n  if (payload.type !== \"manifest\") {\n    throw new Error(\"Failed to patch routes\");\n  }\n  paths.forEach((p) => addToFifoQueue(p, discoveredPaths));\n  payload.patches.forEach((p) => {\n    window.__reactRouterDataRouter.patchRoutes(\n      p.parentId ?? null,\n      [createRouteFromServerManifest(p)]\n    );\n  });\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    queue.delete(first);\n  }\n  queue.add(path);\n}\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return (...args) => {\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\n// lib/rsc/server.ssr.tsx\nimport * as React5 from \"react\";\n\n// lib/rsc/html-stream/server.ts\nvar encoder2 = new TextEncoder();\nvar trailer = \"</body></html>\";\nfunction injectRSCPayload(rscStream) {\n  let decoder = new TextDecoder();\n  let resolveFlightDataPromise;\n  let flightDataPromise = new Promise(\n    (resolve) => resolveFlightDataPromise = resolve\n  );\n  let startedRSC = false;\n  let buffered = [];\n  let timeout = null;\n  function flushBufferedChunks(controller) {\n    for (let chunk of buffered) {\n      let buf = decoder.decode(chunk, { stream: true });\n      if (buf.endsWith(trailer)) {\n        buf = buf.slice(0, -trailer.length);\n      }\n      controller.enqueue(encoder2.encode(buf));\n    }\n    buffered.length = 0;\n    timeout = null;\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      buffered.push(chunk);\n      if (timeout) {\n        return;\n      }\n      timeout = setTimeout(async () => {\n        flushBufferedChunks(controller);\n        if (!startedRSC) {\n          startedRSC = true;\n          writeRSCStream(rscStream, controller).catch((err) => controller.error(err)).then(resolveFlightDataPromise);\n        }\n      }, 0);\n    },\n    async flush(controller) {\n      await flightDataPromise;\n      if (timeout) {\n        clearTimeout(timeout);\n        flushBufferedChunks(controller);\n      }\n      controller.enqueue(encoder2.encode(\"</body></html>\"));\n    }\n  });\n}\nasync function writeRSCStream(rscStream, controller) {\n  let decoder = new TextDecoder(\"utf-8\", { fatal: true });\n  const reader = rscStream.getReader();\n  try {\n    let read;\n    while ((read = await reader.read()) && !read.done) {\n      const chunk = read.value;\n      try {\n        writeChunk(\n          JSON.stringify(decoder.decode(chunk, { stream: true })),\n          controller\n        );\n      } catch (err) {\n        let base64 = JSON.stringify(btoa(String.fromCodePoint(...chunk)));\n        writeChunk(\n          `Uint8Array.from(atob(${base64}), m => m.codePointAt(0))`,\n          controller\n        );\n      }\n    }\n  } finally {\n    reader.releaseLock();\n  }\n  let remaining = decoder.decode();\n  if (remaining.length) {\n    writeChunk(JSON.stringify(remaining), controller);\n  }\n}\nfunction writeChunk(chunk, controller) {\n  controller.enqueue(\n    encoder2.encode(\n      `<script>${escapeScript(\n        `(self.__FLIGHT_DATA||=[]).push(${chunk})`\n      )}</script>`\n    )\n  );\n}\nfunction escapeScript(script) {\n  return script.replace(/<!--/g, \"<\\\\!--\").replace(/<\\/(script)/gi, \"</\\\\$1\");\n}\n\n// lib/rsc/server.ssr.tsx\nvar REACT_USE = \"use\";\nvar useImpl = React5[REACT_USE];\nfunction useSafe(promise) {\n  if (useImpl) {\n    return useImpl(promise);\n  }\n  throw new Error(\"React Router v7 requires React 19+ for RSC features.\");\n}\nasync function routeRSCServerRequest({\n  request,\n  fetchServer,\n  createFromReadableStream,\n  renderHTML,\n  hydrate = true\n}) {\n  const url = new URL(request.url);\n  const isDataRequest = isReactServerRequest(url);\n  const respondWithRSCPayload = isDataRequest || isManifestRequest(url) || request.headers.has(\"rsc-action-id\");\n  const serverResponse = await fetchServer(request);\n  if (respondWithRSCPayload || serverResponse.headers.get(\"React-Router-Resource\") === \"true\") {\n    return serverResponse;\n  }\n  if (!serverResponse.body) {\n    throw new Error(\"Missing body in server response\");\n  }\n  let serverResponseB = null;\n  if (hydrate) {\n    serverResponseB = serverResponse.clone();\n  }\n  const body = serverResponse.body;\n  let payloadPromise;\n  const getPayload = async () => {\n    if (payloadPromise) return payloadPromise;\n    payloadPromise = createFromReadableStream(body);\n    return payloadPromise;\n  };\n  try {\n    const payload = await getPayload();\n    if (serverResponse.status === SINGLE_FETCH_REDIRECT_STATUS && payload.type === \"redirect\") {\n      const headers2 = new Headers(serverResponse.headers);\n      headers2.delete(\"Content-Encoding\");\n      headers2.delete(\"Content-Length\");\n      headers2.delete(\"Content-Type\");\n      headers2.delete(\"x-remix-response\");\n      headers2.set(\"Location\", payload.location);\n      return new Response(serverResponseB?.body || \"\", {\n        headers: headers2,\n        status: payload.status,\n        statusText: serverResponse.statusText\n      });\n    }\n    const html = await renderHTML(getPayload);\n    const headers = new Headers(serverResponse.headers);\n    headers.set(\"Content-Type\", \"text/html\");\n    if (!hydrate) {\n      return new Response(html, {\n        status: serverResponse.status,\n        headers\n      });\n    }\n    if (!serverResponseB?.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n    return new Response(body2, {\n      status: serverResponse.status,\n      headers\n    });\n  } catch (reason) {\n    if (reason instanceof Response) {\n      return reason;\n    }\n    throw reason;\n  }\n}\nfunction RSCStaticRouter({ getPayload }) {\n  const payload = useSafe(getPayload());\n  if (payload.type === \"redirect\") {\n    throw new Response(null, {\n      status: payload.status,\n      headers: {\n        Location: payload.location\n      }\n    });\n  }\n  if (payload.type !== \"render\") return null;\n  let patchedLoaderData = { ...payload.loaderData };\n  for (const match of payload.matches) {\n    if (shouldHydrateRouteLoader(\n      match.id,\n      match.clientLoader,\n      match.hasLoader,\n      false\n    ) && (match.hydrateFallbackElement || !match.hasLoader)) {\n      delete patchedLoaderData[match.id];\n    }\n  }\n  const context = {\n    actionData: payload.actionData,\n    actionHeaders: {},\n    basename: payload.basename,\n    errors: payload.errors,\n    loaderData: patchedLoaderData,\n    loaderHeaders: {},\n    location: payload.location,\n    statusCode: 200,\n    matches: payload.matches.map((match) => ({\n      params: match.params,\n      pathname: match.pathname,\n      pathnameBase: match.pathnameBase,\n      route: {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        handle: match.handle,\n        hasErrorBoundary: match.hasErrorBoundary,\n        loader: match.hasLoader || !!match.clientLoader,\n        index: match.index,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      }\n    }))\n  };\n  const router = createStaticRouter(\n    payload.matches.reduceRight((previous, match) => {\n      const route = {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        element: match.element,\n        errorElement: match.errorElement,\n        handle: match.handle,\n        hasErrorBoundary: !!match.errorElement,\n        hydrateFallbackElement: match.hydrateFallbackElement,\n        index: match.index,\n        loader: match.hasLoader || !!match.clientLoader,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      };\n      if (previous.length > 0) {\n        route.children = previous;\n      }\n      return [route];\n    }, []),\n    context\n  );\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" },\n    routeModules: createRSCRouteModules(payload)\n  };\n  return /* @__PURE__ */ React5.createElement(RSCRouterContext.Provider, { value: true }, /* @__PURE__ */ React5.createElement(RSCRouterGlobalErrorBoundary, { location: payload.location }, /* @__PURE__ */ React5.createElement(FrameworkContext.Provider, { value: frameworkContext }, /* @__PURE__ */ React5.createElement(\n    StaticRouterProvider,\n    {\n      context,\n      router,\n      hydrate: false,\n      nonce: payload.nonce\n    }\n  ))));\n}\nfunction isReactServerRequest(url) {\n  return url.pathname.endsWith(\".rsc\");\n}\nfunction isManifestRequest(url) {\n  return url.pathname.endsWith(\".manifest\");\n}\n\n// lib/rsc/html-stream/browser.ts\nfunction getRSCStream() {\n  let encoder3 = new TextEncoder();\n  let streamController = null;\n  let rscStream = new ReadableStream({\n    start(controller) {\n      if (typeof window === \"undefined\") {\n        return;\n      }\n      let handleChunk = (chunk) => {\n        if (typeof chunk === \"string\") {\n          controller.enqueue(encoder3.encode(chunk));\n        } else {\n          controller.enqueue(chunk);\n        }\n      };\n      window.__FLIGHT_DATA || (window.__FLIGHT_DATA = []);\n      window.__FLIGHT_DATA.forEach(handleChunk);\n      window.__FLIGHT_DATA.push = (chunk) => {\n        handleChunk(chunk);\n        return 0;\n      };\n      streamController = controller;\n    }\n  });\n  if (typeof document !== \"undefined\" && document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      streamController?.close();\n    });\n  } else {\n    streamController?.close();\n  }\n  return rscStream;\n}\n\n// lib/dom/ssr/errors.ts\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\nexport {\n  ServerRouter,\n  createRoutesStub,\n  createCookie,\n  isCookie,\n  ServerMode,\n  setDevServerHooks,\n  createRequestHandler,\n  createSession,\n  isSession,\n  createSessionStorage,\n  createCookieSessionStorage,\n  createMemorySessionStorage,\n  href,\n  getHydrationData,\n  RSCDefaultRootErrorBoundary,\n  createCallServer,\n  RSCHydratedRouter,\n  routeRSCServerRequest,\n  RSCStaticRouter,\n  getRSCStream,\n  deserializeErrors\n};\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SACEA,mBAAmB,EACnBC,iBAAiB,EACjBC,gBAAgB,EAChBC,oBAAoB,EACpBC,MAAM,EACNC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,4BAA4B,EAC5BC,yBAAyB,EACzBC,oBAAoB,EACpBC,cAAc,EACdC,yBAAyB,EACzBC,oBAAoB,EACpBC,kBAAkB,EAClBC,iBAAiB,EACjBC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,kBAAkB,EAClBC,oBAAoB,EACpBC,MAAM,EACNC,eAAe,EACfC,8BAA8B,EAC9BC,yBAAyB,EACzBC,SAAS,EACTC,sBAAsB,EACtBC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,UAAU,EACVC,oBAAoB,EACpBC,WAAW,EACXC,oBAAoB,EACpBC,QAAQ,EACRC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,wBAAwB,EACxBC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,8BAA8B,EAC9BC,sBAAsB,EACtBC,aAAa,EACbC,QAAQ,EACRC,kBAAkB,EAClBC,sBAAsB,EACtBC,wBAAwB,QACnB,sBAAsB;;AAE7B;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAYA,CAAAC,IAAA,EAIlB;EAAA,IAJmB;IACpBC,OAAO;IACPC,GAAG;IACHC;EACF,CAAC,GAAAH,IAAA;EACC,IAAI,OAAOE,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAG,IAAIE,GAAG,CAACF,GAAG,CAAC;EACpB;EACA,IAAI;IAAEG,QAAQ;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAoB,CAAC,GAAGP,OAAO;EAC1E,IAAIQ,MAAM,GAAG3C,kBAAkB,CAC7BuC,QAAQ,CAACI,MAAM,EACfH,YAAY,EACZL,OAAO,CAACS,MAAM,EACdT,OAAO,CAACU,SACV,CAAC;EACDV,OAAO,CAACW,oBAAoB,CAACC,UAAU,GAAAC,aAAA,KAClCb,OAAO,CAACW,oBAAoB,CAACC,UAAU,CAC3C;EACD,KAAK,IAAIE,KAAK,IAAId,OAAO,CAACW,oBAAoB,CAACI,OAAO,EAAE;IACtD,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;IAC5B,IAAID,KAAK,GAAGZ,YAAY,CAACW,OAAO,CAAC;IACjC,IAAIG,aAAa,GAAGnB,OAAO,CAACI,QAAQ,CAACI,MAAM,CAACQ,OAAO,CAAC;IACpD,IAAIC,KAAK,IAAIE,aAAa,IAAIjC,wBAAwB,CACpD8B,OAAO,EACPC,KAAK,CAACG,YAAY,EAClBD,aAAa,CAACE,SAAS,EACvBrB,OAAO,CAACU,SACV,CAAC,KAAKO,KAAK,CAACK,eAAe,IAAI,CAACH,aAAa,CAACE,SAAS,CAAC,EAAE;MACxD,OAAOrB,OAAO,CAACW,oBAAoB,CAACC,UAAU,CAACI,OAAO,CAAC;IACzD;EACF;EACA,IAAIO,MAAM,GAAGxD,kBAAkB,CAACyC,MAAM,EAAER,OAAO,CAACW,oBAAoB,CAAC;EACrE,OAAO,eAAgBd,KAAK,CAAC2B,aAAa,CAAC3B,KAAK,CAAC4B,QAAQ,EAAE,IAAI,EAAE,eAAgB5B,KAAK,CAAC2B,aAAa,CAClG1E,gBAAgB,CAAC4E,QAAQ,EACzB;IACEC,KAAK,EAAE;MACLvB,QAAQ;MACRC,YAAY;MACZC,WAAW;MACXC,mBAAmB;MACnBE,MAAM,EAAET,OAAO,CAACS,MAAM;MACtBmB,GAAG,EAAE5B,OAAO,CAAC4B,GAAG;MAChBlB,SAAS,EAAEV,OAAO,CAACU,SAAS;MAC5BmB,cAAc,EAAE7B,OAAO,CAAC6B,cAAc;MACtCC,cAAc,EAAE9B,OAAO,CAAC8B,cAAc;MACtCC,UAAU,EAAE/B,OAAO,CAAC+B;IACtB;EACF,CAAC,EACD,eAAgBlC,KAAK,CAAC2B,aAAa,CAACtE,kBAAkB,EAAE;IAAE8E,QAAQ,EAAET,MAAM,CAACU,KAAK,CAACD;EAAS,CAAC,EAAE,eAAgBnC,KAAK,CAAC2B,aAAa,CAC9HlE,oBAAoB,EACpB;IACEiE,MAAM;IACNvB,OAAO,EAAEA,OAAO,CAACW,oBAAoB;IACrCuB,OAAO,EAAE;EACX,CACF,CAAC,CACH,CAAC,EAAElC,OAAO,CAACmC,mBAAmB,GAAG,eAAgBtC,KAAK,CAAC2B,aAAa,CAAC3B,KAAK,CAACuC,QAAQ,EAAE,IAAI,EAAE,eAAgBvC,KAAK,CAAC2B,aAAa,CAC5HjE,cAAc,EACd;IACEyC,OAAO;IACPqC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAEtC,OAAO,CAACmC,mBAAmB,CAACI,SAAS,CAAC,CAAC;IAC/CC,WAAW,EAAE,IAAIC,WAAW,CAAC,CAAC;IAC9BvC;EACF,CACF,CAAC,CAAC,GAAG,IAAI,CAAC;AACZ;;AAEA;AACA,OAAO,KAAKwC,MAAM,MAAM,OAAO;AAC/B,SAASC,gBAAgBA,CAACnC,MAAM,EAAEoC,QAAQ,EAAE;EAC1C,OAAO,SAASC,cAAcA,CAAAC,KAAA,EAK3B;IAAA,IAL4B;MAC7BC,cAAc;MACdC,YAAY;MACZC,aAAa;MACbxC;IACF,CAAC,GAAAqC,KAAA;IACC,IAAII,SAAS,GAAGR,MAAM,CAACS,MAAM,CAAC,CAAC;IAC/B,IAAIC,mBAAmB,GAAGV,MAAM,CAACS,MAAM,CAAC,CAAC;IACzC,IAAID,SAAS,CAACG,OAAO,IAAI,IAAI,EAAE;MAC7BD,mBAAmB,CAACC,OAAO,GAAG;QAC5B5C,MAAM,EAAE;UACN6C,6BAA6B,EAAE,CAAA7C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE6C,6BAA6B,MAAK,IAAI;UAC7EC,mBAAmB,EAAE,CAAA9C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE8C,mBAAmB,MAAK;QACvD,CAAC;QACDnD,QAAQ,EAAE;UACRI,MAAM,EAAE,CAAC,CAAC;UACVgD,KAAK,EAAE;YAAEC,OAAO,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAC;UAClCzD,GAAG,EAAE,EAAE;UACP0D,OAAO,EAAE;QACX,CAAC;QACDtD,YAAY,EAAE,CAAC,CAAC;QAChBuB,GAAG,EAAE,KAAK;QACVlB,SAAS,EAAE,KAAK;QAChBmB,cAAc,EAAE;UAAE+B,IAAI,EAAE,MAAM;UAAEC,YAAY,EAAE;QAAc;MAC9D,CAAC;MACD,IAAIC,OAAO,GAAGC,aAAa;MACzB;MACA;MACAvG,yBAAyB,CAACgD,MAAM,EAAGwD,CAAC,IAAKA,CAAC,CAAC,EAC3CpB,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGnC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE8C,mBAAmB,GAAG,IAAIjE,8BAA8B,CAAC,CAAC,GAAG,CAAC,CAAC,EACxG8D,mBAAmB,CAACC,OAAO,CAACjD,QAAQ,EACpCgD,mBAAmB,CAACC,OAAO,CAAChD,YAC9B,CAAC;MACD6C,SAAS,CAACG,OAAO,GAAG3F,kBAAkB,CAACoG,OAAO,EAAE;QAC9Cf,cAAc;QACdC,YAAY;QACZC;MACF,CAAC,CAAC;IACJ;IACA,OAAO,eAAgBP,MAAM,CAAClB,aAAa,CAAC1E,gBAAgB,CAAC4E,QAAQ,EAAE;MAAEC,KAAK,EAAEyB,mBAAmB,CAACC;IAAQ,CAAC,EAAE,eAAgBX,MAAM,CAAClB,aAAa,CAACrE,cAAc,EAAE;MAAEoE,MAAM,EAAE2B,SAAS,CAACG;IAAQ,CAAC,CAAC,CAAC;EACrM,CAAC;AACH;AACA,SAASU,aAAaA,CAACvD,MAAM,EAAER,OAAO,EAAEI,QAAQ,EAAEC,YAAY,EAAE4D,QAAQ,EAAE;EACxE,OAAOzD,MAAM,CAAC0D,GAAG,CAAEjD,KAAK,IAAK;IAC3B,IAAI,CAACA,KAAK,CAACC,EAAE,EAAE;MACb,MAAM,IAAIiD,KAAK,CACb,8DACF,CAAC;IACH;IACA,IAAIC,QAAQ,GAAG;MACblD,EAAE,EAAED,KAAK,CAACC,EAAE;MACZmD,IAAI,EAAEpD,KAAK,CAACoD,IAAI;MAChBC,KAAK,EAAErD,KAAK,CAACqD,KAAK;MAClBC,SAAS,EAAEtD,KAAK,CAACsD,SAAS,GAAG7E,kBAAkB,CAACuB,KAAK,CAACsD,SAAS,CAAC,GAAG,KAAK,CAAC;MACzEjD,eAAe,EAAEL,KAAK,CAACK,eAAe,GAAG1B,wBAAwB,CAACqB,KAAK,CAACK,eAAe,CAAC,GAAG,KAAK,CAAC;MACjGkD,aAAa,EAAEvD,KAAK,CAACuD,aAAa,GAAG7E,sBAAsB,CAACsB,KAAK,CAACuD,aAAa,CAAC,GAAG,KAAK,CAAC;MACzFC,MAAM,EAAExD,KAAK,CAACwD,MAAM,GAAIC,IAAI,IAAKzD,KAAK,CAACwD,MAAM,CAAA5D,aAAA,CAAAA,aAAA,KAAM6D,IAAI;QAAE1E;MAAO,EAAE,CAAC,GAAG,KAAK,CAAC;MAC5E2E,MAAM,EAAE1D,KAAK,CAAC0D,MAAM,GAAID,IAAI,IAAKzD,KAAK,CAAC0D,MAAM,CAAA9D,aAAA,CAAAA,aAAA,KAAM6D,IAAI;QAAE1E;MAAO,EAAE,CAAC,GAAG,KAAK,CAAC;MAC5E4E,MAAM,EAAE3D,KAAK,CAAC2D,MAAM;MACpBC,gBAAgB,EAAE5D,KAAK,CAAC4D;IAC1B,CAAC;IACD,IAAIC,UAAU,GAAG;MACf5D,EAAE,EAAED,KAAK,CAACC,EAAE;MACZmD,IAAI,EAAEpD,KAAK,CAACoD,IAAI;MAChBC,KAAK,EAAErD,KAAK,CAACqD,KAAK;MAClBL,QAAQ;MACRc,SAAS,EAAE9D,KAAK,CAACwD,MAAM,IAAI,IAAI;MAC/BpD,SAAS,EAAEJ,KAAK,CAAC0D,MAAM,IAAI,IAAI;MAC/B;MACA;MACA;MACAK,eAAe,EAAE,KAAK;MACtBC,eAAe,EAAE,KAAK;MACtBC,mBAAmB,EAAE,KAAK;MAC1BC,gBAAgB,EAAElE,KAAK,CAACuD,aAAa,IAAI,IAAI;MAC7C;MACAd,MAAM,EAAE,8BAA8B;MACtC0B,kBAAkB,EAAE,KAAK,CAAC;MAC1BC,kBAAkB,EAAE,KAAK,CAAC;MAC1BC,sBAAsB,EAAE,KAAK,CAAC;MAC9BC,qBAAqB,EAAE,KAAK;IAC9B,CAAC;IACDnF,QAAQ,CAACI,MAAM,CAAC4D,QAAQ,CAAClD,EAAE,CAAC,GAAG4D,UAAU;IACzCzE,YAAY,CAACY,KAAK,CAACC,EAAE,CAAC,GAAG;MACvBsE,OAAO,EAAEpB,QAAQ,CAACG,SAAS,IAAIvH,MAAM;MACrCwH,aAAa,EAAEJ,QAAQ,CAACI,aAAa,IAAI,KAAK,CAAC;MAC/CI,MAAM,EAAE3D,KAAK,CAAC2D,MAAM;MACpBa,KAAK,EAAExE,KAAK,CAACwE,KAAK;MAClBC,IAAI,EAAEzE,KAAK,CAACyE,IAAI;MAChBb,gBAAgB,EAAE5D,KAAK,CAAC4D;IAC1B,CAAC;IACD,IAAI5D,KAAK,CAAC0E,QAAQ,EAAE;MAClBvB,QAAQ,CAACuB,QAAQ,GAAG5B,aAAa,CAC/B9C,KAAK,CAAC0E,QAAQ,EACd3F,OAAO,EACPI,QAAQ,EACRC,YAAY,EACZ+D,QAAQ,CAAClD,EACX,CAAC;IACH;IACA,OAAOkD,QAAQ;EACjB,CAAC,CAAC;AACJ;;AAEA;AACA,SAASwB,KAAK,EAAEC,SAAS,QAAQ,QAAQ;;AAEzC;AACA,IAAIC,OAAO,GAAG,eAAgB,IAAIC,WAAW,CAAC,CAAC;AAC/C,IAAIC,IAAI,GAAG,MAAAA,CAAOrE,KAAK,EAAEsE,MAAM,KAAK;EAClC,IAAIC,KAAK,GAAGJ,OAAO,CAAC7H,MAAM,CAAC0D,KAAK,CAAC;EACjC,IAAIwE,GAAG,GAAG,MAAMC,SAAS,CAACH,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;EAC3C,IAAII,SAAS,GAAG,MAAMC,MAAM,CAACC,MAAM,CAACP,IAAI,CAAC,MAAM,EAAEG,GAAG,EAAED,KAAK,CAAC;EAC5D,IAAIM,IAAI,GAAGC,IAAI,CAACC,MAAM,CAACC,YAAY,CAAC,GAAG,IAAIC,UAAU,CAACP,SAAS,CAAC,CAAC,CAAC,CAACrH,OAAO,CACxE,KAAK,EACL,EACF,CAAC;EACD,OAAO2C,KAAK,GAAG,GAAG,GAAG6E,IAAI;AAC3B,CAAC;AACD,IAAIK,MAAM,GAAG,MAAAA,CAAOC,MAAM,EAAEb,MAAM,KAAK;EACrC,IAAI3B,KAAK,GAAGwC,MAAM,CAACC,WAAW,CAAC,GAAG,CAAC;EACnC,IAAIpF,KAAK,GAAGmF,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE1C,KAAK,CAAC;EAClC,IAAIkC,IAAI,GAAGM,MAAM,CAACE,KAAK,CAAC1C,KAAK,GAAG,CAAC,CAAC;EAClC,IAAI4B,KAAK,GAAGJ,OAAO,CAAC7H,MAAM,CAAC0D,KAAK,CAAC;EACjC,IAAIwE,GAAG,GAAG,MAAMC,SAAS,CAACH,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC;EAC7C,IAAI;IACF,IAAII,SAAS,GAAGY,sBAAsB,CAACC,IAAI,CAACV,IAAI,CAAC,CAAC;IAClD,IAAIW,KAAK,GAAG,MAAMb,MAAM,CAACC,MAAM,CAACa,MAAM,CAAC,MAAM,EAAEjB,GAAG,EAAEE,SAAS,EAAEH,KAAK,CAAC;IACrE,OAAOiB,KAAK,GAAGxF,KAAK,GAAG,KAAK;EAC9B,CAAC,CAAC,OAAO0F,KAAK,EAAE;IACd,OAAO,KAAK;EACd;AACF,CAAC;AACD,IAAIjB,SAAS,GAAG,MAAAA,CAAOH,MAAM,EAAEqB,MAAM,KAAKhB,MAAM,CAACC,MAAM,CAACgB,SAAS,CAC/D,KAAK,EACLzB,OAAO,CAAC7H,MAAM,CAACgI,MAAM,CAAC,EACtB;EAAEuB,IAAI,EAAE,MAAM;EAAEhB,IAAI,EAAE;AAAU,CAAC,EACjC,KAAK,EACLc,MACF,CAAC;AACD,SAASL,sBAAsBA,CAACQ,UAAU,EAAE;EAC1C,IAAIC,KAAK,GAAG,IAAId,UAAU,CAACa,UAAU,CAACE,MAAM,CAAC;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC1CF,KAAK,CAACE,CAAC,CAAC,GAAGH,UAAU,CAACI,UAAU,CAACD,CAAC,CAAC;EACrC;EACA,OAAOF,KAAK;AACd;;AAEA;AACA,IAAII,YAAY,GAAG,SAAAA,CAACN,IAAI,EAAyB;EAAA,IAAvBO,aAAa,GAAAC,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAC1C,IAAAE,qBAAA,GAAArH,aAAA;MACEwD,IAAI,EAAE,GAAG;MACT8D,QAAQ,EAAE;IAAK,GACZJ,aAAa;IAHd;MAAEK,OAAO,GAAG;IAAe,CAAC,GAAAF,qBAAA;IAATG,OAAO,GAAAC,wBAAA,CAAAJ,qBAAA,EAAAK,SAAA;EAK9BC,0BAA0B,CAAChB,IAAI,EAAEa,OAAO,CAACI,OAAO,CAAC;EACjD,OAAO;IACL,IAAIjB,IAAIA,CAAA,EAAG;MACT,OAAOA,IAAI;IACb,CAAC;IACD,IAAIkB,QAAQA,CAAA,EAAG;MACb,OAAON,OAAO,CAACT,MAAM,GAAG,CAAC;IAC3B,CAAC;IACD,IAAIc,OAAOA,CAAA,EAAG;MACZ,OAAO,OAAOJ,OAAO,CAACM,MAAM,KAAK,WAAW,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGR,OAAO,CAACM,MAAM,GAAG,GAAG,CAAC,GAAGN,OAAO,CAACI,OAAO;IAC9G,CAAC;IACD,MAAM7C,KAAKA,CAACkD,YAAY,EAAEC,YAAY,EAAE;MACtC,IAAI,CAACD,YAAY,EAAE,OAAO,IAAI;MAC9B,IAAIE,OAAO,GAAGpD,KAAK,CAACkD,YAAY,EAAAjI,aAAA,CAAAA,aAAA,KAAOwH,OAAO,GAAKU,YAAY,CAAE,CAAC;MAClE,IAAIvB,IAAI,IAAIwB,OAAO,EAAE;QACnB,IAAIrH,KAAK,GAAGqH,OAAO,CAACxB,IAAI,CAAC;QACzB,IAAI,OAAO7F,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,EAAE,EAAE;UAC7C,IAAIsH,OAAO,GAAG,MAAMC,iBAAiB,CAACvH,KAAK,EAAEyG,OAAO,CAAC;UACrD,OAAOa,OAAO;QAChB,CAAC,MAAM;UACL,OAAO,EAAE;QACX;MACF,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC;IACD,MAAMpD,SAASA,CAAClE,KAAK,EAAEwH,gBAAgB,EAAE;MACvC,OAAOtD,SAAS,CACd2B,IAAI,EACJ7F,KAAK,KAAK,EAAE,GAAG,EAAE,GAAG,MAAMyH,iBAAiB,CAACzH,KAAK,EAAEyG,OAAO,CAAC,EAAAvH,aAAA,CAAAA,aAAA,KAEtDwH,OAAO,GACPc,gBAAgB,CAEvB,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD,IAAIE,QAAQ,GAAIC,MAAM,IAAK;EACzB,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAAC9B,IAAI,KAAK,QAAQ,IAAI,OAAO8B,MAAM,CAACZ,QAAQ,KAAK,SAAS,IAAI,OAAOY,MAAM,CAAC1D,KAAK,KAAK,UAAU,IAAI,OAAO0D,MAAM,CAACzD,SAAS,KAAK,UAAU;AAClL,CAAC;AACD,eAAeuD,iBAAiBA,CAACzH,KAAK,EAAEyG,OAAO,EAAE;EAC/C,IAAImB,OAAO,GAAGC,UAAU,CAAC7H,KAAK,CAAC;EAC/B,IAAIyG,OAAO,CAACT,MAAM,GAAG,CAAC,EAAE;IACtB4B,OAAO,GAAG,MAAMvD,IAAI,CAACuD,OAAO,EAAEnB,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3C;EACA,OAAOmB,OAAO;AAChB;AACA,eAAeL,iBAAiBA,CAACvH,KAAK,EAAEyG,OAAO,EAAE;EAC/C,IAAIA,OAAO,CAACT,MAAM,GAAG,CAAC,EAAE;IACtB,KAAK,IAAI1B,MAAM,IAAImC,OAAO,EAAE;MAC1B,IAAIqB,aAAa,GAAG,MAAM5C,MAAM,CAAClF,KAAK,EAAEsE,MAAM,CAAC;MAC/C,IAAIwD,aAAa,KAAK,KAAK,EAAE;QAC3B,OAAOC,UAAU,CAACD,aAAa,CAAC;MAClC;IACF;IACA,OAAO,IAAI;EACb;EACA,OAAOC,UAAU,CAAC/H,KAAK,CAAC;AAC1B;AACA,SAAS6H,UAAUA,CAAC7H,KAAK,EAAE;EACzB,OAAO8E,IAAI,CAACkD,UAAU,CAACC,kBAAkB,CAACC,IAAI,CAACC,SAAS,CAACnI,KAAK,CAAC,CAAC,CAAC,CAAC;AACpE;AACA,SAAS+H,UAAUA,CAAC/H,KAAK,EAAE;EACzB,IAAI;IACF,OAAOkI,IAAI,CAACjE,KAAK,CAACmE,kBAAkB,CAACC,QAAQ,CAAC9C,IAAI,CAACvF,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC,OAAO0F,KAAK,EAAE;IACd,OAAO,CAAC,CAAC;EACX;AACF;AACA,SAAS2C,QAAQA,CAACrI,KAAK,EAAE;EACvB,IAAIsI,GAAG,GAAGtI,KAAK,CAACuI,QAAQ,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAI7F,KAAK,GAAG,CAAC;EACb,IAAI8F,GAAG,EAAEC,IAAI;EACb,OAAO/F,KAAK,GAAG2F,GAAG,CAACtC,MAAM,EAAE;IACzByC,GAAG,GAAGH,GAAG,CAACK,MAAM,CAAChG,KAAK,EAAE,CAAC;IACzB,IAAI,aAAa,CAACiG,IAAI,CAACH,GAAG,CAAC,EAAE;MAC3BD,MAAM,IAAIC,GAAG;IACf,CAAC,MAAM;MACLC,IAAI,GAAGD,GAAG,CAACvC,UAAU,CAAC,CAAC,CAAC;MACxB,IAAIwC,IAAI,GAAG,GAAG,EAAE;QACdF,MAAM,IAAI,GAAG,GAAGK,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC;MAC9B,CAAC,MAAM;QACLF,MAAM,IAAI,IAAI,GAAGK,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;MAC7C;IACF;EACF;EACA,OAAON,MAAM;AACf;AACA,SAASK,GAAGA,CAACH,IAAI,EAAE1C,MAAM,EAAE;EACzB,IAAIwC,MAAM,GAAGE,IAAI,CAACH,QAAQ,CAAC,EAAE,CAAC;EAC9B,OAAOC,MAAM,CAACxC,MAAM,GAAGA,MAAM,EAAEwC,MAAM,GAAG,GAAG,GAAGA,MAAM;EACpD,OAAOA,MAAM;AACf;AACA,SAASR,UAAUA,CAAChI,KAAK,EAAE;EACzB,IAAIsI,GAAG,GAAGtI,KAAK,CAACuI,QAAQ,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAI7F,KAAK,GAAG,CAAC;EACb,IAAI8F,GAAG,EAAEM,IAAI;EACb,OAAOpG,KAAK,GAAG2F,GAAG,CAACtC,MAAM,EAAE;IACzByC,GAAG,GAAGH,GAAG,CAACK,MAAM,CAAChG,KAAK,EAAE,CAAC;IACzB,IAAI8F,GAAG,KAAK,GAAG,EAAE;MACf,IAAIH,GAAG,CAACK,MAAM,CAAChG,KAAK,CAAC,KAAK,GAAG,EAAE;QAC7BoG,IAAI,GAAGT,GAAG,CAACjD,KAAK,CAAC1C,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,CAAC;QACtC,IAAI,eAAe,CAACiG,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9BP,MAAM,IAAIzD,MAAM,CAACC,YAAY,CAACgE,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,CAAC;UACjDpG,KAAK,IAAI,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACLoG,IAAI,GAAGT,GAAG,CAACjD,KAAK,CAAC1C,KAAK,EAAEA,KAAK,GAAG,CAAC,CAAC;QAClC,IAAI,eAAe,CAACiG,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9BP,MAAM,IAAIzD,MAAM,CAACC,YAAY,CAACgE,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,CAAC;UACjDpG,KAAK,IAAI,CAAC;UACV;QACF;MACF;IACF;IACA6F,MAAM,IAAIC,GAAG;EACf;EACA,OAAOD,MAAM;AACf;AACA,SAAS3B,0BAA0BA,CAAChB,IAAI,EAAEiB,OAAO,EAAE;EACjDhJ,QAAQ,CACN,CAACgJ,OAAO,WAAAmC,MAAA,CACApD,IAAI,iXACd,CAAC;AACH;;AAEA;AACA,SAASqD,uBAAuBA,CAACzK,QAAQ,EAAE;EACzC,OAAO0K,MAAM,CAACC,IAAI,CAAC3K,QAAQ,CAAC,CAAC4K,MAAM,CAAC,CAACC,IAAI,EAAEjK,OAAO,KAAK;IACrD,IAAIC,KAAK,GAAGb,QAAQ,CAACY,OAAO,CAAC;IAC7B,IAAIC,KAAK,EAAE;MACTgK,IAAI,CAACjK,OAAO,CAAC,GAAGC,KAAK,CAACyC,MAAM;IAC9B;IACA,OAAOuH,IAAI;EACb,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;;AAEA;AACA,IAAIC,UAAU,GAAG,eAAgB,CAAEC,WAAW,IAAK;EACjDA,WAAW,CAAC,aAAa,CAAC,GAAG,aAAa;EAC1CA,WAAW,CAAC,YAAY,CAAC,GAAG,YAAY;EACxCA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;EAC5B,OAAOA,WAAW;AACpB,CAAC,EAAED,UAAU,IAAI,CAAC,CAAC,CAAC;AACpB,SAASE,YAAYA,CAACzJ,KAAK,EAAE;EAC3B,OAAOA,KAAK,KAAK,aAAa,CAAC,qBAAqBA,KAAK,KAAK,YAAY,CAAC,oBAAoBA,KAAK,KAAK,MAAM,CAAC;AAClH;;AAEA;AACA,SAAS0J,aAAaA,CAAChE,KAAK,EAAEiE,UAAU,EAAE;EACxC,IAAIjE,KAAK,YAAYlD,KAAK,IAAImH,UAAU,KAAK,aAAa,CAAC,mBAAmB;IAC5E,IAAIC,SAAS,GAAG,IAAIpH,KAAK,CAAC,yBAAyB,CAAC;IACpDoH,SAAS,CAACC,KAAK,GAAG,KAAK,CAAC;IACxB,OAAOD,SAAS;EAClB;EACA,OAAOlE,KAAK;AACd;AACA,SAASoE,cAAcA,CAACC,MAAM,EAAEJ,UAAU,EAAE;EAC1C,OAAOR,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC,CAACV,MAAM,CAAC,CAACY,GAAG,EAAAC,KAAA,KAAuB;IAAA,IAArB,CAAC7K,OAAO,EAAEqG,KAAK,CAAC,GAAAwE,KAAA;IACzD,OAAOf,MAAM,CAACgB,MAAM,CAACF,GAAG,EAAE;MAAE,CAAC5K,OAAO,GAAGqK,aAAa,CAAChE,KAAK,EAAEiE,UAAU;IAAE,CAAC,CAAC;EAC5E,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,SAASxJ,cAAcA,CAACuF,KAAK,EAAEiE,UAAU,EAAE;EACzC,IAAIC,SAAS,GAAGF,aAAa,CAAChE,KAAK,EAAEiE,UAAU,CAAC;EAChD,OAAO;IACLS,OAAO,EAAER,SAAS,CAACQ,OAAO;IAC1BP,KAAK,EAAED,SAAS,CAACC;EACnB,CAAC;AACH;AACA,SAASQ,eAAeA,CAACN,MAAM,EAAEJ,UAAU,EAAE;EAC3C,IAAI,CAACI,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIC,OAAO,GAAGb,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC;EACpC,IAAIO,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI,CAAC9F,GAAG,EAAE+F,GAAG,CAAC,IAAIP,OAAO,EAAE;IAC9B,IAAIhN,oBAAoB,CAACuN,GAAG,CAAC,EAAE;MAC7BD,UAAU,CAAC9F,GAAG,CAAC,GAAAtF,aAAA,CAAAA,aAAA,KAAQqL,GAAG;QAAEC,MAAM,EAAE;MAAoB,EAAE;IAC5D,CAAC,MAAM,IAAID,GAAG,YAAY/H,KAAK,EAAE;MAC/B,IAAIoH,SAAS,GAAGF,aAAa,CAACa,GAAG,EAAEZ,UAAU,CAAC;MAC9CW,UAAU,CAAC9F,GAAG,CAAC,GAAAtF,aAAA;QACbkL,OAAO,EAAER,SAAS,CAACQ,OAAO;QAC1BP,KAAK,EAAED,SAAS,CAACC,KAAK;QACtBW,MAAM,EAAE;MAAO,GAKZZ,SAAS,CAAC/D,IAAI,KAAK,OAAO,GAAG;QAC9B4E,SAAS,EAAEb,SAAS,CAAC/D;MACvB,CAAC,GAAG,CAAC,CAAC,CACP;IACH,CAAC,MAAM;MACLyE,UAAU,CAAC9F,GAAG,CAAC,GAAG+F,GAAG;IACvB;EACF;EACA,OAAOD,UAAU;AACnB;;AAEA;AACA,SAASI,iBAAiBA,CAAC7L,MAAM,EAAE8L,QAAQ,EAAEC,QAAQ,EAAE;EACrD,IAAIxL,OAAO,GAAGnC,WAAW,CACvB4B,MAAM,EACN8L,QAAQ,EACRC,QACF,CAAC;EACD,IAAI,CAACxL,OAAO,EAAE,OAAO,IAAI;EACzB,OAAOA,OAAO,CAACmD,GAAG,CAAEpD,KAAK,KAAM;IAC7B0L,MAAM,EAAE1L,KAAK,CAAC0L,MAAM;IACpBF,QAAQ,EAAExL,KAAK,CAACwL,QAAQ;IACxBrL,KAAK,EAAEH,KAAK,CAACG;EACf,CAAC,CAAC,CAAC;AACL;;AAEA;AACA,eAAewL,gBAAgBA,CAACC,OAAO,EAAEhI,IAAI,EAAE;EAC7C,IAAIyF,MAAM,GAAG,MAAMuC,OAAO,CAAC;IACzBC,OAAO,EAAEC,gBAAgB,CAACC,gBAAgB,CAACnI,IAAI,CAACiI,OAAO,CAAC,CAAC;IACzDH,MAAM,EAAE9H,IAAI,CAAC8H,MAAM;IACnBxM,OAAO,EAAE0E,IAAI,CAAC1E;EAChB,CAAC,CAAC;EACF,IAAI1B,sBAAsB,CAAC6L,MAAM,CAAC,IAAIA,MAAM,CAAC2C,IAAI,IAAI3C,MAAM,CAAC2C,IAAI,CAACC,MAAM,IAAItO,oBAAoB,CAAC0L,MAAM,CAAC2C,IAAI,CAACC,MAAM,CAAC,EAAE;IACnH,MAAM,IAAIC,QAAQ,CAAC,IAAI,EAAE7C,MAAM,CAAC2C,IAAI,CAAC;EACvC;EACA,OAAO3C,MAAM;AACf;AACA,SAAS0C,gBAAgBA,CAACF,OAAO,EAAE;EACjC,IAAI1M,GAAG,GAAG,IAAIE,GAAG,CAACwM,OAAO,CAAC1M,GAAG,CAAC;EAC9B,IAAIgN,WAAW,GAAGhN,GAAG,CAACiN,YAAY,CAACC,MAAM,CAAC,OAAO,CAAC;EAClDlN,GAAG,CAACiN,YAAY,CAACE,MAAM,CAAC,OAAO,CAAC;EAChC,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,KAAK,IAAIC,UAAU,IAAIL,WAAW,EAAE;IAClC,IAAIK,UAAU,EAAE;MACdD,iBAAiB,CAACE,IAAI,CAACD,UAAU,CAAC;IACpC;EACF;EACA,KAAK,IAAIE,MAAM,IAAIH,iBAAiB,EAAE;IACpCpN,GAAG,CAACiN,YAAY,CAACO,MAAM,CAAC,OAAO,EAAED,MAAM,CAAC;EAC1C;EACA,IAAIV,IAAI,GAAG;IACTY,MAAM,EAAEf,OAAO,CAACe,MAAM;IACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;IAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;IACxBC,MAAM,EAAElB,OAAO,CAACkB;EAClB,CAAC;EACD,IAAIf,IAAI,CAACa,IAAI,EAAE;IACbb,IAAI,CAACgB,MAAM,GAAG,MAAM;EACtB;EACA,OAAO,IAAIC,OAAO,CAAC9N,GAAG,CAAC+N,IAAI,EAAElB,IAAI,CAAC;AACpC;AACA,SAASF,gBAAgBA,CAACD,OAAO,EAAE;EACjC,IAAI1M,GAAG,GAAG,IAAIE,GAAG,CAACwM,OAAO,CAAC1M,GAAG,CAAC;EAC9BA,GAAG,CAACiN,YAAY,CAACE,MAAM,CAAC,SAAS,CAAC;EAClC,IAAIN,IAAI,GAAG;IACTY,MAAM,EAAEf,OAAO,CAACe,MAAM;IACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;IAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;IACxBC,MAAM,EAAElB,OAAO,CAACkB;EAClB,CAAC;EACD,IAAIf,IAAI,CAACa,IAAI,EAAE;IACbb,IAAI,CAACgB,MAAM,GAAG,MAAM;EACtB;EACA,OAAO,IAAIC,OAAO,CAAC9N,GAAG,CAAC+N,IAAI,EAAElB,IAAI,CAAC;AACpC;;AAEA;AACA,SAASmB,UAAUA,CAACtM,KAAK,EAAEoK,OAAO,EAAE;EAClC,IAAIpK,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;IACrEuM,OAAO,CAAC7G,KAAK,CACX,iIACF,CAAC;IACD,MAAM,IAAIlD,KAAK,CAAC4H,OAAO,CAAC;EAC1B;AACF;;AAEA;AACA,IAAIoC,uBAAuB,GAAG,6BAA6B;AAC3D,SAASC,iBAAiBA,CAACC,cAAc,EAAE;EACzCC,UAAU,CAACH,uBAAuB,CAAC,GAAGE,cAAc;AACtD;AACA,SAASE,iBAAiBA,CAAA,EAAG;EAC3B,OAAOD,UAAU,CAACH,uBAAuB,CAAC;AAC5C;AACA,SAASK,kBAAkBA,CAAC7B,OAAO,EAAE8B,UAAU,EAAE;EAC/C,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;IAClC,IAAI;MAAA,IAAAC,YAAA;MACF,IAAI,EAAAA,YAAA,GAAAD,OAAO,CAACE,GAAG,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,mBAAmB,MAAK,KAAK,EAAE;QAC9C,OAAOlC,OAAO,CAACiB,OAAO,CAACkB,GAAG,CAACL,UAAU,CAAC;MACxC;IACF,CAAC,CAAC,OAAOM,CAAC,EAAE,CACZ;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAASC,qBAAqBA,CAAC5O,QAAQ,EAAE;EACvC,IAAII,MAAM,GAAG,CAAC,CAAC;EACfsK,MAAM,CAACmE,MAAM,CAAC7O,QAAQ,CAAC,CAAC8O,OAAO,CAAEjO,KAAK,IAAK;IACzC,IAAIA,KAAK,EAAE;MACT,IAAIgD,QAAQ,GAAGhD,KAAK,CAACgD,QAAQ,IAAI,EAAE;MACnC,IAAI,CAACzD,MAAM,CAACyD,QAAQ,CAAC,EAAE;QACrBzD,MAAM,CAACyD,QAAQ,CAAC,GAAG,EAAE;MACvB;MACAzD,MAAM,CAACyD,QAAQ,CAAC,CAACsJ,IAAI,CAACtM,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,OAAOT,MAAM;AACf;AACA,SAAS2O,YAAYA,CAAC/O,QAAQ,EAAqE;EAAA,IAAnE6D,QAAQ,GAAA+D,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EAAA,IAAEoH,gBAAgB,GAAApH,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGgH,qBAAqB,CAAC5O,QAAQ,CAAC;EAC/F,OAAO,CAACgP,gBAAgB,CAACnL,QAAQ,CAAC,IAAI,EAAE,EAAEC,GAAG,CAAEjD,KAAK,IAAAJ,aAAA,CAAAA,aAAA,KAC/CI,KAAK;IACR0E,QAAQ,EAAEwJ,YAAY,CAAC/O,QAAQ,EAAEa,KAAK,CAACC,EAAE,EAAEkO,gBAAgB;EAAC,EAC5D,CAAC;AACL;AACA,SAASC,6BAA6BA,CAACjP,QAAQ,EAAEK,MAAM,EAAqE;EAAA,IAAnEwD,QAAQ,GAAA+D,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EAAA,IAAEoH,gBAAgB,GAAApH,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGgH,qBAAqB,CAAC5O,QAAQ,CAAC;EACxH,OAAO,CAACgP,gBAAgB,CAACnL,QAAQ,CAAC,IAAI,EAAE,EAAEC,GAAG,CAAEjD,KAAK,IAAK;IACvD,IAAIqO,WAAW,GAAG;MAChB;MACAnK,gBAAgB,EAAElE,KAAK,CAACC,EAAE,KAAK,MAAM,IAAID,KAAK,CAACyC,MAAM,CAACc,aAAa,IAAI,IAAI;MAC3EtD,EAAE,EAAED,KAAK,CAACC,EAAE;MACZmD,IAAI,EAAEpD,KAAK,CAACoD,IAAI;MAChBd,mBAAmB,EAAEtC,KAAK,CAACyC,MAAM,CAACH,mBAAmB;MACrD;MACA;MACAoB,MAAM,EAAE1D,KAAK,CAACyC,MAAM,CAACiB,MAAM,GAAG,MAAOD,IAAI,IAAK;QAC5C,IAAI6K,eAAe,GAAGf,kBAAkB,CACtC9J,IAAI,CAACiI,OAAO,EACZ,+BACF,CAAC;QACD,IAAI4C,eAAe,IAAI,IAAI,EAAE;UAC3B,IAAIhG,OAAO,GAAGgG,eAAe,GAAGC,SAAS,CAACD,eAAe,CAAC,GAAGA,eAAe;UAC5EtB,UAAU,CAAC1E,OAAO,EAAE,oCAAoC,CAAC;UACzD,IAAIkG,UAAU,GAAG,IAAI1J,WAAW,CAAC,CAAC,CAAC9H,MAAM,CAACsL,OAAO,CAAC;UAClD,IAAImG,MAAM,GAAG,IAAIC,cAAc,CAAC;YAC9BC,KAAKA,CAACC,UAAU,EAAE;cAChBA,UAAU,CAACC,OAAO,CAACL,UAAU,CAAC;cAC9BI,UAAU,CAACE,KAAK,CAAC,CAAC;YACpB;UACF,CAAC,CAAC;UACF,IAAI9G,OAAO,GAAG,MAAMjL,oBAAoB,CAAC0R,MAAM,EAAEM,MAAM,CAAC;UACxD,IAAI9J,KAAK,GAAG+C,OAAO,CAACtH,KAAK;UACzB,IAAIuE,KAAK,IAAI7I,yBAAyB,IAAI6I,KAAK,EAAE;YAC/C,IAAIiE,MAAM,GAAGjE,KAAK,CAAC7I,yBAAyB,CAAC;YAC7C,IAAIyP,IAAI,GAAG;cAAEC,MAAM,EAAE5C,MAAM,CAAC4C;YAAO,CAAC;YACpC,IAAI5C,MAAM,CAAC8F,MAAM,EAAE;cACjB,MAAMlR,gBAAgB,CAACoL,MAAM,CAACrL,QAAQ,EAAEgO,IAAI,CAAC;YAC/C,CAAC,MAAM,IAAI3C,MAAM,CAACnL,OAAO,EAAE;cACzB,MAAMA,OAAO,CAACmL,MAAM,CAACrL,QAAQ,EAAEgO,IAAI,CAAC;YACtC,CAAC,MAAM;cACL,MAAMhO,QAAQ,CAACqL,MAAM,CAACrL,QAAQ,EAAEgO,IAAI,CAAC;YACvC;UACF,CAAC,MAAM;YACLmB,UAAU,CACR/H,KAAK,IAAIjF,KAAK,CAACC,EAAE,IAAIgF,KAAK,EAC1B,mCACF,CAAC;YACD,IAAIiE,MAAM,GAAGjE,KAAK,CAACjF,KAAK,CAACC,EAAE,CAAC;YAC5B+M,UAAU,CACR,MAAM,IAAI9D,MAAM,EAChB,oCACF,CAAC;YACD,OAAOA,MAAM,CAAC+F,IAAI;UACpB;QACF;QACA,IAAIhE,GAAG,GAAG,MAAMO,gBAAgB,CAACxL,KAAK,CAACyC,MAAM,CAACiB,MAAM,EAAED,IAAI,CAAC;QAC3D,OAAOwH,GAAG;MACZ,CAAC,GAAG,KAAK,CAAC;MACVzH,MAAM,EAAExD,KAAK,CAACyC,MAAM,CAACe,MAAM,GAAIC,IAAI,IAAK+H,gBAAgB,CAACxL,KAAK,CAACyC,MAAM,CAACe,MAAM,EAAEC,IAAI,CAAC,GAAG,KAAK,CAAC;MAC5FE,MAAM,EAAE3D,KAAK,CAACyC,MAAM,CAACkB;IACvB,CAAC;IACD,OAAO3D,KAAK,CAACqD,KAAK,GAAAzD,aAAA;MAChByD,KAAK,EAAE;IAAI,GACRgL,WAAW,IAAAzO,aAAA;MAEdsP,aAAa,EAAElP,KAAK,CAACkP,aAAa;MAClCxK,QAAQ,EAAE0J,6BAA6B,CACrCjP,QAAQ,EACRK,MAAM,EACNQ,KAAK,CAACC,EAAE,EACRkO,gBACF;IAAC,GACEE,WAAW,CACf;EACH,CAAC,CAAC;AACJ;;AAEA;AACA,IAAIc,aAAa,GAAG;EAClB,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,QAAQ,EAAE,SAAS;EACnB,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIC,YAAY,GAAG,oBAAoB;AACvC,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAOA,IAAI,CAACvR,OAAO,CAACqR,YAAY,EAAGvP,KAAK,IAAKsP,aAAa,CAACtP,KAAK,CAAC,CAAC;AACpE;;AAEA;AACA,SAAS0P,yBAAyBA,CAACC,aAAa,EAAE;EAChD,OAAOH,UAAU,CAACzG,IAAI,CAACC,SAAS,CAAC2G,aAAa,CAAC,CAAC;AAClD;;AAEA;AACA,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,SAASC,kBAAkBA,CAAC3Q,OAAO,EAAE4Q,KAAK,EAAE;EAC1C,OAAOC,sBAAsB,CAAC7Q,OAAO,EAAG8Q,CAAC,IAAK;IAC5C,IAAI7P,KAAK,GAAG2P,KAAK,CAACpQ,MAAM,CAACsQ,CAAC,CAAC7P,KAAK,CAACC,EAAE,CAAC;IACpC+M,UAAU,CAAChN,KAAK,qBAAA2J,MAAA,CAAoBkG,CAAC,CAAC7P,KAAK,CAACC,EAAE,0BAAsB,CAAC;IACrE,OAAOD,KAAK,CAACyC,MAAM,CAACkK,OAAO;EAC7B,CAAC,CAAC;AACJ;AACA,SAASiD,sBAAsBA,CAAC7Q,OAAO,EAAE+Q,iBAAiB,EAAEC,eAAe,EAAE;EAC3E,IAAIC,WAAW,GAAGjR,OAAO,CAAC0L,MAAM,GAAG1L,OAAO,CAACe,OAAO,CAACmQ,SAAS,CAAEJ,CAAC,IAAK9Q,OAAO,CAAC0L,MAAM,CAACoF,CAAC,CAAC7P,KAAK,CAACC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;EACpG,IAAIH,OAAO,GAAGkQ,WAAW,IAAI,CAAC,GAAGjR,OAAO,CAACe,OAAO,CAACiG,KAAK,CAAC,CAAC,EAAEiK,WAAW,GAAG,CAAC,CAAC,GAAGjR,OAAO,CAACe,OAAO;EAC5F,IAAIoQ,YAAY;EAChB,IAAIF,WAAW,IAAI,CAAC,EAAE;IACpB,IAAI;MAAEG,aAAa;MAAEC,UAAU;MAAEC,aAAa;MAAE1Q;IAAW,CAAC,GAAGZ,OAAO;IACtEA,OAAO,CAACe,OAAO,CAACiG,KAAK,CAACiK,WAAW,CAAC,CAACM,IAAI,CAAEzQ,KAAK,IAAK;MACjD,IAAII,EAAE,GAAGJ,KAAK,CAACG,KAAK,CAACC,EAAE;MACvB,IAAIkQ,aAAa,CAAClQ,EAAE,CAAC,KAAK,CAACmQ,UAAU,IAAI,CAACA,UAAU,CAACG,cAAc,CAACtQ,EAAE,CAAC,CAAC,EAAE;QACxEiQ,YAAY,GAAGC,aAAa,CAAClQ,EAAE,CAAC;MAClC,CAAC,MAAM,IAAIoQ,aAAa,CAACpQ,EAAE,CAAC,IAAI,CAACN,UAAU,CAAC4Q,cAAc,CAACtQ,EAAE,CAAC,EAAE;QAC9DiQ,YAAY,GAAGG,aAAa,CAACpQ,EAAE,CAAC;MAClC;MACA,OAAOiQ,YAAY,IAAI,IAAI;IAC7B,CAAC,CAAC;EACJ;EACA,MAAMM,cAAc,GAAG,IAAIC,OAAO,CAACV,eAAe,CAAC;EACnD,OAAOjQ,OAAO,CAACiK,MAAM,CAAC,CAAC2G,aAAa,EAAE7Q,KAAK,EAAE8Q,GAAG,KAAK;IACnD,IAAI;MAAE1Q;IAAG,CAAC,GAAGJ,KAAK,CAACG,KAAK;IACxB,IAAIqQ,aAAa,GAAGtR,OAAO,CAACsR,aAAa,CAACpQ,EAAE,CAAC,IAAI,IAAIwQ,OAAO,CAAC,CAAC;IAC9D,IAAIN,aAAa,GAAGpR,OAAO,CAACoR,aAAa,CAAClQ,EAAE,CAAC,IAAI,IAAIwQ,OAAO,CAAC,CAAC;IAC9D,IAAIG,mBAAmB,GAAGV,YAAY,IAAI,IAAI,IAAIS,GAAG,KAAK7Q,OAAO,CAAC4G,MAAM,GAAG,CAAC;IAC5E,IAAImK,mBAAmB,GAAGD,mBAAmB,IAAIV,YAAY,KAAKG,aAAa,IAAIH,YAAY,KAAKC,aAAa;IACjH,IAAIW,SAAS,GAAGhB,iBAAiB,CAACjQ,KAAK,CAAC;IACxC,IAAIiR,SAAS,IAAI,IAAI,EAAE;MACrB,IAAIC,QAAQ,GAAG,IAAIN,OAAO,CAACC,aAAa,CAAC;MACzC,IAAIG,mBAAmB,EAAE;QACvBG,cAAc,CAACd,YAAY,EAAEa,QAAQ,CAAC;MACxC;MACAC,cAAc,CAACb,aAAa,EAAEY,QAAQ,CAAC;MACvCC,cAAc,CAACX,aAAa,EAAEU,QAAQ,CAAC;MACvC,OAAOA,QAAQ;IACjB;IACA,IAAIpE,OAAO,GAAG,IAAI8D,OAAO,CACvB,OAAOK,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC;MAC1CT,aAAa;MACbK,aAAa;MACbP,aAAa;MACbD,YAAY,EAAEU,mBAAmB,GAAGV,YAAY,GAAG,KAAK;IAC1D,CAAC,CAAC,GAAGY,SACP,CAAC;IACD,IAAID,mBAAmB,EAAE;MACvBG,cAAc,CAACd,YAAY,EAAEvD,OAAO,CAAC;IACvC;IACAqE,cAAc,CAACb,aAAa,EAAExD,OAAO,CAAC;IACtCqE,cAAc,CAACX,aAAa,EAAE1D,OAAO,CAAC;IACtCqE,cAAc,CAACN,aAAa,EAAE/D,OAAO,CAAC;IACtC,OAAOA,OAAO;EAChB,CAAC,EAAE,IAAI8D,OAAO,CAACD,cAAc,CAAC,CAAC;AACjC;AACA,SAASQ,cAAcA,CAACN,aAAa,EAAEO,YAAY,EAAE;EACnD,IAAIC,qBAAqB,GAAGR,aAAa,CAAC7C,GAAG,CAAC,YAAY,CAAC;EAC3D,IAAIqD,qBAAqB,EAAE;IACzB,IAAInJ,OAAO,GAAG0H,kBAAkB,CAACyB,qBAAqB,CAAC;IACvD,IAAIC,YAAY,GAAG,IAAIC,GAAG,CAACH,YAAY,CAACI,YAAY,CAAC,CAAC,CAAC;IACvDtJ,OAAO,CAACkG,OAAO,CAAEpI,MAAM,IAAK;MAC1B,IAAI,CAACsL,YAAY,CAACG,GAAG,CAACzL,MAAM,CAAC,EAAE;QAC7BoL,YAAY,CAACzE,MAAM,CAAC,YAAY,EAAE3G,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,IAAI0L,2BAA2B,GAAG,eAAgB,IAAIH,GAAG,CAAC,CACxD,GAAGtV,oBAAoB,EACvB,GAAG,CACJ,CAAC;AACF,eAAe0V,iBAAiBA,CAAC7B,KAAK,EAAEtF,UAAU,EAAEoH,aAAa,EAAE/F,OAAO,EAAEgG,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EAChH,IAAI;IACF,IAAIC,cAAc,GAAG,IAAI/E,OAAO,CAAC4E,UAAU,EAAA9R,aAAA;MACzC6M,MAAM,EAAEf,OAAO,CAACe,MAAM;MACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;MAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;MACxBC,MAAM,EAAElB,OAAO,CAACkB;IAAM,GACnBlB,OAAO,CAACgB,IAAI,GAAG;MAAEG,MAAM,EAAE;IAAO,CAAC,GAAG,KAAK,CAAC,CAC9C,CAAC;IACF,IAAI3D,MAAM,GAAG,MAAMuI,aAAa,CAACK,KAAK,CAACD,cAAc,EAAE;MACrDE,cAAc,EAAEJ,WAAW;MAC3BK,uBAAuB,EAAE,IAAI;MAC7BC,gBAAgB,EAAE,IAAI;MACtBC,mCAAmC,EAAEvC,KAAK,CAACnQ,MAAM,CAAC8C,mBAAmB,GAAG,MAAOwP,KAAK,IAAK;QACvF,IAAI;UACF,IAAIK,WAAW,GAAG,MAAML,KAAK,CAACD,cAAc,CAAC;UAC7C,OAAOO,iBAAiB,CAACD,WAAW,CAAC;QACvC,CAAC,CAAC,OAAO/L,KAAK,EAAE;UACd,OAAOiM,gBAAgB,CAACjM,KAAK,CAAC;QAChC;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,OAAOgM,iBAAiB,CAAClJ,MAAM,CAAC;EAClC,CAAC,CAAC,OAAO9C,KAAK,EAAE;IACd,OAAOiM,gBAAgB,CAACjM,KAAK,CAAC;EAChC;EACA,SAASgM,iBAAiBA,CAAClJ,MAAM,EAAE;IACjC,OAAOzL,UAAU,CAACyL,MAAM,CAAC,GAAGA,MAAM,GAAGoJ,uBAAuB,CAACpJ,MAAM,CAAC;EACtE;EACA,SAASmJ,gBAAgBA,CAACjM,KAAK,EAAE;IAC/BwL,WAAW,CAACxL,KAAK,CAAC;IAClB,OAAOmM,2BAA2B,CAAC7G,OAAO,EAAEiE,KAAK,EAAEtF,UAAU,EAAE;MAC7DnB,MAAM,EAAE;QAAE9C;MAAM,CAAC;MACjBuG,OAAO,EAAE,IAAI8D,OAAO,CAAC,CAAC;MACtB3E,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EACA,SAASwG,uBAAuBA,CAACvT,OAAO,EAAE;IACxC,IAAI4N,OAAO,GAAG+C,kBAAkB,CAAC3Q,OAAO,EAAE4Q,KAAK,CAAC;IAChD,IAAInS,oBAAoB,CAACuB,OAAO,CAACyT,UAAU,CAAC,IAAI7F,OAAO,CAAC2E,GAAG,CAAC,UAAU,CAAC,EAAE;MACvE,OAAO,IAAIvF,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAE/M,OAAO,CAACyT,UAAU;QAAE7F;MAAQ,CAAC,CAAC;IACpE;IACA,IAAI5N,OAAO,CAAC0L,MAAM,EAAE;MAClBZ,MAAM,CAACmE,MAAM,CAACjP,OAAO,CAAC0L,MAAM,CAAC,CAACwD,OAAO,CAAEwE,GAAG,IAAK;QAC7C,IAAI,CAAC/U,oBAAoB,CAAC+U,GAAG,CAAC,IAAIA,GAAG,CAACrM,KAAK,EAAE;UAC3CwL,WAAW,CAACa,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACF1T,OAAO,CAAC0L,MAAM,GAAGD,cAAc,CAACzL,OAAO,CAAC0L,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAIqI,iBAAiB;IACrB,IAAI3T,OAAO,CAAC0L,MAAM,EAAE;MAClBiI,iBAAiB,GAAG;QAAEtM,KAAK,EAAEyD,MAAM,CAACmE,MAAM,CAACjP,OAAO,CAAC0L,MAAM,CAAC,CAAC,CAAC;MAAE,CAAC;IACjE,CAAC,MAAM;MACLiI,iBAAiB,GAAG;QAClBzD,IAAI,EAAEpF,MAAM,CAACmE,MAAM,CAACjP,OAAO,CAACqR,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC;IACH;IACA,OAAOmC,2BAA2B,CAAC7G,OAAO,EAAEiE,KAAK,EAAEtF,UAAU,EAAE;MAC7DnB,MAAM,EAAEwJ,iBAAiB;MACzB/F,OAAO;MACPb,MAAM,EAAE/M,OAAO,CAACyT;IAClB,CAAC,CAAC;EACJ;AACF;AACA,eAAeG,kBAAkBA,CAAChD,KAAK,EAAEtF,UAAU,EAAEoH,aAAa,EAAE/F,OAAO,EAAEgG,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACjH,IAAIgB,WAAW,GAAG,IAAI1T,GAAG,CAACwM,OAAO,CAAC1M,GAAG,CAAC,CAACiN,YAAY,CAAC4B,GAAG,CAAC,SAAS,CAAC;EAClE,IAAIgF,YAAY,GAAGD,WAAW,GAAG,IAAIxB,GAAG,CAACwB,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;EACvE,IAAI;IACF,IAAIjB,cAAc,GAAG,IAAI/E,OAAO,CAAC4E,UAAU,EAAE;MAC3C/E,OAAO,EAAEjB,OAAO,CAACiB,OAAO;MACxBC,MAAM,EAAElB,OAAO,CAACkB;IAClB,CAAC,CAAC;IACF,IAAI1D,MAAM,GAAG,MAAMuI,aAAa,CAACK,KAAK,CAACD,cAAc,EAAE;MACrDE,cAAc,EAAEJ,WAAW;MAC3BoB,mBAAmB,EAAGlD,CAAC,IAAK,CAACgD,YAAY,IAAIA,YAAY,CAACvB,GAAG,CAACzB,CAAC,CAAC7P,KAAK,CAACC,EAAE,CAAC;MACzE+R,uBAAuB,EAAE,IAAI;MAC7BE,mCAAmC,EAAEvC,KAAK,CAACnQ,MAAM,CAAC8C,mBAAmB,GAAG,MAAOwP,KAAK,IAAK;QACvF,IAAI;UACF,IAAIK,WAAW,GAAG,MAAML,KAAK,CAACD,cAAc,CAAC;UAC7C,OAAOO,iBAAiB,CAACD,WAAW,CAAC;QACvC,CAAC,CAAC,OAAO/L,KAAK,EAAE;UACd,OAAOiM,gBAAgB,CAACjM,KAAK,CAAC;QAChC;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,OAAOgM,iBAAiB,CAAClJ,MAAM,CAAC;EAClC,CAAC,CAAC,OAAO9C,KAAK,EAAE;IACd,OAAOiM,gBAAgB,CAACjM,KAAK,CAAC;EAChC;EACA,SAASgM,iBAAiBA,CAAClJ,MAAM,EAAE;IACjC,OAAOzL,UAAU,CAACyL,MAAM,CAAC,GAAGA,MAAM,GAAGoJ,uBAAuB,CAACpJ,MAAM,CAAC;EACtE;EACA,SAASmJ,gBAAgBA,CAACjM,KAAK,EAAE;IAC/BwL,WAAW,CAACxL,KAAK,CAAC;IAClB,OAAOmM,2BAA2B,CAAC7G,OAAO,EAAEiE,KAAK,EAAEtF,UAAU,EAAE;MAC7DnB,MAAM,EAAE;QAAE9C;MAAM,CAAC;MACjBuG,OAAO,EAAE,IAAI8D,OAAO,CAAC,CAAC;MACtB3E,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EACA,SAASwG,uBAAuBA,CAACvT,OAAO,EAAE;IACxC,IAAI4N,OAAO,GAAG+C,kBAAkB,CAAC3Q,OAAO,EAAE4Q,KAAK,CAAC;IAChD,IAAInS,oBAAoB,CAACuB,OAAO,CAACyT,UAAU,CAAC,IAAI7F,OAAO,CAAC2E,GAAG,CAAC,UAAU,CAAC,EAAE;MACvE,OAAO,IAAIvF,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAE/M,OAAO,CAACyT,UAAU;QAAE7F;MAAQ,CAAC,CAAC;IACpE;IACA,IAAI5N,OAAO,CAAC0L,MAAM,EAAE;MAClBZ,MAAM,CAACmE,MAAM,CAACjP,OAAO,CAAC0L,MAAM,CAAC,CAACwD,OAAO,CAAEwE,GAAG,IAAK;QAC7C,IAAI,CAAC/U,oBAAoB,CAAC+U,GAAG,CAAC,IAAIA,GAAG,CAACrM,KAAK,EAAE;UAC3CwL,WAAW,CAACa,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACF1T,OAAO,CAAC0L,MAAM,GAAGD,cAAc,CAACzL,OAAO,CAAC0L,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAI2I,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIC,aAAa,GAAG,IAAI7B,GAAG,CACzBrS,OAAO,CAACe,OAAO,CAACoT,MAAM,CACnBrD,CAAC,IAAKgD,YAAY,GAAGA,YAAY,CAACvB,GAAG,CAACzB,CAAC,CAAC7P,KAAK,CAACC,EAAE,CAAC,GAAG4P,CAAC,CAAC7P,KAAK,CAAC0D,MAAM,IAAI,IACzE,CAAC,CAACT,GAAG,CAAE4M,CAAC,IAAKA,CAAC,CAAC7P,KAAK,CAACC,EAAE,CACzB,CAAC;IACD,IAAIlB,OAAO,CAAC0L,MAAM,EAAE;MAClB,KAAK,IAAI,CAACxK,EAAE,EAAEmG,KAAK,CAAC,IAAIyD,MAAM,CAACa,OAAO,CAAC3L,OAAO,CAAC0L,MAAM,CAAC,EAAE;QACtDuI,OAAO,CAAC/S,EAAE,CAAC,GAAG;UAAEmG;QAAM,CAAC;MACzB;IACF;IACA,KAAK,IAAI,CAACnG,EAAE,EAAEgF,KAAK,CAAC,IAAI4E,MAAM,CAACa,OAAO,CAAC3L,OAAO,CAACY,UAAU,CAAC,EAAE;MAC1D,IAAI,EAAEM,EAAE,IAAI+S,OAAO,CAAC,IAAIC,aAAa,CAAC3B,GAAG,CAACrR,EAAE,CAAC,EAAE;QAC7C+S,OAAO,CAAC/S,EAAE,CAAC,GAAG;UAAEgP,IAAI,EAAEhK;QAAM,CAAC;MAC/B;IACF;IACA,OAAOsN,2BAA2B,CAAC7G,OAAO,EAAEiE,KAAK,EAAEtF,UAAU,EAAE;MAC7DnB,MAAM,EAAE8J,OAAO;MACfrG,OAAO;MACPb,MAAM,EAAE/M,OAAO,CAACyT;IAClB,CAAC,CAAC;EACJ;AACF;AACA,SAASD,2BAA2BA,CAAC7G,OAAO,EAAEiE,KAAK,EAAEtF,UAAU,EAAA8I,KAAA,EAI5D;EAAA,IAJ8D;IAC/DjK,MAAM;IACNyD,OAAO;IACPb;EACF,CAAC,GAAAqH,KAAA;EACC,IAAIC,aAAa,GAAG,IAAI3C,OAAO,CAAC9D,OAAO,CAAC;EACxCyG,aAAa,CAACC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC;EAC5C,IAAI9B,2BAA2B,CAACD,GAAG,CAACxF,MAAM,CAAC,EAAE;IAC3C,OAAO,IAAIC,QAAQ,CAAC,IAAI,EAAE;MAAED,MAAM;MAAEa,OAAO,EAAEyG;IAAc,CAAC,CAAC;EAC/D;EACAA,aAAa,CAACC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;EAClDD,aAAa,CAACjH,MAAM,CAAC,gBAAgB,CAAC;EACtC,OAAO,IAAIJ,QAAQ,CACjBuH,oBAAoB,CAClBpK,MAAM,EACNwC,OAAO,CAACkB,MAAM,EACd+C,KAAK,CAACpN,KAAK,CAACE,MAAM,CAAC8Q,aAAa,EAChClJ,UACF,CAAC,EACD;IACEyB,MAAM,EAAEA,MAAM,IAAI,GAAG;IACrBa,OAAO,EAAEyG;EACX,CACF,CAAC;AACH;AACA,SAASI,mCAAmCA,CAACC,gBAAgB,EAAE/H,OAAO,EAAEiE,KAAK,EAAEtF,UAAU,EAAE;EACzF,IAAIqJ,SAAS,GAAGC,sBAAsB,CACpCF,gBAAgB,CAAC3H,MAAM,EACvB2H,gBAAgB,CAAC9G,OAAO,EACxBgD,KAAK,CAACrE,QACR,CAAC;EACD,IAAIqB,OAAO,GAAG,IAAI8D,OAAO,CAACgD,gBAAgB,CAAC9G,OAAO,CAAC;EACnDA,OAAO,CAACR,MAAM,CAAC,UAAU,CAAC;EAC1BQ,OAAO,CAAC0G,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;EAC5C,OAAOd,2BAA2B,CAAC7G,OAAO,EAAEiE,KAAK,EAAEtF,UAAU,EAAE;IAC7DnB,MAAM,EAAEwC,OAAO,CAACe,MAAM,KAAK,KAAK,GAAG;MAAE,CAACrQ,yBAAyB,GAAGsX;IAAU,CAAC,GAAGA,SAAS;IACzF/G,OAAO;IACPb,MAAM,EAAE3P;EACV,CAAC,CAAC;AACJ;AACA,SAASwX,sBAAsBA,CAAC7H,MAAM,EAAEa,OAAO,EAAErB,QAAQ,EAAE;EACzD,IAAIoI,SAAS,GAAG/G,OAAO,CAACkB,GAAG,CAAC,UAAU,CAAC;EACvC,IAAIvC,QAAQ,EAAE;IACZoI,SAAS,GAAGvV,aAAa,CAACuV,SAAS,EAAEpI,QAAQ,CAAC,IAAIoI,SAAS;EAC7D;EACA,OAAO;IACL7V,QAAQ,EAAE6V,SAAS;IACnB5H,MAAM;IACN8H,UAAU;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACAjH,OAAO,CAAC2E,GAAG,CAAC,oBAAoB,CAAC,IAAI3E,OAAO,CAAC2E,GAAG,CAAC,YAAY,CAC9D;IACDtC,MAAM,EAAErC,OAAO,CAAC2E,GAAG,CAAC,yBAAyB,CAAC;IAC9CvT,OAAO,EAAE4O,OAAO,CAAC2E,GAAG,CAAC,iBAAiB;EACxC,CAAC;AACH;AACA,SAASgC,oBAAoBA,CAACrO,KAAK,EAAE4O,aAAa,EAAEN,aAAa,EAAElJ,UAAU,EAAE;EAC7E,IAAIuE,UAAU,GAAG,IAAIkF,eAAe,CAAC,CAAC;EACtC,IAAIC,SAAS,GAAGC,UAAU,CACxB,MAAMpF,UAAU,CAACqF,KAAK,CAAC,IAAI/Q,KAAK,CAAC,gBAAgB,CAAC,CAAC,EACnD,OAAOqQ,aAAa,KAAK,QAAQ,GAAGA,aAAa,GAAG,IACtD,CAAC;EACDM,aAAa,CAACK,gBAAgB,CAAC,OAAO,EAAE,MAAMC,YAAY,CAACJ,SAAS,CAAC,CAAC;EACtE,OAAO/W,MAAM,CAACiI,KAAK,EAAE;IACnB2H,MAAM,EAAEgC,UAAU,CAAChC,MAAM;IACzBwH,OAAO,EAAE,CACN1T,KAAK,IAAK;MACT,IAAIA,KAAK,YAAYwC,KAAK,EAAE;QAC1B,IAAI;UAAEqD,IAAI;UAAEuE,OAAO;UAAEP;QAAM,CAAC,GAAGF,UAAU,KAAK,YAAY,CAAC,mBAAmBD,aAAa,CAAC1J,KAAK,EAAE2J,UAAU,CAAC,GAAG3J,KAAK;QACtH,OAAO,CAAC,gBAAgB,EAAE6F,IAAI,EAAEuE,OAAO,EAAEP,KAAK,CAAC;MACjD;MACA,IAAI7J,KAAK,YAAY9E,iBAAiB,EAAE;QACtC,IAAI;UAAEqT,IAAI,EAAEoF,KAAK;UAAEvI,MAAM;UAAEwI;QAAW,CAAC,GAAG5T,KAAK;QAC/C,OAAO,CAAC,eAAe,EAAE2T,KAAK,EAAEvI,MAAM,EAAEwI,UAAU,CAAC;MACrD;MACA,IAAI5T,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAItE,yBAAyB,IAAIsE,KAAK,EAAE;QAC5E,OAAO,CAAC,qBAAqB,EAAEA,KAAK,CAACtE,yBAAyB,CAAC,CAAC;MAClE;IACF,CAAC,CACF;IACDmY,WAAW,EAAE,CACV7T,KAAK,IAAK;MACT,IAAI,CAACA,KAAK,EAAE;MACZ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC/B,OAAO,CACL,0BAA0B,EAC1BmJ,MAAM,CAAC2K,WAAW,CAAC3K,MAAM,CAACa,OAAO,CAAChK,KAAK,CAAC,CAAC,CAC1C;IACH,CAAC,EACD,MAAM,CAAC,qBAAqB,CAAC;EAEjC,CAAC,CAAC;AACJ;;AAEA;AACA,SAAS+T,MAAMA,CAAC9E,KAAK,EAAEhN,IAAI,EAAE;EAC3B,IAAIpD,MAAM,GAAG2O,YAAY,CAACyB,KAAK,CAACpQ,MAAM,CAAC;EACvC,IAAImV,UAAU,GAAGtG,6BAA6B,CAACuB,KAAK,CAACpQ,MAAM,EAAEoQ,KAAK,CAACnQ,MAAM,CAAC;EAC1E,IAAI6K,UAAU,GAAGF,YAAY,CAACxH,IAAI,CAAC,GAAGA,IAAI,GAAG,YAAY,CAAC;EAC1D,IAAI8O,aAAa,GAAG5U,mBAAmB,CAAC6X,UAAU,EAAE;IAClDpJ,QAAQ,EAAEqE,KAAK,CAACrE;EAClB,CAAC,CAAC;EACF,IAAIqJ,YAAY,GAAGhF,KAAK,CAACpN,KAAK,CAACE,MAAM,CAACmP,WAAW,KAAK,CAACxL,KAAK,EAAAwO,KAAA,KAAkB;IAAA,IAAhB;MAAElJ;IAAQ,CAAC,GAAAkJ,KAAA;IACvE,IAAIvK,UAAU,KAAK,MAAM,CAAC,cAAc,CAACqB,OAAO,CAACkB,MAAM,CAACiI,OAAO,EAAE;MAC/D5H,OAAO,CAAC7G,KAAK;MACX;MACA1I,oBAAoB,CAAC0I,KAAK,CAAC,IAAIA,KAAK,CAACA,KAAK,GAAGA,KAAK,CAACA,KAAK,GAAGA,KAC7D,CAAC;IACH;EACF,CAAC,CAAC;EACF,OAAO;IACL7G,MAAM;IACNmV,UAAU;IACVrK,UAAU;IACVoH,aAAa;IACbkD;EACF,CAAC;AACH;AACA,IAAIG,oBAAoB,GAAGA,CAACnF,KAAK,EAAEhN,IAAI,KAAK;EAC1C,IAAIoS,MAAM;EACV,IAAIxV,MAAM;EACV,IAAI8K,UAAU;EACd,IAAIoH,aAAa;EACjB,IAAIkD,YAAY;EAChB,OAAO,eAAeK,cAAcA,CAACtJ,OAAO,EAAEuJ,cAAc,EAAE;IAC5DF,MAAM,GAAG,OAAOpF,KAAK,KAAK,UAAU,GAAG,MAAMA,KAAK,CAAC,CAAC,GAAGA,KAAK;IAC5D,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAC/B,IAAIuF,OAAO,GAAGT,MAAM,CAACM,MAAM,EAAEpS,IAAI,CAAC;MAClCpD,MAAM,GAAG2V,OAAO,CAAC3V,MAAM;MACvB8K,UAAU,GAAG6K,OAAO,CAAC7K,UAAU;MAC/BoH,aAAa,GAAGyD,OAAO,CAACzD,aAAa;MACrCkD,YAAY,GAAGO,OAAO,CAACP,YAAY;IACrC,CAAC,MAAM,IAAI,CAACpV,MAAM,IAAI,CAAC8K,UAAU,IAAI,CAACoH,aAAa,IAAI,CAACkD,YAAY,EAAE;MACpE,IAAIO,OAAO,GAAGT,MAAM,CAACM,MAAM,EAAEpS,IAAI,CAAC;MAClCpD,MAAM,GAAG2V,OAAO,CAAC3V,MAAM;MACvB8K,UAAU,GAAG6K,OAAO,CAAC7K,UAAU;MAC/BoH,aAAa,GAAGyD,OAAO,CAACzD,aAAa;MACrCkD,YAAY,GAAGO,OAAO,CAACP,YAAY;IACrC;IACA,IAAIpJ,MAAM,GAAG,CAAC,CAAC;IACf,IAAIoG,WAAW;IACf,IAAIC,WAAW,GAAIxL,KAAK,IAAK;MAC3B,IAAIzD,IAAI,KAAK,aAAa,CAAC,mBAAmB;QAAA,IAAAwS,kBAAA,EAAAC,qBAAA;QAC5C,CAAAD,kBAAA,GAAA7H,iBAAiB,CAAC,CAAC,cAAA6H,kBAAA,gBAAAC,qBAAA,GAAnBD,kBAAA,CAAqBE,mBAAmB,cAAAD,qBAAA,eAAxCA,qBAAA,CAAAE,IAAA,CAAAH,kBAAA,EAA2C/O,KAAK,CAAC;MACnD;MACAuO,YAAY,CAACvO,KAAK,EAAE;QAClBrH,OAAO,EAAE4S,WAAW;QACpBpG,MAAM;QACNG;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAIqJ,MAAM,CAACvV,MAAM,CAAC8C,mBAAmB,EAAE;MACrC,IAAI2S,cAAc,IAAI,EAAEA,cAAc,YAAY5W,8BAA8B,CAAC,EAAE;QACjF,IAAI+H,KAAK,GAAG,IAAIlD,KAAK,CACnB,sLACF,CAAC;QACD0O,WAAW,CAACxL,KAAK,CAAC;QAClB,OAAOmP,6BAA6B,CAACnP,KAAK,EAAEiE,UAAU,CAAC;MACzD;MACAsH,WAAW,GAAGsD,cAAc,IAAI,IAAI5W,8BAA8B,CAAC,CAAC;IACtE,CAAC,MAAM;MACLsT,WAAW,GAAGsD,cAAc,IAAI,CAAC,CAAC;IACpC;IACA,IAAIjW,GAAG,GAAG,IAAIE,GAAG,CAACwM,OAAO,CAAC1M,GAAG,CAAC;IAC9B,IAAIwW,kBAAkB,GAAGT,MAAM,CAACzJ,QAAQ,IAAI,GAAG;IAC/C,IAAImK,cAAc,GAAGzW,GAAG,CAACqM,QAAQ;IACjC,IAAIlN,aAAa,CAACsX,cAAc,EAAED,kBAAkB,CAAC,KAAK,aAAa,EAAE;MACvEC,cAAc,GAAGD,kBAAkB;IACrC,CAAC,MAAM,IAAIC,cAAc,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC3CD,cAAc,GAAGA,cAAc,CAAC1X,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IACxD;IACA,IAAII,aAAa,CAACsX,cAAc,EAAED,kBAAkB,CAAC,KAAK,GAAG,IAAIC,cAAc,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7FD,cAAc,GAAGA,cAAc,CAAC1P,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C;IACA,IAAItG,SAAS,GAAG8N,kBAAkB,CAAC7B,OAAO,EAAE,yBAAyB,CAAC,KAAK,KAAK;IAChF,IAAI,CAACqJ,MAAM,CAACpU,GAAG,EAAE;MACf,IAAIgV,WAAW,GAAGpH,SAAS,CAACkH,cAAc,CAAC;MAC3C,IAAID,kBAAkB,KAAK,GAAG,EAAE;QAC9B,IAAII,YAAY,GAAGzX,aAAa,CAACwX,WAAW,EAAEH,kBAAkB,CAAC;QACjE,IAAII,YAAY,IAAI,IAAI,EAAE;UACxBjB,YAAY,CACV,IAAI/Y,iBAAiB,CACnB,GAAG,EACH,WAAW,gCAAA+N,MAAA,CACoBgM,WAAW,0DAAAhM,MAAA,CAAyD6L,kBAAkB,MACvH,CAAC,EACD;YACEzW,OAAO,EAAE4S,WAAW;YACpBpG,MAAM;YACNG;UACF,CACF,CAAC;UACD,OAAO,IAAIK,QAAQ,CAAC,WAAW,EAAE;YAC/BD,MAAM,EAAE,GAAG;YACXwI,UAAU,EAAE;UACd,CAAC,CAAC;QACJ;QACAqB,WAAW,GAAGC,YAAY;MAC5B;MACA,IAAIb,MAAM,CAACc,SAAS,CAACnP,MAAM,KAAK,CAAC,EAAE;QACjCjH,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM,IAAI,CAACsV,MAAM,CAACc,SAAS,CAACC,QAAQ,CAACH,WAAW,CAAC,IAAI,CAACZ,MAAM,CAACc,SAAS,CAACC,QAAQ,CAACH,WAAW,GAAG,GAAG,CAAC,EAAE;QACnG,IAAI3W,GAAG,CAACqM,QAAQ,CAACqK,QAAQ,CAAC,OAAO,CAAC,EAAE;UAClCf,YAAY,CACV,IAAI/Y,iBAAiB,CACnB,GAAG,EACH,WAAW,+BAAA+N,MAAA,CACmBgM,WAAW,kIAC3C,CAAC,EACD;YACE5W,OAAO,EAAE4S,WAAW;YACpBpG,MAAM;YACNG;UACF,CACF,CAAC;UACD,OAAO,IAAIK,QAAQ,CAAC,WAAW,EAAE;YAC/BD,MAAM,EAAE,GAAG;YACXwI,UAAU,EAAE;UACd,CAAC,CAAC;QACJ,CAAC,MAAM;UACL7U,SAAS,GAAG,IAAI;QAClB;MACF;IACF;IACA,IAAIsW,WAAW,GAAG9Y,eAAe,CAC/B8X,MAAM,CAACnU,cAAc,CAACgC,YAAY,EAClC4S,kBACF,CAAC;IACD,IAAIxW,GAAG,CAACqM,QAAQ,KAAK0K,WAAW,EAAE;MAChC,IAAI;QACF,IAAIC,GAAG,GAAG,MAAMC,qBAAqB,CAAClB,MAAM,EAAExV,MAAM,EAAEP,GAAG,CAAC;QAC1D,OAAOgX,GAAG;MACZ,CAAC,CAAC,OAAOlI,CAAC,EAAE;QACV8D,WAAW,CAAC9D,CAAC,CAAC;QACd,OAAO,IAAI/B,QAAQ,CAAC,sBAAsB,EAAE;UAAED,MAAM,EAAE;QAAI,CAAC,CAAC;MAC9D;IACF;IACA,IAAIhM,OAAO,GAAGsL,iBAAiB,CAAC7L,MAAM,EAAEkW,cAAc,EAAEV,MAAM,CAACzJ,QAAQ,CAAC;IACxE,IAAIxL,OAAO,IAAIA,OAAO,CAAC4G,MAAM,GAAG,CAAC,EAAE;MACjCmD,MAAM,CAACgB,MAAM,CAACU,MAAM,EAAEzL,OAAO,CAAC,CAAC,CAAC,CAACyL,MAAM,CAAC;IAC1C;IACA,IAAI2K,QAAQ;IACZ,IAAIlX,GAAG,CAACqM,QAAQ,CAACqK,QAAQ,CAAC,OAAO,CAAC,EAAE;MAClC,IAAIhE,UAAU,GAAG,IAAIxS,GAAG,CAACwM,OAAO,CAAC1M,GAAG,CAAC;MACrC0S,UAAU,CAACrG,QAAQ,GAAGoK,cAAc;MACpC,IAAIU,kBAAkB,GAAG/K,iBAAiB,CACxC7L,MAAM,EACNmS,UAAU,CAACrG,QAAQ,EACnB0J,MAAM,CAACzJ,QACT,CAAC;MACD4K,QAAQ,GAAG,MAAME,wBAAwB,CACvC/L,UAAU,EACV0K,MAAM,EACNtD,aAAa,EACb/F,OAAO,EACPgG,UAAU,EACVC,WAAW,EACXC,WACF,CAAC;MACD,IAAIrU,kBAAkB,CAAC2Y,QAAQ,CAAC,EAAE;QAChCA,QAAQ,GAAG1C,mCAAmC,CAC5C0C,QAAQ,EACRxK,OAAO,EACPqJ,MAAM,EACN1K,UACF,CAAC;MACH;MACA,IAAI0K,MAAM,CAACxS,KAAK,CAACE,MAAM,CAAC4T,iBAAiB,EAAE;QACzCH,QAAQ,GAAG,MAAMnB,MAAM,CAACxS,KAAK,CAACE,MAAM,CAAC4T,iBAAiB,CAACH,QAAQ,EAAE;UAC/DnX,OAAO,EAAE4S,WAAW;UACpBpG,MAAM,EAAE4K,kBAAkB,GAAGA,kBAAkB,CAAC,CAAC,CAAC,CAAC5K,MAAM,GAAG,CAAC,CAAC;UAC9DG;QACF,CAAC,CAAC;QACF,IAAInO,kBAAkB,CAAC2Y,QAAQ,CAAC,EAAE;UAChCA,QAAQ,GAAG1C,mCAAmC,CAC5C0C,QAAQ,EACRxK,OAAO,EACPqJ,MAAM,EACN1K,UACF,CAAC;QACH;MACF;IACF,CAAC,MAAM,IAAI,CAAC5K,SAAS,IAAIK,OAAO,IAAIA,OAAO,CAACA,OAAO,CAAC4G,MAAM,GAAG,CAAC,CAAC,CAAC1G,KAAK,CAACyC,MAAM,CAAC8B,OAAO,IAAI,IAAI,IAAIzE,OAAO,CAACA,OAAO,CAAC4G,MAAM,GAAG,CAAC,CAAC,CAAC1G,KAAK,CAACyC,MAAM,CAACc,aAAa,IAAI,IAAI,EAAE;MAC9J2S,QAAQ,GAAG,MAAMI,qBAAqB,CACpCjM,UAAU,EACV0K,MAAM,EACNtD,aAAa,EACb3R,OAAO,CAACiG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC/F,KAAK,CAACC,EAAE,EAC7ByL,OAAO,EACPiG,WAAW,EACXC,WACF,CAAC;IACH,CAAC,MAAM;MAAA,IAAA2E,mBAAA;MACL,IAAI;QAAElL;MAAS,CAAC,GAAGrM,GAAG;MACtB,IAAIK,WAAW,GAAG,KAAK,CAAC;MACxB,IAAI0V,MAAM,CAACyB,uBAAuB,EAAE;QAClCnX,WAAW,GAAG,MAAM0V,MAAM,CAACyB,uBAAuB,CAAC;UAAEnL;QAAS,CAAC,CAAC;MAClE,CAAC,MAAM,IAAI1I,IAAI,KAAK,aAAa,CAAC,sBAAA4T,mBAAA,GAAqBjJ,iBAAiB,CAAC,CAAC,cAAAiJ,mBAAA,eAAnBA,mBAAA,CAAqBE,cAAc,EAAE;QAAA,IAAAC,mBAAA,EAAAC,qBAAA;QAC1FtX,WAAW,GAAG,QAAAqX,mBAAA,GAAMpJ,iBAAiB,CAAC,CAAC,cAAAoJ,mBAAA,gBAAAC,qBAAA,GAAnBD,mBAAA,CAAqBD,cAAc,cAAAE,qBAAA,uBAAnCA,qBAAA,CAAArB,IAAA,CAAAoB,mBAAA,EAAsCrL,QAAQ,CAAC;MACrE;MACA6K,QAAQ,GAAG,MAAMU,qBAAqB,CACpCvM,UAAU,EACV0K,MAAM,EACNtD,aAAa,EACb/F,OAAO,EACPiG,WAAW,EACXC,WAAW,EACXnS,SAAS,EACTJ,WACF,CAAC;IACH;IACA,IAAIqM,OAAO,CAACe,MAAM,KAAK,MAAM,EAAE;MAC7B,OAAO,IAAIV,QAAQ,CAAC,IAAI,EAAE;QACxBY,OAAO,EAAEuJ,QAAQ,CAACvJ,OAAO;QACzBb,MAAM,EAAEoK,QAAQ,CAACpK,MAAM;QACvBwI,UAAU,EAAE4B,QAAQ,CAAC5B;MACvB,CAAC,CAAC;IACJ;IACA,OAAO4B,QAAQ;EACjB,CAAC;AACH,CAAC;AACD,eAAeD,qBAAqBA,CAACtG,KAAK,EAAEpQ,MAAM,EAAEP,GAAG,EAAE;EACvD,IAAI2Q,KAAK,CAACkH,MAAM,CAACnU,OAAO,KAAK1D,GAAG,CAACiN,YAAY,CAAC4B,GAAG,CAAC,SAAS,CAAC,EAAE;IAC5D,OAAO,IAAI9B,QAAQ,CAAC,IAAI,EAAE;MACxBD,MAAM,EAAE,GAAG;MACXa,OAAO,EAAE;QACP,yBAAyB,EAAE;MAC7B;IACF,CAAC,CAAC;EACJ;EACA,IAAImK,OAAO,GAAG,CAAC,CAAC;EAChB,IAAI9X,GAAG,CAACiN,YAAY,CAACqF,GAAG,CAAC,GAAG,CAAC,EAAE;IAC7B,IAAIyF,KAAK,GAAG,eAAgB,IAAI3F,GAAG,CAAC,CAAC;IACrCpS,GAAG,CAACiN,YAAY,CAACC,MAAM,CAAC,GAAG,CAAC,CAAC+B,OAAO,CAAE7K,IAAI,IAAK;MAC7C,IAAI,CAACA,IAAI,CAAC4T,UAAU,CAAC,GAAG,CAAC,EAAE;QACzB5T,IAAI,OAAAuG,MAAA,CAAOvG,IAAI,CAAE;MACnB;MACA,IAAI6T,QAAQ,GAAG7T,IAAI,CAAC0P,KAAK,CAAC,GAAG,CAAC,CAAC/M,KAAK,CAAC,CAAC,CAAC;MACvCkR,QAAQ,CAAChJ,OAAO,CAAC,CAACiJ,CAAC,EAAEvQ,CAAC,KAAK;QACzB,IAAIwQ,WAAW,GAAGF,QAAQ,CAAClR,KAAK,CAAC,CAAC,EAAEY,CAAC,GAAG,CAAC,CAAC,CAACyQ,IAAI,CAAC,GAAG,CAAC;QACpDL,KAAK,CAACM,GAAG,KAAA1N,MAAA,CAAKwN,WAAW,CAAE,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,KAAK,IAAI/T,IAAI,IAAI2T,KAAK,EAAE;MACtB,IAAIjX,OAAO,GAAGsL,iBAAiB,CAAC7L,MAAM,EAAE6D,IAAI,EAAEuM,KAAK,CAACrE,QAAQ,CAAC;MAC7D,IAAIxL,OAAO,EAAE;QACX,KAAK,IAAID,KAAK,IAAIC,OAAO,EAAE;UACzB,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;UAC5B,IAAID,KAAK,GAAG2P,KAAK,CAACkH,MAAM,CAACtX,MAAM,CAACQ,OAAO,CAAC;UACxC,IAAIC,KAAK,EAAE;YACT8W,OAAO,CAAC/W,OAAO,CAAC,GAAGC,KAAK;UAC1B;QACF;MACF;IACF;IACA,OAAO+L,QAAQ,CAACuL,IAAI,CAACR,OAAO,EAAE;MAC5BnK,OAAO,EAAE;QACP,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;EACJ;EACA,OAAO,IAAIZ,QAAQ,CAAC,iBAAiB,EAAE;IAAED,MAAM,EAAE;EAAI,CAAC,CAAC;AACzD;AACA,eAAesK,wBAAwBA,CAAC/L,UAAU,EAAEsF,KAAK,EAAE8B,aAAa,EAAE/F,OAAO,EAAEgG,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACvH,IAAIsE,QAAQ,GAAGxK,OAAO,CAACe,MAAM,KAAK,KAAK,GAAG,MAAM+E,iBAAiB,CAC/D7B,KAAK,EACLtF,UAAU,EACVoH,aAAa,EACb/F,OAAO,EACPgG,UAAU,EACVC,WAAW,EACXC,WACF,CAAC,GAAG,MAAMe,kBAAkB,CAC1BhD,KAAK,EACLtF,UAAU,EACVoH,aAAa,EACb/F,OAAO,EACPgG,UAAU,EACVC,WAAW,EACXC,WACF,CAAC;EACD,OAAOsE,QAAQ;AACjB;AACA,eAAeU,qBAAqBA,CAACvM,UAAU,EAAEsF,KAAK,EAAE8B,aAAa,EAAE/F,OAAO,EAAEiG,WAAW,EAAEC,WAAW,EAAEnS,SAAS,EAAEJ,WAAW,EAAE;EAChI,IAAI;IACF,IAAI6J,MAAM,GAAG,MAAMuI,aAAa,CAACK,KAAK,CAACpG,OAAO,EAAE;MAC9CqG,cAAc,EAAEJ,WAAW;MAC3BO,mCAAmC,EAAEvC,KAAK,CAACnQ,MAAM,CAAC8C,mBAAmB,GAAG,MAAOwP,KAAK,IAAK;QACvF,IAAI;UACF,IAAIK,WAAW,GAAG,MAAML,KAAK,CAACpG,OAAO,CAAC;UACtC,IAAI,CAACjO,UAAU,CAAC0U,WAAW,CAAC,EAAE;YAC5BA,WAAW,GAAG,MAAMoF,UAAU,CAACpF,WAAW,EAAE1S,SAAS,CAAC;UACxD;UACA,OAAO0S,WAAW;QACpB,CAAC,CAAC,OAAO/L,KAAK,EAAE;UACdwL,WAAW,CAACxL,KAAK,CAAC;UAClB,OAAO,IAAI2F,QAAQ,CAAC,IAAI,EAAE;YAAED,MAAM,EAAE;UAAI,CAAC,CAAC;QAC5C;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,IAAI,CAACrO,UAAU,CAACyL,MAAM,CAAC,EAAE;MACvBA,MAAM,GAAG,MAAMqO,UAAU,CAACrO,MAAM,EAAEzJ,SAAS,CAAC;IAC9C;IACA,OAAOyJ,MAAM;EACf,CAAC,CAAC,OAAO9C,KAAK,EAAE;IACdwL,WAAW,CAACxL,KAAK,CAAC;IAClB,OAAO,IAAI2F,QAAQ,CAAC,IAAI,EAAE;MAAED,MAAM,EAAE;IAAI,CAAC,CAAC;EAC5C;EACA,eAAeyL,UAAUA,CAACxY,OAAO,EAAEyY,UAAU,EAAE;IAC7C,IAAI7K,OAAO,GAAG+C,kBAAkB,CAAC3Q,OAAO,EAAE4Q,KAAK,CAAC;IAChD,IAAI4B,2BAA2B,CAACD,GAAG,CAACvS,OAAO,CAACyT,UAAU,CAAC,EAAE;MACvD,OAAO,IAAIzG,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAE/M,OAAO,CAACyT,UAAU;QAAE7F;MAAQ,CAAC,CAAC;IACpE;IACA,IAAI5N,OAAO,CAAC0L,MAAM,EAAE;MAClBZ,MAAM,CAACmE,MAAM,CAACjP,OAAO,CAAC0L,MAAM,CAAC,CAACwD,OAAO,CAAEwE,GAAG,IAAK;QAC7C,IAAI,CAAC/U,oBAAoB,CAAC+U,GAAG,CAAC,IAAIA,GAAG,CAACrM,KAAK,EAAE;UAC3CwL,WAAW,CAACa,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACF1T,OAAO,CAAC0L,MAAM,GAAGD,cAAc,CAACzL,OAAO,CAAC0L,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAIrJ,KAAK,GAAG;MACVrB,UAAU,EAAEZ,OAAO,CAACY,UAAU;MAC9ByQ,UAAU,EAAErR,OAAO,CAACqR,UAAU;MAC9B3F,MAAM,EAAEM,eAAe,CAAChM,OAAO,CAAC0L,MAAM,EAAEJ,UAAU;IACpD,CAAC;IACD,IAAIoN,iBAAiB,GAAG;MACtBnM,QAAQ,EAAEqE,KAAK,CAACrE,QAAQ;MACxB9L,MAAM,EAAEmQ,KAAK,CAACnQ,MAAM;MACpBoB,cAAc,EAAE+O,KAAK,CAAC/O,cAAc;MACpCD,GAAG,EAAEgP,KAAK,CAAChP,GAAG;MACdlB,SAAS,EAAE+X;IACb,CAAC;IACD,IAAIE,YAAY,GAAG;MACjBvY,QAAQ,EAAEwQ,KAAK,CAACkH,MAAM;MACtBzX,YAAY,EAAEwK,uBAAuB,CAAC+F,KAAK,CAACpQ,MAAM,CAAC;MACnDG,oBAAoB,EAAEX,OAAO;MAC7BM,WAAW;MACXC,mBAAmB,EAAEiQ,yBAAyB,CAAA3P,aAAA,CAAAA,aAAA,KACzC6X,iBAAiB;QACpBpY;MAAW,EACZ,CAAC;MACF6B,mBAAmB,EAAEoS,oBAAoB,CACvCtS,KAAK,EACL0K,OAAO,CAACkB,MAAM,EACd+C,KAAK,CAACpN,KAAK,CAACE,MAAM,CAAC8Q,aAAa,EAChClJ,UACF,CAAC;MACDvJ,UAAU,EAAE,CAAC,CAAC;MACdtB,MAAM,EAAEmQ,KAAK,CAACnQ,MAAM;MACpBmB,GAAG,EAAEgP,KAAK,CAAChP,GAAG;MACdC,cAAc,EAAE+O,KAAK,CAAC/O,cAAc;MACpCnB,SAAS,EAAE+X,UAAU;MACrB3W,cAAc,EAAG4R,GAAG,IAAK5R,cAAc,CAAC4R,GAAG,EAAEpI,UAAU;IACzD,CAAC;IACD,IAAIsN,6BAA6B,GAAGhI,KAAK,CAACpN,KAAK,CAACE,MAAM,CAAC8B,OAAO;IAC9D,IAAI;MACF,OAAO,MAAMoT,6BAA6B,CACxCjM,OAAO,EACP3M,OAAO,CAACyT,UAAU,EAClB7F,OAAO,EACP+K,YAAY,EACZ/F,WACF,CAAC;IACH,CAAC,CAAC,OAAOvL,KAAK,EAAE;MACdwL,WAAW,CAACxL,KAAK,CAAC;MAClB,IAAIwR,oBAAoB,GAAGxR,KAAK;MAChC,IAAI3I,UAAU,CAAC2I,KAAK,CAAC,EAAE;QACrB,IAAI;UACF,IAAInB,KAAK,GAAG,MAAM4S,cAAc,CAACzR,KAAK,CAAC;UACvCwR,oBAAoB,GAAG,IAAIhc,iBAAiB,CAC1CwK,KAAK,CAAC0F,MAAM,EACZ1F,KAAK,CAACkO,UAAU,EAChBrP,KACF,CAAC;QACH,CAAC,CAAC,OAAO6I,CAAC,EAAE,CACZ;MACF;MACA/O,OAAO,GAAG5B,yBAAyB,CACjCsU,aAAa,CAACiD,UAAU,EACxB3V,OAAO,EACP6Y,oBACF,CAAC;MACD,IAAI7Y,OAAO,CAAC0L,MAAM,EAAE;QAClB1L,OAAO,CAAC0L,MAAM,GAAGD,cAAc,CAACzL,OAAO,CAAC0L,MAAM,EAAEJ,UAAU,CAAC;MAC7D;MACA,IAAIyN,MAAM,GAAG;QACXnY,UAAU,EAAEZ,OAAO,CAACY,UAAU;QAC9ByQ,UAAU,EAAErR,OAAO,CAACqR,UAAU;QAC9B3F,MAAM,EAAEM,eAAe,CAAChM,OAAO,CAAC0L,MAAM,EAAEJ,UAAU;MACpD,CAAC;MACDqN,YAAY,GAAA9X,aAAA,CAAAA,aAAA,KACP8X,YAAY;QACfhY,oBAAoB,EAAEX,OAAO;QAC7BO,mBAAmB,EAAEiQ,yBAAyB,CAACkI,iBAAiB,CAAC;QACjEvW,mBAAmB,EAAEoS,oBAAoB,CACvCwE,MAAM,EACNpM,OAAO,CAACkB,MAAM,EACd+C,KAAK,CAACpN,KAAK,CAACE,MAAM,CAAC8Q,aAAa,EAChClJ,UACF,CAAC;QACDvJ,UAAU,EAAE,CAAC;MAAC,EACf;MACD,IAAI;QACF,OAAO,MAAM6W,6BAA6B,CACxCjM,OAAO,EACP3M,OAAO,CAACyT,UAAU,EAClB7F,OAAO,EACP+K,YAAY,EACZ/F,WACF,CAAC;MACH,CAAC,CAAC,OAAOoG,MAAM,EAAE;QACfnG,WAAW,CAACmG,MAAM,CAAC;QACnB,OAAOxC,6BAA6B,CAACwC,MAAM,EAAE1N,UAAU,CAAC;MAC1D;IACF;EACF;AACF;AACA,eAAeiM,qBAAqBA,CAACjM,UAAU,EAAEsF,KAAK,EAAE8B,aAAa,EAAE1R,OAAO,EAAE2L,OAAO,EAAEiG,WAAW,EAAEC,WAAW,EAAE;EACjH,IAAI;IACF,IAAI1I,MAAM,GAAG,MAAMuI,aAAa,CAACuG,UAAU,CAACtM,OAAO,EAAE;MACnD3L,OAAO;MACPgS,cAAc,EAAEJ,WAAW;MAC3BO,mCAAmC,EAAEvC,KAAK,CAACnQ,MAAM,CAAC8C,mBAAmB,GAAG,MAAO0V,UAAU,IAAK;QAC5F,IAAI;UACF,IAAI7F,WAAW,GAAG,MAAM6F,UAAU,CAACtM,OAAO,CAAC;UAC3C,OAAOuM,sBAAsB,CAAC9F,WAAW,CAAC;QAC5C,CAAC,CAAC,OAAO/L,KAAK,EAAE;UACd,OAAO8R,qBAAqB,CAAC9R,KAAK,CAAC;QACrC;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,OAAO6R,sBAAsB,CAAC/O,MAAM,CAAC;EACvC,CAAC,CAAC,OAAO9C,KAAK,EAAE;IACd,OAAO8R,qBAAqB,CAAC9R,KAAK,CAAC;EACrC;EACA,SAAS6R,sBAAsBA,CAAC/O,MAAM,EAAE;IACtC,IAAIzL,UAAU,CAACyL,MAAM,CAAC,EAAE;MACtB,OAAOA,MAAM;IACf;IACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAO,IAAI6C,QAAQ,CAAC7C,MAAM,CAAC;IAC7B;IACA,OAAO6C,QAAQ,CAACuL,IAAI,CAACpO,MAAM,CAAC;EAC9B;EACA,SAASgP,qBAAqBA,CAAC9R,KAAK,EAAE;IACpC,IAAI3I,UAAU,CAAC2I,KAAK,CAAC,EAAE;MACrBA,KAAK,CAACuG,OAAO,CAAC0G,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;MACzC,OAAOjN,KAAK;IACd;IACA,IAAI1I,oBAAoB,CAAC0I,KAAK,CAAC,EAAE;MAC/BwL,WAAW,CAACxL,KAAK,CAAC;MAClB,OAAO+R,mBAAmB,CAAC/R,KAAK,EAAEiE,UAAU,CAAC;IAC/C;IACA,IAAIjE,KAAK,YAAYlD,KAAK,IAAIkD,KAAK,CAAC0E,OAAO,KAAK,qCAAqC,EAAE;MACrF,IAAIsN,QAAQ,GAAG,IAAIlV,KAAK,CACtB,gEACF,CAAC;MACD0O,WAAW,CAACwG,QAAQ,CAAC;MACrB,OAAO7C,6BAA6B,CAAC6C,QAAQ,EAAE/N,UAAU,CAAC;IAC5D;IACAuH,WAAW,CAACxL,KAAK,CAAC;IAClB,OAAOmP,6BAA6B,CAACnP,KAAK,EAAEiE,UAAU,CAAC;EACzD;AACF;AACA,SAAS8N,mBAAmBA,CAACE,aAAa,EAAEhO,UAAU,EAAE;EACtD,OAAO0B,QAAQ,CAACuL,IAAI,CAClBzW,cAAc;EACZ;EACAwX,aAAa,CAACjS,KAAK,IAAI,IAAIlD,KAAK,CAAC,yBAAyB,CAAC,EAC3DmH,UACF,CAAC,EACD;IACEyB,MAAM,EAAEuM,aAAa,CAACvM,MAAM;IAC5BwI,UAAU,EAAE+D,aAAa,CAAC/D,UAAU;IACpC3H,OAAO,EAAE;MACP,eAAe,EAAE;IACnB;EACF,CACF,CAAC;AACH;AACA,SAAS4I,6BAA6BA,CAACnP,KAAK,EAAEiE,UAAU,EAAE;EACxD,IAAIS,OAAO,GAAG,yBAAyB;EACvC,IAAIT,UAAU,KAAK,YAAY,CAAC,kBAAkB;IAChDS,OAAO,WAAAnB,MAAA,CAETlE,MAAM,CAACW,KAAK,CAAC,CAAE;EACf;EACA,OAAO,IAAI2F,QAAQ,CAACjB,OAAO,EAAE;IAC3BgB,MAAM,EAAE,GAAG;IACXa,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ;AACA,SAASkL,cAAcA,CAAC3B,QAAQ,EAAE;EAChC,IAAIoC,WAAW,GAAGpC,QAAQ,CAACvJ,OAAO,CAACkB,GAAG,CAAC,cAAc,CAAC;EACtD,OAAOyK,WAAW,IAAI,uBAAuB,CAACC,IAAI,CAACD,WAAW,CAAC,GAAGpC,QAAQ,CAACxJ,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGwJ,QAAQ,CAACoB,IAAI,CAAC,CAAC,GAAGpB,QAAQ,CAACsC,IAAI,CAAC,CAAC;AACpI;;AAEA;AACA,SAASC,KAAKA,CAAClS,IAAI,EAAE;EACnB,kBAAAoD,MAAA,CAAkBpD,IAAI;AACxB;AACA,IAAImS,aAAa,GAAG,SAAAA,CAAA,EAA+B;EAAA,IAA9BC,WAAW,GAAA5R,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAAA,IAAE9G,EAAE,GAAA8G,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EAC5C,IAAI9D,GAAG,GAAG,IAAI2V,GAAG,CAAC/O,MAAM,CAACa,OAAO,CAACiO,WAAW,CAAC,CAAC;EAC9C,OAAO;IACL,IAAI1Y,EAAEA,CAAA,EAAG;MACP,OAAOA,EAAE;IACX,CAAC;IACD,IAAIgP,IAAIA,CAAA,EAAG;MACT,OAAOpF,MAAM,CAAC2K,WAAW,CAACvR,GAAG,CAAC;IAChC,CAAC;IACDqO,GAAGA,CAAC/K,IAAI,EAAE;MACR,OAAOtD,GAAG,CAACqO,GAAG,CAAC/K,IAAI,CAAC,IAAItD,GAAG,CAACqO,GAAG,CAACmH,KAAK,CAAClS,IAAI,CAAC,CAAC;IAC9C,CAAC;IACDsH,GAAGA,CAACtH,IAAI,EAAE;MACR,IAAItD,GAAG,CAACqO,GAAG,CAAC/K,IAAI,CAAC,EAAE,OAAOtD,GAAG,CAAC4K,GAAG,CAACtH,IAAI,CAAC;MACvC,IAAIsS,SAAS,GAAGJ,KAAK,CAAClS,IAAI,CAAC;MAC3B,IAAItD,GAAG,CAACqO,GAAG,CAACuH,SAAS,CAAC,EAAE;QACtB,IAAInY,KAAK,GAAGuC,GAAG,CAAC4K,GAAG,CAACgL,SAAS,CAAC;QAC9B5V,GAAG,CAACkJ,MAAM,CAAC0M,SAAS,CAAC;QACrB,OAAOnY,KAAK;MACd;MACA,OAAO,KAAK,CAAC;IACf,CAAC;IACD2S,GAAGA,CAAC9M,IAAI,EAAE7F,KAAK,EAAE;MACfuC,GAAG,CAACoQ,GAAG,CAAC9M,IAAI,EAAE7F,KAAK,CAAC;IACtB,CAAC;IACD+X,KAAKA,CAAClS,IAAI,EAAE7F,KAAK,EAAE;MACjBuC,GAAG,CAACoQ,GAAG,CAACoF,KAAK,CAAClS,IAAI,CAAC,EAAE7F,KAAK,CAAC;IAC7B,CAAC;IACDoY,KAAKA,CAACvS,IAAI,EAAE;MACVtD,GAAG,CAACkJ,MAAM,CAAC5F,IAAI,CAAC;IAClB;EACF,CAAC;AACH,CAAC;AACD,IAAIwS,SAAS,GAAI1Q,MAAM,IAAK;EAC1B,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACpI,EAAE,KAAK,QAAQ,IAAI,OAAOoI,MAAM,CAAC4G,IAAI,KAAK,WAAW,IAAI,OAAO5G,MAAM,CAACiJ,GAAG,KAAK,UAAU,IAAI,OAAOjJ,MAAM,CAACwF,GAAG,KAAK,UAAU,IAAI,OAAOxF,MAAM,CAACgL,GAAG,KAAK,UAAU,IAAI,OAAOhL,MAAM,CAACoQ,KAAK,KAAK,UAAU,IAAI,OAAOpQ,MAAM,CAACyQ,KAAK,KAAK,UAAU;AACtR,CAAC;AACD,SAASE,oBAAoBA,CAAAC,KAAA,EAM1B;EAAA,IAN2B;IAC5BpT,MAAM,EAAEqT,SAAS;IACjBC,UAAU;IACVC,QAAQ;IACRC,UAAU;IACVC;EACF,CAAC,GAAAL,KAAA;EACC,IAAIpT,MAAM,GAAGuC,QAAQ,CAAC8Q,SAAS,CAAC,GAAGA,SAAS,GAAGrS,YAAY,CAAC,CAAAqS,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE3S,IAAI,KAAI,WAAW,EAAE2S,SAAS,CAAC;EACtGK,iCAAiC,CAAC1T,MAAM,CAAC;EACzC,OAAO;IACL,MAAM2T,UAAUA,CAAC3R,YAAY,EAAET,OAAO,EAAE;MACtC,IAAInH,EAAE,GAAG4H,YAAY,KAAI,MAAMhC,MAAM,CAAClB,KAAK,CAACkD,YAAY,EAAET,OAAO,CAAC;MAClE,IAAInC,KAAK,GAAGhF,EAAE,KAAI,MAAMmZ,QAAQ,CAACnZ,EAAE,CAAC;MACpC,OAAOyY,aAAa,CAACzT,KAAK,IAAI,CAAC,CAAC,EAAEhF,EAAE,IAAI,EAAE,CAAC;IAC7C,CAAC;IACD,MAAMwZ,aAAaA,CAACC,OAAO,EAAEtS,OAAO,EAAE;MACpC,IAAI;QAAEnH,EAAE;QAAEgP,IAAI,EAAEhK;MAAM,CAAC,GAAGyU,OAAO;MACjC,IAAIlS,OAAO,GAAG,CAAAJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,MAAM,KAAI,IAAI,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGR,OAAO,CAACM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAAN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,OAAO,KAAI,IAAI,GAAGJ,OAAO,CAACI,OAAO,GAAG3B,MAAM,CAAC2B,OAAO;MACjJ,IAAIvH,EAAE,EAAE;QACN,MAAMoZ,UAAU,CAACpZ,EAAE,EAAEgF,KAAK,EAAEuC,OAAO,CAAC;MACtC,CAAC,MAAM;QACLvH,EAAE,GAAG,MAAMkZ,UAAU,CAAClU,KAAK,EAAEuC,OAAO,CAAC;MACvC;MACA,OAAO3B,MAAM,CAACjB,SAAS,CAAC3E,EAAE,EAAEmH,OAAO,CAAC;IACtC,CAAC;IACD,MAAMuS,cAAcA,CAACD,OAAO,EAAEtS,OAAO,EAAE;MACrC,MAAMkS,UAAU,CAACI,OAAO,CAACzZ,EAAE,CAAC;MAC5B,OAAO4F,MAAM,CAACjB,SAAS,CAAC,EAAE,EAAAhF,aAAA,CAAAA,aAAA,KACrBwH,OAAO;QACVM,MAAM,EAAE,KAAK,CAAC;QACdF,OAAO,EAAE,eAAgB,IAAIG,IAAI,CAAC,CAAC;MAAC,EACrC,CAAC;IACJ;EACF,CAAC;AACH;AACA,SAAS4R,iCAAiCA,CAAC1T,MAAM,EAAE;EACjDrH,QAAQ,CACNqH,MAAM,CAAC4B,QAAQ,WAAAkC,MAAA,CACP9D,MAAM,CAACU,IAAI,iPACrB,CAAC;AACH;;AAEA;AACA,SAASqT,0BAA0BA,CAAA,EAA6B;EAAA,IAA5B;IAAE/T,MAAM,EAAEqT;EAAU,CAAC,GAAAnS,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAC5D,IAAIlB,MAAM,GAAGuC,QAAQ,CAAC8Q,SAAS,CAAC,GAAGA,SAAS,GAAGrS,YAAY,CAAC,CAAAqS,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE3S,IAAI,KAAI,WAAW,EAAE2S,SAAS,CAAC;EACtGK,iCAAiC,CAAC1T,MAAM,CAAC;EACzC,OAAO;IACL,MAAM2T,UAAUA,CAAC3R,YAAY,EAAET,OAAO,EAAE;MACtC,OAAOsR,aAAa,CAClB7Q,YAAY,KAAI,MAAMhC,MAAM,CAAClB,KAAK,CAACkD,YAAY,EAAET,OAAO,CAAC,KAAI,CAAC,CAChE,CAAC;IACH,CAAC;IACD,MAAMqS,aAAaA,CAACC,OAAO,EAAEtS,OAAO,EAAE;MACpC,IAAIyS,gBAAgB,GAAG,MAAMhU,MAAM,CAACjB,SAAS,CAAC8U,OAAO,CAACzK,IAAI,EAAE7H,OAAO,CAAC;MACpE,IAAIyS,gBAAgB,CAACnT,MAAM,GAAG,IAAI,EAAE;QAClC,MAAM,IAAIxD,KAAK,CACb,qDAAqD,GAAG2W,gBAAgB,CAACnT,MAC3E,CAAC;MACH;MACA,OAAOmT,gBAAgB;IACzB,CAAC;IACD,MAAMF,cAAcA,CAACG,QAAQ,EAAE1S,OAAO,EAAE;MACtC,OAAOvB,MAAM,CAACjB,SAAS,CAAC,EAAE,EAAAhF,aAAA,CAAAA,aAAA,KACrBwH,OAAO;QACVM,MAAM,EAAE,KAAK,CAAC;QACdF,OAAO,EAAE,eAAgB,IAAIG,IAAI,CAAC,CAAC;MAAC,EACrC,CAAC;IACJ;EACF,CAAC;AACH;;AAEA;AACA,SAASoS,0BAA0BA,CAAA,EAAkB;EAAA,IAAjB;IAAElU;EAAO,CAAC,GAAAkB,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EACjD,IAAI9D,GAAG,GAAG,eAAgB,IAAI2V,GAAG,CAAC,CAAC;EACnC,OAAOI,oBAAoB,CAAC;IAC1BnT,MAAM;IACN,MAAMsT,UAAUA,CAAClU,KAAK,EAAEuC,OAAO,EAAE;MAC/B,IAAIvH,EAAE,GAAG+Z,IAAI,CAACC,MAAM,CAAC,CAAC,CAAChR,QAAQ,CAAC,EAAE,CAAC,CAACiR,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;MACpDjX,GAAG,CAACoQ,GAAG,CAACpT,EAAE,EAAE;QAAEgP,IAAI,EAAEhK,KAAK;QAAEuC;MAAQ,CAAC,CAAC;MACrC,OAAOvH,EAAE;IACX,CAAC;IACD,MAAMmZ,QAAQA,CAACnZ,EAAE,EAAE;MACjB,IAAIgD,GAAG,CAACqO,GAAG,CAACrR,EAAE,CAAC,EAAE;QACf,IAAI;UAAEgP,IAAI,EAAEhK,KAAK;UAAEuC;QAAQ,CAAC,GAAGvE,GAAG,CAAC4K,GAAG,CAAC5N,EAAE,CAAC;QAC1C,IAAI,CAACuH,OAAO,IAAIA,OAAO,GAAG,eAAgB,IAAIG,IAAI,CAAC,CAAC,EAAE;UACpD,OAAO1C,KAAK;QACd;QACA,IAAIuC,OAAO,EAAEvE,GAAG,CAACkJ,MAAM,CAAClM,EAAE,CAAC;MAC7B;MACA,OAAO,IAAI;IACb,CAAC;IACD,MAAMoZ,UAAUA,CAACpZ,EAAE,EAAEgF,KAAK,EAAEuC,OAAO,EAAE;MACnCvE,GAAG,CAACoQ,GAAG,CAACpT,EAAE,EAAE;QAAEgP,IAAI,EAAEhK,KAAK;QAAEuC;MAAQ,CAAC,CAAC;IACvC,CAAC;IACD,MAAM8R,UAAUA,CAACrZ,EAAE,EAAE;MACnBgD,GAAG,CAACkJ,MAAM,CAAClM,EAAE,CAAC;IAChB;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,SAAS8M,IAAIA,CAAC3J,IAAI,EAAW;EAC3B,IAAImI,MAAM,GAAAxE,SAAA,CAAAL,MAAA,QAAAM,SAAA,GAAAD,SAAA,GAAU;EACpB,OAAO3D,IAAI,CAAC0P,KAAK,CAAC,GAAG,CAAC,CAAC7P,GAAG,CAAEkX,OAAO,IAAK;IACtC,IAAIA,OAAO,KAAK,GAAG,EAAE;MACnB,OAAO5O,MAAM,GAAGA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACtC;IACA,MAAM1L,KAAK,GAAGsa,OAAO,CAACta,KAAK,CAAC,iBAAiB,CAAC;IAC9C,IAAI,CAACA,KAAK,EAAE,OAAOsa,OAAO;IAC1B,MAAMC,KAAK,GAAGva,KAAK,CAAC,CAAC,CAAC;IACtB,MAAMa,KAAK,GAAG6K,MAAM,GAAGA,MAAM,CAAC6O,KAAK,CAAC,GAAG,KAAK,CAAC;IAC7C,MAAMC,UAAU,GAAGxa,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC;IACtC,IAAIwa,UAAU,IAAI3Z,KAAK,KAAK,KAAK,CAAC,EAAE;MAClC,MAAMwC,KAAK,UAAAyG,MAAA,CACAvG,IAAI,wBAAAuG,MAAA,CAAqByQ,KAAK,8BACzC,CAAC;IACH;IACA,OAAO1Z,KAAK;EACd,CAAC,CAAC,CAACwS,MAAM,CAAEiH,OAAO,IAAKA,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC/C,IAAI,CAAC,GAAG,CAAC;AACtD;;AAEA;AACA,OAAO,KAAKkD,MAAM,MAAM,OAAO;AAC/B,OAAO,KAAKC,QAAQ,MAAM,WAAW;;AAErC;AACA,SAASC,gBAAgBA,CAACxZ,KAAK,EAAEzB,MAAM,EAAEkb,YAAY,EAAEC,SAAS,EAAEpP,QAAQ,EAAE7L,SAAS,EAAE;EACrF,IAAIuC,aAAa,GAAApC,aAAA,CAAAA,aAAA,KACZoB,KAAK;IACRrB,UAAU,EAAAC,aAAA,KAAOoB,KAAK,CAACrB,UAAU;EAAE,EACpC;EACD,IAAIgb,cAAc,GAAGhd,WAAW,CAAC4B,MAAM,EAAEmb,SAAS,EAAEpP,QAAQ,CAAC;EAC7D,IAAIqP,cAAc,EAAE;IAClB,KAAK,IAAI9a,KAAK,IAAI8a,cAAc,EAAE;MAChC,IAAI5a,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;MAC5B,IAAI2a,SAAS,GAAGH,YAAY,CAAC1a,OAAO,CAAC;MACrC,IAAI9B,wBAAwB,CAC1B8B,OAAO,EACP6a,SAAS,CAACza,YAAY,EACtBya,SAAS,CAACxa,SAAS,EACnBX,SACF,CAAC,KAAKmb,SAAS,CAACC,kBAAkB,IAAI,CAACD,SAAS,CAACxa,SAAS,CAAC,EAAE;QAC3D,OAAO4B,aAAa,CAACrC,UAAU,CAACI,OAAO,CAAC;MAC1C,CAAC,MAAM,IAAI,CAAC6a,SAAS,CAACxa,SAAS,EAAE;QAC/B4B,aAAa,CAACrC,UAAU,CAACI,OAAO,CAAC,GAAG,IAAI;MAC1C;IACF;EACF;EACA,OAAOiC,aAAa;AACtB;;AAEA;AACA,OAAO8Y,MAAM,MAAM,OAAO;AAC1B,IAAIC,4BAA4B,GAAG,cAAcD,MAAM,CAACxX,SAAS,CAAC;EAChE0X,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACja,KAAK,GAAG;MAAEoF,KAAK,EAAE,IAAI;MAAErF,QAAQ,EAAEka,KAAK,CAACla;IAAS,CAAC;EACxD;EACA,OAAOma,wBAAwBA,CAAC9U,KAAK,EAAE;IACrC,OAAO;MAAEA;IAAM,CAAC;EAClB;EACA,OAAO+U,wBAAwBA,CAACF,KAAK,EAAEja,KAAK,EAAE;IAC5C,IAAIA,KAAK,CAACD,QAAQ,KAAKka,KAAK,CAACla,QAAQ,EAAE;MACrC,OAAO;QAAEqF,KAAK,EAAE,IAAI;QAAErF,QAAQ,EAAEka,KAAK,CAACla;MAAS,CAAC;IAClD;IACA,OAAO;MAAEqF,KAAK,EAAEpF,KAAK,CAACoF,KAAK;MAAErF,QAAQ,EAAEC,KAAK,CAACD;IAAS,CAAC;EACzD;EACAqa,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACpa,KAAK,CAACoF,KAAK,EAAE;MACpB,OAAO,eAAgB0U,MAAM,CAACva,aAAa,CACzC8a,+BAA+B,EAC/B;QACEjV,KAAK,EAAE,IAAI,CAACpF,KAAK,CAACoF,KAAK;QACvBkV,cAAc,EAAE;MAClB,CACF,CAAC;IACH,CAAC,MAAM;MACL,OAAO,IAAI,CAACL,KAAK,CAACvW,QAAQ;IAC5B;EACF;AACF,CAAC;AACD,SAAS6W,YAAYA,CAAAC,KAAA,EAIlB;EAAA,IAJmB;IACpBF,cAAc;IACdG,KAAK;IACL/W;EACF,CAAC,GAAA8W,KAAA;EACC,IAAI,CAACF,cAAc,EAAE;IACnB,OAAO5W,QAAQ;EACjB;EACA,OAAO,eAAgBoW,MAAM,CAACva,aAAa,CAAC,MAAM,EAAE;IAAEmb,IAAI,EAAE;EAAK,CAAC,EAAE,eAAgBZ,MAAM,CAACva,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,eAAgBua,MAAM,CAACva,aAAa,CAAC,MAAM,EAAE;IAAEob,OAAO,EAAE;EAAQ,CAAC,CAAC,EAAE,eAAgBb,MAAM,CAACva,aAAa,CAC7N,MAAM,EACN;IACEgG,IAAI,EAAE,UAAU;IAChBqV,OAAO,EAAE;EACX,CACF,CAAC,EAAE,eAAgBd,MAAM,CAACva,aAAa,CAAC,OAAO,EAAE,IAAI,EAAEkb,KAAK,CAAC,CAAC,EAAE,eAAgBX,MAAM,CAACva,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,eAAgBua,MAAM,CAACva,aAAa,CAAC,MAAM,EAAE;IAAEsb,KAAK,EAAE;MAAEC,UAAU,EAAE,uBAAuB;MAAEC,OAAO,EAAE;IAAO;EAAE,CAAC,EAAErX,QAAQ,CAAC,CAAC,CAAC;AAClP;AACA,SAAS2W,+BAA+BA,CAAAW,KAAA,EAGrC;EAAA,IAHsC;IACvC5V,KAAK;IACLkV;EACF,CAAC,GAAAU,KAAA;EACC/O,OAAO,CAAC7G,KAAK,CAACA,KAAK,CAAC;EACpB,IAAI6V,YAAY,GAAG,eAAgBnB,MAAM,CAACva,aAAa,CACrD,QAAQ,EACR;IACE2b,uBAAuB,EAAE;MACvBC,MAAM;IAKR;EACF,CACF,CAAC;EACD,IAAIze,oBAAoB,CAAC0I,KAAK,CAAC,EAAE;IAC/B,OAAO,eAAgB0U,MAAM,CAACva,aAAa,CACzCgb,YAAY,EACZ;MACED,cAAc;MACdG,KAAK,EAAE;IACT,CAAC,EACD,eAAgBX,MAAM,CAACva,aAAa,CAAC,IAAI,EAAE;MAAEsb,KAAK,EAAE;QAAEO,QAAQ,EAAE;MAAO;IAAE,CAAC,EAAEhW,KAAK,CAAC0F,MAAM,EAAE,GAAG,EAAE1F,KAAK,CAACkO,UAAU,CAAC,EAChH3Y,mBAAmB,GAAGsgB,YAAY,GAAG,IACvC,CAAC;EACH;EACA,IAAII,aAAa;EACjB,IAAIjW,KAAK,YAAYlD,KAAK,EAAE;IAC1BmZ,aAAa,GAAGjW,KAAK;EACvB,CAAC,MAAM;IACL,IAAIkW,WAAW,GAAGlW,KAAK,IAAI,IAAI,GAAG,eAAe,GAAG,OAAOA,KAAK,KAAK,QAAQ,IAAI,UAAU,IAAIA,KAAK,GAAGA,KAAK,CAAC6C,QAAQ,CAAC,CAAC,GAAGL,IAAI,CAACC,SAAS,CAACzC,KAAK,CAAC;IAC/IiW,aAAa,GAAG,IAAInZ,KAAK,CAACoZ,WAAW,CAAC;EACxC;EACA,OAAO,eAAgBxB,MAAM,CAACva,aAAa,CAACgb,YAAY,EAAE;IAAED,cAAc;IAAEG,KAAK,EAAE;EAAqB,CAAC,EAAE,eAAgBX,MAAM,CAACva,aAAa,CAAC,IAAI,EAAE;IAAEsb,KAAK,EAAE;MAAEO,QAAQ,EAAE;IAAO;EAAE,CAAC,EAAE,mBAAmB,CAAC,EAAE,eAAgBtB,MAAM,CAACva,aAAa,CAC/O,KAAK,EACL;IACEsb,KAAK,EAAE;MACLE,OAAO,EAAE,MAAM;MACfQ,UAAU,EAAE,yBAAyB;MACrCC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC,EACDJ,aAAa,CAAC9R,KAChB,CAAC,EAAE0R,YAAY,CAAC;AAClB;AACA,SAASS,2BAA2BA,CAAAC,KAAA,EAEjC;EAAA,IAFkC;IACnCC;EACF,CAAC,GAAAD,KAAA;EACC,IAAIvW,KAAK,GAAG7H,aAAa,CAAC,CAAC;EAC3B,IAAIqe,aAAa,KAAK,KAAK,CAAC,EAAE;IAC5B,MAAM,IAAI1Z,KAAK,CAAC,8BAA8B,CAAC;EACjD;EACA,OAAO,eAAgB4X,MAAM,CAACva,aAAa,CACzC8a,+BAA+B,EAC/B;IACEC,cAAc,EAAE,CAACsB,aAAa;IAC9BxW;EACF,CACF,CAAC;AACH;;AAEA;AACA,SAASyW,qBAAqBA,CAACC,OAAO,EAAE;EACtC,MAAM1d,YAAY,GAAG,CAAC,CAAC;EACvB,KAAK,MAAMS,KAAK,IAAIid,OAAO,CAAChd,OAAO,EAAE;IACnCid,uBAAuB,CAAC3d,YAAY,EAAES,KAAK,CAAC;EAC9C;EACA,OAAOT,YAAY;AACrB;AACA,SAAS2d,uBAAuBA,CAAC3d,YAAY,EAAEU,OAAO,EAAE;EACtDA,OAAO,GAAGkd,KAAK,CAACC,OAAO,CAACnd,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EACtD,KAAK,MAAMD,KAAK,IAAIC,OAAO,EAAE;IAC3BV,YAAY,CAACS,KAAK,CAACI,EAAE,CAAC,GAAG;MACvBuE,KAAK,EAAE3E,KAAK,CAAC2E,KAAK;MAClBC,IAAI,EAAE5E,KAAK,CAAC4E,IAAI;MAChBF,OAAO,EAAE2Y;IACX,CAAC;EACH;AACF;AACA,IAAIA,aAAa,GAAGA,CAAA,KAAM,IAAI;;AAE9B;AACA,SAASC,gBAAgBA,CAAAC,KAAA,EAKtB;EAAA,IALuB;IACxBC,wBAAwB;IACxBC,2BAA2B;IAC3BC,WAAW;IACXC,KAAK,EAAEC,mBAAmB,GAAGD;EAC/B,CAAC,GAAAJ,KAAA;EACC,MAAMM,SAAS,GAAGC,MAAM;EACxB,IAAIC,cAAc,GAAG,CAAC;EACtB,OAAO,OAAO3d,EAAE,EAAEwD,IAAI,KAAK;IAAA,IAAAoa,qBAAA;IACzB,IAAIC,QAAQ,GAAGJ,SAAS,CAACK,gBAAgB,GAAG,EAAAF,qBAAA,GAACH,SAAS,CAACK,gBAAgB,cAAAF,qBAAA,cAAAA,qBAAA,GAAKH,SAAS,CAACK,gBAAgB,GAAG,CAAC,IAAK,CAAC;IAChH,MAAMC,mBAAmB,GAAGV,2BAA2B,CAAC,CAAC;IACzD,MAAMpH,QAAQ,GAAG,MAAMuH,mBAAmB,CACxC,IAAI3Q,OAAO,CAAC/L,QAAQ,CAACgM,IAAI,EAAE;MACzBL,IAAI,EAAE,MAAM6Q,WAAW,CAAC9Z,IAAI,EAAE;QAAEua;MAAoB,CAAC,CAAC;MACtDvR,MAAM,EAAE,MAAM;MACdE,OAAO,EAAE;QACPsR,MAAM,EAAE,kBAAkB;QAC1B,eAAe,EAAEhe;MACnB;IACF,CAAC,CACH,CAAC;IACD,IAAI,CAACiW,QAAQ,CAACxJ,IAAI,EAAE;MAClB,MAAM,IAAIxJ,KAAK,CAAC,kBAAkB,CAAC;IACrC;IACA,MAAM4Z,OAAO,GAAG,MAAMO,wBAAwB,CAACnH,QAAQ,CAACxJ,IAAI,EAAE;MAC5DsR;IACF,CAAC,CAAC;IACF,IAAIlB,OAAO,CAACoB,IAAI,KAAK,UAAU,EAAE;MAC/B,IAAIpB,OAAO,CAAC9N,MAAM,EAAE;QAClB2O,MAAM,CAAC5c,QAAQ,CAACgM,IAAI,GAAG+P,OAAO,CAAC/b,QAAQ;QACvC;MACF;MACA2c,SAAS,CAACS,uBAAuB,CAACC,QAAQ,CAACtB,OAAO,CAAC/b,QAAQ,EAAE;QAC3DhD,OAAO,EAAE+e,OAAO,CAAC/e;MACnB,CAAC,CAAC;MACF,OAAO+e,OAAO,CAACuB,YAAY;IAC7B;IACA,IAAIvB,OAAO,CAACoB,IAAI,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIhb,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IACA,IAAI4Z,OAAO,CAACwB,QAAQ,EAAE;MACpBhE,MAAM,CAACiE,eAAe;MACpB;MACA,YAAY;QACV,MAAMD,QAAQ,GAAG,MAAMxB,OAAO,CAACwB,QAAQ;QACvC,IAAI,CAACA,QAAQ,EAAE;QACf,IAAIV,cAAc,GAAGE,QAAQ,IAAIJ,SAAS,CAACK,gBAAgB,IAAID,QAAQ,EAAE;UACvEF,cAAc,GAAGE,QAAQ;UACzB,IAAIQ,QAAQ,CAACJ,IAAI,KAAK,UAAU,EAAE;YAChC,IAAII,QAAQ,CAACtP,MAAM,EAAE;cACnB2O,MAAM,CAAC5c,QAAQ,CAACgM,IAAI,GAAGuR,QAAQ,CAACvd,QAAQ;cACxC;YACF;YACA2c,SAAS,CAACS,uBAAuB,CAACC,QAAQ,CAACE,QAAQ,CAACvd,QAAQ,EAAE;cAC5DhD,OAAO,EAAEugB,QAAQ,CAACvgB;YACpB,CAAC,CAAC;YACF;UACF;UACA,IAAIygB,SAAS;UACb,KAAK,MAAM3e,KAAK,IAAIye,QAAQ,CAACxe,OAAO,EAAE;YAAA,IAAA2e,aAAA,EAAAC,UAAA;YACpChB,SAAS,CAACS,uBAAuB,CAACQ,WAAW,EAAAF,aAAA,IAAAC,UAAA,GAC3CF,SAAS,cAAAE,UAAA,uBAATA,UAAA,CAAWze,EAAE,cAAAwe,aAAA,cAAAA,aAAA,GAAI,IAAI,EACrB,CAACG,6BAA6B,CAAC/e,KAAK,CAAC,CAAC,EACtC,IACF,CAAC;YACD2e,SAAS,GAAG3e,KAAK;UACnB;UACA8d,MAAM,CAACQ,uBAAuB,CAACU,8CAA8C,CAC3E,CAAC,CACH,CAAC;UACDvE,MAAM,CAACiE,eAAe,CAAC,MAAM;YAC3BZ,MAAM,CAACQ,uBAAuB,CAACU,8CAA8C,CAC3E;cACElf,UAAU,EAAEkK,MAAM,CAACgB,MAAM,CACvB,CAAC,CAAC,EACF6S,SAAS,CAACS,uBAAuB,CAACnd,KAAK,CAACrB,UAAU,EAClD2e,QAAQ,CAAC3e,UACX,CAAC;cACD8K,MAAM,EAAE6T,QAAQ,CAAC7T,MAAM,GAAGZ,MAAM,CAACgB,MAAM,CACrC,CAAC,CAAC,EACF6S,SAAS,CAACS,uBAAuB,CAACnd,KAAK,CAACyJ,MAAM,EAC9C6T,QAAQ,CAAC7T,MACX,CAAC,GAAG;YACN,CACF,CAAC;UACH,CAAC,CAAC;QACJ;MACF,CACF,CAAC;IACH;IACA,OAAOqS,OAAO,CAACuB,YAAY;EAC7B,CAAC;AACH;AACA,SAASS,uBAAuBA,CAAAC,KAAA,EAK7B;EAAA,IAAAC,qBAAA,EAAAC,gBAAA;EAAA,IAL8B;IAC/BxB,mBAAmB;IACnBJ,wBAAwB;IACxB6B,mBAAmB;IACnBpC;EACF,CAAC,GAAAiC,KAAA;EACC,MAAMrB,SAAS,GAAGC,MAAM;EACxB,IAAID,SAAS,CAACS,uBAAuB,IAAIT,SAAS,CAACyB,yBAAyB,EAC1E,OAAO;IACL7e,MAAM,EAAEod,SAAS,CAACS,uBAAuB;IACzC/e,YAAY,EAAEse,SAAS,CAACyB;EAC1B,CAAC;EACH,IAAIrC,OAAO,CAACoB,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAIhb,KAAK,CAAC,sBAAsB,CAAC;EACtEwa,SAAS,CAACyB,yBAAyB,IAAAH,qBAAA,GAAGtB,SAAS,CAACyB,yBAAyB,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;EAC/EjC,uBAAuB,CAACW,SAAS,CAACyB,yBAAyB,EAAErC,OAAO,CAAChd,OAAO,CAAC;EAC7E,IAAIgX,OAAO,GAAG,eAAgB,IAAI8B,GAAG,CAAC,CAAC;EACvC,CAAAqG,gBAAA,GAAAnC,OAAO,CAAChG,OAAO,cAAAmI,gBAAA,eAAfA,gBAAA,CAAiBhR,OAAO,CAAEmR,KAAK,IAAK;IAAA,IAAAC,YAAA;IAClCjiB,SAAS,CAACgiB,KAAK,CAACpc,QAAQ,EAAE,wBAAwB,CAAC;IACnD,IAAI,CAAC8T,OAAO,CAACxF,GAAG,CAAC8N,KAAK,CAACpc,QAAQ,CAAC,EAAE;MAChC8T,OAAO,CAACzD,GAAG,CAAC+L,KAAK,CAACpc,QAAQ,EAAE,EAAE,CAAC;IACjC;IACA,CAAAqc,YAAA,GAAAvI,OAAO,CAACjJ,GAAG,CAACuR,KAAK,CAACpc,QAAQ,CAAC,cAAAqc,YAAA,eAA3BA,YAAA,CAA6B/S,IAAI,CAAC8S,KAAK,CAAC;EAC1C,CAAC,CAAC;EACF,IAAI7f,MAAM,GAAGud,OAAO,CAAChd,OAAO,CAACwf,WAAW,CAAC,CAACC,QAAQ,EAAE1f,KAAK,KAAK;IAC5D,MAAMG,KAAK,GAAG4e,6BAA6B,CACzC/e,KAAK,EACLid,OACF,CAAC;IACD,IAAIyC,QAAQ,CAAC7Y,MAAM,GAAG,CAAC,EAAE;MACvB1G,KAAK,CAAC0E,QAAQ,GAAG6a,QAAQ;MACzB,IAAIC,eAAe,GAAG1I,OAAO,CAACjJ,GAAG,CAAChO,KAAK,CAACI,EAAE,CAAC;MAC3C,IAAIuf,eAAe,EAAE;QACnBxf,KAAK,CAAC0E,QAAQ,CAAC4H,IAAI,CACjB,GAAGkT,eAAe,CAACvc,GAAG,CAAEF,CAAC,IAAK6b,6BAA6B,CAAC7b,CAAC,CAAC,CAChE,CAAC;MACH;IACF;IACA,OAAO,CAAC/C,KAAK,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EACN0d,SAAS,CAACS,uBAAuB,GAAGxhB,YAAY,CAAC;IAC/C4C,MAAM;IACN2f,mBAAmB;IACnB5T,QAAQ,EAAEwR,OAAO,CAACxR,QAAQ;IAC1BmU,OAAO,EAAEjjB,oBAAoB,CAAC,CAAC;IAC/BwF,aAAa,EAAEwY,gBAAgB,CAC7B;MACE7a,UAAU,EAAEmd,OAAO,CAACnd,UAAU;MAC9ByQ,UAAU,EAAE0M,OAAO,CAAC1M,UAAU;MAC9B3F,MAAM,EAAEqS,OAAO,CAACrS;IAClB,CAAC,EACDlL,MAAM,EACLQ,OAAO,IAAK;MACX,IAAIF,KAAK,GAAGid,OAAO,CAAChd,OAAO,CAAC4f,IAAI,CAAE7P,CAAC,IAAKA,CAAC,CAAC5P,EAAE,KAAKF,OAAO,CAAC;MACzD3C,SAAS,CAACyC,KAAK,EAAE,4BAA4B,CAAC;MAC9C,OAAO;QACLM,YAAY,EAAEN,KAAK,CAACM,YAAY;QAChCC,SAAS,EAAEP,KAAK,CAACO,SAAS;QAC1Bya,kBAAkB,EAAEhb,KAAK,CAAC8f,sBAAsB,IAAI;MACtD,CAAC;IACH,CAAC,EACD7C,OAAO,CAAC/b,QAAQ,EAChB,KAAK,CAAC,EACN,KACF,CAAC;IACD,MAAM6e,uBAAuBA,CAAAC,MAAA,EAAmB;MAAA,IAAlB;QAAEzc,IAAI;QAAEwJ;MAAO,CAAC,GAAAiT,MAAA;MAC5C,IAAIC,eAAe,CAACxO,GAAG,CAAClO,IAAI,CAAC,EAAE;QAC7B;MACF;MACA,MAAM2c,4BAA4B,CAChC,CAAC3c,IAAI,CAAC,EACNia,wBAAwB,EACxBI,mBAAmB,EACnB7Q,MACF,CAAC;IACH,CAAC;IACD;IACAoT,YAAY,EAAEC,6BAA6B,CACzC,MAAMvC,SAAS,CAACS,uBAAuB,EACvC,IAAI,EACJrB,OAAO,CAACxR,QAAQ,EAChB+R,wBAAwB,EACxBI,mBACF;EACF,CAAC,CAAC;EACF,IAAIC,SAAS,CAACS,uBAAuB,CAACnd,KAAK,CAACkf,WAAW,EAAE;IACvDxC,SAAS,CAACyC,mBAAmB,GAAG,IAAI;IACpCzC,SAAS,CAACS,uBAAuB,CAACiC,UAAU,CAAC,CAAC;EAChD,CAAC,MAAM;IACL1C,SAAS,CAACyC,mBAAmB,GAAG,KAAK;EACvC;EACA,IAAIE,cAAc,GAAG,KAAK,CAAC;EAC3B3C,SAAS,CAACS,uBAAuB,CAACmC,SAAS,CAACC,MAAA,IAAgC;IAAA,IAA/B;MAAE5gB,UAAU;MAAEyQ;IAAW,CAAC,GAAAmQ,MAAA;IACrE,IAAIF,cAAc,KAAK1gB,UAAU,EAAE;MAAA,IAAA6gB,sBAAA;MACjC9C,SAAS,CAACK,gBAAgB,GAAG,EAAAyC,sBAAA,GAAC9C,SAAS,CAACK,gBAAgB,cAAAyC,sBAAA,cAAAA,sBAAA,GAAK9C,SAAS,CAACK,gBAAgB,GAAG,CAAC,IAAK,CAAC;IACnG;EACF,CAAC,CAAC;EACFL,SAAS,CAACS,uBAAuB,CAACsC,mBAAmB,GAAIC,oBAAoB,IAAK;IAChF,MAAMC,SAAS,GAAGhD,MAAM,CAACQ,uBAAuB,CAAC5e,MAAM;IACvD,MAAMqhB,SAAS,GAAG,EAAE;IACpB,SAASC,UAAUA,CAACC,OAAO,EAAE9d,QAAQ,EAAE;MACrC,OAAO8d,OAAO,CAAC7d,GAAG,CAAEjD,KAAK,IAAK;QAC5B,MAAM+gB,WAAW,GAAGL,oBAAoB,CAAC7S,GAAG,CAAC7N,KAAK,CAACC,EAAE,CAAC;QACtD,IAAI8gB,WAAW,EAAE;UACf,MAAM;YACJC,WAAW;YACXld,SAAS;YACTmd,YAAY;YACZ/c,gBAAgB;YAChB9D;UACF,CAAC,GAAG2gB,WAAW;UACf,MAAM5d,QAAQ,GAAGyb,6BAA6B,CAAC;YAC7CsC,YAAY,EAAEF,WAAW,CAACE,YAAY;YACtC/gB,YAAY,EAAE6gB,WAAW,CAAC7gB,YAAY;YACtCghB,OAAO,EAAEnhB,KAAK,CAACmhB,OAAO;YACtBC,YAAY,EAAEphB,KAAK,CAACohB,YAAY;YAChCzd,MAAM,EAAE3D,KAAK,CAAC2D,MAAM;YACpBG,SAAS;YACTmd,YAAY;YACZ/c,gBAAgB;YAChB9D,SAAS;YACTuf,sBAAsB,EAAE3f,KAAK,CAAC2f,sBAAsB;YACpD1f,EAAE,EAAED,KAAK,CAACC,EAAE;YACZoD,KAAK,EAAErD,KAAK,CAACqD,KAAK;YAClBmB,KAAK,EAAEwc,WAAW,CAACxc,KAAK;YACxBC,IAAI,EAAEuc,WAAW,CAACvc,IAAI;YACtBzB,QAAQ;YACRI,IAAI,EAAEpD,KAAK,CAACoD,IAAI;YAChBQ,gBAAgB,EAAEod,WAAW,CAACpd;UAChC,CAAC,CAAC;UACF,IAAI5D,KAAK,CAAC0E,QAAQ,EAAE;YAClBvB,QAAQ,CAACuB,QAAQ,GAAGmc,UAAU,CAAC7gB,KAAK,CAAC0E,QAAQ,EAAE1E,KAAK,CAACC,EAAE,CAAC;UAC1D;UACA,OAAOkD,QAAQ;QACjB;QACA,MAAMke,YAAY,GAAAzhB,aAAA,KAAQI,KAAK,CAAE;QACjC,IAAIA,KAAK,CAAC0E,QAAQ,EAAE;UAClB2c,YAAY,CAAC3c,QAAQ,GAAGmc,UAAU,CAAC7gB,KAAK,CAAC0E,QAAQ,EAAE1E,KAAK,CAACC,EAAE,CAAC;QAC9D;QACA,OAAOohB,YAAY;MACrB,CAAC,CAAC;IACJ;IACAT,SAAS,CAACtU,IAAI,CACZ,GAAGuU,UAAU,CAACF,SAAS,EAAE,KAAK,CAAC,CACjC,CAAC;IACDhD,MAAM,CAACQ,uBAAuB,CAACmD,kBAAkB,CAACV,SAAS,CAAC;EAC9D,CAAC;EACD,OAAO;IACLtgB,MAAM,EAAEod,SAAS,CAACS,uBAAuB;IACzC/e,YAAY,EAAEse,SAAS,CAACyB;EAC1B,CAAC;AACH;AACA,IAAIoC,qBAAqB,GAAGjjB,sBAAsB,CAAC,CAAC;AACpD,SAAS2hB,6BAA6BA,CAACuB,SAAS,EAAE7gB,GAAG,EAAE2K,QAAQ,EAAE+R,wBAAwB,EAAEI,mBAAmB,EAAE;EAC9G,IAAIuC,YAAY,GAAG9iB,8BAA8B,CAC/CskB,SAAS,EACR3hB,KAAK,IAAK;IACT,IAAI4hB,CAAC,GAAG5hB,KAAK;IACb,OAAO;MACLO,SAAS,EAAEqhB,CAAC,CAACzhB,KAAK,CAACI,SAAS;MAC5B4D,eAAe,EAAEyd,CAAC,CAACzhB,KAAK,CAACgE,eAAe;MACxCid,YAAY,EAAEQ,CAAC,CAACzhB,KAAK,CAACihB,YAAY;MAClCnd,SAAS,EAAE2d,CAAC,CAACzhB,KAAK,CAAC8D,SAAS;MAC5BC,eAAe,EAAE0d,CAAC,CAACzhB,KAAK,CAAC+D,eAAe;MACxC2d,mBAAmB,EAAED,CAAC,CAACzhB,KAAK,CAAC0hB;IAC/B,CAAC;EACH,CAAC;EACD;EACAC,uBAAuB,CAACtE,wBAAwB,EAAEI,mBAAmB,CAAC,EACtE9c,GAAG,EACH2K,QAAQ;EACR;EACA;EACA;EACCzL,KAAK,IAAK;IACT,IAAI4hB,CAAC,GAAG5hB,KAAK;IACb,OAAO4hB,CAAC,CAACzhB,KAAK,CAACihB,YAAY,IAAI,CAACQ,CAAC,CAACzhB,KAAK,CAACmhB,OAAO;EACjD,CACF,CAAC;EACD,OAAO,MAAO1d,IAAI,IAAKA,IAAI,CAACme,4BAA4B,CAAC,YAAY;IACnE,IAAI7iB,OAAO,GAAG0E,IAAI,CAAC1E,OAAO;IAC1BA,OAAO,CAACsU,GAAG,CAACkO,qBAAqB,EAAE,EAAE,CAAC;IACtC,IAAIvO,OAAO,GAAG,MAAMgN,YAAY,CAACvc,IAAI,CAAC;IACtC,MAAMoe,kBAAkB,GAAG,eAAgB,IAAIjJ,GAAG,CAAC,CAAC;IACpD,KAAK,MAAM5Y,KAAK,IAAIjB,OAAO,CAAC8O,GAAG,CAAC0T,qBAAqB,CAAC,EAAE;MACtD,IAAI,CAACM,kBAAkB,CAACvQ,GAAG,CAACtR,KAAK,CAACC,EAAE,CAAC,EAAE;QACrC4hB,kBAAkB,CAACxO,GAAG,CAACrT,KAAK,CAACC,EAAE,EAAE,EAAE,CAAC;MACtC;MACA4hB,kBAAkB,CAAChU,GAAG,CAAC7N,KAAK,CAACC,EAAE,CAAC,CAACqM,IAAI,CAACtM,KAAK,CAAC;IAC9C;IACA,KAAK,MAAMH,KAAK,IAAI4D,IAAI,CAAC3D,OAAO,EAAE;MAChC,MAAMgiB,cAAc,GAAGD,kBAAkB,CAAChU,GAAG,CAAChO,KAAK,CAACG,KAAK,CAACC,EAAE,CAAC;MAC7D,IAAI6hB,cAAc,EAAE;QAClB,KAAK,MAAMC,QAAQ,IAAID,cAAc,EAAE;UAAA,IAAAE,kBAAA;UACrCrE,MAAM,CAACQ,uBAAuB,CAACQ,WAAW,EAAAqD,kBAAA,GACxCD,QAAQ,CAAC/e,QAAQ,cAAAgf,kBAAA,cAAAA,kBAAA,GAAI,IAAI,EACzB,CAACpD,6BAA6B,CAACmD,QAAQ,CAAC,CAAC,EACzC,IACF,CAAC;QACH;MACF;IACF;IACA,OAAO/O,OAAO;EAChB,CAAC,CAAC;AACJ;AACA,SAAS2O,uBAAuBA,CAACtE,wBAAwB,EAAEI,mBAAmB,EAAE;EAC9E,OAAO,OAAOha,IAAI,EAAE6H,QAAQ,EAAE2W,YAAY,KAAK;IAC7C,IAAI;MAAEvW,OAAO;MAAE3M;IAAQ,CAAC,GAAG0E,IAAI;IAC/B,IAAIzE,GAAG,GAAGd,cAAc,CAACwN,OAAO,CAAC1M,GAAG,EAAEsM,QAAQ,EAAE,KAAK,CAAC;IACtD,IAAII,OAAO,CAACe,MAAM,KAAK,KAAK,EAAE;MAC5BzN,GAAG,GAAGZ,eAAe,CAACY,GAAG,CAAC;MAC1B,IAAIijB,YAAY,EAAE;QAChBjjB,GAAG,CAACiN,YAAY,CAACoH,GAAG,CAAC,SAAS,EAAE4O,YAAY,CAAC7K,IAAI,CAAC,GAAG,CAAC,CAAC;MACzD;IACF;IACA,IAAIpB,GAAG,GAAG,MAAMyH,mBAAmB,CACjC,IAAI3Q,OAAO,CAAC9N,GAAG,EAAE,MAAMtC,iBAAiB,CAACgP,OAAO,CAAC,CACnD,CAAC;IACD,IAAIsK,GAAG,CAAClK,MAAM,KAAK,GAAG,IAAI,CAACkK,GAAG,CAACrJ,OAAO,CAAC2E,GAAG,CAAC,kBAAkB,CAAC,EAAE;MAC9D,MAAM,IAAI1V,iBAAiB,CAAC,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC;IACrD;IACAwB,SAAS,CAAC4Y,GAAG,CAACtJ,IAAI,EAAE,4BAA4B,CAAC;IACjD,IAAI;MACF,MAAMoQ,OAAO,GAAG,MAAMO,wBAAwB,CAACrH,GAAG,CAACtJ,IAAI,EAAE;QACvDsR,mBAAmB,EAAE,KAAK;MAC5B,CAAC,CAAC;MACF,IAAIlB,OAAO,CAACoB,IAAI,KAAK,UAAU,EAAE;QAC/B,OAAO;UACLpS,MAAM,EAAEkK,GAAG,CAAClK,MAAM;UAClBmD,IAAI,EAAE;YACJpR,QAAQ,EAAE;cACRA,QAAQ,EAAEif,OAAO,CAAC/b,QAAQ;cAC1BiO,MAAM,EAAE8N,OAAO,CAAC9N,MAAM;cACtBjR,OAAO,EAAE+e,OAAO,CAAC/e,OAAO;cACxB6V,UAAU,EAAE,KAAK;cACjB9H,MAAM,EAAEgR,OAAO,CAAChR;YAClB;UACF;QACF,CAAC;MACH;MACA,IAAIgR,OAAO,CAACoB,IAAI,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAIhb,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MACAnE,OAAO,CAAC8O,GAAG,CAAC0T,qBAAqB,CAAC,CAACjV,IAAI,CAAC,GAAGwQ,OAAO,CAAChd,OAAO,CAAC;MAC3D,IAAIkT,OAAO,GAAG;QAAEzT,MAAM,EAAE,CAAC;MAAE,CAAC;MAC5B,MAAM2iB,OAAO,GAAG5kB,gBAAgB,CAACoO,OAAO,CAACe,MAAM,CAAC,GAAG,YAAY,GAAG,YAAY;MAC9E,KAAK,IAAI,CAAC1M,OAAO,EAAEkF,KAAK,CAAC,IAAI4E,MAAM,CAACa,OAAO,CAACoS,OAAO,CAACoF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QACnElP,OAAO,CAACzT,MAAM,CAACQ,OAAO,CAAC,GAAG;UAAEkP,IAAI,EAAEhK;QAAM,CAAC;MAC3C;MACA,IAAI6X,OAAO,CAACrS,MAAM,EAAE;QAClB,KAAK,IAAI,CAAC1K,OAAO,EAAEqG,KAAK,CAAC,IAAIyD,MAAM,CAACa,OAAO,CAACoS,OAAO,CAACrS,MAAM,CAAC,EAAE;UAC3DuI,OAAO,CAACzT,MAAM,CAACQ,OAAO,CAAC,GAAG;YAAEqG;UAAM,CAAC;QACrC;MACF;MACA,OAAO;QAAE0F,MAAM,EAAEkK,GAAG,CAAClK,MAAM;QAAEmD,IAAI,EAAE+D;MAAQ,CAAC;IAC9C,CAAC,CAAC,OAAOlF,CAAC,EAAE;MACV,MAAM,IAAI5K,KAAK,CAAC,+BAA+B,CAAC;IAClD;EACF,CAAC;AACH;AACA,SAASif,iBAAiBA,CAAAC,MAAA,EAMvB;EAAA,IANwB;IACzB/E,wBAAwB;IACxBG,KAAK,EAAEC,mBAAmB,GAAGD,KAAK;IAClCV,OAAO;IACPlc,cAAc,GAAG,OAAO;IACxBse;EACF,CAAC,GAAAkD,MAAA;EACC,IAAItF,OAAO,CAACoB,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAIhb,KAAK,CAAC,sBAAsB,CAAC;EACtE,IAAI;IAAE5C,MAAM;IAAElB;EAAa,CAAC,GAAGkb,MAAM,CAAC+H,OAAO,CAC3C,MAAMvD,uBAAuB,CAAC;IAC5BhC,OAAO;IACPW,mBAAmB;IACnByB,mBAAmB;IACnB7B;EACF,CAAC,CAAC,EACF,CACEA,wBAAwB,EACxBP,OAAO,EACPW,mBAAmB,EACnByB,mBAAmB,CAEvB,CAAC;EACD5E,MAAM,CAACgI,SAAS,CAAC,MAAM;IACrBtkB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EACNsc,MAAM,CAACiI,eAAe,CAAC,MAAM;IAC3B,MAAM7E,SAAS,GAAGC,MAAM;IACxB,IAAI,CAACD,SAAS,CAACyC,mBAAmB,EAAE;MAClCzC,SAAS,CAACyC,mBAAmB,GAAG,IAAI;MACpCzC,SAAS,CAACS,uBAAuB,CAACiC,UAAU,CAAC,CAAC;IAChD;EACF,CAAC,EAAE,EAAE,CAAC;EACN,IAAI,CAAC1F,SAAS,EAAE8H,WAAW,CAAC,GAAGlI,MAAM,CAACmI,QAAQ,CAACniB,MAAM,CAACU,KAAK,CAACD,QAAQ,CAAC;EACrEuZ,MAAM,CAACiI,eAAe,CACpB,MAAMjiB,MAAM,CAACggB,SAAS,CAAEoC,QAAQ,IAAK;IACnC,IAAIA,QAAQ,CAAC3hB,QAAQ,KAAK2Z,SAAS,EAAE;MACnC8H,WAAW,CAACE,QAAQ,CAAC3hB,QAAQ,CAAC;IAChC;EACF,CAAC,CAAC,EACF,CAACT,MAAM,EAAEoa,SAAS,CACpB,CAAC;EACDJ,MAAM,CAACgI,SAAS,CAAC,MAAM;IAAA,IAAAK,iBAAA;IACrB,IAAI/hB,cAAc,KAAK,MAAM;IAAI;IACjC,EAAA+hB,iBAAA,GAAAhF,MAAM,CAACiF,SAAS,cAAAD,iBAAA,gBAAAA,iBAAA,GAAhBA,iBAAA,CAAkBE,UAAU,cAAAF,iBAAA,uBAA5BA,iBAAA,CAA8BG,QAAQ,MAAK,IAAI,EAAE;MAC/C;IACF;IACA,SAASC,eAAeA,CAACC,EAAE,EAAE;MAC3B,IAAI5f,IAAI,GAAG4f,EAAE,CAACC,OAAO,KAAK,MAAM,GAAGD,EAAE,CAACE,YAAY,CAAC,QAAQ,CAAC,GAAGF,EAAE,CAACE,YAAY,CAAC,MAAM,CAAC;MACtF,IAAI,CAAC9f,IAAI,EAAE;QACT;MACF;MACA,IAAIiI,QAAQ,GAAG2X,EAAE,CAACC,OAAO,KAAK,GAAG,GAAGD,EAAE,CAAC3X,QAAQ,GAAG,IAAInM,GAAG,CAACkE,IAAI,EAAEua,MAAM,CAAC5c,QAAQ,CAACoiB,MAAM,CAAC,CAAC9X,QAAQ;MAChG,IAAI,CAACyU,eAAe,CAACxO,GAAG,CAACjG,QAAQ,CAAC,EAAE;QAClC+X,SAAS,CAAC/L,GAAG,CAAChM,QAAQ,CAAC;MACzB;IACF;IACA,eAAegY,YAAYA,CAAA,EAAG;MAC5BC,QAAQ,CAACC,gBAAgB,CAAC,uCAAuC,CAAC,CAACtV,OAAO,CAAC8U,eAAe,CAAC;MAC3F,IAAIhM,KAAK,GAAGiG,KAAK,CAACwG,IAAI,CAACJ,SAAS,CAACtZ,IAAI,CAAC,CAAC,CAAC,CAACoJ,MAAM,CAAE9P,IAAI,IAAK;QACxD,IAAI0c,eAAe,CAACxO,GAAG,CAAClO,IAAI,CAAC,EAAE;UAC7BggB,SAAS,CAACjX,MAAM,CAAC/I,IAAI,CAAC;UACtB,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAI2T,KAAK,CAACrQ,MAAM,KAAK,CAAC,EAAE;QACtB;MACF;MACA,IAAI;QACF,MAAMqZ,4BAA4B,CAChChJ,KAAK,EACLsG,wBAAwB,EACxBI,mBACF,CAAC;MACH,CAAC,CAAC,OAAO3P,CAAC,EAAE;QACVb,OAAO,CAAC7G,KAAK,CAAC,kCAAkC,EAAE0H,CAAC,CAAC;MACtD;IACF;IACA,IAAI2V,qBAAqB,GAAGC,QAAQ,CAACL,YAAY,EAAE,GAAG,CAAC;IACvDA,YAAY,CAAC,CAAC;IACd,IAAIM,QAAQ,GAAG,IAAIC,gBAAgB,CAAC,MAAMH,qBAAqB,CAAC,CAAC,CAAC;IAClEE,QAAQ,CAACE,OAAO,CAACP,QAAQ,CAACQ,eAAe,EAAE;MACzCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,CAAC,eAAe,EAAE,MAAM,EAAE,QAAQ;IACrD,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtjB,cAAc,EAAEyc,wBAAwB,EAAEI,mBAAmB,CAAC,CAAC;EACnE,MAAM0G,gBAAgB,GAAG;IACvB3kB,MAAM,EAAE;MACN;MACA;MACA8C,mBAAmB,EAAE,KAAK;MAC1BD,6BAA6B,EAAE;IACjC,CAAC;IACD5C,SAAS,EAAE,KAAK;IAChBkB,GAAG,EAAE,IAAI;IACTtB,WAAW,EAAE,EAAE;IACfF,QAAQ,EAAE;MACRI,MAAM,EAAE,CAAC,CAAC;MACVmD,OAAO,EAAE,GAAG;MACZ1D,GAAG,EAAE,EAAE;MACPuD,KAAK,EAAE;QACLE,MAAM,EAAE,EAAE;QACVD,OAAO,EAAE;MACX;IACF,CAAC;IACD5B,cAAc,EAAE;MAAE+B,IAAI,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAc,CAAC;IAC7DxD;EACF,CAAC;EACD,OAAO,eAAgBkb,MAAM,CAAC/Z,aAAa,CAACvE,gBAAgB,CAACyE,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAAE,eAAgB4Z,MAAM,CAAC/Z,aAAa,CAACwa,4BAA4B,EAAE;IAAEha,QAAQ,EAAE2Z;EAAU,CAAC,EAAE,eAAgBJ,MAAM,CAAC/Z,aAAa,CAAC1E,gBAAgB,CAAC4E,QAAQ,EAAE;IAAEC,KAAK,EAAEyjB;EAAiB,CAAC,EAAE,eAAgB7J,MAAM,CAAC/Z,aAAa,CAACrE,cAAc,EAAE;IAAEoE,MAAM;IAAE8jB,SAAS,EAAE7J,QAAQ,CAAC6J;EAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACrX;AACA,SAASxF,6BAA6BA,CAAC/e,KAAK,EAAEid,OAAO,EAAE;EAAA,IAAAuH,eAAA,EAAAC,mBAAA;EACrD,IAAIC,cAAc,GAAGzH,OAAO,IAAIjd,KAAK,CAACI,EAAE,IAAI6c,OAAO,CAACnd,UAAU;EAC9D,IAAIgZ,WAAW,GAAGmE,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEnd,UAAU,CAACE,KAAK,CAACI,EAAE,CAAC;EAC/C,IAAIukB,eAAe,GAAG,CAAA1H,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAErS,MAAM,KAAI5K,KAAK,CAACI,EAAE,IAAI6c,OAAO,CAACrS,MAAM;EACnE,IAAIga,YAAY,GAAG3H,OAAO,aAAPA,OAAO,gBAAAuH,eAAA,GAAPvH,OAAO,CAAErS,MAAM,cAAA4Z,eAAA,uBAAfA,eAAA,CAAkBxkB,KAAK,CAACI,EAAE,CAAC;EAC9C,IAAIykB,kBAAkB,GAAG,EAAAJ,mBAAA,GAAAzkB,KAAK,CAACM,YAAY,cAAAmkB,mBAAA,uBAAlBA,mBAAA,CAAoBrjB,OAAO,MAAK,IAAI,IAAI,CAACpB,KAAK,CAACO,SAAS;EAAI;EACrF;EACA;EACAP,KAAK,CAACohB,YAAY,IAAI,CAACphB,KAAK,CAACshB,OAAO;EACpC/jB,SAAS,CAACugB,MAAM,CAACwB,yBAAyB,CAAC;EAC3CpC,uBAAuB,CAACY,MAAM,CAACwB,yBAAyB,EAAEtf,KAAK,CAAC;EAChE,IAAI8kB,SAAS,GAAG;IACd1kB,EAAE,EAAEJ,KAAK,CAACI,EAAE;IACZkhB,OAAO,EAAEthB,KAAK,CAACshB,OAAO;IACtBC,YAAY,EAAEvhB,KAAK,CAACuhB,YAAY;IAChCzd,MAAM,EAAE9D,KAAK,CAAC8D,MAAM;IACpBO,gBAAgB,EAAErE,KAAK,CAACqE,gBAAgB;IACxCyb,sBAAsB,EAAE9f,KAAK,CAAC8f,sBAAsB;IACpDtc,KAAK,EAAExD,KAAK,CAACwD,KAAK;IAClBK,MAAM,EAAE7D,KAAK,CAACM,YAAY,GAAG,OAAOsD,IAAI,EAAEmhB,WAAW,KAAK;MACxD,IAAI;QACF,IAAI1b,MAAM,GAAG,MAAMrJ,KAAK,CAACM,YAAY,CAAAP,aAAA,CAAAA,aAAA,KAChC6D,IAAI;UACPohB,YAAY,EAAEA,CAAA,KAAM;YAClBC,+BAA+B,CAC7B,QAAQ,EACRjlB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACO,SACR,CAAC;YACD,IAAIskB,kBAAkB,EAAE;cACtB,IAAIH,cAAc,EAAE;gBAClB,OAAO5L,WAAW;cACpB;cACA,IAAI6L,eAAe,EAAE;gBACnB,MAAMC,YAAY;cACpB;YACF;YACA,OAAOM,eAAe,CAACH,WAAW,CAAC;UACrC;QAAC,EACF,CAAC;QACF,OAAO1b,MAAM;MACf,CAAC,SAAS;QACRwb,kBAAkB,GAAG,KAAK;MAC5B;IACF,CAAC;IACC;IACA;IACA,CAACxN,CAAC,EAAE0N,WAAW,KAAKG,eAAe,CAACH,WAAW,CAChD;IACDphB,MAAM,EAAE3D,KAAK,CAACqhB,YAAY,GAAG,CAACzd,IAAI,EAAEmhB,WAAW,KAAK/kB,KAAK,CAACqhB,YAAY,CAAAthB,aAAA,CAAAA,aAAA,KACjE6D,IAAI;MACPuhB,YAAY,EAAE,MAAAA,CAAA,KAAY;QACxBF,+BAA+B,CAC7B,QAAQ,EACRjlB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACO,SACR,CAAC;QACD,OAAO,MAAM2kB,eAAe,CAACH,WAAW,CAAC;MAC3C;IAAC,EACF,CAAC,GAAG/kB,KAAK,CAACiE,SAAS,GAAG,CAACoT,CAAC,EAAE0N,WAAW,KAAKG,eAAe,CAACH,WAAW,CAAC,GAAG,MAAM;MAC9E,MAAMhnB,oBAAoB,CAAC,QAAQ,EAAEiC,KAAK,CAACI,EAAE,CAAC;IAChD,CAAC;IACDmD,IAAI,EAAEvD,KAAK,CAACuD,IAAI;IAChBQ,gBAAgB,EAAE/D,KAAK,CAAC+D,gBAAgB;IACxC;IACA;IACAxD,SAAS,EAAE,IAAI;IACf4D,eAAe,EAAEnE,KAAK,CAACM,YAAY,IAAI,IAAI;IAC3C2D,SAAS,EAAEjE,KAAK,CAACiE,SAAS;IAC1BC,eAAe,EAAElE,KAAK,CAACqhB,YAAY,IAAI,IAAI;IAC3CQ,mBAAmB,EAAE7hB,KAAK,CAAC+D,gBAAgB,IAAI;EACjD,CAAC;EACD,IAAI,OAAO+gB,SAAS,CAACjhB,MAAM,KAAK,UAAU,EAAE;IAC1CihB,SAAS,CAACjhB,MAAM,CAACzC,OAAO,GAAGhD,wBAAwB,CACjD4B,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACM,YAAY,EAClBN,KAAK,CAACO,SAAS,EACf,KACF,CAAC;EACH;EACA,OAAOukB,SAAS;AAClB;AACA,SAASI,eAAeA,CAACH,WAAW,EAAE;EACpCxnB,SAAS,CAAC,OAAOwnB,WAAW,KAAK,UAAU,EAAE,+BAA+B,CAAC;EAC7E,OAAOA,WAAW,CAAC,CAAC;AACtB;AACA,SAASE,+BAA+BA,CAAC5G,IAAI,EAAEne,OAAO,EAAEklB,UAAU,EAAE;EAClE,IAAI,CAACA,UAAU,EAAE;IACf,IAAIC,EAAE,GAAGhH,IAAI,KAAK,QAAQ,GAAG,gBAAgB,GAAG,gBAAgB;IAChE,IAAIiH,GAAG,6BAAAxb,MAAA,CAA6Bub,EAAE,8CAAAvb,MAAA,CAA2CuU,IAAI,mBAAAvU,MAAA,CAAe5J,OAAO,QAAI;IAC/GkN,OAAO,CAAC7G,KAAK,CAAC+e,GAAG,CAAC;IAClB,MAAM,IAAIvpB,iBAAiB,CAAC,GAAG,EAAE,aAAa,EAAE,IAAIsH,KAAK,CAACiiB,GAAG,CAAC,EAAE,IAAI,CAAC;EACvE;AACF;AACA,IAAI/B,SAAS,GAAG,eAAgB,IAAIhS,GAAG,CAAC,CAAC;AACzC,IAAIgU,sBAAsB,GAAG,GAAG;AAChC,IAAItF,eAAe,GAAG,eAAgB,IAAI1O,GAAG,CAAC,CAAC;AAC/C,IAAIiU,SAAS,GAAG,IAAI;AACpB,SAASC,cAAcA,CAACvO,KAAK,EAAE;EAAA,IAAAwO,sBAAA;EAC7B,IAAIxO,KAAK,CAACrQ,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;EACb;EACA,IAAIqQ,KAAK,CAACrQ,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAIxH,GAAG,IAAAyK,MAAA,CAAIoN,KAAK,CAAC,CAAC,CAAC,gBAAa4G,MAAM,CAAC5c,QAAQ,CAACoiB,MAAM,CAAC;EAChE;EACA,MAAMzF,SAAS,GAAGC,MAAM;EACxB,IAAIrS,QAAQ,GAAG,EAAAia,sBAAA,GAAC7H,SAAS,CAACS,uBAAuB,CAAC7S,QAAQ,cAAAia,sBAAA,cAAAA,sBAAA,GAAI,EAAE,EAAExnB,OAAO,CACvE,UAAU,EACV,EACF,CAAC;EACD,IAAIiB,GAAG,GAAG,IAAIE,GAAG,IAAAyK,MAAA,CAAI2B,QAAQ,iBAAcqS,MAAM,CAAC5c,QAAQ,CAACoiB,MAAM,CAAC;EAClEpM,KAAK,CAACyO,IAAI,CAAC,CAAC,CAACvX,OAAO,CAAE7K,IAAI,IAAKpE,GAAG,CAACiN,YAAY,CAACO,MAAM,CAAC,GAAG,EAAEpJ,IAAI,CAAC,CAAC;EAClE,OAAOpE,GAAG;AACZ;AACA,eAAe+gB,4BAA4BA,CAAChJ,KAAK,EAAEsG,wBAAwB,EAAEI,mBAAmB,EAAE7Q,MAAM,EAAE;EACxG,IAAI5N,GAAG,GAAGsmB,cAAc,CAACvO,KAAK,CAAC;EAC/B,IAAI/X,GAAG,IAAI,IAAI,EAAE;IACf;EACF;EACA,IAAIA,GAAG,CAACiK,QAAQ,CAAC,CAAC,CAACvC,MAAM,GAAG2e,SAAS,EAAE;IACrCjC,SAAS,CAACqC,KAAK,CAAC,CAAC;IACjB;EACF;EACA,IAAIvP,QAAQ,GAAG,MAAMuH,mBAAmB,CAAC,IAAI3Q,OAAO,CAAC9N,GAAG,EAAE;IAAE4N;EAAO,CAAC,CAAC,CAAC;EACtE,IAAI,CAACsJ,QAAQ,CAACxJ,IAAI,IAAIwJ,QAAQ,CAACpK,MAAM,GAAG,GAAG,IAAIoK,QAAQ,CAACpK,MAAM,IAAI,GAAG,EAAE;IACrE,MAAM,IAAI5I,KAAK,CAAC,mDAAmD,CAAC;EACtE;EACA,IAAI4Z,OAAO,GAAG,MAAMO,wBAAwB,CAACnH,QAAQ,CAACxJ,IAAI,EAAE;IAC1DsR,mBAAmB,EAAE,KAAK;EAC5B,CAAC,CAAC;EACF,IAAIlB,OAAO,CAACoB,IAAI,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAIhb,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EACA6T,KAAK,CAAC9I,OAAO,CAAEyX,CAAC,IAAKC,cAAc,CAACD,CAAC,EAAE5F,eAAe,CAAC,CAAC;EACxDhD,OAAO,CAAChG,OAAO,CAAC7I,OAAO,CAAEyX,CAAC,IAAK;IAAA,IAAAE,WAAA;IAC7BjI,MAAM,CAACQ,uBAAuB,CAACQ,WAAW,EAAAiH,WAAA,GACxCF,CAAC,CAAC1iB,QAAQ,cAAA4iB,WAAA,cAAAA,WAAA,GAAI,IAAI,EAClB,CAAChH,6BAA6B,CAAC8G,CAAC,CAAC,CACnC,CAAC;EACH,CAAC,CAAC;AACJ;AACA,SAASC,cAAcA,CAACviB,IAAI,EAAEyiB,KAAK,EAAE;EACnC,IAAIA,KAAK,CAACC,IAAI,IAAIV,sBAAsB,EAAE;IACxC,IAAIW,KAAK,GAAGF,KAAK,CAAC7X,MAAM,CAAC,CAAC,CAACgY,IAAI,CAAC,CAAC,CAACtlB,KAAK;IACvCmlB,KAAK,CAAC1Z,MAAM,CAAC4Z,KAAK,CAAC;EACrB;EACAF,KAAK,CAACxO,GAAG,CAACjU,IAAI,CAAC;AACjB;AACA,SAASsgB,QAAQA,CAACuC,QAAQ,EAAEC,IAAI,EAAE;EAChC,IAAInS,SAAS;EACb,OAAO,YAAa;IAAA,SAAAoS,IAAA,GAAApf,SAAA,CAAAL,MAAA,EAATjD,IAAI,OAAAuZ,KAAA,CAAAmJ,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;MAAJ3iB,IAAI,CAAA2iB,IAAA,IAAArf,SAAA,CAAAqf,IAAA;IAAA;IACbzI,MAAM,CAACxJ,YAAY,CAACJ,SAAS,CAAC;IAC9BA,SAAS,GAAG4J,MAAM,CAAC3J,UAAU,CAAC,MAAMiS,QAAQ,CAAC,GAAGxiB,IAAI,CAAC,EAAEyiB,IAAI,CAAC;EAC9D,CAAC;AACH;;AAEA;AACA,OAAO,KAAKG,MAAM,MAAM,OAAO;;AAE/B;AACA,IAAIC,QAAQ,GAAG,IAAIxhB,WAAW,CAAC,CAAC;AAChC,IAAIyhB,OAAO,GAAG,gBAAgB;AAC9B,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EACnC,IAAIC,OAAO,GAAG,IAAIllB,WAAW,CAAC,CAAC;EAC/B,IAAImlB,wBAAwB;EAC5B,IAAIC,iBAAiB,GAAG,IAAIC,OAAO,CAChCC,OAAO,IAAKH,wBAAwB,GAAGG,OAC1C,CAAC;EACD,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,OAAO,GAAG,IAAI;EAClB,SAASC,mBAAmBA,CAACtY,UAAU,EAAE;IACvC,KAAK,IAAIuY,KAAK,IAAIH,QAAQ,EAAE;MAC1B,IAAII,GAAG,GAAGV,OAAO,CAACW,MAAM,CAACF,KAAK,EAAE;QAAE1Y,MAAM,EAAE;MAAK,CAAC,CAAC;MACjD,IAAI2Y,GAAG,CAAC1R,QAAQ,CAAC6Q,OAAO,CAAC,EAAE;QACzBa,GAAG,GAAGA,GAAG,CAACrhB,KAAK,CAAC,CAAC,EAAE,CAACwgB,OAAO,CAAC7f,MAAM,CAAC;MACrC;MACAkI,UAAU,CAACC,OAAO,CAACyX,QAAQ,CAACtpB,MAAM,CAACoqB,GAAG,CAAC,CAAC;IAC1C;IACAJ,QAAQ,CAACtgB,MAAM,GAAG,CAAC;IACnBugB,OAAO,GAAG,IAAI;EAChB;EACA,OAAO,IAAIK,eAAe,CAAC;IACzBC,SAASA,CAACJ,KAAK,EAAEvY,UAAU,EAAE;MAC3BoY,QAAQ,CAAC1a,IAAI,CAAC6a,KAAK,CAAC;MACpB,IAAIF,OAAO,EAAE;QACX;MACF;MACAA,OAAO,GAAGjT,UAAU,CAAC,YAAY;QAC/BkT,mBAAmB,CAACtY,UAAU,CAAC;QAC/B,IAAI,CAACmY,UAAU,EAAE;UACfA,UAAU,GAAG,IAAI;UACjBS,cAAc,CAACf,SAAS,EAAE7X,UAAU,CAAC,CAAC6Y,KAAK,CAAEhV,GAAG,IAAK7D,UAAU,CAACxI,KAAK,CAACqM,GAAG,CAAC,CAAC,CAACiV,IAAI,CAACf,wBAAwB,CAAC;QAC5G;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IACD,MAAMgB,KAAKA,CAAC/Y,UAAU,EAAE;MACtB,MAAMgY,iBAAiB;MACvB,IAAIK,OAAO,EAAE;QACX9S,YAAY,CAAC8S,OAAO,CAAC;QACrBC,mBAAmB,CAACtY,UAAU,CAAC;MACjC;MACAA,UAAU,CAACC,OAAO,CAACyX,QAAQ,CAACtpB,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACvD;EACF,CAAC,CAAC;AACJ;AACA,eAAewqB,cAAcA,CAACf,SAAS,EAAE7X,UAAU,EAAE;EACnD,IAAI8X,OAAO,GAAG,IAAIllB,WAAW,CAAC,OAAO,EAAE;IAAEomB,KAAK,EAAE;EAAK,CAAC,CAAC;EACvD,MAAMvmB,MAAM,GAAGolB,SAAS,CAACnlB,SAAS,CAAC,CAAC;EACpC,IAAI;IACF,IAAIumB,IAAI;IACR,OAAO,CAACA,IAAI,GAAG,MAAMxmB,MAAM,CAACwmB,IAAI,CAAC,CAAC,KAAK,CAACA,IAAI,CAACC,IAAI,EAAE;MACjD,MAAMX,KAAK,GAAGU,IAAI,CAACnnB,KAAK;MACxB,IAAI;QACFqnB,UAAU,CACRnf,IAAI,CAACC,SAAS,CAAC6d,OAAO,CAACW,MAAM,CAACF,KAAK,EAAE;UAAE1Y,MAAM,EAAE;QAAK,CAAC,CAAC,CAAC,EACvDG,UACF,CAAC;MACH,CAAC,CAAC,OAAO6D,GAAG,EAAE;QACZ,IAAIuV,MAAM,GAAGpf,IAAI,CAACC,SAAS,CAACrD,IAAI,CAACC,MAAM,CAACwiB,aAAa,CAAC,GAAGd,KAAK,CAAC,CAAC,CAAC;QACjEY,UAAU,yBAAApe,MAAA,CACgBqe,MAAM,gCAC9BpZ,UACF,CAAC;MACH;IACF;EACF,CAAC,SAAS;IACRvN,MAAM,CAAC6mB,WAAW,CAAC,CAAC;EACtB;EACA,IAAIC,SAAS,GAAGzB,OAAO,CAACW,MAAM,CAAC,CAAC;EAChC,IAAIc,SAAS,CAACzhB,MAAM,EAAE;IACpBqhB,UAAU,CAACnf,IAAI,CAACC,SAAS,CAACsf,SAAS,CAAC,EAAEvZ,UAAU,CAAC;EACnD;AACF;AACA,SAASmZ,UAAUA,CAACZ,KAAK,EAAEvY,UAAU,EAAE;EACrCA,UAAU,CAACC,OAAO,CAChByX,QAAQ,CAACtpB,MAAM,YAAA2M,MAAA,CACFye,YAAY,mCAAAze,MAAA,CACawd,KAAK,MACzC,CAAC,cACH,CACF,CAAC;AACH;AACA,SAASiB,YAAYA,CAACC,MAAM,EAAE;EAC5B,OAAOA,MAAM,CAACtqB,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,eAAe,EAAE,QAAQ,CAAC;AAC7E;;AAEA;AACA,IAAIuqB,SAAS,GAAG,KAAK;AACrB,IAAIC,OAAO,GAAGlC,MAAM,CAACiC,SAAS,CAAC;AAC/B,SAASE,OAAOA,CAACC,OAAO,EAAE;EACxB,IAAIF,OAAO,EAAE;IACX,OAAOA,OAAO,CAACE,OAAO,CAAC;EACzB;EACA,MAAM,IAAIvlB,KAAK,CAAC,sDAAsD,CAAC;AACzE;AACA,eAAewlB,qBAAqBA,CAAAC,MAAA,EAMjC;EAAA,IANkC;IACnCjd,OAAO;IACPkd,WAAW;IACXvL,wBAAwB;IACxBwL,UAAU;IACV5nB,OAAO,GAAG;EACZ,CAAC,GAAA0nB,MAAA;EACC,MAAM3pB,GAAG,GAAG,IAAIE,GAAG,CAACwM,OAAO,CAAC1M,GAAG,CAAC;EAChC,MAAM8pB,aAAa,GAAGC,oBAAoB,CAAC/pB,GAAG,CAAC;EAC/C,MAAMgqB,qBAAqB,GAAGF,aAAa,IAAIG,iBAAiB,CAACjqB,GAAG,CAAC,IAAI0M,OAAO,CAACiB,OAAO,CAAC2E,GAAG,CAAC,eAAe,CAAC;EAC7G,MAAM4X,cAAc,GAAG,MAAMN,WAAW,CAACld,OAAO,CAAC;EACjD,IAAIsd,qBAAqB,IAAIE,cAAc,CAACvc,OAAO,CAACkB,GAAG,CAAC,uBAAuB,CAAC,KAAK,MAAM,EAAE;IAC3F,OAAOqb,cAAc;EACvB;EACA,IAAI,CAACA,cAAc,CAACxc,IAAI,EAAE;IACxB,MAAM,IAAIxJ,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACA,IAAIimB,eAAe,GAAG,IAAI;EAC1B,IAAIloB,OAAO,EAAE;IACXkoB,eAAe,GAAGD,cAAc,CAACE,KAAK,CAAC,CAAC;EAC1C;EACA,MAAM1c,IAAI,GAAGwc,cAAc,CAACxc,IAAI;EAChC,IAAI2c,cAAc;EAClB,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAID,cAAc,EAAE,OAAOA,cAAc;IACzCA,cAAc,GAAGhM,wBAAwB,CAAC3Q,IAAI,CAAC;IAC/C,OAAO2c,cAAc;EACvB,CAAC;EACD,IAAI;IAAA,IAAAE,iBAAA;IACF,MAAMzM,OAAO,GAAG,MAAMwM,UAAU,CAAC,CAAC;IAClC,IAAIJ,cAAc,CAACpd,MAAM,KAAK3P,4BAA4B,IAAI2gB,OAAO,CAACoB,IAAI,KAAK,UAAU,EAAE;MAAA,IAAAsL,gBAAA;MACzF,MAAMzY,QAAQ,GAAG,IAAIN,OAAO,CAACyY,cAAc,CAACvc,OAAO,CAAC;MACpDoE,QAAQ,CAAC5E,MAAM,CAAC,kBAAkB,CAAC;MACnC4E,QAAQ,CAAC5E,MAAM,CAAC,gBAAgB,CAAC;MACjC4E,QAAQ,CAAC5E,MAAM,CAAC,cAAc,CAAC;MAC/B4E,QAAQ,CAAC5E,MAAM,CAAC,kBAAkB,CAAC;MACnC4E,QAAQ,CAACsC,GAAG,CAAC,UAAU,EAAEyJ,OAAO,CAAC/b,QAAQ,CAAC;MAC1C,OAAO,IAAIgL,QAAQ,CAAC,EAAAyd,gBAAA,GAAAL,eAAe,cAAAK,gBAAA,uBAAfA,gBAAA,CAAiB9c,IAAI,KAAI,EAAE,EAAE;QAC/CC,OAAO,EAAEoE,QAAQ;QACjBjF,MAAM,EAAEgR,OAAO,CAAChR,MAAM;QACtBwI,UAAU,EAAE4U,cAAc,CAAC5U;MAC7B,CAAC,CAAC;IACJ;IACA,MAAMhF,IAAI,GAAG,MAAMuZ,UAAU,CAACS,UAAU,CAAC;IACzC,MAAM3c,OAAO,GAAG,IAAI8D,OAAO,CAACyY,cAAc,CAACvc,OAAO,CAAC;IACnDA,OAAO,CAAC0G,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;IACxC,IAAI,CAACpS,OAAO,EAAE;MACZ,OAAO,IAAI8K,QAAQ,CAACuD,IAAI,EAAE;QACxBxD,MAAM,EAAEod,cAAc,CAACpd,MAAM;QAC7Ba;MACF,CAAC,CAAC;IACJ;IACA,IAAI,GAAA4c,iBAAA,GAACJ,eAAe,cAAAI,iBAAA,eAAfA,iBAAA,CAAiB7c,IAAI,GAAE;MAC1B,MAAM,IAAIxJ,KAAK,CAAC,iCAAiC,CAAC;IACpD;IACA,MAAMumB,KAAK,GAAGna,IAAI,CAACoa,WAAW,CAAClD,gBAAgB,CAAC2C,eAAe,CAACzc,IAAI,CAAC,CAAC;IACtE,OAAO,IAAIX,QAAQ,CAAC0d,KAAK,EAAE;MACzB3d,MAAM,EAAEod,cAAc,CAACpd,MAAM;MAC7Ba;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOgd,MAAM,EAAE;IACf,IAAIA,MAAM,YAAY5d,QAAQ,EAAE;MAC9B,OAAO4d,MAAM;IACf;IACA,MAAMA,MAAM;EACd;AACF;AACA,SAASC,eAAeA,CAAAC,MAAA,EAAiB;EAAA,IAAhB;IAAEP;EAAW,CAAC,GAAAO,MAAA;EACrC,MAAM/M,OAAO,GAAG0L,OAAO,CAACc,UAAU,CAAC,CAAC,CAAC;EACrC,IAAIxM,OAAO,CAACoB,IAAI,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAInS,QAAQ,CAAC,IAAI,EAAE;MACvBD,MAAM,EAAEgR,OAAO,CAAChR,MAAM;MACtBa,OAAO,EAAE;QACPmd,QAAQ,EAAEhN,OAAO,CAAC/b;MACpB;IACF,CAAC,CAAC;EACJ;EACA,IAAI+b,OAAO,CAACoB,IAAI,KAAK,QAAQ,EAAE,OAAO,IAAI;EAC1C,IAAI6L,iBAAiB,GAAAnqB,aAAA,KAAQkd,OAAO,CAACnd,UAAU,CAAE;EACjD,KAAK,MAAME,KAAK,IAAIid,OAAO,CAAChd,OAAO,EAAE;IACnC,IAAI7B,wBAAwB,CAC1B4B,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACM,YAAY,EAClBN,KAAK,CAACO,SAAS,EACf,KACF,CAAC,KAAKP,KAAK,CAAC8f,sBAAsB,IAAI,CAAC9f,KAAK,CAACO,SAAS,CAAC,EAAE;MACvD,OAAO2pB,iBAAiB,CAAClqB,KAAK,CAACI,EAAE,CAAC;IACpC;EACF;EACA,MAAMlB,OAAO,GAAG;IACdqR,UAAU,EAAE0M,OAAO,CAAC1M,UAAU;IAC9BD,aAAa,EAAE,CAAC,CAAC;IACjB7E,QAAQ,EAAEwR,OAAO,CAACxR,QAAQ;IAC1Bb,MAAM,EAAEqS,OAAO,CAACrS,MAAM;IACtB9K,UAAU,EAAEoqB,iBAAiB;IAC7B1Z,aAAa,EAAE,CAAC,CAAC;IACjBtP,QAAQ,EAAE+b,OAAO,CAAC/b,QAAQ;IAC1ByR,UAAU,EAAE,GAAG;IACf1S,OAAO,EAAEgd,OAAO,CAAChd,OAAO,CAACmD,GAAG,CAAEpD,KAAK,KAAM;MACvC0L,MAAM,EAAE1L,KAAK,CAAC0L,MAAM;MACpBF,QAAQ,EAAExL,KAAK,CAACwL,QAAQ;MACxB2e,YAAY,EAAEnqB,KAAK,CAACmqB,YAAY;MAChChqB,KAAK,EAAE;QACLC,EAAE,EAAEJ,KAAK,CAACI,EAAE;QACZuD,MAAM,EAAE3D,KAAK,CAACiE,SAAS,IAAI,CAAC,CAACjE,KAAK,CAACqhB,YAAY;QAC/Cvd,MAAM,EAAE9D,KAAK,CAAC8D,MAAM;QACpBO,gBAAgB,EAAErE,KAAK,CAACqE,gBAAgB;QACxCR,MAAM,EAAE7D,KAAK,CAACO,SAAS,IAAI,CAAC,CAACP,KAAK,CAACM,YAAY;QAC/CkD,KAAK,EAAExD,KAAK,CAACwD,KAAK;QAClBD,IAAI,EAAEvD,KAAK,CAACuD,IAAI;QAChBQ,gBAAgB,EAAE/D,KAAK,CAAC+D;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EACD,MAAMtD,MAAM,GAAGxD,kBAAkB,CAC/BggB,OAAO,CAAChd,OAAO,CAACwf,WAAW,CAAC,CAACC,QAAQ,EAAE1f,KAAK,KAAK;IAC/C,MAAMG,KAAK,GAAG;MACZC,EAAE,EAAEJ,KAAK,CAACI,EAAE;MACZuD,MAAM,EAAE3D,KAAK,CAACiE,SAAS,IAAI,CAAC,CAACjE,KAAK,CAACqhB,YAAY;MAC/CC,OAAO,EAAEthB,KAAK,CAACshB,OAAO;MACtBC,YAAY,EAAEvhB,KAAK,CAACuhB,YAAY;MAChCzd,MAAM,EAAE9D,KAAK,CAAC8D,MAAM;MACpBO,gBAAgB,EAAE,CAAC,CAACrE,KAAK,CAACuhB,YAAY;MACtCzB,sBAAsB,EAAE9f,KAAK,CAAC8f,sBAAsB;MACpDtc,KAAK,EAAExD,KAAK,CAACwD,KAAK;MAClBK,MAAM,EAAE7D,KAAK,CAACO,SAAS,IAAI,CAAC,CAACP,KAAK,CAACM,YAAY;MAC/CiD,IAAI,EAAEvD,KAAK,CAACuD,IAAI;MAChBQ,gBAAgB,EAAE/D,KAAK,CAAC+D;IAC1B,CAAC;IACD,IAAI2b,QAAQ,CAAC7Y,MAAM,GAAG,CAAC,EAAE;MACvB1G,KAAK,CAAC0E,QAAQ,GAAG6a,QAAQ;IAC3B;IACA,OAAO,CAACvf,KAAK,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC,EACNjB,OACF,CAAC;EACD,MAAMolB,gBAAgB,GAAG;IACvB3kB,MAAM,EAAE;MACN;MACA;MACA8C,mBAAmB,EAAE,KAAK;MAC1BD,6BAA6B,EAAE;IACjC,CAAC;IACD5C,SAAS,EAAE,KAAK;IAChBkB,GAAG,EAAE,IAAI;IACTtB,WAAW,EAAE,EAAE;IACfF,QAAQ,EAAE;MACRI,MAAM,EAAE,CAAC,CAAC;MACVmD,OAAO,EAAE,GAAG;MACZ1D,GAAG,EAAE,EAAE;MACPuD,KAAK,EAAE;QACLE,MAAM,EAAE,EAAE;QACVD,OAAO,EAAE;MACX;IACF,CAAC;IACD5B,cAAc,EAAE;MAAE+B,IAAI,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAc,CAAC;IAC7DxD,YAAY,EAAEyd,qBAAqB,CAACC,OAAO;EAC7C,CAAC;EACD,OAAO,eAAgBuJ,MAAM,CAAC9lB,aAAa,CAACvE,gBAAgB,CAACyE,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAAE,eAAgB2lB,MAAM,CAAC9lB,aAAa,CAACwa,4BAA4B,EAAE;IAAEha,QAAQ,EAAE+b,OAAO,CAAC/b;EAAS,CAAC,EAAE,eAAgBslB,MAAM,CAAC9lB,aAAa,CAAC1E,gBAAgB,CAAC4E,QAAQ,EAAE;IAAEC,KAAK,EAAEyjB;EAAiB,CAAC,EAAE,eAAgBkC,MAAM,CAAC9lB,aAAa,CAC1TlE,oBAAoB,EACpB;IACE0C,OAAO;IACPuB,MAAM;IACNW,OAAO,EAAE,KAAK;IACdhC,KAAK,EAAE6d,OAAO,CAAC7d;EACjB,CACF,CAAC,CAAC,CAAC,CAAC;AACN;AACA,SAAS8pB,oBAAoBA,CAAC/pB,GAAG,EAAE;EACjC,OAAOA,GAAG,CAACqM,QAAQ,CAACqK,QAAQ,CAAC,MAAM,CAAC;AACtC;AACA,SAASuT,iBAAiBA,CAACjqB,GAAG,EAAE;EAC9B,OAAOA,GAAG,CAACqM,QAAQ,CAACqK,QAAQ,CAAC,WAAW,CAAC;AAC3C;;AAEA;AACA,SAASuU,YAAYA,CAAA,EAAG;EACtB,IAAIC,QAAQ,GAAG,IAAIplB,WAAW,CAAC,CAAC;EAChC,IAAIqlB,gBAAgB,GAAG,IAAI;EAC3B,IAAI1D,SAAS,GAAG,IAAI/X,cAAc,CAAC;IACjCC,KAAKA,CAACC,UAAU,EAAE;MAChB,IAAI,OAAO+O,MAAM,KAAK,WAAW,EAAE;QACjC;MACF;MACA,IAAIyM,WAAW,GAAIjD,KAAK,IAAK;QAC3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC7BvY,UAAU,CAACC,OAAO,CAACqb,QAAQ,CAACltB,MAAM,CAACmqB,KAAK,CAAC,CAAC;QAC5C,CAAC,MAAM;UACLvY,UAAU,CAACC,OAAO,CAACsY,KAAK,CAAC;QAC3B;MACF,CAAC;MACDxJ,MAAM,CAAC0M,aAAa,KAAK1M,MAAM,CAAC0M,aAAa,GAAG,EAAE,CAAC;MACnD1M,MAAM,CAAC0M,aAAa,CAACpc,OAAO,CAACmc,WAAW,CAAC;MACzCzM,MAAM,CAAC0M,aAAa,CAAC/d,IAAI,GAAI6a,KAAK,IAAK;QACrCiD,WAAW,CAACjD,KAAK,CAAC;QAClB,OAAO,CAAC;MACV,CAAC;MACDgD,gBAAgB,GAAGvb,UAAU;IAC/B;EACF,CAAC,CAAC;EACF,IAAI,OAAO0U,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAACgH,UAAU,KAAK,SAAS,EAAE;IACxEhH,QAAQ,CAACpP,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;MAAA,IAAAqW,iBAAA;MAClD,CAAAA,iBAAA,GAAAJ,gBAAgB,cAAAI,iBAAA,eAAhBA,iBAAA,CAAkBzb,KAAK,CAAC,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,MAAM;IAAA,IAAA0b,kBAAA;IACL,CAAAA,kBAAA,GAAAL,gBAAgB,cAAAK,kBAAA,eAAhBA,kBAAA,CAAkB1b,KAAK,CAAC,CAAC;EAC3B;EACA,OAAO2X,SAAS;AAClB;;AAEA;AACA,SAASgE,iBAAiBA,CAAChgB,MAAM,EAAE;EACjC,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIC,OAAO,GAAGb,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC;EACpC,IAAIO,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI,CAAC9F,GAAG,EAAE+F,GAAG,CAAC,IAAIP,OAAO,EAAE;IAC9B,IAAIO,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,oBAAoB,EAAE;MAC9CF,UAAU,CAAC9F,GAAG,CAAC,GAAG,IAAItJ,iBAAiB,CACrCqP,GAAG,CAACa,MAAM,EACVb,GAAG,CAACqJ,UAAU,EACdrJ,GAAG,CAACgE,IAAI,EACRhE,GAAG,CAACyf,QAAQ,KAAK,IACnB,CAAC;IACH,CAAC,MAAM,IAAIzf,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,OAAO,EAAE;MACxC,IAAID,GAAG,CAACE,SAAS,EAAE;QACjB,IAAIwf,gBAAgB,GAAGhN,MAAM,CAAC1S,GAAG,CAACE,SAAS,CAAC;QAC5C,IAAI,OAAOwf,gBAAgB,KAAK,UAAU,EAAE;UAC1C,IAAI;YACF,IAAIvkB,KAAK,GAAG,IAAIukB,gBAAgB,CAAC1f,GAAG,CAACH,OAAO,CAAC;YAC7C1E,KAAK,CAACmE,KAAK,GAAGU,GAAG,CAACV,KAAK;YACvBS,UAAU,CAAC9F,GAAG,CAAC,GAAGkB,KAAK;UACzB,CAAC,CAAC,OAAO0H,CAAC,EAAE,CACZ;QACF;MACF;MACA,IAAI9C,UAAU,CAAC9F,GAAG,CAAC,IAAI,IAAI,EAAE;QAC3B,IAAIkB,KAAK,GAAG,IAAIlD,KAAK,CAAC+H,GAAG,CAACH,OAAO,CAAC;QAClC1E,KAAK,CAACmE,KAAK,GAAGU,GAAG,CAACV,KAAK;QACvBS,UAAU,CAAC9F,GAAG,CAAC,GAAGkB,KAAK;MACzB;IACF,CAAC,MAAM;MACL4E,UAAU,CAAC9F,GAAG,CAAC,GAAG+F,GAAG;IACvB;EACF;EACA,OAAOD,UAAU;AACnB;AAEA,SACEnM,YAAY,EACZ6C,gBAAgB,EAChBmF,YAAY,EACZuB,QAAQ,EACR6B,UAAU,EACVkD,iBAAiB,EACjB2H,oBAAoB,EACpB4D,aAAa,EACbK,SAAS,EACTC,oBAAoB,EACpBY,0BAA0B,EAC1BG,0BAA0B,EAC1BhN,IAAI,EACJyN,gBAAgB,EAChBkC,2BAA2B,EAC3BS,gBAAgB,EAChBgF,iBAAiB,EACjBuG,qBAAqB,EACrBkB,eAAe,EACfK,YAAY,EACZQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}