{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getLatitude = _interopRequireDefault(require(\"./getLatitude\"));\nvar _getLongitude = _interopRequireDefault(require(\"./getLongitude\"));\nvar _toRad = _interopRequireDefault(require(\"./toRad\"));\nvar _toDeg = _interopRequireDefault(require(\"./toDeg\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar getCenter = function getCenter(points) {\n  if (Array.isArray(points) === false || points.length === 0) {\n    return false;\n  }\n  var numberOfPoints = points.length;\n  var sum = points.reduce(function (acc, point) {\n    var pointLat = (0, _toRad.default)((0, _getLatitude.default)(point));\n    var pointLon = (0, _toRad.default)((0, _getLongitude.default)(point));\n    return {\n      X: acc.X + Math.cos(pointLat) * Math.cos(pointLon),\n      Y: acc.Y + Math.cos(pointLat) * Math.sin(pointLon),\n      Z: acc.Z + Math.sin(pointLat)\n    };\n  }, {\n    X: 0,\n    Y: 0,\n    Z: 0\n  });\n  var X = sum.X / numberOfPoints;\n  var Y = sum.Y / numberOfPoints;\n  var Z = sum.Z / numberOfPoints;\n  return {\n    longitude: (0, _toDeg.default)(Math.atan2(Y, X)),\n    latitude: (0, _toDeg.default)(Math.atan2(Z, Math.sqrt(X * X + Y * Y)))\n  };\n};\nvar _default = getCenter;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getLatitude", "_interopRequireDefault", "require", "_getLongitude", "_toRad", "_toDeg", "obj", "__esModule", "getCenter", "points", "Array", "isArray", "length", "numberOfPoints", "sum", "reduce", "acc", "point", "pointLat", "pointLon", "X", "Math", "cos", "Y", "sin", "Z", "longitude", "atan2", "latitude", "sqrt", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getCenter.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getLatitude=_interopRequireDefault(require(\"./getLatitude\"));var _getLongitude=_interopRequireDefault(require(\"./getLongitude\"));var _toRad=_interopRequireDefault(require(\"./toRad\"));var _toDeg=_interopRequireDefault(require(\"./toDeg\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var getCenter=function getCenter(points){if(Array.isArray(points)===false||points.length===0){return false}var numberOfPoints=points.length;var sum=points.reduce(function(acc,point){var pointLat=(0,_toRad.default)((0,_getLatitude.default)(point));var pointLon=(0,_toRad.default)((0,_getLongitude.default)(point));return{X:acc.X+Math.cos(pointLat)*Math.cos(pointLon),Y:acc.Y+Math.cos(pointLat)*Math.sin(pointLon),Z:acc.Z+Math.sin(pointLat)}},{X:0,Y:0,Z:0});var X=sum.X/numberOfPoints;var Y=sum.Y/numberOfPoints;var Z=sum.Z/numberOfPoints;return{longitude:(0,_toDeg.default)(Math.atan2(Y,X)),latitude:(0,_toDeg.default)(Math.atan2(Z,Math.sqrt(X*X+Y*Y)))}};var _default=getCenter;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,IAAIC,aAAa,GAACF,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAAC,IAAIE,MAAM,GAACH,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAAC,IAAIG,MAAM,GAACJ,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACK,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACP,OAAO,EAACO;EAAG,CAAC;AAAA;AAAC,IAAIE,SAAS,GAAC,SAASA,SAASA,CAACC,MAAM,EAAC;EAAC,IAAGC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,KAAG,KAAK,IAAEA,MAAM,CAACG,MAAM,KAAG,CAAC,EAAC;IAAC,OAAO,KAAK;EAAA;EAAC,IAAIC,cAAc,GAACJ,MAAM,CAACG,MAAM;EAAC,IAAIE,GAAG,GAACL,MAAM,CAACM,MAAM,CAAC,UAASC,GAAG,EAACC,KAAK,EAAC;IAAC,IAAIC,QAAQ,GAAC,CAAC,CAAC,EAACd,MAAM,CAACL,OAAO,EAAE,CAAC,CAAC,EAACC,YAAY,CAACD,OAAO,EAAEkB,KAAK,CAAC,CAAC;IAAC,IAAIE,QAAQ,GAAC,CAAC,CAAC,EAACf,MAAM,CAACL,OAAO,EAAE,CAAC,CAAC,EAACI,aAAa,CAACJ,OAAO,EAAEkB,KAAK,CAAC,CAAC;IAAC,OAAM;MAACG,CAAC,EAACJ,GAAG,CAACI,CAAC,GAACC,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAC,GAACG,IAAI,CAACC,GAAG,CAACH,QAAQ,CAAC;MAACI,CAAC,EAACP,GAAG,CAACO,CAAC,GAACF,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAC,GAACG,IAAI,CAACG,GAAG,CAACL,QAAQ,CAAC;MAACM,CAAC,EAACT,GAAG,CAACS,CAAC,GAACJ,IAAI,CAACG,GAAG,CAACN,QAAQ;IAAC,CAAC;EAAA,CAAC,EAAC;IAACE,CAAC,EAAC,CAAC;IAACG,CAAC,EAAC,CAAC;IAACE,CAAC,EAAC;EAAC,CAAC,CAAC;EAAC,IAAIL,CAAC,GAACN,GAAG,CAACM,CAAC,GAACP,cAAc;EAAC,IAAIU,CAAC,GAACT,GAAG,CAACS,CAAC,GAACV,cAAc;EAAC,IAAIY,CAAC,GAACX,GAAG,CAACW,CAAC,GAACZ,cAAc;EAAC,OAAM;IAACa,SAAS,EAAC,CAAC,CAAC,EAACrB,MAAM,CAACN,OAAO,EAAEsB,IAAI,CAACM,KAAK,CAACJ,CAAC,EAACH,CAAC,CAAC,CAAC;IAACQ,QAAQ,EAAC,CAAC,CAAC,EAACvB,MAAM,CAACN,OAAO,EAAEsB,IAAI,CAACM,KAAK,CAACF,CAAC,EAACJ,IAAI,CAACQ,IAAI,CAACT,CAAC,GAACA,CAAC,GAACG,CAAC,GAACA,CAAC,CAAC,CAAC;EAAC,CAAC;AAAA,CAAC;AAAC,IAAIO,QAAQ,GAACtB,SAAS;AAACX,OAAO,CAACE,OAAO,GAAC+B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}