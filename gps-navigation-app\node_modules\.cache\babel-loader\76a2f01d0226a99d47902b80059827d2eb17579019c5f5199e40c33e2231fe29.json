{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _getLatitude = _interopRequireDefault(require(\"./getLatitude\"));\nvar _getLongitude = _interopRequireDefault(require(\"./getLongitude\"));\nvar _toRad = _interopRequireDefault(require(\"./toRad\"));\nvar _toDeg = _interopRequireDefault(require(\"./toDeg\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar getGreatCircleBearing = function getGreatCircleBearing(origin, dest) {\n  var destLat = (0, _getLatitude.default)(dest);\n  var detLon = (0, _getLongitude.default)(dest);\n  var originLat = (0, _getLatitude.default)(origin);\n  var originLon = (0, _getLongitude.default)(origin);\n  var bearing = ((0, _toDeg.default)(Math.atan2(Math.sin((0, _toRad.default)(detLon) - (0, _toRad.default)(originLon)) * Math.cos((0, _toRad.default)(destLat)), Math.cos((0, _toRad.default)(originLat)) * Math.sin((0, _toRad.default)(destLat)) - Math.sin((0, _toRad.default)(originLat)) * Math.cos((0, _toRad.default)(destLat)) * Math.cos((0, _toRad.default)(detLon) - (0, _toRad.default)(originLon)))) + 360) % 360;\n  return bearing;\n};\nvar _default = getGreatCircleBearing;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_getLatitude", "_interopRequireDefault", "require", "_getLongitude", "_toRad", "_toDeg", "obj", "__esModule", "getGreatCircleBearing", "origin", "dest", "destLat", "detLon", "originLat", "originLon", "bearing", "Math", "atan2", "sin", "cos", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getGreatCircleBearing.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _getLatitude=_interopRequireDefault(require(\"./getLatitude\"));var _getLongitude=_interopRequireDefault(require(\"./getLongitude\"));var _toRad=_interopRequireDefault(require(\"./toRad\"));var _toDeg=_interopRequireDefault(require(\"./toDeg\"));function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var getGreatCircleBearing=function getGreatCircleBearing(origin,dest){var destLat=(0,_getLatitude.default)(dest);var detLon=(0,_getLongitude.default)(dest);var originLat=(0,_getLatitude.default)(origin);var originLon=(0,_getLongitude.default)(origin);var bearing=((0,_toDeg.default)(Math.atan2(Math.sin((0,_toRad.default)(detLon)-(0,_toRad.default)(originLon))*Math.cos((0,_toRad.default)(destLat)),Math.cos((0,_toRad.default)(originLat))*Math.sin((0,_toRad.default)(destLat))-Math.sin((0,_toRad.default)(originLat))*Math.cos((0,_toRad.default)(destLat))*Math.cos((0,_toRad.default)(detLon)-(0,_toRad.default)(originLon))))+360)%360;return bearing};var _default=getGreatCircleBearing;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,YAAY,GAACC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAAC,IAAIC,aAAa,GAACF,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAAC,IAAIE,MAAM,GAACH,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAAC,IAAIG,MAAM,GAACJ,sBAAsB,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;AAAC,SAASD,sBAAsBA,CAACK,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACP,OAAO,EAACO;EAAG,CAAC;AAAA;AAAC,IAAIE,qBAAqB,GAAC,SAASA,qBAAqBA,CAACC,MAAM,EAACC,IAAI,EAAC;EAAC,IAAIC,OAAO,GAAC,CAAC,CAAC,EAACX,YAAY,CAACD,OAAO,EAAEW,IAAI,CAAC;EAAC,IAAIE,MAAM,GAAC,CAAC,CAAC,EAACT,aAAa,CAACJ,OAAO,EAAEW,IAAI,CAAC;EAAC,IAAIG,SAAS,GAAC,CAAC,CAAC,EAACb,YAAY,CAACD,OAAO,EAAEU,MAAM,CAAC;EAAC,IAAIK,SAAS,GAAC,CAAC,CAAC,EAACX,aAAa,CAACJ,OAAO,EAAEU,MAAM,CAAC;EAAC,IAAIM,OAAO,GAAC,CAAC,CAAC,CAAC,EAACV,MAAM,CAACN,OAAO,EAAEiB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,EAACd,MAAM,CAACL,OAAO,EAAEa,MAAM,CAAC,GAAC,CAAC,CAAC,EAACR,MAAM,CAACL,OAAO,EAAEe,SAAS,CAAC,CAAC,GAACE,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,EAACf,MAAM,CAACL,OAAO,EAAEY,OAAO,CAAC,CAAC,EAACK,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,EAACf,MAAM,CAACL,OAAO,EAAEc,SAAS,CAAC,CAAC,GAACG,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,EAACd,MAAM,CAACL,OAAO,EAAEY,OAAO,CAAC,CAAC,GAACK,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,EAACd,MAAM,CAACL,OAAO,EAAEc,SAAS,CAAC,CAAC,GAACG,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,EAACf,MAAM,CAACL,OAAO,EAAEY,OAAO,CAAC,CAAC,GAACK,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,EAACf,MAAM,CAACL,OAAO,EAAEa,MAAM,CAAC,GAAC,CAAC,CAAC,EAACR,MAAM,CAACL,OAAO,EAAEe,SAAS,CAAC,CAAC,CAAC,CAAC,GAAC,GAAG,IAAE,GAAG;EAAC,OAAOC,OAAO;AAAA,CAAC;AAAC,IAAIK,QAAQ,GAACZ,qBAAqB;AAACX,OAAO,CAACE,OAAO,GAACqB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}