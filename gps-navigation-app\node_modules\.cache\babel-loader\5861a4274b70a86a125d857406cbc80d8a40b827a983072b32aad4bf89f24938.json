{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar getCoordinateKey = function getCoordinateKey(point, keysToLookup) {\n  return keysToLookup.reduce(function (foundKey, key) {\n    if (typeof point === \"undefined\" || point === null) {\n      throw new Error(\"'\".concat(point, \"' is no valid coordinate.\"));\n    }\n    if (Object.prototype.hasOwnProperty.call(point, key) && typeof key !== \"undefined\" && typeof foundKey === \"undefined\") {\n      foundKey = key;\n      return key;\n    }\n    return foundKey;\n  }, undefined);\n};\nvar _default = getCoordinateKey;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "getCoordinateKey", "point", "keysToLookup", "reduce", "<PERSON><PERSON><PERSON>", "key", "Error", "concat", "prototype", "hasOwnProperty", "call", "undefined", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/getCoordinateKey.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var getCoordinateKey=function getCoordinateKey(point,keysToLookup){return keysToLookup.reduce(function(foundKey,key){if(typeof point===\"undefined\"||point===null){throw new Error(\"'\".concat(point,\"' is no valid coordinate.\"))}if(Object.prototype.hasOwnProperty.call(point,key)&&typeof key!==\"undefined\"&&typeof foundKey===\"undefined\"){foundKey=key;return key}return foundKey},undefined)};var _default=getCoordinateKey;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,gBAAgB,GAAC,SAASA,gBAAgBA,CAACC,KAAK,EAACC,YAAY,EAAC;EAAC,OAAOA,YAAY,CAACC,MAAM,CAAC,UAASC,QAAQ,EAACC,GAAG,EAAC;IAAC,IAAG,OAAOJ,KAAK,KAAG,WAAW,IAAEA,KAAK,KAAG,IAAI,EAAC;MAAC,MAAM,IAAIK,KAAK,CAAC,GAAG,CAACC,MAAM,CAACN,KAAK,EAAC,2BAA2B,CAAC,CAAC;IAAA;IAAC,IAAGN,MAAM,CAACa,SAAS,CAACC,cAAc,CAACC,IAAI,CAACT,KAAK,EAACI,GAAG,CAAC,IAAE,OAAOA,GAAG,KAAG,WAAW,IAAE,OAAOD,QAAQ,KAAG,WAAW,EAAC;MAACA,QAAQ,GAACC,GAAG;MAAC,OAAOA,GAAG;IAAA;IAAC,OAAOD,QAAQ;EAAA,CAAC,EAACO,SAAS,CAAC;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACZ,gBAAgB;AAACH,OAAO,CAACE,OAAO,GAACa,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}