{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _isDecimal = _interopRequireDefault(require(\"./isDecimal\"));\nvar _isSexagesimal = _interopRequireDefault(require(\"./isSexagesimal\"));\nvar _sexagesimalToDecimal = _interopRequireDefault(require(\"./sexagesimalToDecimal\"));\nvar _constants = require(\"./constants\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar isValidLatitude = function isValidLatitude(value) {\n  if ((0, _isDecimal.default)(value)) {\n    if (parseFloat(value) > _constants.MAXLAT || value < _constants.MINLAT) {\n      return false;\n    }\n    return true;\n  }\n  if ((0, _isSexagesimal.default)(value)) {\n    return isValidLatitude((0, _sexagesimalToDecimal.default)(value));\n  }\n  return false;\n};\nvar _default = isValidLatitude;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_isDecimal", "_interopRequireDefault", "require", "_isSexagesimal", "_sexagesimalToDecimal", "_constants", "obj", "__esModule", "isValidLatitude", "parseFloat", "MAXLAT", "MINLAT", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/isValidLatitude.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var _isDecimal=_interopRequireDefault(require(\"./isDecimal\"));var _isSexagesimal=_interopRequireDefault(require(\"./isSexagesimal\"));var _sexagesimalToDecimal=_interopRequireDefault(require(\"./sexagesimalToDecimal\"));var _constants=require(\"./constants\");function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var isValidLatitude=function isValidLatitude(value){if((0,_isDecimal.default)(value)){if(parseFloat(value)>_constants.MAXLAT||value<_constants.MINLAT){return false}return true}if((0,_isSexagesimal.default)(value)){return isValidLatitude((0,_sexagesimalToDecimal.default)(value))}return false};var _default=isValidLatitude;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,UAAU,GAACC,sBAAsB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAAC,IAAIC,cAAc,GAACF,sBAAsB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAAC,IAAIE,qBAAqB,GAACH,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAAC,IAAIG,UAAU,GAACH,OAAO,CAAC,aAAa,CAAC;AAAC,SAASD,sBAAsBA,CAACK,GAAG,EAAC;EAAC,OAAOA,GAAG,IAAEA,GAAG,CAACC,UAAU,GAACD,GAAG,GAAC;IAACP,OAAO,EAACO;EAAG,CAAC;AAAA;AAAC,IAAIE,eAAe,GAAC,SAASA,eAAeA,CAACV,KAAK,EAAC;EAAC,IAAG,CAAC,CAAC,EAACE,UAAU,CAACD,OAAO,EAAED,KAAK,CAAC,EAAC;IAAC,IAAGW,UAAU,CAACX,KAAK,CAAC,GAACO,UAAU,CAACK,MAAM,IAAEZ,KAAK,GAACO,UAAU,CAACM,MAAM,EAAC;MAAC,OAAO,KAAK;IAAA;IAAC,OAAO,IAAI;EAAA;EAAC,IAAG,CAAC,CAAC,EAACR,cAAc,CAACJ,OAAO,EAAED,KAAK,CAAC,EAAC;IAAC,OAAOU,eAAe,CAAC,CAAC,CAAC,EAACJ,qBAAqB,CAACL,OAAO,EAAED,KAAK,CAAC,CAAC;EAAA;EAAC,OAAO,KAAK;AAAA,CAAC;AAAC,IAAIc,QAAQ,GAACJ,eAAe;AAACX,OAAO,CAACE,OAAO,GAACa,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}