{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar toDeg = function toDeg(value) {\n  return value * 180 / Math.PI;\n};\nvar _default = toDeg;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "toDeg", "Math", "PI", "_default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/GPS/gps-navigation-app/node_modules/geolib/es/toDeg.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.default=void 0;var toDeg=function toDeg(value){return value*180/Math.PI};var _default=toDeg;exports.default=_default;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,OAAO,GAAC,KAAK,CAAC;AAAC,IAAIC,KAAK,GAAC,SAASA,KAAKA,CAACF,KAAK,EAAC;EAAC,OAAOA,KAAK,GAAC,GAAG,GAACG,IAAI,CAACC,EAAE;AAAA,CAAC;AAAC,IAAIC,QAAQ,GAACH,KAAK;AAACH,OAAO,CAACE,OAAO,GAACI,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}